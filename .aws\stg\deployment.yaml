apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: sjrc-api-service-staging-deployment
    app.kubernetes.io/instance: sjrc-api-service-staging-deployment
    app.kubernetes.io/version: '1.0.0'
    app.kubernetes.io/managed-by: kubectl
  name: sjrc-api-service-staging-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sjrc-api-service-staging # 要和template中的标签保持一致
  template:
    metadata:
      labels:
        app: sjrc-api-service-staging
    spec:
      containers:
        - image: 404904371652.dkr.ecr.ap-southeast-1.amazonaws.com/sjrc-service-staging:latest
          imagePullPolicy: Always
          name: sjrc-api-service-staging
          envFrom:
            - configMapRef:
                name: sjrc-api-service-staging-env
          ports:
            - containerPort: 3000
          resources:
            limits:
              cpu: 2000m
            requests:
              cpu: 500m
