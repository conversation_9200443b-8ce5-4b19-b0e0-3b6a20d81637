apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/name: sjrc-api-service-staging-service
    app.kubernetes.io/instance: sjrc-api-service-staging-service
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: backend
    app.kubernetes.io/managed-by: kubectl
  name: sjrc-api-service-staging-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:ap-southeast-1:404904371652:certificate/c39c9464-10a6-46ad-b40e-a028171b7a31
    # service.beta.kubernetes.io/aws-load-balancer-backend-protocol: (|http|ssl|tcp)
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
    service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
spec:
  selector:
    app: sjrc-api-service-staging
  type: LoadBalancer
  ports:
    - name: http
      port: 80
      targetPort: 3000
    - name: https
      port: 443
      targetPort: 3000
