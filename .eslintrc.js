module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2018, // Allows for the parsing of modern ECMAScript features
    project: 'tsconfig.json',
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin', 'simple-import-sort'],
  extends: ['plugin:@typescript-eslint/recommended'],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    // 'unused-imports/no-unused-imports-ts': 'error',
    '@typescript-eslint/member-ordering': 'error',
    '@typescript-eslint/no-duplicate-imports': 'error',
    '@typescript-eslint/no-require-imports': 'error',
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        argsIgnorePattern: '^_',
      },
    ],
    '@typescript-eslint/strict-boolean-expressions': 'off',
    "@typescript-eslint/no-empty-function": "warn",
    "quotes": ["error", "single", { "allowTemplateLiterals": true }],
    "comma-dangle": ["error", {
      "arrays": "always-multiline",
      "objects": "always-multiline",
      "imports": "always-multiline",
      "exports": "always-multiline",
      "functions": "never"
    }],
    "max-len": ["error", { "code": 200 }],
    "arrow-parens": ["error", "always"],
    "semi": ["error", "never"],
    "vue/html-closing-bracket-newline": "off",
    // Import 排序规则 - 所有导入在一个组中，不添加空行
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          [
            // 1. Node.js 内置模块
            '^node:',
            // 2. NestJS 框架
            '^@nestjs',
            // 3. 第三方包 (moment, ramda, etc.)
            '^[a-z]', '^@[a-z]',
            // 4. 项目内部绝对路径
            '^@/common', '^@/entities', '^@/enums',
            '^@/modules',
            '^@/utils', '^@/',
            // 5. 相对导入 - 父级目录
            '^\\.\\.',
            // 6. 相对导入 - 同级目录
            '^\\.'
          ]
        ]
      }
    ],
    "indent": "off",
    "@typescript-eslint/indent": ["error", 2, {
      "ignoredNodes": [
        "PropertyDefinition",
        "PropertyDefinition[decorators]",
        "Decorator",
        "TSTypeParameterInstantiation",
        "TSPropertySignature",
        "ClassProperty"
      ],
      "SwitchCase": 1,
      "offsetTernaryExpressions": true
    }],
    "curly": ["error", "all"],
    "@typescript-eslint/no-empty-interface": 0
  },
}
