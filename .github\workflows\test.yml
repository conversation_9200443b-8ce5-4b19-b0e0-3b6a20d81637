name: Push Official Node.js 20 to Public ECR

on:
  workflow_dispatch: # 手动触发

jobs:
  push-node-image:
    runs-on: ubuntu-latest
    permissions:
      id-token: write # 如果使用 IAM Roles for OIDC
      contents: read

    steps:
      - name: Install Docker
        run: |
          sudo apt-get update -y
          sudo apt-get install -y docker.io

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          # 使用 IAM Role (推荐) 或 Secrets
          role-to-assume: arn:aws:iam::YOUR_ACCOUNT_ID:role/github-action-role
          aws-region: us-east-1 # ⚠️ Public ECR 只能在 us-east-1

      - name: Login to AWS Public ECR
        run: |
          aws ecr-public get-login-password --region us-east-1 | \
          docker login --username AWS --password-stdin public.ecr.aws

      - name: Pull, Tag, and Push Node.js 20
        env:
          PUBLIC_REPO: public.ecr.aws/z2j2b6e3/node # 👈 替换为你自己的仓库地址
        run: |
          # 拉取官方镜像
          docker pull node:20

          # 打上 Public ECR 的标签
          docker tag node:20 $PUBLIC_REPO:20
          docker tag node:20 $PUBLIC_REPO:latest

          # 推送
          docker push $PUBLIC_REPO:20
          docker push $PUBLIC_REPO:latest

          echo "✅ Node.js 20 镜像已推送至 $PUBLIC_REPO"
          echo "🌐 访问 https://gallery.ecr.aws/z2j2b6e3/node 查看"