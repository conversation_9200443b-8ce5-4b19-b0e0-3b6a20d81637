FROM public.ecr.aws/z2j2b6e3/node:20.15.1
RUN apt-get update \
  && apt-get install -y wget gnupg \
  && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
  && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
  && apt-get update \
  && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
  --no-install-recommends \
  && apt-get install -y ghostscript graphicsmagick \
  && rm -rf /var/lib/apt/lists/*

RUN apt-get update && \
  DEBIAN_FRONTEND=noninteractive \
  apt-get -y install default-jre-headless && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

WORKDIR /home/<USER>/app

ARG TRAVIS_COMMIT
ARG TRAVIS_JOB_NUMBER
ARG TRAVIS_BRANCH

COPY package.json yarn.lock ./

RUN yarn --production --ignore-engines --frozen-lockfile
# RUN yarn build replace from copy

COPY dist ./

ENV PORT=3000 \
  NODE_ENV=${TRAVIS_BRANCH} \
  TRAVIS_COMMIT=${TRAVIS_COMMIT} \
  TRAVIS_JOB_NUMBER=${TRAVIS_JOB_NUMBER}

EXPOSE 3000

CMD ["node", "main.js"]