### About BookCentro Service

&nbsp;&nbsp;&nbsp;&nbsp;This is book centro backend api service

### Prerequirement

- node >= v16.14.0
- yarn >= v1.22.17
- docker-compose >= v1.29.2
- docker >= v20.10.7
- [Prerequirements installing steps, for reference only.](docs/installations.md)

### Canvas requirements

- https://github.com/Automattic/node-canvas

  ```bash

  brew install pkg-config cairo pango libpng jpeg giflib librsvg

  ```

### Set up

- installation

  ```bash
  # set up all databases
  docker-compose up -d

  # If the yarn command has not installed, run `npm i yarn -g` in advance.
  yarn install
  ```

- scripts

  ```bash
  # run test
  yarn test

  # or start
  yarn start

  # or start in dev mode
  yarn start:dev
  ```

### Lint rules and coding agreements

- [BookCentro backend service code lint rules and agreements](docs/agreements.md)

### Exception doc

#
- [Exception documents generated automatically](docs/exceptions.md)
