version: '3.4'

services:
  mysql:
    image: mysql/mysql-server:8.0
    command: --default-authentication-plugin=mysql_native_password
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_USER=general-user
      - MYSQL_PASSWORD=password
      - MYSQL_DATABASE=sjrc
    ports:
      - target: 3306
        published: 3306
        mode: host
  # flyway:
  #   image: flyway/flyway
  #   command: migrate
  #   environment:
  #     FLYWAY_URL: ***********************************
  #     FLYWAY_USER: general-user
  #     FLYWAY_PASSWORD: password
  #     FLYWAY_CONNECT_RETRIES: 30
  #     # You should only use the below flags when in stag/prod
  #     # In local dev&test, you should always init with clean DB
  #     # FLYWAY_BASELINE_ON_MIGRATE: "true"
  #     # FLYWAY_BASELINE_VERSION: "1.0.0"
  #     # FLYWAY_BASELINE_DESCRIPTION: "flyway baseline"
  #   volumes:
  #     - ./migrations:/flyway/sql
  #   depends_on:
  #     - mysql

  redis:
    image: redis:3.2.12-alpine
    command: redis-server --requirepass 123456
    ports:
      - 6379:6379
