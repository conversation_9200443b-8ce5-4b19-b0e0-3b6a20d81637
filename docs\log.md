## 创建合同

curl -X 'POST' \
 'http://127.0.0.1:3012/v1/admin/contracts' \
 -H 'accept: application/json' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'x-current-locale: zh_HK' \
 -H 'Authorization: <PERSON><PERSON> ey<PERSON>hbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU' \
 -H 'Content-Type: application/json' \
 -d '{
"bookCount": 50,
"copiesCount": 50,
"schoolId": 1,
"serialNumber":"abc123",
"books": [
{
"bookId": 184,
"copiesCount": 100
}
]
}'

{
"code": 201,
"data": {
"serialNumber": "abc123",
"bookCount": 50,
"copiesCount": 50,
"status": "DRAFT",
"school": {
"id": 1
},
"createdBy": {
"email": "<EMAIL>",
"userId": "71d94bf7e0aa447d81787829f004385c",
"givenName": "Liu",
"familyName": "Ethan",
"profileImage": "http://124.71.80.170:81/icon/42.png"
},
"bookCountBeforeUpdated": 0,
"copiesCountBeforeUpdated": 0,
"contractBooks": [
{
"book": {
"id": 184
},
"copiesCount": 100,
"id": 2
}
],
"updatedBy": null,
"deletedBy": null,
"publishedAt": null,
"publishedBy": null,
"createdAt": "2024-01-31T10:44:30.052Z",
"updatedAt": "2024-01-31T10:44:30.052Z",
"id": 2
}
}

## 获取合同列表

curl -X 'GET' \
 'http://127.0.0.1:3012/v1/admin/contracts?pageIndex=1&pageSize=5&schoolId=1' \
 -H 'accept: application/json' \
 -H 'x-current-locale: zh_HK' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU'

{
"code": 200,
"data": {
"items": [
{
"createdAt": "2024-01-31T10:44:30.052Z",
"updatedAt": "2024-01-31T10:44:30.052Z",
"createdBy": {
"email": "<EMAIL>",
"userId": "71d94bf7e0aa447d81787829f004385c",
"givenName": "Liu",
"familyName": "Ethan",
"profileImage": "http://124.71.80.170:81/icon/42.png"
},
"updatedBy": null,
"deletedBy": null,
"id": 2,
"serialNumber": "abc123",
"bookCountBeforeUpdated": 0,
"copiesCountBeforeUpdated": 0,
"bookCount": 50,
"copiesCount": 50,
"status": "DRAFT",
"publishedAt": null,
"publishedBy": null,
"school": {
"createdAt": "2022-08-28T18:17:10.579Z",
"updatedAt": "2023-11-20T01:39:53.000Z",
"createdBy": null,
"updatedBy": null,
"deletedBy": null,
"id": 1,
"schoolId": "ff0e4481a9584406a46d54d4711840c8",
"name": {
"en_uk": "Hainan University",
"zh_HK": "海南大学"
},
"address": {
"en_uk": "1212",
"zh_HK": "23231"
},
"region": "SG",
"description": {
"en_uk": "",
"zh_HK": "111"
},
"logo": "https://images-develop.trusive.hk/public/images/8e43c4a39a104ba7b59a1f8c8f2d55f1.png",
"principalName": "Cindy Yang",
"prefixNo": "+86",
"principalNo": "+853121212",
"principalEmail": "<EMAIL>",
"joinedAt": 1661739430,
"webUrl": "hainan",
"adminPanelUrl": "hainan",
"studentEmailSuffix": "",
"teacherEmailSuffix": "",
"status": "active",
"version": "SUBSCRIPTION",
"hasOTP": false,
"isSharingTime": false,
"type": "PRIMARY",
"staffLevelIds": [
2,
3,
4,
5,
1
],
"isAllLevelForStaff": false,
"isAllLevelForStudent": false,
"studentLevelIds": [
2,
3,
4,
5
],
"hasNotified": false
}
}
],
"total": 1,
"pageIndex": 1,
"pageSize": 5
}
}

## 更新合同

curl -X 'PATCH' \
 'http://127.0.0.1:3012/v1/admin/contracts/2' \
 -H 'accept: application/json' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'x-current-locale: zh_HK' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU' \
 -H 'Content-Type: application/json' \
 -d '{
"bookCount": 50,
"copiesCount": 50,
"books": [
{
"bookId": 182,
"copiesCount": 50
}
]
}'

{
"code": 200,
"data": {
"createdAt": "2024-01-31T10:44:30.052Z",
"updatedAt": "2024-01-31T10:44:30.052Z",
"createdBy": {
"email": "<EMAIL>",
"userId": "71d94bf7e0aa447d81787829f004385c",
"givenName": "Liu",
"familyName": "Ethan",
"profileImage": "http://124.71.80.170:81/icon/42.png"
},
"updatedBy": null,
"deletedBy": null,
"id": 2,
"serialNumber": "abc123",
"bookCountBeforeUpdated": 0,
"copiesCountBeforeUpdated": 0,
"bookCount": 50,
"copiesCount": 50,
"status": "DRAFT",
"publishedAt": null,
"publishedBy": null,
"school": {
"createdAt": "2022-08-28T18:17:10.579Z",
"updatedAt": "2023-11-20T01:39:53.000Z",
"createdBy": null,
"updatedBy": null,
"deletedBy": null,
"id": 1,
"schoolId": "ff0e4481a9584406a46d54d4711840c8",
"name": {
"en_uk": "Hainan University",
"zh_HK": "海南大学"
},
"address": {
"en_uk": "1212",
"zh_HK": "23231"
},
"region": "SG",
"description": {
"en_uk": "",
"zh_HK": "111"
},
"logo": "https://images-develop.trusive.hk/public/images/8e43c4a39a104ba7b59a1f8c8f2d55f1.png",
"principalName": "Cindy Yang",
"prefixNo": "+86",
"principalNo": "+853121212",
"principalEmail": "<EMAIL>",
"joinedAt": 1661739430,
"webUrl": "hainan",
"adminPanelUrl": "hainan",
"studentEmailSuffix": "",
"teacherEmailSuffix": "",
"status": "active",
"version": "SUBSCRIPTION",
"hasOTP": false,
"isSharingTime": false,
"type": "PRIMARY",
"staffLevelIds": [
2,
3,
4,
5,
1
],
"isAllLevelForStaff": false,
"isAllLevelForStudent": false,
"studentLevelIds": [
2,
3,
4,
5
],
"hasNotified": false
},
"contractBooks": [
{
"book": {
"id": 182
},
"copiesCount": 50,
"id": 3
}
]
}
}

## 获取指定合同

curl -X 'GET' \
 'http://127.0.0.1:3012/v1/admin/contracts/2' \
 -H 'accept: application/json' \
 -H 'x-current-locale: zh_HK' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU'

{
"code": 200,
"data": {
"createdAt": "2024-01-31T10:44:30.052Z",
"updatedAt": "2024-01-31T10:44:30.052Z",
"deletedAt": null,
"createdBy": {
"email": "<EMAIL>",
"userId": "71d94bf7e0aa447d81787829f004385c",
"givenName": "Liu",
"familyName": "Ethan",
"profileImage": "http://124.71.80.170:81/icon/42.png"
},
"updatedBy": null,
"deletedBy": null,
"id": 2,
"serialNumber": "abc123",
"bookCountBeforeUpdated": 0,
"copiesCountBeforeUpdated": 0,
"bookCount": 50,
"copiesCount": 50,
"status": "DRAFT",
"publishedAt": null,
"publishedBy": null,
"contractBooks": [
{
"isbn": "9789888833177",
"bookName": "testing",
"rowNumber": 0,
"copiesCount": 50,
"bookId": 182,
"authorNames": [
"岑皓"
]
}
]
}
}

## 获取指定学校未发布合同

curl -X 'GET' \
 'http://127.0.0.1:3012/v1/admin/draft-contracts?schoolId=1' \
 -H 'accept: application/json' \
 -H 'x-current-locale: zh_HK' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU'

{
"code": 200,
"data": {
"createdAt": "2024-01-31T10:44:30.052Z",
"updatedAt": "2024-01-31T10:44:30.052Z",
"deletedAt": null,
"createdBy": {
"email": "<EMAIL>",
"userId": "71d94bf7e0aa447d81787829f004385c",
"givenName": "Liu",
"familyName": "Ethan",
"profileImage": "http://124.71.80.170:81/icon/42.png"
},
"updatedBy": null,
"deletedBy": null,
"id": 2,
"serialNumber": "abc123",
"bookCountBeforeUpdated": 0,
"copiesCountBeforeUpdated": 0,
"bookCount": 50,
"copiesCount": 50,
"status": "DRAFT",
"publishedAt": null,
"publishedBy": null,
"contractBooks": [
{
"isbn": "9789888833177",
"bookName": "testing",
"rowNumber": 0,
"copiesCount": 50,
"bookId": 182,
"authorNames": [
"岑皓"
]
}
]
}
}

## 发布

curl -X 'POST' \
 'http://127.0.0.1:3012/v1/admin/contracts/publish' \
 -H 'accept: application/json' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'x-current-locale: zh_HK' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU' \
 -H 'Content-Type: application/json' \
 -d '{
"schoolId": 1
}'

{
"code": 201,
"data": {
"status": true
}
}

## 获取批量上传样本

curl -X 'POST' \
 'http://127.0.0.1:3012/v1/admin/contract-books-file/example' \
 -H 'accept: _/_' \
 -H 'x-current-locale: zh_HK' \
 -H 'x-auth-schema: ADMIN' \
 -H 'x-current-platform: web' \
 -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU' \
 -d ''

## 解码批量上传文件

/v1/admin/contract-books-file/decode
