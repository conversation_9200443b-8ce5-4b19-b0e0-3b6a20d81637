module.exports = {
  moduleFileExtensions: ['ts', 'js', 'json'],
  rootDir: 'src',
  modulePaths: ['<rootDir>'],
  testRegex: '.spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  setupFiles: ['<rootDir>/../test/setup.ts'],
  moduleNameMapper: {
    '@modules/(.*)': '<rootDir>/modules/$1',
    '@test/(.*)': '<rootDir>/../test/$1',
    '@/(.*)': '<rootDir>/$1',
  },
}
