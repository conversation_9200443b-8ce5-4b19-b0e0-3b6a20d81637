{"name": "SJRC-API", "version": "1.1.2", "description": "SJRC backend api service", "main": "main.js", "repository": "https://github.com/BookCentro/BookCentro-Service.git", "author": "<EMAIL>", "license": "MIT", "private": true, "engines": {"npm": ">=8.3.1", "node": ">=16.14.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint --ext .js,.ts src", "lint:fix": "eslint --ext .js,.ts src --fix", "test": "jest -i", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "doc:error": "node bin/cli doc:error", "prepare": "husky install"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/text-to-speech": "^5.8.0", "@nestjs/axios": "^3.0.0", "@nestjs/bull": "^10.0.0", "@nestjs/cache-manager": "^2.0.0", "@nestjs/common": "^10.4.16", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.4.16", "@nestjs/event-emitter": "^2.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/platform-express": "^10.4.16", "@nestjs/platform-socket.io": "^10.4.16", "@nestjs/schedule": "^6.0.0", "@nestjs/sequelize": "^11.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.16", "@sendgrid/mail": "^7.6.2", "@socket.io/redis-adapter": "^7.2.0", "adm-zip": "^0.5.10", "adm-zip-iconv": "^0.4.9", "amqplib": "^0.10.2", "aws-sdk": "^2.1102.0", "bcrypt": "^5.0.1", "bull": "^4.8.3", "cache-manager": "^5.2.0", "cache-manager-redis-store": "3.0.1", "change-case": "^4.1.2", "cheerio": "1.0.0-rc.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "commander": "^9.0.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "decimal.js": "^10.4.3", "docx": "^9.1.0", "excel4node": "^1.8.2", "exceljs": "^4.3.0", "excellent": "^2.1.7", "express": "^4.20.0", "fluent-ffmpeg": "^2.1.3", "generate-password": "^1.7.0", "gm": "^1.25.0", "google-auth-library": "^7.14.0", "immutable": "^4.0.0", "ioredis": "^4.28.5", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "jwk-to-pem": "^2.0.5", "keyv": "^5.0.0", "lodash": "^4.17.21", "mime": "^3.0.0", "moment": "^2.29.1", "moment-timezone": "0.6.0", "mongoose": "^6.13.6", "mustache": "^4.2.0", "mysql2": "^3.9.8", "nestjs-pino": "^2.3.0", "node-ebook-converter": "^1.0.11", "node-excel-export": "^1.4.4", "node-rsa": "^1.1.1", "openai": "^4.97.0", "p-limit": "3.1.0", "pdf2html": "^3.1.0", "pdf2pic": "^3.2.0", "pdfjs-dist": "^4.2.67", "pino": "^9.7.0", "pino-http": "^6.6.0", "pretty-error": "^4.0.0", "promise-limit": "^2.7.0", "puppeteer": "^14.0.0", "ramda": "^0.27.1", "redis": "^4.5.1", "redlock": "^4.2.0", "reflect-metadata": "^0.1.13", "request-ip": "^2.1.3", "rxjs": "^7.4.0", "sequelize": "^6.29.0", "sharp": "^0.34.3", "socket.io": "^4.5.4", "socket.io-client": "^4.5.1", "source-map-support": "^0.5.21", "stripe": "^8.187.0", "swagger-ui-express": "^4.3.0", "titlecase": "^1.1.3", "twilio": "^5.8.0", "typedarray-to-buffer": "^4.0.0", "typeorm": "^0.3.0", "urlencode": "^1.1.0", "uuid": "^8.3.2", "xml2js": "^0.4.23"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^12.0.0", "@nestjs/cli": "^8.1.4", "@nestjs/testing": "^10.4.16", "@types/bcrypt": "^5.0.0", "@types/bull": "^3.15.8", "@types/cron": "^2.0.0", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.13", "@types/gm": "^1.25.0", "@types/ioredis": "^4.28.8", "@types/jest": "^27.0.2", "@types/jsonwebtoken": "^8.5.8", "@types/jwk-to-pem": "^2.0.1", "@types/lodash": "^4.14.178", "@types/mime": "^3.0.0", "@types/multer": "^1.4.7", "@types/mustache": "^4.1.2", "@types/node": "18", "@types/ramda": "^0.27.48", "@types/redlock": "^4.0.3", "@types/request-ip": "^0.0.37", "@types/sequelize": "^4.28.11", "@types/source-map-support": "^0.5.4", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "eslint": "^8.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.0", "jest": "27.0.6", "lint-staged": "^10.5.4", "pino-pretty": "^7.5.1", "sequelize-typescript": "^2.1.6", "supertest": "^6.1.6", "ts-jest": "^27.0.7", "ts-loader": "^9.2.6", "ts-node": "^10.9.1", "tsconfig-paths": "^3.11.0", "typescript": "~4.9.5"}, "lint-staged": {"*.ts": ["eslint --fix"]}, "resolutions": {"undici": "5.28.4", "commander": "^9.0.0", "constantinople": "^3.1.1", "uglify-js": "2.6.0", "webpack": "5.94.0"}}