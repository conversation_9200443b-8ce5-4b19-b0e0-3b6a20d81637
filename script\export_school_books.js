const mysql = require('mysql2/promise');
const ExcelJS = require('exceljs');
const dayjs = require('dayjs');



// 创建数据库连接
const dbConfig = {
  host: '', // 数据库主机
  user: '', // 数据库用户名
  password: '', // 数据库密码
  database: '', // 数据库名称
  port:3306,
};


async function exportDataToExcel() {
  // 创建连接池
  const connection = await mysql.createConnection(dbConfig);
  //  任务一，导出该学校学生可阅读书籍数据   school 保良局林文燦英文小學  plklmceps   student_level_ids [2, 3, 4, 5, 7]   
  // WHERE JSON_CONTAINS(b.level, '2') 
  // OR JSON_CONTAINS(b.level, '3') 
  // OR JSON_CONTAINS(b.level, '4') 
  // OR JSON_CONTAINS(b.level, '5') 
  // OR JSON_CONTAINS(b.level, '7')
  //  任务二，导出该学校教职员可阅读书籍数据 school 陳瑞祺 (喇沙) 書院 csklsc   

  const school_url_name = "csklsc";
  try {
    // 查询数据库
    const [rows] = await connection.execute(`
        SELECT 
            b.id,
            JSON_UNQUOTE(JSON_EXTRACT(b.name, '$.zh_HK')) AS book_name,
            JSON_UNQUOTE(JSON_EXTRACT(b.description, '$.zh_HK')) AS description,
            JSON_UNQUOTE(JSON_EXTRACT(b.publisher_group_name, '$.zh_HK')) AS publisher_group_name,
            b.isbn,
            b.published_at,
            b.level,
            b.language,
            GROUP_CONCAT(JSON_UNQUOTE(JSON_EXTRACT(a.name, '$.zh_HK')) SEPARATOR ', ') AS author_names
        FROM books b
        LEFT JOIN authors a 
            ON JSON_CONTAINS(b.author_ids, CAST(a.id AS JSON), '$')
        where b.deleted_at is null
        GROUP BY b.id;

    `);
    
    // 创建 Excel 工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Books Data');

    // 添加表头
    worksheet.columns = [
      { header: '書名', key: 'book_name', width: 30 },
      { header: 'URL', key: 'url', width: 40 },
      { header: '書籍語言', key: 'language', width: 10 },
      { header: '作者(多個作者使用英文 ,隔開)', key: 'author_names', width: 15 },
      { header: '出版社', key: 'publisher_group_name', width: 30 },
      { header: '出版年', key: 'published_at', width: 15 },
      { header: '描述', key: 'description', width: 30 },
      { header: 'ISBN', key: 'isbn', width: 15 },
    ];

    // 填充数据
    rows.forEach((row) => {
        row.published_at = dayjs(row.published_at).format('YYYY-MM-DD');
        if(row.language == 'en'){
            row.language = '英文';
        }else{
            row.language = '中文';
        }
        row.url= `https://sjrc.club/${school_url_name}/zh-HK/bookInfo/${row.id}`;
        worksheet.addRow(row);
    });

    // 导出为 Excel 文件
    await workbook.xlsx.writeFile('BooksData.xlsx');
    console.log('数据已成功导出至 BooksData.xlsx');

  } catch (error) {
    console.error('查询数据库时出错:', error);
  } finally {
    await connection.end();
  }
}

// 执行导出函数
exportDataToExcel();
