import R from 'ramda'
import { CommandRegistry } from './common'
import { GenerateApiDocCommand, GenerateErrorDocCommand } from './common/commands'
import { AppModule } from './modules/app.module'

const registry = new CommandRegistry(AppModule)

R.pipe(
  R.values,
  R.forEach((Command: any) => registry.registerCommand(new Command()))
)({ GenerateApiDocCommand, GenerateErrorDocCommand })
registry.run() 
