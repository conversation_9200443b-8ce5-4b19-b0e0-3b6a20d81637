import { WithMongoModule } from './withMongo.module'
import WithRabbitMQModule from './withRabbitMQ.module'
import WithRedisModule from './withRedis.module'
import WithSequelizeModule from './withSequelize.module'
import WithTypeOrmModule from './withTypeOrm.module'

export default {
  withRedis: WithRedisModule,
  withTypeOrm: WithTypeOrmModule,
  withSequelize: WithSequelizeModule,
  withRabbitMQ: WithRabbitMQModule,
  withMongo: WithMongoModule,
}
