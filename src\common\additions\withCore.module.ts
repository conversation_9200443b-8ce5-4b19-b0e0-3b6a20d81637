import { HttpModule, HttpService } from '@nestjs/axios'
import {
  ClassSerializerInterceptor,
  DynamicModule,
  Module,
  OnModuleInit,
  ValidationPipe,
} from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core'
import { ScheduleModule } from '@nestjs/schedule'
import { LoggerModule, PinoLogger } from 'nestjs-pino'
import { CommonController } from '../controllers'
import { HttpExceptionFilter, UnknownExceptionFilter } from '../filters'
import { TransformInterceptor } from '../interceptors'
import { ConfigOptions } from '../interface'
import { EmailService, ExcelService, S3Service } from '../services'
import { JwtService } from '../services/jwt.service'
import { OneSignalService } from '../services/oneSignal.service'

@Module({})
export default class WithCoreModule implements OnModuleInit {
  constructor(
    private readonly httpService: HttpService,
    private readonly logger: PinoLogger,
  ) {}

  static forRoot(configOptions: ConfigOptions): DynamicModule {
    return {
      module: WithCoreModule,
      imports: [
        ScheduleModule.forRoot(),
        ConfigModule.forRoot({
          envFilePath:
            process.env.NODE_ENV === 'test' ? ['.env'] : ['.env.local', '.env'],
          load: configOptions.namespaces,
          validationSchema: configOptions.schema,
          validationOptions: {
            abortEarly: true,
            allowUnknown: true,
          },
        }),
        LoggerModule.forRootAsync({
          inject: [ConfigService],
          useFactory: async (config: ConfigService) => config.get('common.withCore.pino'),
        }),
        HttpModule,
      ],
      controllers: [CommonController],
      providers: [
        ExcelService,
        ConfigService,
        JwtService,
        EmailService,
        S3Service,
        OneSignalService,
        {
          provide: APP_INTERCEPTOR,
          useClass: TransformInterceptor,
        },
        {
          provide: APP_INTERCEPTOR,
          useClass: ClassSerializerInterceptor,
        },
        {
          provide: APP_FILTER,
          useClass: UnknownExceptionFilter,
        },
        {
          provide: APP_FILTER,
          useClass: HttpExceptionFilter,
        },
        {
          provide: APP_PIPE,
          useValue: new ValidationPipe({ transform: true }),
        },
      ],
      exports: [
        ConfigService,
        JwtService,
        S3Service,
        HttpModule,
        EmailService,
        ExcelService,
        OneSignalService,
      ],
    }
  }

  onModuleInit() {
    this.httpService.axiosRef.interceptors.request.use(
      (config) => {
        // this.logger.info(config)
        return config
      },
      (error) => {
        throw error
      },
    )

    this.httpService.axiosRef.interceptors.response.use(
      (response) => response, // TODO transform the response ??
      (error) => {
        // this.logger.error(error)
        throw error
      },
    )
  }
}
