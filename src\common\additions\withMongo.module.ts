import { Global, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { MongooseModule } from '@nestjs/mongoose'

@Global()
@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) =>
        configService.get('common.withMongo'),
      inject: [ConfigService],
    }),
  ],
  providers: [ConfigService],
})
export class WithMongoModule {}
