import { DynamicModule, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { RabbitMQModule } from '../components/rabbitMQ'

const rabbitMQModule: DynamicModule = RabbitMQModule.forRootAsync({
  useFactory: (configService: ConfigService) => configService.get('common.withRabbitMQ'),
  inject: [ConfigService],
})
@Module({
  imports: [rabbitMQModule],
  exports: [rabbitMQModule],
})
export default class WithRabbitMQModule {}
