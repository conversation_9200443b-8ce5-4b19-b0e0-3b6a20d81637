import { DynamicModule, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { RedisModule } from '../components/redis'

const redisModule: DynamicModule = RedisModule.forRootAsync({
  useFactory: async (configService: ConfigService) =>
    configService.get('common.withRedis'),
  inject: [ConfigService],
})
@Module({
  imports: [redisModule],
  exports: [redisModule],
})
export default class WithRedisModule {}
