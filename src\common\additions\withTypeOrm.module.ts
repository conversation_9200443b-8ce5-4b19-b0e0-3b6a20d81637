import { Global, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { TypeOrmModule as NestTypeOrmModule } from '@nestjs/typeorm'

@Global()
@Module({
  imports: [
    NestTypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) =>
        configService.get('common.withTypeOrm'),
      inject: [ConfigService],
    }),
  ],
  providers: [ConfigService],
  exports: [NestTypeOrmModule],
})
export default class WithTypeOrmModule {}
