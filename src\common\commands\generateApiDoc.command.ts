import { INestApplication, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { SwaggerModule } from '@nestjs/swagger'
import fs from 'fs'
import path from 'path'
import { BaseCommand } from '../components/command'

type Options = {
  filepath: string
}

export class GenerateApiDocCommand extends BaseCommand {
  getName() {
    return 'doc:api'
  }

  getOptions() {
    return [
      {
        name: 'filepath',
        shortFlag: 'p',
        description: 'specify the path for file',
        defaultValue: './swagger.json',
      },
    ]
  }

  appContextRequired() {
    return true
  }

  async execute(app: INestApplication, options: Options) {
    const { filepath } = options
    const configService = app.get(ConfigService)
    const swaggerConfig = configService.get('application.swagger')
    const document = SwaggerModule.createDocument(app, swaggerConfig)
    const message = `The absolute path of the file is: ${path.resolve(filepath)}`

    fs.writeFileSync(filepath, JSON.stringify(document))
    Logger.log(message, 'GenerateApiDoc')
  }
}
