import { Logger } from '@nestjs/common'
import fs from 'fs'
import path from 'path'
import prettier from 'prettier'
import R from 'ramda'
import { BaseCommand, CommandOption } from '../components/command'
import { exceptionRegistry } from '../decorators/exceptionMeta.decorator'

const header = `
# Error Doc

## Response Example

\`\`\`json
{
  "code": 10117,
  "message": "Invalid auth header format",
  "data": {
    "name": "UnauthorizedException",
    "multilingualMessage": {
      "zh_HK": "未授權異常",
      "en_us": "UnauthorizedException"
    },
    "params": {
      "statusCode": 401,
      "message": "Invalid auth header format",
      "error": "Unauthorized"
    },
    "detail":"UnauthorizedException: Invalid auth header format - auth.guard.ts:87 AuthGuard.getHeaderToken..."
  }
}
\`\`\`

## Error List

| No. | Error Code | Error Name | HTTP Status | Message |
| --- | ---------- | ---------- | ----------- |---------|
`.trim()

export class GenerateErrorDocCommand extends BaseCommand {
  getName() {
    return 'doc:error'
  }

  getOptions(): CommandOption[] {
    return [
      {
        name: 'd',
        type: 'number',
      },
    ]
  }

  async execute() {
    const filepath = path.join(process.cwd(), './docs/exceptions.md')
    const message = `The absolute path of the file is: ${filepath}`

    R.pipe(
      R.values,
      (values: any) =>
        values.map(
          ({ status, code, name, messageZh }, index) =>
            `| ${index + 1} | ${code} | ${name} | ${status} | ${messageZh} |`,
        ),
      R.prepend(header),
      R.join('\n'),
      (content) => prettier.format(content, { parser: 'markdown' }),
      (content) => fs.writeFileSync(filepath, content),
    )(exceptionRegistry)

    Logger.log(message, 'GenerateErrorDoc')
  }
}
