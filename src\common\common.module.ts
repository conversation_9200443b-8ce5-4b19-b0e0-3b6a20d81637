import { DynamicModule, Global, Module } from '@nestjs/common'
import R from 'ramda'
import additions from './additions'
import WithCoreModule from './additions/withCore.module'
import { ConfigOptions } from './interface'

type Optional = Exclude<keyof typeof additions, 'withCore'>
type ModuleOptions = { [K in Optional]?: boolean }

@Global()
@Module({})
export class CommonModule {
  static forRoot(
    configOptions: ConfigOptions,
    moduleOptions: ModuleOptions = {},
  ): DynamicModule {
    const coreModule = WithCoreModule.forRoot(configOptions)
    const enabled = R.pipe(R.filter(R.equals(true)), R.keys)(moduleOptions)
    const imports = [coreModule, ...R.pipe(R.pick(enabled), R.values)(additions)]

    return {
      global: true,
      module: CommonModule,
      imports,
      exports: imports,
    }
  }
}
