#!/usr/bin/env node
/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

const dotenv = require('dotenv')

const { APP_ENV, NODE_ENV } = dotenv.config().parsed
const hasDist = fs.existsSync(path.join(__dirname, '../../../../../', 'dist'))
const requiredTs =
  APP_ENV === 'local' ||
  NODE_ENV === 'test' ||
  !hasDist
 
const runningWithTs = !!process[Symbol.for('ts-node.register.instance')]
const reloadWithTs = requiredTs && !runningWithTs

if (reloadWithTs) {
  const rest = process.argv.slice(1)
  return spawn('yarn ts-node -r tsconfig-paths/register', rest, {
    stdio: 'inherit',
    shell: true,
  })
}
require(path.join(__dirname, 'run.spec-command'))
