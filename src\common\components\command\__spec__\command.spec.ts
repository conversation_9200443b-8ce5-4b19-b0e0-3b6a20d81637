import { exec } from 'child_process'
import path from 'path'
import R from 'ramda'

const runCommand = (command: string) =>
  new Promise((resolve, reject) => {
    exec(command, (err) => {
      R.isNil(err) ? resolve(true) : reject(err)
    })
  })

describe('command', () => {
  beforeAll(async () => {
    const cliFile = path.join(__dirname, 'cli')
    await runCommand(`chmod 777 ${cliFile}`)
  })

  it(
    'execute command should ok',
    async () => {
      await expect(
        runCommand(`${path.join(__dirname, 'cli')} test:command`),
      ).resolves.toBeTruthy()
    },
    15 * 1000,
  )
})
