import { INestApplication } from '@nestjs/common'
import { BaseCommand } from '../base.command'
import { CommandRegistry } from '../commandRegistry'

class TestCommand extends BaseCommand {
  getName(): string {
    return 'test:command'
  }

  async execute(
    app: INestApplication,
    options: Record<string, any>,
    args: string[],
  ): Promise<void> {
    console.log(options, args)
  }
}

const registry = new CommandRegistry()
registry.registerCommand(new TestCommand())
registry.run()
