import { INestApplication } from '@nestjs/common'
import { noCase, paramCase } from 'change-case'
import R from 'ramda'
import { langUtil } from '../../utils'
import { CommandOption, ICommand } from './interfaces'

export abstract class BaseCommand implements ICommand {
  getName(): string {
    return this.getDefaultName()
  }

  getDescription(): string {
    return noCase(this.getDefaultName())
  }

  getOptions(): CommandOption[] {
    return []
  }

  appContextRequired(): boolean {
    return false
  }

  private getDefaultName() {
    return R.pipe(langUtil.getClassName, R.replace('Command', ''), paramCase)(this)
  }

  abstract execute(
    app: INestApplication,
    options: Record<string, any>,
    args: string[],
  ): Promise<void>
}
