import { Logger } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { strict as assert } from 'assert'
import { paramCase } from 'change-case'
import { Command } from 'commander'
import _ from 'lodash/fp'
import R from 'ramda'
import { ICommand } from './interfaces'

export class CommandRegistry {
  private readonly module: unknown
  private readonly program: Command
  private readonly resolvers = {
    array: R.split(','),
    boolean: R.identity,
    number: _.toNumber,
    string: R.identity,
  }

  constructor(module?: unknown) {
    this.module = module
    this.program = new Command()
    this.program.storeOptionsAsProperties(false)
  }

  registerCommand(command: ICommand) {
    const name = command.getName()
    const description = command.getDescription()
    const options = command.getOptions()
    const appContextRequired = command.appContextRequired()
    const subProgram = new Command()

    subProgram
      .name(name)
      .description(description)
      .action(async (options, cmd) => {
        const app = appContextRequired
          ? await NestFactory.create(this.module, { logger: ['error'] })
          : null

        Logger.log(`Start executing command: ${cmd.name()}`)

        await command
          .execute(app, options, cmd.args)
          .catch((error) => Logger.error(error))

        Logger.log(`Command has been executed: ${cmd.name()}`)

        appContextRequired ? await app.close() : null
        process.exit()
      })

    options.forEach((option) => {
      const {
        name,
        shortFlag: flag,
        type = 'string',
        description,
        required,
        defaultValue,
      } = option

      assert.ok(!!name, 'name cannot be empty')

      const longerFlag = `--${paramCase(name)}`
      const shorterFlag = flag ? `-${paramCase(flag)},` : ''
      const textType = type === 'boolean' ? '' : `<${type}>`
      const flags = `${shorterFlag} ${longerFlag} ${textType}`
      const resolver = this.resolvers[type]

      const setOption = required ? subProgram.requiredOption : subProgram.option
      setOption.bind(subProgram)(flags, description, resolver, defaultValue)
    })
    this.program.addCommand(subProgram)

    return this
  }

  run() {
    this.program.parse(process.argv)
  }
}
