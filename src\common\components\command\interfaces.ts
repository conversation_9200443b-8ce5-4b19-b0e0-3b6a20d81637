import { INestApplication } from '@nestjs/common'

export type CommandOption = {
  name: string
  defaultValue?: any
  description?: string
  shortFlag?: string
  required?: boolean
  type?: 'boolean' | 'number' | 'string' | 'array'
}

export interface ICommand {
  getName(): string

  getOptions(): CommandOption[]

  getDescription(): string

  appContextRequired(): boolean

  execute(
    app: INestApplication,
    options: Record<string, any>,
    args: string[],
  ): Promise<void>
}
