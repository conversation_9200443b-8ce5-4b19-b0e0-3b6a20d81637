import { Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { MongooseModule } from '@nestjs/mongoose'

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          uri: configService.get('mongo.uri'),
          loggerLevel: configService.get('mongo.loggerLevel'),
        }
      },
      inject: [ConfigService],
    }),
  ],
  providers: [],
  exports: [],
})
export class MongoModule {}
