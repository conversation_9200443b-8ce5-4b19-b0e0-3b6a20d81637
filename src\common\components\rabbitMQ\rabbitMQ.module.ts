import { DynamicModule, Global, Module } from '@nestjs/common'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { HookEventEmitter } from '@/common/hookEvent'
import { MQ_CONFIGS } from './constants'
import { RabiitMQDelayTaskService } from './rabbitMQDelayTask.service'
import { RabbitMQTaskService } from './rabbitMQTask.service'
import { RabbitTopicMQService } from './rabbitMQTopic.service'

@Global()
@Module({})
export class RabbitMQModule {
  static forRootAsync({ useFactory, imports = [], inject = [] }): DynamicModule {
    return {
      module: RabbitMQModule,
      imports: [EventEmitterModule.forRoot(), ...imports],
      providers: [
        { provide: MQ_CONFIGS, useFactory, inject },
        RabbitMQTaskService,
        RabbitTopicMQService,
        RabiitMQDelayTaskService,
        HookEventEmitter,
      ],
      exports: [RabbitMQTaskService, RabbitTopicMQService, RabiitMQDelayTaskService],
    }
  }
}
