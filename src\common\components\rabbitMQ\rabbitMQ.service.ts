import amqp, { ConfirmChannel, Connection, ConsumeMessage } from 'amqplib'
import { PinoLogger } from 'nestjs-pino'
import R from 'ramda'
import { HookEventEmitter } from '@/common/hookEvent'
import { PayLoad, PublishPayload } from './interface'

export class RabbitMQService {
  protected connection: Connection
  protected channel: ConfirmChannel
  protected isConnected = false

  constructor(
    protected readonly options,
    private readonly eventEmitter: HookEventEmitter,
    private readonly logger: PinoLogger,
  ) {}

  async onInit() {
    this.connection = await amqp.connect(this.options.url)
    this.isConnected = true
    this.connection.on('error', (err) => this.logger.error(err, 'MQ connection error'))
    this.channel = await this.connection.createConfirmChannel()
    this.channel.on('close', () => {
      this.isConnected = false
      this.logger.info(this.options, 'channel is close')
    })
    this.channel.on('error', (err) =>
      this.logger.error(err, 'An error occurred on channel'),
    )
  }

  async onDestroy() {
    await this.channel?.close()
    await this.connection?.close()
  }

  protected async publish(
    exchangeName: string,
    routingKey: string,
    payload: PayLoad,
    options: Partial<amqp.Options.Publish> = {},
  ): Promise<boolean> {
    if (!this.isConnected) {
      await this.onInit()
    }
    const content: PublishPayload = {
      sendingTimestamp: Date.now(),
      ...payload,
    }
    try {
      // TODO retry publish when failed
      const result = this.channel.publish(
        exchangeName,
        routingKey,
        Buffer.from(JSON.stringify(content)),
        { ...options, persistent: true },
      )
      result
        ? this.logger.info(`job ${payload.msgName} sent successfully`)
        : this.logger.error(
            `the channel’s write buffer is full, ${payload.msgName} was not sent`,
          )
      return result
    } catch (err) {
      this.logger.error(err, 'publish message failed')
      return false
    }
  }

  protected async onMessage(msg: ConsumeMessage | null) {
    let payload: PublishPayload
    try {
      payload = JSON.parse(msg.content.toString())
      await this.eventEmitter.dispatch({
        type: payload.msgName,
        payload,
      })
      this.channel.ack(msg)
    } catch (err) {
      if (R.isNil(msg) || R.isNil(payload)) {
        return this.channel.ack(msg)
      }
      this.logger.error(err, 'consume message failed')
      this.channel.nack(msg, false, false)
    }
  }
}
