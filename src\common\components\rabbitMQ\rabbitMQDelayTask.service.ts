import { Inject, Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common'
import { PinoLogger } from 'nestjs-pino'
import { HookEventEmitter } from '@/common/hookEvent'
import { PayLoad } from './interface'
import { RabbitMQService } from './rabbitMQ.service'

@Injectable()
export class RabiitMQDelayTaskService
  extends RabbitMQService
  implements OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('MQ_CONFIGS') config,
    eventEmitter: HookEventEmitter,
    logger: PinoLogger,
  ) {
    super(config.delayTaskMQ, eventEmitter, logger)
  }

  async onModuleInit() {
    await this.onInit()

    await this.channel.prefetch(this.options.prefetch)

    await Promise.all([
      this.channel.assertExchange(this.options.exchangeName, 'direct', {
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertExchange(this.options.deadLetterExchangeName, 'direct', {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.assertQueue(this.options.queueName, {
        deadLetterExchange: this.options.deadLetterExchangeName,
        deadLetterRoutingKey: this.options.deadLetterQueueRoutingKey,
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertQueue(this.options.deadLetterQueueName, {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.bindQueue(
        this.options.queueName,
        this.options.exchangeName,
        this.options.queueRoutingKey,
      ),
      this.channel.bindQueue(
        this.options.deadLetterQueueName,
        this.options.deadLetterExchangeName,
        this.options.deadLetterQueueRoutingKey,
      ),
    ])

    await this.subscribe()
  }

  async publishDeplayTask(payload: PayLoad): Promise<boolean> {
    return this.publish(
      this.options.exchangeName,
      this.options.queueRoutingKey,
      payload,
      {
        expiration: Number(process.env.MINING_SESSION_PERIOD || 86400) * 1000,
      },
    )
  }

  async onModuleDestroy() {
    await this.onDestroy()
  }

  private async subscribe() {
    await this.channel.consume(this.options.deadQueueName, (msg) => this.onMessage(msg), {
      noAck: false,
    })
  }
}
