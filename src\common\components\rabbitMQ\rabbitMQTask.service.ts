import { Inject, Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common'
import { <PERSON><PERSON>Logger } from 'nestjs-pino'
import { HookEventEmitter } from '@/common/hookEvent'
import { PayLoad } from './interface'
import { RabbitMQService } from './rabbitMQ.service'

@Injectable()
export class RabbitMQTaskService
  extends RabbitMQService
  implements OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('MQ_CONFIGS') config,
    eventEmitter: HookEventEmitter,
    logger: PinoLogger,
  ) {
    super(config.asyncTaskMQ, eventEmitter, logger)
  }

  async onModuleInit() {
    await this.onInit()
    await this.channel.prefetch(this.options.prefetch)
    await Promise.all([
      this.channel.assertExchange(this.options.exchangeName, 'direct', {
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertExchange(this.options.deadLetterExchangeName, 'direct', {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.assertQueue(this.options.queueName, {
        deadLetterExchange: this.options.deadLetterExchangeName,
        deadLetterRoutingKey: this.options.deadLetterQueueRoutingKey,
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertQueue(this.options.deadLetterQueueName, {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.bindQueue(
        this.options.queueName,
        this.options.exchangeName,
        this.options.queueRoutingKey,
      ),
      this.channel.bindQueue(
        this.options.deadLetterQueueName,
        this.options.deadLetterExchangeName,
        this.options.deadLetterQueueRoutingKey,
      ),
    ])
    await this.subscribe()
  }

  async publishTask(payload: PayLoad): Promise<boolean> {
    return this.publish(this.options.exchangeName, this.options.queueRoutingKey, payload)
  }

  async onModuleDestroy() {
    await this.onDestroy()
  }

  private async subscribe() {
    await this.channel.consume(this.options.queueName, (msg) => this.onMessage(msg), {
      noAck: false,
    })
  }
}
