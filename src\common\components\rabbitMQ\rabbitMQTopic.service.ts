import { Inject, Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common'
import { <PERSON><PERSON>Logger } from 'nestjs-pino'
import { HookEventEmitter } from '@/common/hookEvent'
import { PayLoad, QueueOptions } from './interface'
import { RabbitMQService } from './rabbitMQ.service'

@Injectable()
export class RabbitTopicMQService
  extends RabbitMQService
  implements OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject('MQ_CONFIGS') config,
    eventEmitter: HookEventEmitter,
    logger: PinoLogger,
  ) {
    super(config.topicMQ, eventEmitter, logger)
  }

  async onModuleInit() {
    await this.onInit()
    await Promise.all([
      this.channel.assertExchange(this.options.exchangeName, 'topic', {
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertExchange(this.options.deadLetterExchangeName, 'topic', {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.assertQueue(this.options.queueName, {
        deadLetterExchange: this.options.deadLetterExchangeName,
        durable: true,
        autoDelete: false,
      }),
      this.channel.assertQueue(this.options.deadLetterQueueName, {
        durable: true,
        autoDelete: false,
      }),
    ])

    await Promise.all([
      this.channel.bindQueue(
        this.options.queueName,
        this.options.exchangeName,
        `#.${this.options.queueRoutingKey}.#`,
      ),
      this.channel.bindQueue(
        this.options.deadQueueName,
        this.options.deadLetterExchangeName,
        `#.${this.options.deadLetterQueueRoutingKey}.#`,
      ),
    ])
  }

  async publishMessage(topic: string, payload: PayLoad): Promise<boolean> {
    return this.publish(this.options.exchangeName, topic, payload)
  }

  async subscribe(
    queueName: string,
    deadQueueName: string,
    binding: string,
    options: QueueOptions,
  ) {
    await Promise.all([
      this.channel.assertQueue(queueName, {
        deadLetterExchange: this.options.deadLetterExchangeName,
        ...options,
      }),
      this.channel.assertQueue(deadQueueName, options),
    ])
    await this.channel.prefetch(this.options.prefetch)
    await Promise.all([
      this.channel.bindQueue(queueName, this.options.exchangeName, binding),
      this.channel.bindQueue(deadQueueName, this.options.deadLetterExchangeName, binding),
    ])
    await this.channel.consume(queueName, async (msg) => this.onMessage(msg), {
      noAck: false,
    })
  }

  async onModuleDestroy() {
    await this.onDestroy()
  }
}
