import { Test, TestingModule } from '@nestjs/testing'
import { CommonModule } from '@/common/common.module'
import config from '../../../../../test/config'
import { RedisService } from '../redis.service'

describe('RedisService', () => {
  let module: TestingModule
  let redisService: RedisService

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config, { withRedis: true })],
    }).compile()

    redisService = module.get(RedisService)
  })

  afterAll(async () => {
    await module.close()
  })

  describe('redis is ok', () => {
    it('can get redis instance', async () => {
      expect(redisService.instance).not.toBeUndefined()

      await expect(redisService.instance.set('name', 'sjrc')).resolves.toBe('OK')
      await expect(redisService.instance.get('name')).resolves.toBe('sjrc')
    })
  })
})
