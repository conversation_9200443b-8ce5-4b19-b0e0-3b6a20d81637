import { Test, TestingModule } from '@nestjs/testing'
import { AssertionError } from 'assert'
import Redlock from 'redlock'
import { CommonModule } from '@/common/common.module'
import config from '../../../../../test/config'
import { RedlockService } from '../redlock.service'

describe('RedlockService', () => {
  let module: TestingModule
  let redlockService: RedlockService

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config, { withRedis: true })],
    }).compile()

    redlockService = module.get(RedlockService)
  })

  afterAll(async () => {
    await module.close()
  })

  describe('#lock', () => {
    it('should be locked', async () => {
      const resource = 'resource-id-1'
      await expect(redlockService.lock(resource, 3000)).resolves.toBeInstanceOf(
        Redlock.Lock,
      )
      await expect(redlockService.lock(resource, 100)).rejects.toThrow(Redlock.LockError)
    })

    it('should be manually unlocked', async () => {
      const resource = 'resource-id-2'
      const lock = await redlockService.lock(resource, 3000)
      expect(lock).toBeInstanceOf(Redlock.Lock)
      await lock.unlock()
      await expect(redlockService.lock(resource, 100)).resolves.toBeInstanceOf(
        Redlock.Lock,
      )
    })

    it('should be automatically unlocked when it expires', async () => {
      const resource = 'resource-id-3'
      await expect(redlockService.lock(resource, 100)).resolves.toBeInstanceOf(
        Redlock.Lock,
      )
      await expect(redlockService.lock(resource, 100)).resolves.toBeInstanceOf(
        Redlock.Lock,
      )
    })

    it('should throw AssertionError', async () => {
      try {
        const resource = 'resource-id-1'
        await redlockService.lock(resource, 60001)
        throw 'ensures that the catch block is executed'
      } catch (err) {
        expect(err).toBeInstanceOf(AssertionError)
        expect(err.message).toBe('ttl 60001 is too large')
      }
    })
  })
})
