import { Test, TestingModule } from '@nestjs/testing'
import moment from 'moment'
import config from '../../../../../test/config'
import { CommonModule } from '../../../common.module'
import { AuthSchema, ELoginMethod, EPlatform } from '../../../enums'
import { EUserSessionStatus, IUserSession, SessionService } from '../session.service'

describe('SessionService', () => {
  let module: TestingModule
  let service: SessionService
  const session: IUserSession = {
    userId: 'user_123',
    authSchema: AuthSchema.CLIENT,
    loginMethod: ELoginMethod.EMAIL,
    platform: EPlatform.MOBILE_WEB,
    createdAt: moment().format('YYYY/MM/DD HH:mm:ss'),
    expireIn: '30S',
    status: EUserSessionStatus.ENABLED,
    permissions: [],
  }

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config, { withRedis: true })],
    }).compile()

    service = module.get(SessionService)
  })

  afterAll(async () => {
    await module.close()
  })

  it('refresh token ok', async () => {
    await expect(
      service.refreshSession(session.userId, session, { expiration: 10 }),
    ).resolves.toBeTruthy()
  })

  it('get session ok', async () => {
    const savedSession = await service.getSession(session.userId)
    expect(savedSession).toMatchObject(session)
  })

  it('delete session ok', async () => {
    await expect(service.deleteSession(session.userId)).resolves.toBeTruthy()
  })
})
