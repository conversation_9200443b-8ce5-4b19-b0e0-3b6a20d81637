import { DynamicModule, Module } from '@nestjs/common'
import { REDIS_OPTIONS } from './constants'
import { RedisService } from './redis.service'
import { RedlockService } from './redlock.service'
import { SessionService } from './session.service'

@Module({})
export class RedisModule {
  static forRootAsync({ useFactory, imports = [], inject = [] }): DynamicModule {
    return {
      module: RedisModule,
      imports,
      providers: [
        { provide: REDIS_OPTIONS, useFactory, inject },
        RedisService,
        RedlockService,
        SessionService,
      ],
      exports: [RedisService, RedlockService, SessionService],
    }
  }
}
