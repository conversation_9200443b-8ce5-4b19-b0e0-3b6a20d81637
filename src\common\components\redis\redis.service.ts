import { Inject, Injectable, OnModuleD<PERSON>roy } from '@nestjs/common'
import { default as IORedis, Redis } from 'ioredis'
import { PinoLogger } from 'nestjs-pino'
import { preprocessKeyword } from '@/common/utils/regex.util'
import { REDIS_KEY } from '../../../common/constants'
import { REDIS_OPTIONS } from './constants'

@Injectable()
export class RedisService implements OnModuleDestroy {
  private redis: Redis

  constructor(@Inject(REDIS_OPTIONS) options, private readonly logger: PinoLogger) {
    const { host, password, port, prefix = 'redis' } = options
    this.redis = new IORedis(`${prefix}://:${password}@${host}:${port}`)
    this.redis.on('connect', () => this.logger.info('redis is connected'))
    this.redis.on('ready', () => this.logger.info('redis is ready'))
    this.redis.on('error', (err) => this.logger.error(err))
  }

  get instance(): Redis {
    return this.redis
  }

  async jsonSet(key: string, path: string, value: any): Promise<void> {
    await this.redis.send_command('JSON.SET', key, path, JSON.stringify(value))
  }

  async ftSearch(indexName: string, keyword: string, type: string) {
    try {
      const preprocessedKeyword = preprocessKeyword(keyword)
      const keywords = preprocessedKeyword.split(' ')
      let redisearchQuery
      if (keywords.length > 1) {
        redisearchQuery = keywords
          .map((keyword) => `${keyword}* | *${keyword}*`)
          .join(' | ')
      } else {
        redisearchQuery = ` ${keyword}* | *${keyword}*`
      }
      const args = [indexName, redisearchQuery, 'WITHSCORES', 'LIMIT', '0', '100']
      const result = await this.redis.send_command('FT.SEARCH', args)
      const [totalCount, ...rows] = result
      console.log('ftSearch totalCount', totalCount)

      const bookDataList = []
      for (let i = 0; i < rows.length; i += 3) {
        const bookId = Number(rows[i].split(':')[1])
        const score = parseFloat(rows[i + 1])
        bookDataList.push({ bookId, score })
      }

      bookDataList.sort((a, b) => b.score - a.score)
      if (type === 'NOCONTENT') {
        return bookDataList.map((book) => book.bookId)
      }
      const limit = 20
      const fields = ['name', 'hidde_school_ids', 'status', 'version', 'level']
      const books = new Array(Math.min(limit, bookDataList.length))
      await Promise.all(
        bookDataList.slice(0, limit).map(async ({ bookId }, index) => {
          const bookData = await this.redis.send_command(
            'JSON.GET',
            `booksKeyword:${bookId}`,
            ...fields,
          )
          books[index] = { bookId, ...JSON.parse(bookData) }
        }),
      )

      return books
    } catch (error) {
      console.error('Error executing FT.SEARCH', error)
      throw new Error('Failed to execute search query')
    }
  }

  async get(key: string): Promise<string> {
    return this.redis.get(`${REDIS_KEY}.${key}`)
  }

  async getByJson(key: string): Promise<any> {
    const data = await this.redis.get(`${REDIS_KEY}.${key}`)

    return data ? JSON.parse(data) : null
  }

  async incr(key: string) {
    return this.redis.incr(`${REDIS_KEY}.${key}`)
  }

  async del(key: string) {
    return this.redis.del(`${REDIS_KEY}.${key}`)
  }

  async set(key: string, payload: string | Buffer | number | any[], expireTime?: number) {
    if (!expireTime) {
      return this.redis.set(`${REDIS_KEY}.${key}`, payload)
    } else {
      return this.redis.set(`${REDIS_KEY}.${key}`, payload, 'EX', expireTime)
    }
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return this.redis.lrange(`${REDIS_KEY}.${key}`, start, stop)
  }

  async ltrim(key: string, start: number, stop: number): Promise<'OK'> {
    return this.redis.ltrim(`${REDIS_KEY}.${key}`, start, stop)
  }

  async rpush(key: string, ...values: string[]): Promise<number> {
    return this.redis.rpush(`${REDIS_KEY}.${key}`, ...values)
  }

  async expire(key: string, expireTime: number) {
    return this.redis.expire(`${REDIS_KEY}.${key}`, expireTime)
  }

  async sadd(setName: string, member: string) {
    return this.redis.sadd(setName, member)
  }

  async smembers(setName: string) {
    return this.redis.smembers(setName)
  }

  async srem(setName: string, member: string) {
    return this.redis.srem(setName, member)
  }

  async hmset(key: string, obj: any) {
    return this.redis.hmset(`${REDIS_KEY}.${key}`, obj)
  }

  async hset(key: string, field: string, value: any) {
    return this.redis.hset(`${REDIS_KEY}.${key}`, field, value)
  }

  async hgetall(key: string) {
    return this.redis.hgetall(`${REDIS_KEY}.${key}`)
  }

  async hsetnx(key: string, field: string, value: string) {
    return this.redis.hsetnx(`${REDIS_KEY}.${key}`, field, value)
  }

  async hget(key: string, field: string) {
    return this.redis.hget(`${REDIS_KEY}.${key}`, field)
  }

  async mget(keys: string[]) {
    return this.redis.mget(keys.map((key) => `${REDIS_KEY}.${key}`))
  }

  async exists(key: string) {
    return this.redis.exists(`${REDIS_KEY}.${key}`)
  }

  pipeline() {
    return this.redis.pipeline()
  }

  async hincrby(key: string, field: string, value: number) {
    return this.redis.hincrby(`${REDIS_KEY}.${key}`, field, value)
  }

  async scanKeys(prefix: string): Promise<string[]> {
    const keys = []
    let cursor = '0'
    do {
      const res = await this.redis.scan(cursor, 'MATCH', prefix)
      cursor = res[0]
      keys.push(...res[1])
    } while (cursor !== '0')

    return [...new Set(keys.flat())]
  }

  async mdel(str: string) {
    const keys = await this.redis.keys(str)

    await Promise.all(keys.map((key) => this.redis.del(key)))
  }

  async getTtl(key: string) {
    return this.redis.ttl(`${REDIS_KEY}.${key}`)
  }

  async onModuleDestroy() {
    this.redis.disconnect()
  }
}
