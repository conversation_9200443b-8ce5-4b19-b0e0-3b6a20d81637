import { Injectable } from '@nestjs/common'
import { strict as assert } from 'assert'
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino'
import R from 'ramda'
import Red<PERSON> from 'redlock'
import { REDLOCK_LOCK } from './constants'
import { RedisService } from './redis.service'

const MAX_TTL = 11 * 60 * 1000

@Injectable()
export class RedlockService {
  private readonly redlock: Redlock

  constructor(
    private readonly redisService: RedisService,
    private readonly logger: PinoLogger,
  ) {
    this.redlock = new Redlock([this.redisService.instance], {
      driftFactor: 0.01,
      retryCount: 10,
      retryDelay: 200,
      retryJitter: 100,
    })

    this.redlock.on('clientError', (error) =>
      this.logger.error({ error }, 'A redlock error has occurred'),
    )
  }

  async lock(resource: string | string[], ttl: number): Promise<Redlock.Lock> {
    assert.ok(ttl <= MAX_TTL, `ttl ${ttl} is too large`)

    const list = Array.isArray(resource) ? resource : [resource]
    const resources = R.map((name: string) => `${REDLOCK_LOCK}-${name}`, list)

    const locker = await this.redlock.lock(resources, ttl)

    return locker
  }

  async lockWrapper<T>(
    resource: string | string[],
    ttl: number,
    fn: () => T | Promise<T>,
  ) {
    let lock
    try {
      lock = await this.lock(resource, ttl)
      return await fn()
    } catch (error) {
      // console.log('lock error', error)
      throw error
    } finally {
      await lock
        ?.unlock()
        ?.catch((error) =>
          this.logger.error({ error }, `failed to unlock resource ${resource}`),
        )
    }
  }
}
