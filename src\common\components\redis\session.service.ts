import { Injectable } from '@nestjs/common'
import { AuthSchema, ELoginMethod, EPlatform } from '../../enums'
import { RedisService } from './redis.service'

export enum EUserSessionStatus {
  ENABLED = 'enable',
  DISABLED = 'disable',
  FROZEN = 'frozen',
}

export interface IUserSession {
  userId: string
  authSchema: AuthSchema
  loginMethod: ELoginMethod
  platform: EPlatform
  status: EUserSessionStatus
  iat: number
  exp: number
  permissions?: string[]
}

export interface ICreateSessionOptions {
  expiration: number
}

@Injectable()
export class SessionService {
  constructor(private readonly redisService: RedisService) {}

  async refreshSession(
    sessionId: string,
    payload: IUserSession,
    options: ICreateSessionOptions,
  ): Promise<boolean> {
    const { expiration } = options
    const res = await this.redisService.instance.set(
      `session.${sessionId}`,
      JSON.stringify(payload),
      'EX',
      expiration,
    )
    return res === 'OK'
  }

  async createSession(
    id: string,
    sessionId: string,
    payload: IUserSession,
    options: ICreateSessionOptions,
  ) {
    const { expiration } = options
    const res = await this.redisService.instance.set(
      `session.${sessionId}`,
      JSON.stringify(payload),
      'EX',
      expiration,
    )
    if (res !== 'OK') throw Error('Create session error')

    // delete old session
    await this.deleteSession(id)

    // create session index
    await this.createSessionIndex(id, sessionId)
    // console.log({
    //   sessionId,
    //   id,
    //   payload,
    //   options,
    //   name: 'createSession',
    //   now: Date.now(),
    // })

    return true
  }

  async getSession(sessionId: string): Promise<IUserSession> {
    const session = await this.redisService.instance.get(`session.${sessionId}`)
    return session ? JSON.parse(session as string) : null
  }

  async getSessionTtl(userId: string) {
    return this.redisService.getTtl(`session.${userId}`)
  }

  async deleteSession(userId: string): Promise<boolean> {
    const sessionId = await this.getSessionIndex(userId)
    const res = await this.redisService.instance.del(`session.${sessionId}`)
    return res === 1
  }

  async deleteSessions(userIds: string[]) {
    const pipe = this.redisService.instance.pipeline()
    userIds.map((userId) => pipe.get(`session.index.${userId}`))
    const sessionIds = await pipe.exec()
    const delPipe = this.redisService.instance.pipeline()
    sessionIds
      .map((item) => item[1])
      .map((sessionId) => delPipe.del(`session.${sessionId}`))
    await delPipe.exec()
  }

  async createSessionIndex(userId: string, sessionId: string) {
    const res = await this.redisService.instance.set(`session.index.${userId}`, sessionId)
    return res === 'OK'
  }

  async getSessionIndex(userId: string) {
    return this.redisService.instance.get(`session.index.${userId}`)
  }
}
