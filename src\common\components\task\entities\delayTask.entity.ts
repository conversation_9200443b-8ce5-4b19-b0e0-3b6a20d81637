import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { ETaskStatus } from '../enums'

@Entity('tasks')
export class Task extends BaseEntity<Task> {
  @PrimaryGeneratedColumn()
  id: number

  @Column({})
  queueName: string

  @Column({})
  jobId: string

  @Column({})
  jobName: string

  @Column({ nullable: true, type: 'json', default: null })
  payload?: Record<string, any>

  @Column({ nullable: true, type: 'json', default: null })
  options?: Record<string, any>

  @Column({ nullable: true, default: null })
  error?: string

  @Column({})
  status: ETaskStatus
}
