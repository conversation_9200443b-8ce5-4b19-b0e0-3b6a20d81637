export enum ETaskType {
  PARSE_BOOK_TASK = 'parse_book_task',
  UPDATE_HOT_BOOK_LEVEL_TASK = 'update_hot_book_level_task',
  HOT_SEARCH_WORD_TASK = 'hot_search_word_task',
  UPDATE_BOOK_STATUS = 'UPDATE_BOOK_STATUS',
  WEB_SOCKET_MESSAGE = 'WEB_SOCKET_MESSAGE',
  OPERATE_APPLICATION = 'OPERATE_APPLICATION',
  DATA_EXPORT = 'data_export',
  CSV = 'CSV',
  UPDATE_SCHOOL_LEVEL = 'UPDATE_SCHOOL_LEVEL',
  UPLOADED_FILE_ASSISTANT = 'uploaded_file_assistant',
  DELETE_FILE_ASSISTANT = 'delete_file_assistant',
  UPDATE_FILE_ASSISTANT = 'update_file_assistant',
  CHECK_FILE_ASSISTANT = 'check_file_assistant',
  CREATE_AI_ASSISTANT = 'CREATE_AI_ASSISTANT',
  DELETE_AI_ASSISTANT = 'DELETE_AI_ASSISTANT',
  UPDATE_AI_ASSISTANT = 'UPDATE_AI_ASSISTANT',
}

export enum ETaskStatus {
  PENDING = 'pending',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  REMOVED = 'removed',
}
