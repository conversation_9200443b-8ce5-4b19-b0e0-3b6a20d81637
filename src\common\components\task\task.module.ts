import { BullModule } from '@nestjs/bull'
import { Global, Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { TASK_QUEUE_NAME } from './constant'
import { Task } from './entities/delayTask.entity'
import { TaskService } from './task.service'

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([Task]),
    BullModule.forRootAsync({
      useFactory: (configService: ConfigService) => {
        const {
          host,
          password,
          port,
          prefix = 'redis',
        } = configService.get('common.withRedis')
        return {
          redis: `${prefix}://:${password}@${host}:${port}`,
        } as any
      },
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: TASK_QUEUE_NAME,
    }),
  ],
  controllers: [],
  providers: [TaskService],
  exports: [TaskService],
})
export class TaskModule {}
