import { InjectQueue } from '@nestjs/bull'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Queue } from 'bull'
import { PinoLogger } from 'nestjs-pino'
import R from 'ramda'
import { Repository } from 'typeorm'
import { generateUniqueId } from '@/common/utils'
import { TASK_QUEUE_NAME } from './constant'
import { Task } from './entities/delayTask.entity'
import { ETaskStatus, ETaskType } from './enums'

@Injectable()
export class TaskService {
  constructor(
    @InjectQueue(TASK_QUEUE_NAME)
    private taskQueue: Queue,

    @InjectRepository(Task)
    private readonly taskRepository: Repository<Task>,

    private readonly logger: PinoLogger
  ) {}

  async deliver<T>(
    type: ETaskType,
    payload: T,
    options?: {
      delay?: number
      attempts?: number
      lifo?: boolean
    }
  ) {
    const _options = {
      ...options,
      removeOnComplete: true,
    }
    // const num = await this.taskQueue.getActiveCount()
    // const delayCount = await this.taskQueue.getDelayedCount()
    // const items = await this.taskQueue.getJobs([
    //   'completed',
    //   'waiting',
    //   'active',
    //   'delayed',
    //   'failed',
    //   'paused',
    // ])
    // console.log(num, delayCount)

    const delayTask = new Task({
      queueName: this.taskQueue.name,
      jobId: generateUniqueId('job'),
      jobName: type,
      payload,
      options: _options,
      status: ETaskStatus.PENDING,
    })
    const task = await this.replaceTaskLog(delayTask)
    const job = await this.taskQueue.add(type, { ...payload, taskId: task.id }, _options)
    // console.log({ job })
    task.jobId = String(job.id)
    return this.replaceTaskLog(task)
  }

  async cancel(jobId: string, jobName: string) {
    const job = await this.taskQueue.getJob(jobId)
    if (job) {await job.remove()}
    const log = await this.getTaskLog({ jobId, jobName })
    log.status = ETaskStatus.REMOVED
    return this.replaceTaskLog(log)
  }

  async runTask<T>(id: number, fn: () => T | Promise<T>) {
    const task = await this.getTaskLog({ id })
    // console.log({ task })
    if (!task || task.status !== ETaskStatus.PENDING) {
      this.logger.warn(`can not find  task ${id} or job was finished`)
    }
    let res: T
    try {
      res = await fn()
      if (task) {task.status = ETaskStatus.SUCCEEDED}
    } catch (error) {
      console.log(JSON.stringify({ id, ...error }))
      if (task) {
        task.error = { id, ...error }
        task.status = ETaskStatus.FAILED
      }
      res = undefined
    } finally {
      await this.replaceTaskLog(task)
    }
    return res
  }

  private async replaceTaskLog(delayTask: Task) {
    if (!delayTask) {
      return
    }
    return this.taskRepository.save(delayTask)
  }

  private async getTaskLog(options: { jobId?: string; jobName?: string; id?: number }) {
    const condition = R.pick(['jobName', 'jobId', 'id'], options)
    if (Object.keys(condition).length === 0) {
      console.log({ warning: 'no task id' })
      return
    }
    return this.taskRepository.findOne({ where: condition })
  }
}
