// metadata
export const METADATA_EXCEPTION_CODE = 'metadata:exceptionCode'
export const METADATA_EXCEPTION_STATUS = 'metadata:exceptionStatus'
export const METADATA_RESOURCE_LOCK = 'metadata:resourceLock'

export const METADATA_EXCEPTION_MESSAGE_ZH_HK = 'metadata:exceptionMessageZh'
export const METADATA_EXCEPTION_MESSAGE_EN_US = 'metadata:exceptionMessageEn'
export const METADATA_EXCEPTION_MESSAGE_ZH_CN = 'metadata:exceptionMessageCn'

// auth
export const METADATA_AUTH_SCHEMA = 'metadata:authSchema'
export const METADATA_PERMISSIONS = 'metadata:permissions'
export const AUTH_SECRET_SERVICE = 'authSecret:Service'

export const HEADER_X_AUTH_SCHEMA = 'x-auth-schema'
export const HEADER_X_CURRENT_PLATFORM = 'x-current-platform'
export const HEADER_X_CURRENT_LOCALE = 'x-current-locale'
export const HEADER_X_CURRENT_SIGNATURE = 'x-current-signature'
export const HEADER_X_CURRENT_ENCRYPTED_METHOD = 'x-current-encrypted-method'

export const REDIS_KEY = 'redis.key'
