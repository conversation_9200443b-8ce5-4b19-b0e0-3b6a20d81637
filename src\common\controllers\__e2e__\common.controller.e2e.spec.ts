import { INestApplication } from '@nestjs/common'
import { Test } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'
import { CommonController } from '../common.controller'

describe('Common controller', () => {
  let app: INestApplication

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
      controllers: [CommonController],
    }).compile()
    app = await module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
  })

  it('health check', async () => {
    await request(app.getHttpServer()).get('/healthz').expect(200)
  })
})
