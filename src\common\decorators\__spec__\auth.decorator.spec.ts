import { Controller, Get, INestApplication } from '@nestjs/common'
import { Test, TestingModule } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'
import { ELocaleType } from '../../enums'
import { PublicAuth } from '../auth.decorator'
import { CurrentLocale } from '../currentHeaderField.decorator'

@Controller()
class TestingController {
  @Get('local')
  @PublicAuth()
  getLocal(@CurrentLocale() local: unknown) {
    return { local }
  }
}

describe('CurrentUser', () => {
  let module: TestingModule
  let app: INestApplication

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config, { withRedis: true })],
      controllers: [TestingController],
    }).compile()

    app = module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
    await module.close()
  })

  it('should return local', async () => {
    await request(app.getHttpServer())
      .get('/local')
      .set('x-current-locale', ELocaleType.ENGLISH)
      .expect(200)
      .expect({ code: 200, data: { local: ELocaleType.ENGLISH } })
  })
})
