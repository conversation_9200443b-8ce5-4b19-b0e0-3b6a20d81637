import { Controller, Get, INestApplication } from '@nestjs/common'
import { Test, TestingModule } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { AuthSchema, ELoginMethod } from '../../../common'
import { CommonModule } from '../../common.module'
import { AdminAuth, ApiAuth, ClientAuth, InternalsAuth } from '../auth.decorator'
import {
  CurrentLoginMethod,
  CurrentPartner,
  CurrentService,
  CurrentUser,
} from '../currentAuthInfo.decorator'

@Controller()
class TestingController {
  @Get('fake/:id')
  @ClientAuth()
  getUser(@CurrentUser() user: unknown) {
    console.log('user:', user)
    return user
  }

  @Get('partners/:id')
  @ApiAuth()
  getPartner(@CurrentPartner() partner: unknown) {
    return partner
  }

  @Get('login/:id')
  @AdminAuth()
  getLoginInfo(@CurrentLoginMethod() method: unknown) {
    return { loginMethod: method }
  }

  @Get('service')
  @InternalsAuth()
  getService(@CurrentService() service: unknown) {
    return service
  }
}

describe('CurrentUser', () => {
  let module: TestingModule
  let app: INestApplication

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config, { withRedis: true })],
      controllers: [TestingController],
    }).compile()

    app = module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
    await module.close()
  })

  it('should return client', async () => {
    const client = { userId: 1, givenName: 'CC', familyName: 'orange' }
    await request(app.getHttpServer())
      .get('/fake/1')
      .authenticate(client, AuthSchema.CLIENT)
      .expect(200)
      .expect({ code: 200, data: client })
  })

  it('should return login method', async () => {
    const loginMethod = { loginMethod: ELoginMethod.GOOGLE }
    await request(app.getHttpServer())
      .get('/login/1')
      .authenticate(loginMethod, AuthSchema.ADMIN)
      .expect(200)
      .expect({ code: 200, data: loginMethod })
  })
})
