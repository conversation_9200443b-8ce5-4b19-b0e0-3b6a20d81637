import { HttpStatus } from '@nestjs/common'
import { AssertionError } from 'assert'
import {
  METADATA_EXCEPTION_CODE,
  METADATA_EXCEPTION_MESSAGE_EN_US,
  METADATA_EXCEPTION_MESSAGE_ZH_HK,
  METADATA_EXCEPTION_STATUS,
} from '../../constants'
import { exceptionRegistry, getExceptionGroup } from '../exceptionMeta.decorator'

const UserExceptionMeta = getExceptionGroup(999)

@UserExceptionMeta(1, { en_us: 'error', zh_HK: '錯誤' }, HttpStatus.NOT_FOUND)
class UserNotFoundException {}

describe('ExceptionMeta', () => {
  it('exceptionRegistry', () => {
    expect(exceptionRegistry[99901]).toStrictEqual({
      code: 99901,
      name: 'UserNotFoundException',
      status: HttpStatus.NOT_FOUND,
      messageEn: 'error',
      messageZh: '錯誤',
    })
  })

  it('should be store meta data', () => {
    const status = Reflect.getMetadata(METADATA_EXCEPTION_STATUS, UserNotFoundException)
    const code = Reflect.getMetadata(METADATA_EXCEPTION_CODE, UserNotFoundException)
    const messageEn = Reflect.getMetadata(
      METADATA_EXCEPTION_MESSAGE_EN_US,
      UserNotFoundException,
    )
    const messageZh = Reflect.getMetadata(
      METADATA_EXCEPTION_MESSAGE_ZH_HK,
      UserNotFoundException,
    )
    expect(status).toBe(HttpStatus.NOT_FOUND)
    expect(code).toBe(99901)
    expect(messageEn).toBe('error')
    expect(messageZh).toBe('錯誤')
  })

  it('should throw AssertionError', () => {
    try {
      UserExceptionMeta(1, { en_us: 'error', zh_HK: '錯誤' }, HttpStatus.FORBIDDEN)
      throw 'ensures that the catch block is executed'
    } catch (err) {
      expect(err).toBeInstanceOf(AssertionError)
      expect(err.message).toBe(
        'Duplicate exception code 99901 with group 999 and order 1',
      )
    }
  })
})
