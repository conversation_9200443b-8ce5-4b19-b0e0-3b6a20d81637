import { applyDecorators } from '@nestjs/common'
import {
  ApiProperty as SwaggerApiProperty,
  ApiPropertyOptional as SwaggerApiPropertyOptional,
  ApiPropertyOptions,
} from '@nestjs/swagger'
import { Expose, Transform } from 'class-transformer'

export const ApiProperty = (options?: ApiPropertyOptions) => {
  return applyDecorators(
    SwaggerApiProperty(options),
    Expose(),
    Transform((x) => (x.value === null ? undefined : x.value)),
  )
}

export const ApiPropertyOptional = (options?: ApiPropertyOptions) => {
  return applyDecorators(
    SwaggerApiPropertyOptional(options),
    Expose(),
    Transform((x) => (x.value === null ? undefined : x.value)),
  )
}
