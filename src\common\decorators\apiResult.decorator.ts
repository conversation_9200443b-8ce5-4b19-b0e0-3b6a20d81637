import { applyDecorators, Type } from '@nestjs/common'
import { ApiResponse, getSchemaPath } from '@nestjs/swagger'

const code = { type: 'number', example: 200 }
const message = { type: 'string', example: 'success' }
const total = { type: 'number', example: 100 }
const pageIndex = { type: 'number', example: 1 }
const pageSize = { type: 'number', example: 10 }
export const ApiListResult = <TModel extends Type<any>>(
  model: TModel,
  status: number,
  description?: string,
) => {
  return applyDecorators(
    ApiResponse({
      status: status,
      description: description,
      schema: {
        required: ['code'],
        properties: {
          code,
          data: {
            type: 'array',
            items: {
              $ref: getSchemaPath(model),
            },
          },
        },
      },
    }),
  )
}

export const ApiPageResult = <TModel extends Type<any>>(
  model: TModel,
  status: number,
  description?: string,
) => {
  return applyDecorators(
    ApiResponse({
      status: status,
      description: description,
      schema: {
        required: ['code', 'message'],
        properties: {
          code,
          message,
          data: {
            required: ['total', 'pageIndex', 'pageSize'],
            properties: {
              total,
              pageIndex,
              pageSize,
              items: {
                type: 'array',
                items: {
                  $ref: getSchemaPath(model),
                },
              },
            },
          },
        },
      },
    }),
  )
}

export const ApiBaseResult = <TModel extends Type<any>>(
  model: TModel,
  status: number,
  description?: string,
) => {
  return applyDecorators(
    ApiResponse({
      status: status,
      description: description,
      schema: {
        required: ['code', 'message'],
        properties: {
          code,
          message,
          data: {
            type: 'object',
            $ref: getSchemaPath(model),
          },
        },
      },
    }),
  )
}
