import { applyDecorators, SetMetadata, UseGuards } from '@nestjs/common'
import { Api<PERSON>earerAuth, ApiHeader } from '@nestjs/swagger'
import {
  HEADER_X_AUTH_SCHEMA,
  HEADER_X_CURRENT_ENCRYPTED_METHOD,
  HEADER_X_CURRENT_LOCALE,
  HEADER_X_CURRENT_PLATFORM,
  HEADER_X_CURRENT_SIGNATURE,
  METADATA_AUTH_SCHEMA,
  METADATA_PERMISSIONS,
} from '../constants'
import { AuthSchema, EEncryptMethod, ELocaleType, EPlatform } from '../enums'
import { AuthGuard } from '../guards'

type AuthInfo = {
  schema: AuthSchema | AuthSchema[]
  permissions?: string[]
}

const AuthSchemaHeader = (schema: AuthSchema | AuthSchema[]) => {
  return ApiHeader({
    name: HEADER_X_AUTH_SCHEMA,
    required: true,
    schema: {
      default: [].concat(schema).join(' | '),
    },
  })
}

export const CurrentPlatformHeader = (defaultPlatform?: string) => {
  return ApiHeader({
    name: HEADER_X_CURRENT_PLATFORM,
    required: false,
    schema: {
      default: defaultPlatform ?? EPlatform.WEB,
    },
  })
}

export const CurrentLocaleHeader = (locale?: ELocaleType) => {
  return ApiHeader({
    name: HEADER_X_CURRENT_LOCALE,
    required: false,
    schema: {
      default: locale ?? ELocaleType.ZH_HK,
    },
  })
}

export const CurrentSignatureHeader = () => {
  return ApiHeader({
    name: HEADER_X_CURRENT_SIGNATURE,
    required: true,
    schema: {
      default: 'unknown',
    },
  })
}

export const CurrentEncryptMethodHeader = () => {
  return ApiHeader({
    name: HEADER_X_CURRENT_ENCRYPTED_METHOD,
    required: true,
    schema: {
      default: EEncryptMethod.PASSCODE,
    },
  })
}

export const Auth = (info: AuthInfo) => {
  const { schema, permissions } = info
  return applyDecorators(
    SetMetadata(METADATA_AUTH_SCHEMA, [].concat(schema)),
    SetMetadata(METADATA_PERMISSIONS, permissions),
    ApiBearerAuth(),
    AuthSchemaHeader(schema),
    CurrentPlatformHeader(),
    CurrentLocaleHeader(),
    UseGuards(AuthGuard),
  )
}

export const ApiAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.API, permissions })

export const ClientAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.CLIENT, permissions })

export const InternalsAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.INTERNAL, permissions })

export const AdminAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.ADMIN, permissions })

export const SchoolAdminAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.SCHOOL_ADMIN, permissions })

export const PublicAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.PUBLIC, permissions })

export const MerchantAuth = (permissions?: string[]) =>
  Auth({ schema: AuthSchema.MERCHANT, permissions })
