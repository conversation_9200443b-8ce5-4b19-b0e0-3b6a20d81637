import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { Request } from 'express'
import R from 'ramda'

export const CurrentUser = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    if (R.isNil(request.auth)) return undefined
    const user = R.pick(
      [
        'userId',
        'familyName',
        'givenName',
        'profileImage',
        'email',
        'inviterUserId',
        'username',
        'displayName',
        'schoolId',
        'isTeacher',
        'isRoot',
        'levels',
        'staffLevelIds',
        'isAllLevelForStaff',
        'region',
        'gradeId',
        'classId',
        'hasScienceRoom',
        'hasAssistant',
        'assistantId',
      ],
      request.auth || {},
    )
    return data ? user[data] : user
  },
)

export const CurrentAdmin = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    if (R.isNil(request.auth)) return undefined
    const user = R.pick(
      [
        'userId',
        'familyName',
        'givenName',
        'displayName',
        'profileImage',
        'roles',
        'email',
        'isRoot',
        'publisherIds',
        'roleName',
      ],
      request.auth,
    )
    return data ? user[data] : user
  },
)

export const CurrentSchoolAdmin = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    if (R.isNil(request.auth)) return undefined
    const user = R.pick(
      [
        'userId',
        'familyName',
        'givenName',
        'displayName',
        'profileImage',
        'email',
        'schoolId',
        'isRoot',
        'staffLevelIds',
        'isAllLevelForStaff',
        'studentLevelIds',
        'isAllLevelForStudent',
        'roleName',
      ],
      request.auth,
    )
    return data ? user[data] : user
  },
)

export const CurrentAuthInfo = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    if (R.isNil(request.auth)) return undefined
    return data ? request.auth[data] : request.auth
  },
)

// For partner or merchant backend
export const CurrentPartner = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const partner = R.pick(['partnerId'], request.auth)
    return data ? partner[data] : partner
  },
)

export const CurrentService = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const service = R.pick(['serviceId'], request.auth)
    return data ? service[data] : service
  },
)

export const CurrentLoginMethod = createParamDecorator(
  (data: string, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    return request.auth?.loginMethod
  },
)
