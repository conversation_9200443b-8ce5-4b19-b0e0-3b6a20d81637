import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { Request } from 'express'
import R from 'ramda'
import { HEADER_X_CURRENT_ENCRYPTED_METHOD } from '../constants'
import { EEncryptMethod, ELocaleType, EPlatform, ESource } from '../enums'

const ParamsInHeader = ['x-current-platform']

const ParamsInAuth = []

export const CurrentSource = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const source = request.headers['source']
    return source ?? ESource.WEB
  },
)

export const CurrentLocale = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const locale = request.headers['x-current-locale']
    return locale ?? ELocaleType.ZH_HK
  },
)

export const CurrentPlatform = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const platform = request.headers['x-current-platform']
    return platform || EPlatform.WEB
  },
)

export const CurrentSignature = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const signature = request.headers['x-current-signature']
    return signature || 'unknown'
  },
)

export const CurrentEncryptMethod = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()
    const method = request.headers[HEADER_X_CURRENT_ENCRYPTED_METHOD]
    return method || EEncryptMethod.PASSCODE
  },
)

export const CurrentCacheKey = createParamDecorator(
  (data: any, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest<Request>()

    const _paramsInHeader = [].concat(ParamsInHeader, data?.headers || [])
    const _paramsInAuth = [].concat(ParamsInAuth, data?.auth || [])

    const key = `cacheKey.CurrentCacheKey.${request.url}${JSON.stringify({
      ...R.pick(_paramsInHeader, request?.headers || {}),
    })}${JSON.stringify({ ...R.pick(_paramsInAuth, request?.auth || {}) })}`

    return key
  },
)
