import { HttpStatus } from '@nestjs/common'
import { strict as assert } from 'assert'
import {
  METADATA_EXCEPTION_CODE,
  METADATA_EXCEPTION_MESSAGE_EN_US,
  METADATA_EXCEPTION_MESSAGE_ZH_CN,
  METADATA_EXCEPTION_MESSAGE_ZH_HK,
  METADATA_EXCEPTION_STATUS,
} from '../constants'
import { langUtil } from '../utils'

type MultilingualErrorMessage = {
  zh_HK: string
  en_us: string
  zh_cn: string
}

export const exceptionRegistry = {}

export function getExceptionGroup(
  group: number,
  defaultStatus: HttpStatus = HttpStatus.BAD_REQUEST,
) {
  return (
    order: number,
    message: MultilingualErrorMessage = {
      zh_HK: '未知錯誤',
      en_us: 'Unknown error',
      zh_cn: '未知错误',
    },
    status: HttpStatus = defaultStatus,
  ) => ExceptionMeta(group, order, message, status)
}

function ExceptionMeta(
  group: number,
  order: number,
  message: MultilingualErrorMessage,
  status: HttpStatus,
): ClassDecorator {
  const code = group * 100 + order
  assert.ok(
    !exceptionRegistry[code],
    `Duplicate exception code ${code} with group ${group} and order ${order}`,
  )

  return (target) => {
    const name = langUtil.getClassName(target)
    exceptionRegistry[code] = {
      code,
      status,
      name,
      messageEn: message.en_us,
      messageZh: message.zh_HK,
    }

    Reflect.defineMetadata(METADATA_EXCEPTION_CODE, code, target)
    Reflect.defineMetadata(METADATA_EXCEPTION_STATUS, status, target)
    Reflect.defineMetadata(METADATA_EXCEPTION_MESSAGE_ZH_HK, message.zh_HK, target)
    Reflect.defineMetadata(METADATA_EXCEPTION_MESSAGE_ZH_CN, message.zh_cn, target)
    Reflect.defineMetadata(METADATA_EXCEPTION_MESSAGE_EN_US, message.en_us, target)
  }
}
