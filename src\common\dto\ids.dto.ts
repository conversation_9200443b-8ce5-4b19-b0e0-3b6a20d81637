import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { ArrayMinSize, IsArray, IsNumber, IsOptional } from 'class-validator'

export class IdsDto {
  @ApiProperty({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @ArrayMinSize(1)
  ids: number[]
}

export class DeleteDto {
  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1)
  ids?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1)
  exceptions?: number[]
}
