import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import R from 'ramda'

export class PageResponse<T, TModel> {
  @ApiProperty({
    description: '总记录数',
    example: 10,
  })
  total: number

  @ApiPropertyOptional({
    description: '当前分页',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @Transform(({ value }) => parseInt(value))
  pageIndex?: number

  @ApiPropertyOptional({
    description: '每页记录数',
    example: 5,
    default: 10,
    maximum: 200,
  })
  @Transform(({ value }) => parseInt(value))
  pageSize?: number

  @ApiProperty({
    description: '分页数据',
  })
  items?: T[]

  META_TYPE = 'page'

  constructor(
    partial: Partial<PageResponse<T, TModel>>,
    items?: TModel[],
    fn?: (value: TModel, option?: any) => T,
    options?: any,
  ) {
    if (!R.isNil(fn) && items?.length > 0) {
      Object.assign(this, R.omit(['items'], partial))
      this.items = items.map((item) => fn(item, options))
    } else {
      Object.assign(this, partial)
    }
  }
}

export const getPageResponse = <T, TModel>(
  partial: Partial<PageResponse<T, TModel>>,
  items?: TModel[],
  fn?: (value: TModel) => T,
  options?: any,
) => new PageResponse(partial, items, fn, options)
