import { ApiPropertyOptional } from '@nestjs/swagger'
import { Exclude, Type } from 'class-transformer'
import { IsDate, IsOptional } from 'class-validator'
import { Column, CreateDateColumn, DeleteDateColumn, UpdateDateColumn } from 'typeorm'

export abstract class BaseEntity<T> {
  @CreateDateColumn()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  createdAt?: Date

  @UpdateDateColumn()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  updatedAt?: Date

  @DeleteDateColumn()
  @Exclude()
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  deletedAt?: Date

  @ApiPropertyOptional({})
  @Column({ nullable: true, type: 'json', default: null })
  createdBy?: Record<string, any>

  @ApiPropertyOptional({})
  @Column({ nullable: true, type: 'json', default: null })
  updatedBy?: Record<string, any>

  @ApiPropertyOptional({})
  @Column({ nullable: true, type: 'json', default: null })
  deletedBy?: Record<string, any>

  constructor(partial: Partial<T>) {
    Object.assign(this, partial)
  }
}
