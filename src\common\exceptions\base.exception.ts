import { HttpException, HttpStatus } from '@nestjs/common'
import PrettyError from 'pretty-error'
import {
  METADATA_EXCEPTION_CODE,
  METADATA_EXCEPTION_MESSAGE_EN_US,
  METADATA_EXCEPTION_MESSAGE_ZH_CN,
  METADATA_EXCEPTION_MESSAGE_ZH_HK,
  METADATA_EXCEPTION_STATUS,
} from '../constants'
import { langUtil } from '../utils'

const pe = new PrettyError().withoutColors()

type BasicResponse = {
  message: string
  data?: any
}

type ErrorResponse = {
  name: string
  detail: any
  multilingualMessage?: any
  params?: any
}

export abstract class BaseException extends HttpException {
  // Original error
  private readonly originalError: Error

  constructor(
    message: string = undefined,
    data: any = undefined,
    err: Error = undefined,
  ) {
    super({ message, data }, HttpStatus.BAD_REQUEST)
    this.originalError = err
  }

  // override super.getStatus()
  getStatus() {
    return (
      Reflect.getMetadata(METADATA_EXCEPTION_STATUS, this.constructor) ||
      super.getStatus()
    )
  }

  getCode() {
    return Reflect.getMetadata(METADATA_EXCEPTION_CODE, this.constructor)
  }

  getCustomMessage() {
    const { message } = super.getResponse() as BasicResponse
    const multilingualMessage = this.getMultilingualMessage()
    return message || multilingualMessage.en_us
  }

  getMultilingualMessage() {
    return {
      zh_HK: Reflect.getMetadata(METADATA_EXCEPTION_MESSAGE_ZH_HK, this.constructor),
      zh_cn: Reflect.getMetadata(METADATA_EXCEPTION_MESSAGE_ZH_CN, this.constructor),
      en_us: Reflect.getMetadata(METADATA_EXCEPTION_MESSAGE_EN_US, this.constructor),
    }
  }

  formatError() {
    const error = this.originalError

    if (error instanceof Error) {
      return pe.render(error)
    }
    return error
  }

  getErrorName(): string {
    return langUtil.getClassName(this)
  }

  getOriginalError(): string {
    return langUtil.getClassName(this)
  }

  getResponse(): ErrorResponse {
    const baseResponse = super.getResponse() as BasicResponse
    return {
      name: langUtil.getClassName(this),
      multilingualMessage: this.getMultilingualMessage(),
      params: baseResponse.data || {},
      detail: this.formatError(),
    }
  }
}
