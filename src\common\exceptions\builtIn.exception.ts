import { HttpException, HttpStatus } from '@nestjs/common'
import { getExceptionGroup } from '../decorators'
import { ReservedExceptionGroup } from '../enums'
import { BaseException } from './base.exception'

const BuiltInExceptionMeta = getExceptionGroup(ReservedExceptionGroup.BUILT_IN)

class BuiltInException extends BaseException {
  constructor(exception: HttpException) {
    super(exception.message, exception.getResponse(), exception)
  }
}

@BuiltInExceptionMeta(
  1,
  { en_us: 'BadGatewayException', zh_HK: '網關異常', zh_cn: '网关异常' },
  HttpStatus.BAD_GATEWAY,
)
export class BadGatewayException extends BuiltInException {}

@BuiltInExceptionMeta(
  2,
  { en_us: 'BadRequestException', zh_HK: '請求參數異常', zh_cn: '请求参数异常' },
  HttpStatus.BAD_REQUEST,
)
export class BadRequestException extends BuiltInException {}

@BuiltInExceptionMeta(
  3,
  { en_us: 'ConflictException', zh_HK: '衝突異常', zh_cn: '冲突异常' },
  HttpStatus.CONFLICT,
)
export class ConflictException extends BuiltInException {}

@BuiltInExceptionMeta(
  4,
  { en_us: 'ForbiddenException', zh_HK: '訪問權限異常', zh_cn: '访问权限异常' },
  HttpStatus.FORBIDDEN,
)
export class ForbiddenException extends BuiltInException {}

@BuiltInExceptionMeta(
  5,
  { en_us: 'GatewayTimeoutException', zh_HK: '網關超時', zh_cn: '网关超时' },
  HttpStatus.GATEWAY_TIMEOUT,
)
export class GatewayTimeoutException extends BuiltInException {}

@BuiltInExceptionMeta(
  6,
  {
    en_us: 'GoneException',
    zh_HK: '目标资源的访问在源服务器上不再可用',
    zh_cn: '目标资源的访问在源服务器上不再可用',
  },
  HttpStatus.GONE,
)
export class GoneException extends BuiltInException {}

@BuiltInExceptionMeta(
  7,
  {
    en_us: 'HttpVersionNotSupportedException',
    zh_HK: 'Http協議版本異常',
    zh_cn: 'Http协议版本异常',
  },
  HttpStatus.HTTP_VERSION_NOT_SUPPORTED,
)
export class HttpVersionNotSupportedException extends BuiltInException {}

@BuiltInExceptionMeta(
  8,
  {
    en_us: 'ImATeapotException',
    zh_HK: 'ImATeapotException',
    zh_cn: 'ImATeapotException',
  },
  HttpStatus.I_AM_A_TEAPOT,
)
export class ImATeapotException extends BuiltInException {}

@BuiltInExceptionMeta(
  9,
  {
    en_us: 'InternalServerErrorException',
    zh_HK: '服務內部異常',
    zh_cn: '服务内部异常',
  },
  HttpStatus.INTERNAL_SERVER_ERROR,
)
export class InternalServerErrorException extends BuiltInException {}

@BuiltInExceptionMeta(
  10,
  {
    en_us: 'MethodNotAllowedException',
    zh_HK: 'Http Method 不支持',
    zh_cn: 'Http Method 不支持',
  },
  HttpStatus.METHOD_NOT_ALLOWED,
)
export class MethodNotAllowedException extends BuiltInException {}

@BuiltInExceptionMeta(
  11,
  { en_us: 'NotAcceptableException', zh_HK: '不可訪問', zh_cn: '不可访问' },
  HttpStatus.NOT_ACCEPTABLE,
)
export class NotAcceptableException extends BuiltInException {}

@BuiltInExceptionMeta(
  12,
  { en_us: 'NotFoundException', zh_HK: '未找到資源', zh_cn: '未找到资源' },
  HttpStatus.NOT_FOUND,
)
export class NotFoundException extends BuiltInException {
  constructor(message: string) {
    super(new HttpException(message, HttpStatus.NOT_FOUND))
  }
}

@BuiltInExceptionMeta(
  13,
  { en_us: 'NotImplementedException', zh_HK: '未實現異常', zh_cn: '未实现异常' },
  HttpStatus.NOT_IMPLEMENTED,
)
export class NotImplementedException extends BuiltInException {}

@BuiltInExceptionMeta(
  14,
  {
    en_us: 'PayloadTooLargeException',
    zh_HK: '請求負載數據量過大',
    zh_cn: '请求负载数据量过大',
  },
  HttpStatus.PAYLOAD_TOO_LARGE,
)
export class PayloadTooLargeException extends BuiltInException {}

@BuiltInExceptionMeta(
  15,
  { en_us: 'RequestTimeoutException', zh_HK: '請求超時', zh_cn: '请求超时' },
  HttpStatus.REQUEST_TIMEOUT,
)
export class RequestTimeoutException extends BuiltInException {}

@BuiltInExceptionMeta(
  16,
  {
    en_us: 'ServiceUnavailableException',
    zh_HK: '服務器不可訪問',
    zh_cn: '服务器不可访问',
  },
  HttpStatus.SERVICE_UNAVAILABLE,
)
export class ServiceUnavailableException extends BuiltInException {}

@BuiltInExceptionMeta(
  17,
  { en_us: 'UnauthorizedException', zh_HK: '未授權異常', zh_cn: '未授权异常' },
  HttpStatus.UNAUTHORIZED,
)
export class UnauthorizedException extends BuiltInException {}

@BuiltInExceptionMeta(
  18,
  {
    en_us: 'UnprocessableEntityException',
    zh_HK: '無法處理包含的實體指令',
    zh_cn: '无法处理包含的实体指令',
  },
  HttpStatus.UNPROCESSABLE_ENTITY,
)
export class UnprocessableEntityException extends BuiltInException {}

@BuiltInExceptionMeta(
  19,
  {
    en_us: 'UnsupportedMediaTypeException',
    zh_HK: '不支持的媒體類型',
    zh_cn: '不支持的媒体类型',
  },
  HttpStatus.UNSUPPORTED_MEDIA_TYPE,
)
export class UnsupportedMediaTypeException extends BuiltInException {}
