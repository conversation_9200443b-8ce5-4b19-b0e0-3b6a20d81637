import { HttpStatus } from '@nestjs/common'
import { getExceptionGroup } from '../decorators'
import { ReservedExceptionGroup } from '../enums'
import { BaseException } from './base.exception'

const GenericExceptionMeta = getExceptionGroup(ReservedExceptionGroup.GENERIC)

@GenericExceptionMeta(
  1,
  {
    en_us: 'unkown services exception',
    zh_HK: '未定義異常',
    zh_cn: '未定义异常',
  },
  HttpStatus.INTERNAL_SERVER_ERROR,
)
export class UnknownServerException extends BaseException {
  constructor(err: Error) {
    super(
      err.message || 'unkown services exception',
      {
        name: err.name,
      },
      err,
    )
  }
}

@GenericExceptionMeta(2, {
  en_us: 'Validation error',
  zh_HK: '合法性校驗異常',
  zh_cn: '合法性校验异常',
})
export class ValidationException extends BaseException {}
