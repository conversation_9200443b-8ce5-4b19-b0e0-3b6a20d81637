import { BadRequestException, Controller, Get, INestApplication } from '@nestjs/common'
import { Test, TestingModule } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'

@Controller('exception')
class TestController {
  @Get('http')
  getHttpException() {
    throw new BadRequestException()
  }
}

describe('HttpExceptionFilter', () => {
  let module: TestingModule
  let app: INestApplication

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
      providers: [],
      controllers: [TestController],
    }).compile()

    app = module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
  })

  it('should return BadRequestException', async () => {
    const res = await request(app.getHttpServer()).get('/exception/http').expect(400)
    expect(res.body).toMatchObject({
      code: 10102,
      message: 'Bad Request',
      data: {
        name: 'BadRequestException',
        multilingualMessage: { zh_HK: '請求參數異常', en_us: 'BadRequestException' },
        params: { statusCode: 400, message: 'Bad Request' },
      },
    })
  })
})
