import { Controller, Get, INestApplication } from '@nestjs/common'
import { Test, TestingModule } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'

const expectedMessage = 'just for test'

@Controller('exception')
class TestController {
  @Get('unknown')
  getUnknownException() {
    throw new Error(expectedMessage)
  }
}

describe('HttpExceptionFilter', () => {
  let module: TestingModule
  let app: INestApplication

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
      providers: [],
      controllers: [TestController],
    }).compile()

    app = module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
  })

  it('should return UnknownServerException', async () => {
    const res = await request(app.getHttpServer()).get('/exception/unknown').expect(500)
    expect(res.body).toMatchObject({
      message: expectedMessage,
      data: { name: 'UnknownServerException' },
    })
  })
})
