import { ArgumentsHost, Catch, HttpException, Logger } from '@nestjs/common'
import { BaseExceptionFilter } from '@nestjs/core'
import { Response } from 'express'
import { BaseException, UnknownServerException } from '../exceptions'
import * as BuiltInExceptions from '../exceptions/builtIn.exception'
import { langUtil } from '../utils'

const transformToBuiltInException = (exception: HttpException): BaseException => {
  const name = langUtil.getClassName(exception)
  const ActualException = BuiltInExceptions[name] ?? UnknownServerException
  return new ActualException(exception)
}

@Catch(HttpException)
export class HttpExceptionFilter extends BaseExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()
    const response = ctx.getResponse<Response>()
    if (!(exception instanceof BaseException)) {
      exception = transformToBuiltInException(exception)
    }

    const _exception = exception as BaseException
    const payload = {
      code: _exception.getCode(),
      message: _exception.getCustomMessage(),
      data: _exception.getResponse(),
    }
    Logger.error(_exception.formatError())

    response.status(_exception.getStatus()).json(payload)
  }
}

// for global exception in worker
export const getExceptionDetail = (exception: any) => {
  if (!(exception instanceof BaseException)) {
    exception = new UnknownServerException(exception)
  }
  return (exception as BaseException).getResponse()
}
