import { ArgumentsHost, Catch } from '@nestjs/common'
import { BaseExceptionFilter } from '@nestjs/core'
import { UnknownServerException } from '../exceptions'
import { HttpExceptionFilter } from './httpException.filter'

@Catch()
export class UnknownExceptionFilter extends BaseExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const actualException = new UnknownServerException(exception as Error)
    new HttpExceptionFilter().catch(actualException, host)
  }
}
