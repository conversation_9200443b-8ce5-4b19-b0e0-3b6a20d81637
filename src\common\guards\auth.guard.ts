import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Request } from 'express'
import R from 'ramda'
import { SessionService } from '../components/redis'
import {
  HEADER_X_AUTH_SCHEMA,
  METADATA_AUTH_SCHEMA,
  METADATA_PERMISSIONS,
} from '../constants'
import { AuthSchema, ELoginMethod, EPlatform } from '../enums'
import { JwtService } from '../services'

declare module 'express' {
  interface Request {
    auth: {
      // web、admin、merchant
      userId?: string
      role?: string
      platform?: EPlatform
      permissions?: string[]
      // merchant or partner's backend system
      partnerId?: string

      // internal system
      serviceId?: string

      loginMethod?: ELoginMethod
      isRoot?: boolean
    }
  }
}

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly jwtService: JwtService,
    private readonly sessionProvider: SessionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest()
    const authSchema = request.get(HEADER_X_AUTH_SCHEMA) ?? AuthSchema.PUBLIC

    const handler = context.getHandler()
    const requiredAuthSchema =
      this.reflector.get<string>(METADATA_AUTH_SCHEMA, handler) ?? []
    const requiredPermissions =
      this.reflector.get<string[]>(METADATA_PERMISSIONS, handler) ?? []

    if (
      requiredAuthSchema.length <= 0 ||
      (requiredAuthSchema.includes(AuthSchema.PUBLIC) && authSchema === AuthSchema.PUBLIC)
    ) {
      return true
    }

    // check auth schema
    if (!requiredAuthSchema.includes(authSchema)) {
      return false
    }

    request.auth = await this.verifyToken(
      request.headers.authorization,
      authSchema as AuthSchema,
    )

    if (authSchema === AuthSchema.CLIENT) {
      await this.verifySession(request.auth)
    }

    if (request.auth.isRoot || requiredPermissions.length <= 0) {
      return true
    }

    // await this.verifyPermission(
    //   request.auth?.platform,
    //   request.auth.userId,
    //   requiredPermissions,
    // )

    return true
  }

  async verifySession(auth: any): Promise<any> {
    const { sessionId } = auth
    try {
      const session = await this.sessionProvider.getSession(sessionId)
      if (!session) {
        console.log({ name: 'verifySession', sessionId, auth })
        throw new UnauthorizedException('session is not found')
      }
      // if (session.permissions) auth.permissions = session.permissions
    } catch (error) {
      console.log({ name: 'verifySession', sessionId, auth, error })
      throw new UnauthorizedException(error)
    }
  }

  private getHeaderToken(authorization = ''): string {
    const isValid = authorization.startsWith('Bearer ')
    if (!isValid) {
      throw new UnauthorizedException('Invalid auth header format')
    }
    return authorization.substring(7)
  }

  private async verifyToken(authorization: string, authSchema: AuthSchema): Promise<any> {
    const token = this.getHeaderToken(authorization)
    try {
      const secret = this.jwtService.getSecret(authSchema)
      return this.jwtService.verify(token, { authSchema, secret }) as any
      // return this.jwtService.decode(token)
    } catch (error) {
      if (authSchema === AuthSchema.CLIENT) {
        console.log({ error, authorization })
      }
      throw new UnauthorizedException(error)
    }
  }

  private async verifyPermission(
    platform: EPlatform,
    userId: string,
    permissions: string[],
  ) {
    // check session and bind permissions
    const session = await this.sessionProvider.getSession(userId)
    if (!session) throw new UnauthorizedException()
    if (R.intersection(session.permissions, permissions).length <= 0) {
      throw new UnauthorizedException('no permission')
    }
    return true
  }
}
