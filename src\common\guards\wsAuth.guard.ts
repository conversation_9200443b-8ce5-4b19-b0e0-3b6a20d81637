import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common'
import { WsException } from '@nestjs/websockets'
import { Socket } from 'socket.io'
import { JwtService } from '../services'
import { AuthGuard } from './auth.guard'

@Injectable()
export class WsAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly authGuard: AuthGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<any> {
    const client = context.switchToWs().getClient<Socket>()
    return this.verify(client)
  }

  async verify(client: Socket): Promise<any> {
    const urlParams = new URLSearchParams(client.request.url?.split('?')[1])
    const authorization =
      client.handshake?.headers?.authorization ?? urlParams.get('authorization')
    const locale =
      client.handshake?.headers?.['x-current-locale'] ?? urlParams.get('x-current-locale')

    if (!authorization) {
      throw new WsException('Authorization token not found')
    }

    try {
      const decodedToken = await this.jwtService.verifyWssToken(authorization)
      await this.authGuard.verifySession(decodedToken)

      client.data = {
        ...client.data,
        user: decodedToken,
        locale,
      }

      return decodedToken
    } catch (error) {
      throw new WsException('Invalid token')
    }
  }
}
