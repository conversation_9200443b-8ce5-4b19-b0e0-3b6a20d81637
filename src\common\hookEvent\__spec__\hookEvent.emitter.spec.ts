import { Injectable } from '@nestjs/common'
import { EventEmitter2, EventEmitterModule } from '@nestjs/event-emitter'
import { Test, TestingModule } from '@nestjs/testing'
import { HookEvent } from '../hookEvent.decorator'
import { HookEventEmitter } from '../hookEvent.emitter'
import { IHookEvent } from '../interfaces'

const fakeEvent: IHookEvent = {
  payload: {
    id: 'src_1CiPsl2eZvKYlo2CVVyt3LKy',
    object: 'source',
    amount: 1000,
  },
  type: 'source.chargeable',
}

@Injectable()
class SourceHandler {
  // @HookEvent('source.chargeable', 'description')
  // chargeable() {}
}

describe('HookEventEmitter', () => {
  let module: TestingModule
  let eventEmitter: EventEmitter2
  let hookEventEmitter: HookEventEmitter
  let sourceHandler: SourceHandler

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [EventEmitterModule.forRoot()],
      providers: [<PERSON><PERSON><PERSON><PERSON>mitter, SourceHandler],
    }).compile()

    eventEmitter = module.get(EventEmitter2)
    hookEventEmitter = module.get(HookEventEmitter)
    sourceHandler = module.get(SourceHandler)

    await module.init()
  })

  afterAll(async () => {
    await module.close()
  })

  it('emitter', async () => {
    const spy = jest.spyOn(eventEmitter, 'emitAsync').mockImplementation(async () => [])
    await hookEventEmitter.dispatch(fakeEvent)

    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenCalledWith(
      'prefix:eventHook:source.chargeable',
      fakeEvent.payload,
    )

    spy.mockRestore()
  })

  it('handler', async () => {
    const spy = jest.spyOn(sourceHandler, 'chargeable').mockImplementation(() => true)
    await expect(
      hookEventEmitter.dispatch({ ...fakeEvent, type: 'fake.type' }),
    ).rejects.toThrow('Unavailable handlers')

    await expect(hookEventEmitter.dispatch(fakeEvent)).resolves.toEqual([true])
    expect(spy).toHaveBeenCalledTimes(1)
    expect(spy).toHaveBeenCalledWith(fakeEvent.payload)

    spy.mockRestore()
  })
})
