import { Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { IHookEvent } from './interfaces'
import { getEventName } from './utils'

@Injectable()
export class HookEventEmitter {
  constructor(private eventEmitter: EventEmitter2) {}

  async dispatch<T extends IHookEvent>(event: T) {
    const { type, payload } = event
    const name = getEventName(type)
    const hasListeners = this.eventEmitter.hasListeners(name).valueOf()
    if (!hasListeners) {
      throw new Error('Unavailable handlers')
    }
    return this.eventEmitter.emitAsync(name, payload)
  }
}
