import { Controller, Get, INestApplication } from '@nestjs/common'
import { Test, TestingModule } from '@nestjs/testing'
import request from 'supertest'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'
import { PageResponse } from '../../dto'

const expectedData = { name: '<PERSON>' }
const expectedPage = { pageIndex: 1, pageSize: 5, total: 1, items: [expectedData] }

@Controller('transform')
class TestController {
  @Get('data')
  getData() {
    return expectedData
  }

  @Get('page')
  getPage() {
    return new PageResponse(expectedPage)
  }
}

describe('TransformInterceptor', () => {
  let module: TestingModule
  let app: INestApplication

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
      providers: [],
      controllers: [TestController],
    }).compile()

    app = module.createNestApplication()
    await app.init()
  })

  afterAll(async () => {
    await app.close()
  })

  it('transform data ok', async () => {
    await request(app.getHttpServer())
      .get('/transform/data')
      .expect(200)
      .expect({ code: 200, data: expectedData })
  })

  it('transform page ok', async () => {
    await request(app.getHttpServer())
      .get('/transform/page')
      .expect(200)
      .expect({ code: 200, data: expectedPage })
  })
})
