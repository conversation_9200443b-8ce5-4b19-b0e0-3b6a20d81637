import { CACHE_MANAGER,CacheInterceptor } from '@nestjs/cache-manager'
import { ExecutionContext, Inject,Injectable } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Cache } from 'cache-manager'
import { Request } from 'express'
import * as R from 'ramda'

const ParamsInHeader = ['x-current-platform']
const ParamsInAuth = []

@Injectable()
export class HttpCacheInterceptor extends CacheInterceptor {
  constructor(
    @Inject(CACHE_MANAGER) cacheManager: Cache,
      reflector: Reflector
  ) {
    super(cacheManager, reflector)
  }

  trackBy(context: ExecutionContext): string | undefined {
    const request: Request = context.switchToHttp().getRequest()
    const isGetRequest = request.method === 'GET'
    const excludePaths = []
    const url = request.url

    if (!isGetRequest || excludePaths.includes(url)) {
      return undefined
    }

    const headerParams = R.pick(ParamsInHeader, request?.headers || {})
    const authParams = R.pick(ParamsInAuth, (request as any)?.user || {})

    const key = `${url}${JSON.stringify(headerParams)}${JSON.stringify(authParams)}`

    return key
  }
}
