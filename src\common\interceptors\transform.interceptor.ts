import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { ServerResponse } from 'http'
import { PinoLogger } from 'nestjs-pino'
import R from 'ramda'
import { map } from 'rxjs/operators'

@Injectable()
export class TransformInterceptor implements NestInterceptor {
  constructor(private readonly logger: PinoLogger) {}
  intercept(context: ExecutionContext, next: CallHandler) {
    const response = context.switchToHttp().getResponse<ServerResponse>()
    const { statusCode: code, statusMessage: message } = response

    return next.handle().pipe(
      map((data) => {
        // Due to serialization, the data type is no longer known at this time
        const isPageInstance = R.pathEq(['META_TYPE'], 'page', data)
        if (isPageInstance) {
          const { items, ...meta } = R.omit(['META_TYPE'], data)
          return { code, message, data: { items, ...meta } }
        }

        return { code, message, data }
      }),
    )
  }
}
