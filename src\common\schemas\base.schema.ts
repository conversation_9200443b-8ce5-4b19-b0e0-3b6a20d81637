import { Prop, Schema } from '@nestjs/mongoose'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsOptional, IsString } from 'class-validator'

@Schema({ _id: false })
export class Operator {
  @ApiProperty()
  @Prop({ required: true, type: String, default: '' })
  userId: string

  @ApiProperty()
  @Prop({ required: true, type: String, default: '' })
  userName: string

  constructor(partial: Partial<Operator>) {
    this.userId = partial.userId
    this.userName = partial.userName
  }
}

@Schema({ _id: false })
export class MultilingualContent {
  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  english?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  espanol?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  french?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  deutsch?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  turk?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  korean?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  japanese?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  chinese?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  vietnam: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  indian?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  indonesia?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  portugal?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  malysiam?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  italian?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  fillipono?: string

  @IsString()
  @ApiPropertyOptional()
  @IsOptional()
  @Prop({ required: false, type: String })
  arab?: string
}

@Schema()
export class BaseSchema {
  @ApiProperty()
  _id: string

  @ApiProperty()
  @Prop({ required: true, type: Date, default: new Date() })
  createdAt: Date

  @ApiProperty()
  @Type(() => Operator)
  @Prop({ required: true, type: Operator })
  createdBy: Operator

  @IsOptional()
  @ApiPropertyOptional()
  @Prop({ required: false, type: Date })
  updatedAt?: Date

  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Operator)
  @Prop({ required: false, type: Operator })
  updatedBy?: Operator

  @IsOptional()
  @ApiPropertyOptional()
  @Prop({ required: false, type: Date })
  deletedAt?: Date

  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Operator)
  @Prop({ required: false, type: Operator })
  deletedBy?: Operator

  constructor(partial: Partial<BaseSchema>) {
    this.createdAt = new Date()
    Object.assign(this, partial)
  }
}
