import { Test, TestingModule } from '@nestjs/testing'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'
import { JwtService } from '../jwt.service'

describe('JwtService', () => {
  let module: TestingModule
  let service: JwtService

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
      providers: [JwtService],
    }).compile()

    service = module.get(JwtService)
  })

  afterAll(async () => {
    await module.close()
  })

  describe('sign jwt', () => {
    it('can sign', () => {
      const token = service.sign({ foo: 'bar' })

      expect(token).not.toBeNull()
      expect(typeof token).toBe('string')
    })
  })

  describe('decode jwt', () => {
    it('can sign', () => {
      const token = service.sign({ foo: 'bar' })
      const decodedPayload = service.decode(token)

      expect(decodedPayload).toHaveProperty('foo', 'bar')
    })
  })

  describe('verify jwt', () => {
    it('can verify', () => {
      const token = service.sign({ foo: 'bar' })
      const verifiedToken = service.verify(token)

      expect(verifiedToken).toHaveProperty('foo', 'bar')
    })

    it('can verify invalid token', () => {
      const token = service.sign({ foo: 'bar' }, { secret: 'abc' })

      expect(() => service.verify(token)).toThrow()
    })
  })
})
