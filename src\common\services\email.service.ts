import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import moment from 'moment-timezone'
import Mustache from 'mustache'
import R from 'ramda'
import { MailService } from '@sendgrid/mail'
import { MailSendingError } from '../exceptions'
import { MailTemplateName, templates } from '../templates'

@Injectable()
export class EmailService {
  private mailerService: MailService
  private defaultFrom: string
  constructor(private readonly configService: ConfigService) {
    this.mailerService = new MailService()
    this.mailerService.setApiKey(
      this.configService.get('common.email.transport.auth.pass'),
    )
    this.defaultFrom = this.configService.get('common.email.defaults.from')
  }

  /**
   * ``` ts
   *  await sendCustomized({
   *    to: '<EMAIL>',
   *    subject: 'Your vouchers confirmed',
   *    text: 'vouchers info...'
   *  })
   * ```
   * @param options
   * @returns
   */
  public async sendCustomized(options: {
    to: string | string[]
    cc?: string
    replyTo?: string
    subject: string
    from?: string
    text?: string
    html?: string
    attachments?: any
  }) {
    const { to, cc, replyTo, subject, text, html, attachments = null } = options
    let response = null
    try {
      const mailOptions = {
        to,
        cc,
        replyTo,
        from: this.defaultFrom,
        subject,
        text,
        html,
      }
      if (attachments && attachments.length > 0) {
        Object.assign(mailOptions, {
          attachments: attachments.map((x: any) => ({
            ...x,
            content:
              typeof x.content !== 'string' ? x.content.toString('base64') : x.content,
          })),
        })
      }
      response = await this.mailerService.send(mailOptions)
      return response
    } catch (error) {
      throw new MailSendingError(
        'Mail Sending Error',
        R.pick(['from', 'to', 'subject'], options),
        error,
      )
    }
  }

  /**
   * ``` ts
   * await sendPrepared( '<EMAIL>', 'vouchersConfirmed', { productDescription: '香港四季酒店餐飲住宿優惠連早餐' })
   * ```
   * @param to  customer's email address
   * @param templateName  the name of prepared template
   * @param payload the data used to populate the template
   * @returns
   */
  public async sendPrepared(
    to: string | string[],
    templateName: MailTemplateName,
    payload?: any,
  ) {
    const { subject: specifiedSubject, attachments = null, ...options } = payload
    const { subject, html } = templates[templateName]
    const rendered = Mustache.render(html, {
      ...options,
      copyrightYear: moment().tz('Asia/Hong_Kong').format('YYYY'),
    })
    const mailOptions = {
      to,
      from: this.defaultFrom,
      subject: specifiedSubject || subject,
      html: rendered,
    }
    if (attachments && attachments.length > 0) {
      Object.assign(mailOptions, {
        attachments: attachments.map((x: any) => ({
          ...x,
          content:
            typeof x.content !== 'string' ? x.content.toString('base64') : x.content,
        })),
      })
    }
    try {
      return await this.mailerService.send(mailOptions)
    } catch (error) {
      console.log('send mail error:', error.response.body.errors)
      throw new MailSendingError(
        'Mail Sending Error',
        R.pick(['from', 'to', 'subject'], options),
        error,
      )
    }
  }
}
