import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import excel from 'node-excel-export'
import { ELocaleType } from '../enums'

type SheetOptions = {
  name: string
  data: { [key: string]: any }[]
  heading?: any
  specification?: { [key: string]: any }[]
  merges?: { [key: string]: any }[]
  resolveSheetConfig?: (config) => any
}

@Injectable()
export class ExcelService {
  styles: any
  constructor(private configService: ConfigService) {
    this.styles = {
      headerGray: {
        fill: {
          patternType: 'none',
          // fgColor: {
          //   rgb: 'ffffff',
          // },
        },
      },
    }
  }

  public buildExcel(data: SheetOptions[] | SheetOptions, name?: string) {
    const sheets = [].concat(data)
    return excel.buildExport(
      sheets.map((sheet) => {
        let config: any = this.configService.get<string>(`excel.${sheet.name}`)
        if (typeof sheet.resolveSheetConfig == 'function') {
          config = sheet.resolveSheetConfig(config)
        }
        return {
          name: name ?? config.name,
          heading: sheet.heading || [],
          specification:
            sheet.specification || this.getSpecification(config.specification),
          data: this.numFmtWrapper(
            sheet.data,
            this.getSpecification(config.specification),
          ),
          merges: sheet.merges || [],
        }
      }),
    )
  }

  public buildRegionExcel(data: SheetOptions[] | SheetOptions, name?: string) {
    const sheets = [].concat(data)
    return excel.buildExport(
      sheets.map((sheet) => {
        const config: any = this.configService.get<string>(`excel.${sheet.name}`)
        const specification = {
          ...this.getSpecification(config.specification),
          ...this.getSpecification(sheet.specification),
        }
        return {
          name: name ?? config.name,
          heading: sheet.heading || [],
          specification,
          data: this.numFmtWrapper(sheet.data, specification),
          merges: sheet.merges || [],
        }
      }),
    )
  }

  public buildBookRegionExcel(
    data: SheetOptions[] | SheetOptions,
    name?: string,
    local = ELocaleType.ZH_HK,
  ) {
    const sheets = [].concat(data)
    return excel.buildExport(
      sheets.map((sheet) => {
        const config: any = this.configService.get<string>(`excel.${sheet.name}`)
        const list = [...config.specification]
        list.splice(4, 0, ...sheet.specification)
        const specification = this.getSpecification(list)
        return {
          name: name ?? config.name,
          heading: sheet.heading || [],
          specification,
          data: this.numFmtWrapper(sheet.data, specification),
          merges: sheet.merges || [],
        }
      }),
    )
  }

  private getSpecification = (
    config: {
      keyName: string
      displayName: string
      width?: string
      numFmt?: string
    }[],
  ) => {
    const specification = {}
    config.forEach((item) => {
      const { keyName, displayName, width = '10', numFmt = null } = item
      specification[keyName] = Object.assign(
        {},
        {
          headerStyle: this.styles.headerGray,
          displayName,
          width,
          numFmt,
        },
      )
    })
    return specification
  }

  /**
   * 若是 number, 根据 numFmt 加一层格式化，适应 Excel 中的Number、 Date、Currency、Text中的Number 类型
   */
  private numFmtWrapper(
    data: { [x: string]: any }[],
    specifications: {
      [key: string]: {
        keyName: string
        displayName: string
        width?: string
        numFmt?: string
      }
    },
  ) {
    data.forEach((item) => {
      Object.keys(item).forEach((x) => {
        const { numFmt } = specifications[x] || {}
        if (typeof item[x] === 'number' && numFmt) {
          item[x] = {
            value: item[x],
            style: {
              numFmt,
            },
          }
        }
      })
    })
    return data
  }
}
