import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { WsException } from '@nestjs/websockets'
import jwt, { SignOptions, VerifyOptions } from 'jsonwebtoken'
import { AuthSchema } from '../enums'

type JwtSignOptions = {
  secret?: string
  authSchema?: AuthSchema
} & SignOptions

type JwtVerifyOptions = {
  secret?: string
  authSchema?: AuthSchema
} & VerifyOptions

@Injectable()
export class JwtService {
  constructor(private readonly configService: ConfigService) {}

  sign(payload: any, options: JwtSignOptions = {}) {
    const { secret, authSchema = AuthSchema.CLIENT, ...restOptions } = options
    const res = jwt.sign(payload, secret ?? this.getSecret(authSchema), restOptions)

    return res
  }

  decode(token: string) {
    return jwt.decode(token)
  }

  verify(token: any, options: JwtVerifyOptions = {}) {
    const { secret, authSchema = AuthSchema.CLIENT, ...restOptions } = options
    return jwt.verify(token, secret ?? this.getSecret(authSchema), restOptions)
  }

  getSecret(authSchema: AuthSchema) {
    return this.configService.get(`common.jwt.secret.${authSchema.toLowerCase()}`)
  }

  getWssHeaderToken(authorization = ''): string {
    const isValid = authorization.startsWith('Bearer ')
    if (!isValid) {
      throw new WsException('Invalid auth header format')
    }
    return authorization.substring(7)
  }

  async verifyWssToken(
    authorization: string,
    authSchema: AuthSchema = AuthSchema.CLIENT,
  ): Promise<any> {
    const token = this.getWssHeaderToken(authorization)
    try {
      const secret = this.getSecret(authSchema)

      return this.verify(token, { authSchema, secret }) as any
    } catch (error) {
      throw new WsException(error) // throw 后服务挂了
    }
  }
}
