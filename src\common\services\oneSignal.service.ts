import { HttpService } from '@nestjs/axios'
import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { OneSignalPayloadDto } from '../dto'

@Injectable()
export class OneSignalService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async sendNotification(configName: string, payload: OneSignalPayloadDto) {
    const oneSignalConfig = this.configService.get(configName)
    console.log({ oneSignalConfig })
    const res: any = await this.httpService
      .post(
        `${oneSignalConfig.url}/api/v1/notifications`,
        {
          app_id: oneSignalConfig.appId,
          headings: payload.headings,
          contents: payload.contents,
          include_player_ids: payload.playerIds,
          url: payload?.url,
          data: payload?.data || {},
        },
        {
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Authorization: `Basic ${oneSignalConfig.key}`,
          },
        },
      )
      .toPromise()
      .catch((error) => {
        console.log(
          `failed to push review message ${payload.headings.en} ${
            payload.contents.en
          } to ${payload.playerIds}, error: ${JSON.stringify(
            error?.response?.data ?? error,
          )}`,
        )
      })

    console.log({ res, name: 'one signal' })
    if (res?.data?.errors) {
      console.log(
        `failed to push review message ${payload.headings.en} ${payload.contents.en} to ${
          payload.playerIds
        }, error: ${JSON.stringify(res.data?.errors)}`,
      )
    } else
      console.log(
        `finished to push review message ${payload.headings.en} ${payload.contents.en} to ${payload.playerIds}`,
      )
  }
}
