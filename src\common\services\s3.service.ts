import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import AWS from 'aws-sdk'
import { Body, ManagedUpload } from 'aws-sdk/clients/s3'
import { IS3Config } from '../interfaces/s3.interface'

@Injectable()
export class S3Service {
  private connection: AWS.S3
  private config: IS3Config
  constructor(private readonly configService: ConfigService) {
    this.config = this.configService.get('common.imagesS3')
    this.connection = new AWS.S3(this.config)
  }

  async upload(options: {
    fileName: string
    fileBuffer: any
    path: string
    contentType?: string
    acl?: string
  }): Promise<string> {
    return new Promise((resolve, reject) => {
      const { fileName, fileBuffer, path, contentType, acl = 'public-read' } = options
      const defaultContentType = fileName.split('.').pop()
      const params = {
        Bucket: this.config.bucketName,
        Key: `${path}/${fileName}`,
        Body: fileBuffer instanceof Buffer ? fileBuffer : fileBuffer.buffer,
        ACL: acl,
        ContentType: contentType || defaultContentType,
      }
      this.connection.upload(params, {}, (error: Error, data: ManagedUpload.SendData) => {
        if (error) {
          console.log(error, error.stack)
          return reject(error)
        }
        return resolve(`https://${this.config.cdn}/${data.Key}`)
      })
    })
  }

  async fetch(options: { fileFullName: string; bucket?: string }): Promise<Body> {
    return new Promise((resolve, reject) => {
      this.connection.getObject(
        {
          Bucket: options.bucket,
          Key: options.fileFullName,
        },
        (error: AWS.AWSError, data: AWS.S3.GetObjectOutput) => {
          if (error) {
            console.log(error, error.stack)
            return reject(error)
          }
          return resolve(data.Body)
        },
      )
    })
  }
}
