import { COPY_EMAIL_IMAGE, VERIFICATION_CODE_EMAIL_IMAGE } from '../../modules/constants'
import { appleImage, googleImage } from './config'

export default {
  subject: 'SJRC | 申請閱讀時數',
  html: `<!doctype html>
  <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

  <head>
  <title>
  </title>
  <!--[if !mso]><!-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!--<![endif]-->
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type="text/css">
     #outlook a {
        padding: 0;
     }

     body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
     }

     table,
     td {
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
     }

     img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        -ms-interpolation-mode: bicubic;
     }

     p {
        display: block;
        margin: 13px 0;
     }
  </style>
  <!--[if mso]>
        <noscript>
        <xml>
        <o:OfficeDocumentSettings>
           <o:AllowPNG/>
           <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
        </xml>
        </noscript>
        <![endif]-->
  <!--[if lte mso 11]>
        <style type="text/css">
           .mj-outlook-group-fix { width:100% !important; }
        </style>
        <![endif]-->
  <style type="text/css">
     @media only screen and (min-width:480px) {
        .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
        }

        .mj-column-px-280 {
        width: 280px !important;
        max-width: 280px;
        }
     }
  </style>
  <style media="screen and (min-width:480px)">
     .moz-text-html .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
     }

     .moz-text-html .mj-column-px-280 {
        width: 280px !important;
        max-width: 280px;
     }
  </style>
  <style type="text/css">
     @media only screen and (max-width:480px) {
        table.mj-full-width-mobile {
        width: 100% !important;
        }

        td.mj-full-width-mobile {
        width: auto !important;
        }
     }
  </style>
  <style type="text/css">
     .account-container table {
        text-align: center;
        background: #ffffff;
        border-radius: 8px;
        padding: 12px 24px;
        height: 80px;
     }

     .account-container div img {
        margin-left: 5px;
     }
  </style>
  </head>

  <body style="word-spacing:normal;background-color:#F9FAFB;">
  <div style="background-color:#F9FAFB;">
     <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:600px;" width="600" ><tr><td style="line-height:0px;mso-line-height-rule:exactly;"><![endif]-->
     <div style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
        <tbody>
           <tr>
              <td style="direction:ltr;padding:20px 0;text-align:center;">
              <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
              <div class="mj-column-per-100 mj-outlook-group-fix" style="text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                 <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                    <tbody>
                    <tr>
                       <td align="center" style="padding:10px 25px;word-break:break-word;">
                          <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:collapse;border-spacing:0px;">
                          <tbody>
                             <tr>
                                <td style="width:96px;">
                                <img height="auto" src="${VERIFICATION_CODE_EMAIL_IMAGE}" style="border:0;display:block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13px;" width="96" />
                                </td>
                             </tr>
                          </tbody>
                          </table>
                       </td>
                    </tr>
                    <tr>
                       <td align="center" style="padding:10px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:18px;font-weight:bold;line-height:1;text-align:center;color:#202124;">{{email}}[{{name}}]申請閱讀時數</div>
                       </td>
                    </tr>
                    </tbody>
                 </table>
              </div>
              <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
           </tr>
        </tbody>
        </table>
     </div>
     <!--[if mso | IE]></td></tr></table><table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:600px;" width="600" ><tr><td style="line-height:0px;mso-line-height-rule:exactly;"><![endif]-->
     <div style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
        <tbody>
           <tr>
              <td style="direction:ltr;padding:0;text-align:center;">
              <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td align="center" class="account-container-outlook" style="vertical-align:top;width:220px;" ><![endif]-->
              <div class="mj-column-px-280 mj-outlook-group-fix account-container" style="text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                 <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                    <tbody>
                    <tr>
                       <td align="left" style="padding:25px 25px 10px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:left;color:#000000;"><span class="label">申請帳戶：</span>
                          </div>
                       </td>
                       <td align="right" style="padding:25px 25px 10px 25px;;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:right;color:#000000;"><span class="label">{{ email }}</span>
                          </div>
                       </td>
                    </tr>
                    <tr>
                       <td align="left" style="padding:10px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:left;color:#000000;"><span class="label">申請時數：</span>
                       </td>
                       <td align="right" style="padding:10px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:right;color:#000000;font-weight: bold;"><span class="label">{{time}} 小時</span>
                       </td>
                    </tr>
                    <tr>
                       <td align="left" style="padding:10px 25px 25px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:left;color:#000000;"><span class="label">申請時間：</span>
                       </td>
                       <td align="right" style="padding:10px 25px 25px 25px;word-break:break-word;">
                          <div style="font-family:PingFang HK;font-size:12px;line-height:1;text-align:right;color:#000000;"><span class="label">{{applicationTime}}</span>
                       </td>
                    </tr>
                    </tbody>
                 </table>
              </div>
              <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
           </tr>
        </tbody>
        </table>
     </div>
     <!--[if mso | IE]></td></tr></table><table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:600px;" width="600" ><tr><td style="line-height:0px;mso-line-height-rule:exactly;"><![endif]-->
     <div style="margin:24px auto 10px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
        <tbody>
           <tr>
              <td style="direction:ltr;padding:0;text-align:center;">
              <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
              <div class="mj-column-per-100 mj-outlook-group-fix" style="text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                <div style="color:#3DCAB1;font-size: 12px;text-align: center;">請盡快登入 SJRC學校管理控制台 進行確認</div>
                 <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                    <tbody>
                    <tr>
                       <td align="center" vertical-align="middle" style="padding:10px 25px;word-break:break-word;">
                          <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:separate;line-height:100%;">
                          <tr>
                             <td align="center" bgcolor="#3DCAB1" role="presentation" style="border:none;border-radius:3px;cursor:auto;mso-padding-alt:10px 25px;background:#3DCAB1;" valign="middle">
                                <a style="display:inline-block;background:#3DCAB1;color:white;font-family:PingFang HK;font-size:13px;font-weight:normal;line-height:120%;margin:0;text-decoration:none;text-transform:none;padding:10px 25px;mso-padding-alt:0px;border-radius:3px;cursor:pointer" href="{{{ url }}}"> 現在確認 </a>
                             </td>
                          </tr>
                          </table>
                       </td>
                    </tr>
                    </tbody>
                 </table>
              </div>
              <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
           </tr>
        </tbody>
        </table>
        <!-- 这里的也需要赋值，copy 用的 -->
        <textarea  id="email" style="opacity: 0;width:0;height: 0">{{ email }}</textarea>
        <textarea  id="pwd" style="opacity: 0;width:0;height: 0">{{ password }}</textarea>
     </div>



     <!--[if mso | IE]></td></tr></table><![endif]-->
  
  </div>
  </body>

  </html>
`,
}
