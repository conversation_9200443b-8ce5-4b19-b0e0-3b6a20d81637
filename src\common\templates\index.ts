import applicationHoursTemplate from './applicationHours.template'
import createReadingTimeAppliction from './createReadingTimeAppliction.template'
import dataExport from './dataExport.template'
import insufficientReadTime from './insufficientReadTime.template'
import insufficientReadTimeForPlatform from './insufficientReadTimeForPlatform.template'
import inviteAdmin from './inviteAdmin.template'
import inviteSchoolUser from './inviteSchoolUser.template'
import inviteUser from './inviteUser.template'
import inviteUserWithPassword from './inviteUserWithPassword.template'
import loginByTicket from './loginByTicket.template'
import operateApplicationTemplate from './operateApplication.template'
import publisherReadingTime from './publisherReadingTime.template'
import updateBookStatusTemplate from './updateBookStatus.template'
import verificationCode from './verificationCode.template'

const templates = {
  verificationCode,
  loginByTicket,
  inviteAdmin,
  inviteUser,
  updateBookStatusTemplate,
  applicationHoursTemplate,
  operateApplicationTemplate,
  createReadingTimeAppliction,
  dataExport,
  inviteSchoolUser,
  insufficientReadTime,
  insufficientReadTimeForPlatform,
  publisherReadingTime,
  inviteUserWithPassword,
}

type MailTemplateName =
  | 'verificationCode'
  | 'loginByTicket'
  | 'inviteUser'
  | 'inviteUserWithPassword'
  | 'inviteAdmin'
  | 'updateBookStatusTemplate'
  | 'applicationHoursTemplate'
  | 'operateApplicationTemplate'
  | 'createReadingTimeAppliction'
  | 'dataExport'
  | 'inviteSchoolUser'
  | 'insufficientReadTime'
  | 'insufficientReadTimeForPlatform'
  | 'publisherReadingTime'

export { templates, MailTemplateName }
