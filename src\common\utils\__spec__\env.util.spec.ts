import { Test, TestingModule } from '@nestjs/testing'
import config from '../../../../test/config'
import { CommonModule } from '../../common.module'
import {
  isDev,
  isLocal,
  isNonProduction,
  isProduction,
  isStaging,
  isUTA,
} from '../env.util'

describe('env.util', () => {
  let module: TestingModule

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [CommonModule.forRoot(config)],
    }).compile()
  })

  afterAll(async () => {
    await module.close()
  })

  it('should be local', () => {
    expect(isLocal()).toBeTruthy()
    expect(isProduction()).toBeFalsy()
    expect(isStaging()).toBeFalsy()
    expect(isDev()).toBeFalsy()
    expect(isUTA()).toBeFalsy()
    expect(isNonProduction()).toBeTruthy()
  })
})
