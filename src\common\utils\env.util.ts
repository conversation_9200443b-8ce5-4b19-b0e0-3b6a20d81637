import { AppEnv } from '../enums'

const isEqual = (env: AppEnv) => process.env.APP_ENV === env

export const isProduction = () => isEqual(AppEnv.PRODUCTION)

export const isNonProduction = () => !isProduction()

export const isStaging = () => isEqual(AppEnv.STAGING)

export const isDev = () => isEqual(AppEnv.DEVELOP)

export const isLocal = () => isEqual(AppEnv.LOCAL)

export const isUTA = () => isEqual(AppEnv.UTA)
