import R from 'ramda'
import { Repository } from 'typeorm'
import { PageResponse } from '../dto'
import {
  ListingQueryBuilderOptions,
  SortDirection,
} from '../interfaces/listingQueryBuilder.interface'

const DEFAULT_MAX_LIMIT = 100

/**
 * Stateful builder to build and perform usual listing query
 * Client user should not access the private field
 * You CANNOT re-use the builder after running the query
 * You can chain the builder until builder.query()
 */
export class ListingQueryBuilder<T, M> {
  private MAX_LIMIT: number
  private _model: Repository<M>
  private _pageIndex?: number
  private _pageSize?: number
  private _order: SortDirection<M>
  private _relations: string[]
  private _where: any
  private _select: any

  private constructor(options: ListingQueryBuilderOptions<M>) {
    this.MAX_LIMIT = options.maxLimit

    this._model = options.model

    this._pageIndex = options.pageIndex ?? 1

    this._pageSize = options.pageSize

    this._order = options.orderOptions ?? {}

    this._where = options.where ?? {}

    this._relations = options.relations ?? []

    this._select = options.relations ?? undefined
  }
  private get actualLimit(): number {
    return this._pageSize ? Math.min(this._pageSize, this.MAX_LIMIT) : this.MAX_LIMIT
  }

  public static new<T, M>(maxLimit: number = DEFAULT_MAX_LIMIT) {
    return new ListingQueryBuilder<T, M>({ maxLimit })
  }

  public repository(model: Repository<M>): this {
    this._model = model
    return this
  }

  public pageSize(limit = 10): this {
    this._pageSize = limit
    return this
  }

  public pageIndex(index = 1): this {
    this._pageIndex = index
    return this
  }

  public order(options: SortDirection<M>): this {
    this._order = options
    return this
  }

  public relations(options: any[]): this {
    this._relations = options
    return this
  }

  public select(options: any[]): this {
    this._select = options
    return this
  }

  public where(query: Record<any, any>): this {
    this._where = query || {}
    return this
  }

  public async query(fn?: (value: M) => T): Promise<PageResponse<T, M>> {
    if (R.isNil(this._model)) {
      throw new Error('You must set a valid model')
    }
    const total: number = await this._model.countBy(this._where)

    const items = await this._model.find({
      where: this._where,
      select: this._select,
      relations: this._relations,
      skip: (this._pageIndex - 1) * this.actualLimit,
      take: this.actualLimit,
      order: this._order,
    })

    return new PageResponse<T, M>(
      {
        total,
        pageIndex: this._pageIndex,
        pageSize: this._pageSize,
      },
      items,
      fn
    )
  }
}
