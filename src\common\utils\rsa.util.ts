import crypto from 'crypto'

export const publicEncrypt = (key: string, payload: string): string =>
  crypto.publicEncrypt({ key }, Buffer.from(payload, 'utf8')).toString('base64')

export const publicDecrypt = (key: string, payload: string): string =>
  crypto.publicDecrypt({ key }, Buffer.from(payload, 'hex')).toString('hex')

export const privateDecrypt = (key: string, payload: string) =>
  crypto.privateDecrypt({ key }, Buffer.from(payload, 'base64')).toString('utf8')

export const privateEncrypt = (key: string, payload: string) =>
  crypto.privateEncrypt({ key }, Buffer.from(payload, 'hex')).toString('hex')

export const generateKeyPair = () =>
  crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },

    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem',
    },
  })

export const generateKey = () =>
  crypto.generateKeySync('aes', { length: 256 }).export().toString('hex')

export const encrypt = (key: string, data: Buffer) => {
  const iv = Buffer.from(process.env.AES_IV, 'hex')

  const cipher = crypto.createCipheriv(
    'aes-192-cbc',
    crypto.createSecretKey(key, 'hex'),
    iv,
  )

  return Buffer.concat([cipher.update(data), cipher.final()])
}

export const decrypt = (key: string, data: Buffer) => {
  const iv = Buffer.from(process.env.AES_IV, 'hex')
  const decipher = crypto.createDecipheriv(
    'aes-192-cbc',
    crypto.createSecretKey(key, 'hex'),
    iv,
  )

  return Buffer.concat([decipher.update(data), decipher.final()])
}

export const convertKey = (key: string) => String(key).replace(/\\n/g, '\n')
