import { registerAs } from '@nestjs/config'

export default registerAs('application', () => ({
  port: process.env.APP_PORT,
  swagger: {
    info: {
      title: 'SJRC Backend Service',
      description: `This is sjrc backend api service 
      \n <a href='https://github.com/Sjrc/blob/develop/docs/exceptions.md'>Exception Documents</a>`,
      version: '1.0.0',
    },
    components: {
      securitySchemes: {
        bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QlWgFKA3PBHMTiQG7qwX0HeyW2Mb8cfa6awAFJ0EomU',
        },
      },
    },
    security: [{ bearer: [] }],
    servers: [{ url: `${process.env.APP_API_DOC}` }],
  },
}))
