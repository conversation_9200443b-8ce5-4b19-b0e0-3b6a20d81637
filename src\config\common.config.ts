import { registerAs } from '@nestjs/config'
import { join } from 'path'
import R from 'ramda'
import { TypeOrmNamingStrategy } from './utils'
 
export default registerAs('common', () => ({
  withCore: {
    pino: {
      pinoHttp: {
        level: 'fatal',
        transport: process.env.NODE_ENV === 'development' ? {
          target: 'pino-pretty',
          options: {
            translateTime: true,
          },
        } : undefined,
        serializers: {
          req: (req) => R.evolve({
            headers: R.omit(['authorization', 'cookie']),
          }, req),
        },
      },
    },
  },
  withSequelize: {
    dialect: 'mysql',
    host: process.env.MYSQL_HOST,
    port: process.env.MYSQL_PORT,
    username: process.env.MYSQL_USERNAME,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DBNAME,
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      underscored: true,
      underscoredAll: true,
      timestamps: true,
      paranoid: true,
    },
    autoLoadModels: true,
    synchronize: true,
  },
  withTypeOrm: {
    type: 'mysql',
    host: process.env.MYSQL_HOST || '127.0.0.1',
    port: process.env.MYSQL_PORT || 3306,
    username: process.env.MYSQL_USERNAME || 'general-user',
    password: process.env.MYSQL_PASSWORD || 'password',
    database: process.env.MYSQL_DBNAME || 'Sjrc',
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    // synchronize: true,
    autoLoadEntities: true,
    logging: false,
    logger: 'advanced-console',
    // logging: process.env.APP_ENV === 'local',
    namingStrategy: new TypeOrmNamingStrategy(),
    migrations: [join(__dirname, './../migrations/{*.ts,*.js}')],
  },
  withRedis: {
    prefix: process.env.REDIS_PREFIX || 'redis',
    port: process.env.REDIS_PORT,
    host: process.env.REDIS_HOST,
    password: process.env.REDIS_PASSWORD,
    tls: process.env.REDIS_TLS === 'enable' ? { rejectUnauthorized: false } : undefined,
  },
  withRabbitMQ: {
    asyncTaskMQ: {
      exchangeName: 'SjrcAsyncTaskExchange',
      queueName: 'SjrcAyncTaskQueue',
      deadLetterExchangeName: 'DeadSjrcAsyncTaskExchange',
      deadLetterQueueName: 'DeadSjrcAsyncTaskQueue',
      queueRoutingKey: 'SjrcAyncTaskQueueRoutingKey',
      deadLetterQueueRoutingKey: 'DeadSjrcAyncTaskQueueRoutingKey',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
    topicMQ: {
      exchangeName: 'SjrcTopicExchange',
      queueName: 'SjrcTopicQueue',
      deadLetterExchangeName: 'DeadSjrcTopicExchange',
      deadLetterQueueName: 'DeadSjrcTopicQueue',
      queueRoutingKey: 'SjrcTopicQueueRoutingKey',
      deadLetterQueueRoutingKey: 'DeadSjrcTopicQueueRoutingKey',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
    delayTaskMQ: {
      exchangeName: 'SjrcDelayTaskExchange',
      queueName: 'SjrcDelayTaskQueue',
      deadLetterExchangeName: 'DeadSjrcDelayTaskExchange',
      deadLetterQueueName: 'DeadSjrcDelayTaskQueue',
      queueRoutingKey: 'SjrcDelayTaskQueueRoutingKey',
      deadLetterQueueRoutingKey: 'DeadSjrcDelayTaskQueueRoutingKey',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
  },
  withMongo: {
    uri: process.env.MONGO_URI || 'mongodb://localhost/Sjrc',
    loggerLevel: process.env.NODE_ENV === 'local' ? 'debug' : 'error',
  },
  jwt: {
    secret: {
      // for web client user, x-auth-schema === CLIENT will use this secret
      client: process.env.CLIENT_SECRET || 'json_client_token_secret_key',

      // for merchant user, x-auth-schema === MERCHANT will use this secret
      merchant: process.env.MERCHANT_SECRET || 'json_merchant_token_secret_key',

      // for admin user, x-auth-schema === ADMIN will use this secret
      admin: process.env.ADMIN_SECRET || 'json_admin_token_secret_key',

      // for admin user, x-auth-schema === SCHOOL_ADMIN will use this secret
      school_admin: process.env.SCHOOL_ADMIN || 'json_school_admin_token_secret_key',

      api: process.env.API_SECRET || 'json_api_token_secret_key',

      internal: process.env.INTERNAL_SECRET || 'json_internal_token_secret_key',
    },
  },
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || 'ACxxxxxxxxxxxxxxxxx',
    authToken: process.env.TWILIO_AUTH_TOKEN || 'ACxxxxxxxxxxxxxxxxx',
    serviceId: process.env.TWILIO_SERVICE_ID || 'ACxxxxxxxxxxxxxxxxx',
  },
  email: {
    transport: {
      service: process.env.EMAIL_SERVICE,
      host: process.env.EMAIL_HOST,
      port: Number(process.env.EMAIL_PORT),
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    },
    defaults: {
      from: process.env.EMAIL_DEFAULT_FROM,
    },
    ipPoolName: process.env.EMAIL_IP_POOL_NAME,
  },
  facebook: {
    appId: process.env.FACEBOOK_APP_ID,
    appSecret: process.env.FACEBOOK_APP_SECRET,
  },
  google: {
    web: {
      clientId: process.env.GOOGLE_WEB_CLIENT_ID, //
      clientSecret: process.env.GOOGLE_WEB_CLIENT_SECRET,
    },
    admin: {
      clientId: process.env.GOOGLE_ADMIN_CLIENT_ID, //
      clientSecret: process.env.GOOGLE_ADMIN_CLIENT_SECRET,
    },
    ios: {
      clientId: process.env.GOOGLE_IOS_CLIENT_ID, //
      clientSecret: process.env.GOOGLE_IOS_CLIENT_SECRET,
    },
    android: {
      clientId: process.env.GOOGLE_ANDROID_CLIENT_ID, //
      clientSecret: process.env.GOOGLE_ANDROID_CLIENT_SECRET,
    },
  },
  reCAPTCHA: {
    projectId: process.env.GOOGLE_RECAPTCHA_PROJECT_ID,
    siteKey: process.env.GOOGLE_RECAPTCHA_SITE_KEY,
    secretKey: process.env.GOOGLE_RECAPTCHA_SECRET_KEY,
    action: process.env.GOOGLE_RECAPTCHA_ACTION,
  },
  imagesS3: {
    bucketName: process.env.AWS_S3_IMAGES_BUCKET_NAME, //
    accessKeyId: process.env.AWS_S3_IMAGES_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_IMAGES_SECRET_ACCESS_KEY,
    region: process.env.AWS_S3_IMAGES_REGION,
    cdn: process.env.AWS_S3_IMAGES_BUCKET_CDN,
  },
  openaiS3: {
    bucketName: process.env.AWS_S3_OPENAI_BUCKET_NAME,
    accessKeyId: process.env.AWS_S3_OPENAI_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_OPENAI_SECRET_ACCESS_KEY,
    region: process.env.AWS_S3_OPENAI_REGION,
    cdn: process.env.AWS_S3_OPENAI_BUCKET_CDN,
  },
  googleCloudStorage: {
    cdn: process.env.GOOGLE_CLOUD_CDN || 'storage.googleapis.com',
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || 'Sjrc',
    bucketName: process.env.GOOGLE_CLOUD_BUCKET_NAME || 'Sjrc-dev-public',
    keyFilename:
      process.env.GOOGLE_CLOUD_STORAGE_KEY_FILE ||
      '/home/<USER>/app/storage/mn-storage-admin.json',
  },
  firebaseRealtimeDatabase: {
    keyFilename:
      process.env.FIREBASE_REALTIME_DATABASE_KEY_FILE ||
      '/home/<USER>/app/storage/mn-firebase-admin.json',
  },
  stripe: {
    apiKey: process.env.STRIPE_API_KEY,
    endpointSecret: process.env.STRIPE_ENDPOINT_SECRET,
    config: {
      apiVersion: '2020-08-27',
    },
  },
  dynamicLink: {
    apiKey:
      process.env.GOOGLE_DYNAMIC_LINK_API_KEY ||
      'AIzaSyB4Zt6u7fiXMcuxuRTGZD2O2sKWNNk8cPA',
  },
  oneSignal: {
    appId: process.env.ONE_SIGNAL_APP_ID,
    url: process.env.ONE_SIGNAL_API_URL,
    key: process.env.ONE_SIGNAL_REST_API_KEY,
  },
  openai: {
    apikey: process.env.OPENAI_API_KEY,
    proxy: process.env.OPENAI_PROXY,
  },
}))
