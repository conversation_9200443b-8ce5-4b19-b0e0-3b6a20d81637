// AI助手相关配置
export const assistantConfig = {
  assistantUV: {
    zh_HK: {
      name: '使用人數分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeUserCount',
          displayName: '使用人數(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            '使用人數佔比【使用人數(年級/教職員)/使用總人數(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classUserCount', displayName: '使用人數(班別)' },
        {
          keyName: 'classUserCountRatio',
          displayName: '使用人數佔比【使用人數(班別)/使用總人數(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of engagement (Counts) In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeUserCount',
          displayName: 'Total users (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            'Percentage of users【Total users (Curriculum stages/Stuff)/Total users(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classUserCount', displayName: 'Total users(Classes)' },
        {
          keyName: 'classUserCountRatio',
          displayName:
            'Percentage of users【Total users(Classes)/Total users(Curriculum stages)】',
        },
      ],
    },
  },
  assistantPV: {
    zh_HK: {
      name: '使用人次分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeUsageCount',
          displayName: '使用人次(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeUsageCountRatio',
          displayName:
            '使用人次佔比【使用人次(年級/教職員)/使用總人次(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classUsageCount', displayName: '使用人次(班別)' },
        {
          keyName: 'classUsageCountRatio',
          displayName: '使用人次佔比【使用人次(班別)/使用總人次(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of engagement (Counts) In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeUsageCount',
          displayName: 'Total usage counts (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeUsageCountRatio',
          displayName:
            'Percentage of usage counts【Total usage counts (Curriculum stages/Stuff)/Total usage counts(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classUsageCount', displayName: 'Total usage counts(Classes)' },
        {
          keyName: 'classUsageCountRatio',
          displayName:
            'Percentage of usage counts【Total usage counts(Classes)/Total usage counts(Curriculum stages)】',
        },
      ],
    },
  },
  assistantUserThreadDetail: {
    zh_HK: {
      name: '使用詳情',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'threadId', displayName: '對話ID' },
        { keyName: 'threadTitle', displayName: '對話標題' },
        { keyName: 'messageCount', displayName: '對話消息數量' },
        { keyName: 'createdAt', displayName: '創建時間' },
      ],
    },
    en_uk: {
      name: 'Use details',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'threadId', displayName: 'Thread ID' },
        { keyName: 'threadTitle', displayName: 'Thread Title' },
        { keyName: 'messageCount', displayName: 'Message Count' },
        { keyName: 'createdAt', displayName: 'Created At' },
      ],
    },
  },
  assistantUserMessageDetail: {
    zh_HK: {
      name: '使用詳情',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'threadId', displayName: '對話ID' },
        { keyName: 'threadTitle', displayName: '對話標題' },
        { keyName: 'messageId', displayName: '消息ID' },
        { keyName: 'messageContent', displayName: '消息內容' },
        { keyName: 'messageRole', displayName: '消息角色' },
        { keyName: 'createdAt', displayName: '創建時間' },
      ],
    },
    en_uk: {
      name: 'Use details',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'threadId', displayName: 'Thread ID' },
        { keyName: 'threadTitle', displayName: 'Thread Title' },
        { keyName: 'messageId', displayName: 'Message ID' },
        { keyName: 'messageContent', displayName: 'Message Content' },
        { keyName: 'messageRole', displayName: 'Message Role' },
        { keyName: 'createdAt', displayName: 'Created At' },
      ],
    },
  },
  assistantGroupBySchool: {
    zh_HK: {
      name: '文心智友參與數量(學校) ',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'userCount', displayName: '使用人數' },
        { keyName: 'usageCount', displayName: '使用人次' },
      ],
    },
    en_uk: {
      name: '文心智友參與數量(學校) ',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'schoolName', displayName: 'School Name' },
        { keyName: 'userCount', displayName: 'User Count' },
        { keyName: 'usageCount', displayName: 'Usage Count' },
      ],
    },
  },
  assistantTopics: {
    zh_HK: {
      name: '话题管理列表',
      specification: [
        { keyName: 'id', displayName: 'ID' },
        { keyName: 'title', displayName: '標題' },
        { keyName: 'description', displayName: '描述' },
        { keyName: 'isActive', displayName: '是否啟用' },
        { keyName: 'createdAt', displayName: '創建時間' },
        { keyName: 'updatedAt', displayName: '更新時間' },
      ],
    },
    en_uk: {
      name: 'topics list',
      specification: [
        { keyName: 'id', displayName: 'ID' },
        { keyName: 'title', displayName: 'Title' },
        { keyName: 'description', displayName: 'Description' },
        { keyName: 'isActive', displayName: 'Is Active' },
        { keyName: 'createdAt', displayName: 'Created At' },
        { keyName: 'updatedAt', displayName: 'Updated At' },
      ],
    },
  },
  assistantFiles: {
    zh_HK: {
      name: '文件管理列表',
      specification: [
        { keyName: 'id', displayName: 'ID' },
        { keyName: 'filename', displayName: '文件名' },
        { keyName: 'filesize', displayName: '文件大小' },
        { keyName: 'uploadedAt', displayName: '上傳時間' },
      ],
    },
    en_uk: {
      name: 'files list',
      specification: [
        { keyName: 'id', displayName: 'ID' },
        { keyName: 'filename', displayName: 'Filename' },
        { keyName: 'filesize', displayName: 'File Size' },
        { keyName: 'uploadedAt', displayName: 'Uploaded At' },
      ],
    },
  },
}
