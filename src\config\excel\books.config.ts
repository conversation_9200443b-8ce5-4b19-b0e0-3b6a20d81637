export const booksConfig = {
  bulkUploadBookInformationV2: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'version', displayName: '所屬系統 (SJRC/SJRC+/PSAR)'},
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(繁體中文)',
        },
        {keyName: 'bookNameCn', displayName: '书名\n(简体中文, 如有)'},
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'authorName', displayName: '作者 (繁體中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameCn',
          displayName: '作者 (简体中文, 如有)(多个作者使用英文 ,隔开)',
        },
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(繁體中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionCn',
          displayName: '作者简介(简体中文, 如有)\n(多个作者将忽略此项）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(繁體中文)*'},
        {keyName: 'companyNameCn', displayName: '出版集团(简体中文, 如有)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(繁體中文)*'},
        {keyName: 'publisherNameCn', displayName: '出版社(简体中文, 如有)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        {
          keyName: 'description',
          displayName:
              '書籍簡介(繁體中文)\n(中英對照書籍請順序以繁體中文、简体中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionCn',
          displayName:
              '书籍简介(简体中文, 如有)\n(中英对照书籍请顺序以繁体中文、简体中文、英文书写简介，\n简介内容请勿包含任何推广如签名版、赠品等其它资讯)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以繁體中文、简体中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        {
          keyName: 'price',
          displayName: '定價',
        },
        {
          keyName: 'language',
          displayName:
              '書籍語言（請使用ISO-639-1語言碼與ISO-3166-1國家碼以下劃線組合的方式來表示語言，例如：zh_hk, en_hk, zh_tw）',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'version', displayName: 'SJRC,SJRC+,PSAR'},
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameCn',
          displayName:
              'Book title (Cht)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameCn',
          displayName:
              'Author (Cht)(If more than one authors, please use comma "," to split)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionCn', displayName: 'Authors\' bio (Cht, if any)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameCn', displayName: 'Publishing Group (Cht, if any)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameCn', displayName: 'Imprint (Cht, if any)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionCn',
          displayName: 'Book description(Cht, if any,Please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        {
          keyName: 'List price',
          displayName: '定價',
        },
        {
          keyName: 'language',
          displayName:
              'Book language(s)*（If more than one group, please use comma "," to split.  E.g. zh_hk, en_hk, zh_tw)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
  platformReferenceBook: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(中文)',
        },
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'price', displayName: '定價'},
        {keyName: 'copiesCount', displayName: '累積複本數量'},
        {keyName: 'authorName', displayName: '作者 (中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(中文)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(中文)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        {
          keyName: 'publisherDescription',
          displayName: '出版社簡介(中文)',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: '出版社簡介(英文, 如有)',
        },
        {
          keyName: 'description',
          displayName:
              '書籍簡介(中文)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        {
          keyName: 'language',
          displayName: '書籍語言\n(中文/英文/中英文)',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: '課程綱要分類(英文,如有)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: '圖書品種分類*(英文,如有)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: '價值觀教育標籤(英文,如有)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {keyName: 'price', displayName: 'Price'},
        {keyName: 'copiesCount', displayName: 'Copies'},
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        {
          keyName: 'publisherDescription',
          displayName: 'Imprint\'s introduction',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: 'Imprint\'s introduction',
        },
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        {
          keyName: 'language',
          displayName: 'Book languages\n(zh/en/zh_en)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: 'Category — Per Key Learning Areas(Eng, if any)',
        },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: 'Book categories*(Eng, if any)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: 'Values Education hashtag(Eng, if any)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
  bulkUploadContractBookInformation: {
    zh_HK: {
      name: 'bulkUploadContractBookInformation',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN'},
        {
          keyName: 'bookName',
          displayName: '書名',
        },
        {keyName: 'copiesCount', displayName: '副本数量'},
      ],
    },
    en_uk: {
      name: 'bulkUploadContractBookInformation',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN'},
        {
          keyName: 'bookName',
          displayName: 'Book title',
        },
        {
          keyName: 'copiesCount',
          displayName: ' Copies count',
        },
      ],
    },
  },
  // (Admin) 書籍管理-書籍列表(訂閱版)
  bookInformation: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'sjrc', displayName: 'SJRC書目'},
        {keyName: 'reference', displayName: 'SJRC+書目'},
        {keyName: 'hasSrVer', displayName: 'PSAR書目'},
        {keyName: 'isAIBook', displayName: '文心智友書目'},
        {
          keyName: 'bookName',
          displayName: '書名',
        },
        {keyName: 'isbn', displayName: 'ISBN'},
        {keyName: 'authorName', displayName: '作者'},
        {keyName: 'publisherName', displayName: '出版社'},
        {
          keyName: 'publishedAt',
          displayName: '出版日期',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤',
        },
        {
          keyName: 'level',
          displayName: '目標閱讀群組',
        },
        {keyName: 'status', displayName: '狀態'},
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'sjrc', displayName: 'SJRC'},
        {keyName: 'reference', displayName: 'SJRC+'},
        {keyName: 'hasSrVer', displayName: 'PSAR'},
        {
          keyName: 'bookName',
          displayName: 'Book title',
        },
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'authorName',
          displayName: 'Author',
        },
        {keyName: 'publisherName', displayName: 'Imprint'},
        {
          keyName: 'publishedAt',
          displayName: 'Publishing date',
        },
        {
          keyName: 'firstCategoryName',
          displayName: 'Category',
        },
        {
          keyName: 'categorylabels',
          displayName: 'Book categories',
        },
        {
          keyName: 'educationLabels',
          displayName: 'Values Education hashtag',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {keyName: 'status', displayName: 'Status'},
      ],
    },
  },
  schoolReferenceBook: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(中文)',
        },
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'authorName', displayName: '作者 (中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(中文)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(中文)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        {keyName: 'copiesCount', displayName: '購買複本數量'},
        {
          keyName: 'description',
          displayName:
              '書籍簡介(中文)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        {
          keyName: 'language',
          displayName: '書籍語言\n(中文/英文/中英文)',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: '課程綱要分類(英文,如有)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: '圖書品種分類*(英文,如有)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: '價值觀教育標籤(英文,如有)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        {keyName: 'copiesCount', displayName: 'Number of purchased copies'},
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        {
          keyName: 'language',
          displayName: 'Book languages\n(zh/en/zh_en)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: 'Category — Per Key Learning Areas(Eng, if any)',
        },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: 'Book categories*(Eng, if any)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: 'Values Education hashtag(Eng, if any)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
  // (Admin) 主頁 書籍總數量（本）
  bookInformationForAdminV2: {
    zh_HK: {
      name: '書籍',
      specification: [
        // { keyName: 'sjrc', displayName: 'SJRC書目' },
        // { keyName: 'reference', displayName: 'SJRC+書目' },
        // { keyName: 'hasSrVer', displayName: 'PSAR書目' },
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(中文)',
        },
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'authorName', displayName: '作者 (中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(中文)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(中文)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        {keyName: 'status', displayName: '狀態'},
        {keyName: 'onlineAt', displayName: '上架時間'},
        {keyName: 'offlineAt', displayName: '下架時間'},
        {
          keyName: 'publisherDescription',
          displayName: '出版社簡介(中文)',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: '出版社簡介(英文, 如有)',
        },
        // { keyName: 'hideStatus', displayName: '書籍狀態\n(已隱藏/未隱藏)' },
        {
          keyName: 'description',
          displayName:
              '書籍簡介(中文)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        {
          keyName: 'language',
          displayName: '書籍語言\n(中文/英文/中英文)',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: '課程綱要分類(英文,如有)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: '圖書品種分類*(英文,如有)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: '價值觀教育標籤(英文,如有)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        // { keyName: 'sjrc', displayName: 'SJRC' },
        // { keyName: 'reference', displayName: 'SJRC+' },
        // { keyName: 'hasSrVer', displayName: 'PSAR' },
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        {keyName: 'status', displayName: 'Status'},
        {keyName: 'onlineAt', displayName: 'Online At'},
        {keyName: 'offlineAt', displayName: 'Offline At'},
        {
          keyName: 'publisherDescription',
          displayName: 'Imprint\'s introduction',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: 'Imprint\'s introduction',
        },
        // { keyName: 'hideStatus', displayName: 'Book status\n(hidden/unhidden)' },
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        // {
        //   keyName: 'language',
        //   displayName:
        //     'Book language(s)*（If more than one group, please use comma "," to split.  E.g. zh_hk, en_hk, zh_tw)',
        // },
        {
          keyName: 'language',
          displayName: 'Book languages\n(zh/en/zh_en)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: 'Category — Per Key Learning Areas(Eng, if any)',
        },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: 'Book categories*(Eng, if any)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: 'Values Education hashtag(Eng, if any)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
  // (Admin) 書籍管理-总表 export-all-books
  allBookForAdmin: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'sjrc', displayName: 'SJRC書目'},
        {keyName: 'reference', displayName: 'SJRC+書目'},
        {keyName: 'hasSrVer', displayName: 'PSAR書目'},
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(中文)',
        },
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'authorName', displayName: '作者 (中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(中文)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(中文)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        {keyName: 'status', displayName: 'SJRC狀態'},
        {keyName: 'onlineAt', displayName: 'SJRC上架時間'},
        {keyName: 'offlineAt', displayName: 'SJRC下架時間'},
        {
          keyName: 'publisherDescription',
          displayName: '出版社簡介(中文)',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: '出版社簡介(英文, 如有)',
        },
        // { keyName: 'hideStatus', displayName: '書籍狀態\n(已隱藏/未隱藏)' },
        {
          keyName: 'description',
          displayName:
              '書籍簡介(中文)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        // {
        //   keyName: '', displayName: '書籍排版 (豎排/橫排)',
        // },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        {keyName: 'price', displayName: '定價'},
        {
          keyName: 'language',
          displayName: '書籍語言\n(中文/英文/中英文)',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: '課程綱要分類(英文,如有)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: '圖書品種分類*(英文,如有)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: '價值觀教育標籤(英文,如有)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'sjrc', displayName: 'SJRC'},
        {keyName: 'reference', displayName: 'SJRC+'},
        {keyName: 'hasSrVer', displayName: 'PSAR'},
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        {keyName: 'status', displayName: 'SJRC Status'},
        {keyName: 'onlineAt', displayName: 'SJRC Online At'},
        {keyName: 'offlineAt', displayName: 'SJRC Offline At'},
        {
          keyName: 'publisherDescription',
          displayName: 'Imprint\'s introduction',
        },
        {
          keyName: 'publisherDescriptionEn',
          displayName: 'Imprint\'s introduction',
        },
        // { keyName: 'hideStatus', displayName: 'Book status\n(hidden/unhidden)' },
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        {keyName: 'price', displayName: 'Price'},
        {
          keyName: 'language',
          displayName: 'Book languages\n(zh/en/zh_en)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: 'Category — Per Key Learning Areas(Eng, if any)',
        },
        // {
        //   keyName: 'secondCategoryName',
        //   displayName: 'Curriculum Guidelines Category (adjacent item - is any)',
        // },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: 'Book categories*(Eng, if any)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: 'Values Education hashtag(Eng, if any)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
  // school-書籍管理-書籍列表 export-books
  bookInformationV2: {
    zh_HK: {
      name: '書籍',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN*\n(書號請不要加入任何符號,如 " - ")'},
        {
          keyName: 'bookName',
          displayName: '書名\n(中文)',
        },
        {
          keyName: 'bookNameEn',
          displayName: '書名\n(英文, 如有)',
        },
        {keyName: 'authorName', displayName: '作者 (中文)(多個作者使用英文 ,隔開)'},
        {
          keyName: 'authorNameEn',
          displayName: '作者 (英文, 如有)(多個作者使用英文 ,隔開)',
        },
        {
          keyName: 'authorDescription',
          displayName: '作者簡介(中文)\n(多個作者將忽略此項）',
        },
        {
          keyName: 'authorDescriptionEn',
          displayName: '作者簡介(英文, 如有)\n(多個作者將忽略此項）',
        },
        {keyName: 'companyName', displayName: '出版集團(中文)*'},
        {keyName: 'companyNameEn', displayName: '出版集團(英文, 如有)*'},
        {keyName: 'publisherName', displayName: '出版社(中文)*'},
        {keyName: 'publisherNameEn', displayName: '出版社(英文, 如有)*'},
        // { keyName: 'status', displayName: '狀態' },
        // { keyName: 'onlineAt', displayName: '上架時間' },
        // { keyName: 'offlineAt', displayName: '下架時間' },
        // {
        //   keyName: 'publisherDescription',
        //   displayName: '出版社簡介(中文)',
        // },
        // {
        //   keyName: 'publisherDescriptionEn',
        //   displayName: '出版社簡介(英文, 如有)',
        // },
        {keyName: 'hideStatus', displayName: '書籍狀態\n(已隱藏/未隱藏)'},
        {keyName: 'isScienceRoom', displayName: '屬科學活動室書籍(Yes/No)'},
        {
          keyName: 'description',
          displayName:
              '書籍簡介(中文)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '書籍簡介(英文, 如有)\n(中英對照書籍請順序以中文、英文書寫簡介，\n簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'publishedAt',
          displayName:
              '出版日期(yyyy/mm/dd)*\n(如只知出版年份及月份,一概視為1號出版,請將dd輸入為01)',
        },
        {
          keyName: 'publishAddress',
          displayName: '出版地*(請使用ISO-3166-1國家碼來填寫此欄，例如:hk)',
        },
        // {
        //   keyName: 'language',
        //   displayName:
        //     '書籍語言（請使用ISO-639-1語言碼與ISO-3166-1國家碼以下劃線組合的方式來表示語言，例如：zh_hk, en_hk, zh_tw）',
        // },
        {
          keyName: 'language',
          displayName: '書籍語言\n(中文/英文/中英文)',
        },
        {
          keyName: 'level',
          displayName:
              '目標閱讀群組*\n只可選一項(已預設"教職員"可閱讀所有群組書目，\n如此書目只宜教職員閱讀，請選＂教職員"選項)',
        },
        {
          keyName: 'firstCategoryName',
          displayName: '課程綱要分類(多個課程綱要分類使用英文,隔開)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: '課程綱要分類(英文,如有)',
        },
        {
          keyName: 'categorylabels',
          displayName: '圖書品種分類*(多個圖書品種分類使用英文,隔開)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: '圖書品種分類*(英文,如有)',
        },
        {
          keyName: 'educationLabels',
          displayName: '價值觀教育標籤(價值觀教育標籤使用英文 ,隔開)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: '價值觀教育標籤(英文,如有)',
        },
        {
          keyName: 'hyperlink',
          displayName: '書籍相關的視頻超鏈結，如有。',
        },
      ],
    },
    en_uk: {
      name: 'Books',
      specification: [
        {keyName: 'isbn', displayName: 'ISBN* (input the integer ONLY)'},
        {
          keyName: 'bookName',
          displayName:
              'Book title (Chi)(If it is in bilingual, please first input Chinese then English)',
        },
        {
          keyName: 'bookNameEn',
          displayName: 'Book title (English, if any)',
        },
        {
          keyName: 'authorName',
          displayName: 'Author* (use , to separate multiple authors)',
        },
        {
          keyName: 'authorNameEn',
          displayName:
              'Author(s) (Eng)(If more than one authors, please use comma "," to split)',
        },
        {keyName: 'authorDescription', displayName: 'Authors\' bio (Chi)'},
        {keyName: 'authorDescriptionEn', displayName: 'Authors\' bio (Eng, if any)'},
        {keyName: 'companyName', displayName: 'Publishing Group (Chi)'},
        {keyName: 'companyNameEn', displayName: 'Publishing Group (Eng, if any)'},
        {keyName: 'publisherName', displayName: 'Imprint (Chi)'},
        {keyName: 'publisherNameEn', displayName: 'Imprint (Eng, if any)'},
        // { keyName: 'status', displayName: 'Status' },
        // { keyName: 'onlineAt', displayName: 'Online At' },
        // { keyName: 'offlineAt', displayName: 'Offline At' },
        // {
        //   keyName: 'publisherDescription',
        //   displayName: "Imprint's introduction",
        // },
        // {
        //   keyName: 'publisherDescriptionEn',
        //   displayName: "Imprint's introduction",
        // },
        {keyName: 'hideStatus', displayName: 'Book status\n(hidden/unhidden)'},
        {keyName: 'isScienceRoom', displayName: 'Science room books(Yes/No)'},
        {
          keyName: 'description',
          displayName: 'Book description(If it is in bilingual, please first input Chinese then English，please avoid input any promotion information)',
        },
        {
          keyName: 'descriptionEn',
          displayName: 'Book description(Eng, if any.  Please avoid input any promotion information).',
        },
        {
          keyName: 'publishedAt',
          displayName:
              'Publishing date (yyyy/mm/dd)*(If only contact publishing year and month, please treat "01" as the publishing date (DD)))',
        },
        {
          keyName: 'publishAddress',
          displayName:
              'Publisher location*(Please fill in by using ISO-3166-1 country code, e.g.:hk)',
        },
        // {
        //   keyName: 'language',
        //   displayName:
        //     'Book language(s)*（If more than one group, please use comma "," to split.  E.g. zh_hk, en_hk, zh_tw)',
        // },
        {
          keyName: 'language',
          displayName: 'Book languages\n(zh/en/zh_en)',
        },
        {
          keyName: 'level',
          displayName: 'Target reading group*(Allow multiple choices; If more than one group, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryName',
          displayName:
              'Category — Per Key Learning Areas(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'firstCategoryNameEn',
          displayName: 'Category — Per Key Learning Areas(Eng, if any)',
        },
        // {
        //   keyName: 'secondCategoryName',
        //   displayName: 'Curriculum Guidelines Category (adjacent item - is any)',
        // },
        {
          keyName: 'categorylabels',
          displayName:
              'Book categories*(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'categorylabelsEn',
          displayName: 'Book categories*(Eng, if any)',
        },
        {
          keyName: 'educationLabels',
          displayName:
              'Values Education hashtag(If more than one, please use comma "," to split)',
        },
        {
          keyName: 'educationLabelsEn',
          displayName: 'Values Education hashtag(Eng, if any)',
        },
        {
          keyName: 'hyperlink',
          displayName: 'Book\'s promotion hyperlink, if any',
        },
      ],
    },
  },
}