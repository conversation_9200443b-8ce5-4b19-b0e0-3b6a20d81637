import { registerAs } from '@nestjs/config' 
import { assistantConfig } from './assistant.config'
import { booksConfig } from './books.config'
import { managementConfig } from './management.config'
import { publishersConfig } from './publishers.config'
import { reportsConfig } from './reports.config'
import { schoolsConfig } from './schools.config'
import { scienceRoomConfig } from './science-room.config'
import { subjectsConfig } from './subjects.config'
import { usersConfig } from './users.config'

// 共享的列配置
const referenceReadColumns = {
  zh: [
    { keyName: 'HKReadingUsers', displayName: '香港閱讀人數' },
    { keyName: 'HKReadingCounts', displayName: '香港閱讀人次' },
    { keyName: 'MOReadingUsers', displayName: '澳門閱讀人數' },
    { keyName: 'MOReadingCounts', displayName: '澳門閱讀人次' },
    { keyName: 'readingUsers', displayName: '總閱讀人數 (所有地區總和)' },
    { keyName: 'readingCounts', displayName: '總閱讀人次 (所有地區總和)' },
  ],
  en: [
    { keyName: 'HKReadingUsers', displayName: 'Number of readers in HK' },
    { keyName: 'HKReadingCounts', displayName: 'The total number of times in HK' },
    { keyName: 'MOReadingUsers', displayName: 'Number of readers in MO' },
    { keyName: 'MOReadingCounts', displayName: 'The total number of times in MO' },
    { keyName: 'readingUsers', displayName: 'Total readers' },
    { keyName: 'readingCounts', displayName: 'Total number of times' },
  ],
}

// const referenceColumns = {
//   zh: [
//     { keyName: 'HKBooks', displayName: '銷售書籍數量(香港)' },
//     { keyName: 'MOBooks', displayName: '銷售書籍數量(澳門)' },
//     { keyName: 'books', displayName: '銷售書籍總數量' },
//     { keyName: 'HKCopiesCount', displayName: '銷售複本數量(香港)' },
//     { keyName: 'MOCopiesCount', displayName: '銷售複本數量(澳門)' },
//     { keyName: 'copiesCount', displayName: '銷售複本總數量' },
//   ],
//   en: [
//     { keyName: 'HKBooks', displayName: '銷售書籍數量(香港)' },
//     { keyName: 'MOBooks', displayName: '銷售書籍數量(澳門)' },
//     { keyName: 'books', displayName: '銷售書籍總數量' },
//     { keyName: 'HKCopiesCount', displayName: '銷售複本數量(香港)' },
//     { keyName: 'MOCopiesCount', displayName: '銷售複本數量(澳門)' },
//     { keyName: 'copiesCount', displayName: '銷售複本總數量' },
//   ],
// }

const referenceCopiesColumns = {
  zh: [
    { keyName: 'HKCopiesCount', displayName: '新增銷售複本數量(香港)' },
    { keyName: 'MOCopiesCount', displayName: '新增銷售複本數量(澳門)' },
    { keyName: 'copiesCount', displayName: '新增銷售複本總數量' },
    { keyName: 'allHKCopiesCount', displayName: '累計銷售複本數量(香港)' },
    { keyName: 'allMOCopiesCount', displayName: '累計銷售複本數量(澳門)' },
    { keyName: 'allCopiesCount', displayName: '累積銷售複本數量' },
  ],
  en: [
    { keyName: 'HKCopiesCount', displayName: '新增銷售複本數量(香港)' },
    { keyName: 'MOCopiesCount', displayName: '新增銷售複本數量(澳門)' },
    { keyName: 'copiesCount', displayName: '新增銷售複本總數量' },
    { keyName: 'allHKCopiesCount', displayName: '累計銷售複本數量(香港)' },
    { keyName: 'allMOCopiesCount', displayName: '累計銷售複本數量(澳門)' },
    { keyName: 'allCopiesCount', displayName: '累積銷售複本數量' },
  ],
}

// 导出共享配置供其他模块使用
export { referenceReadColumns, referenceCopiesColumns }

export default registerAs('excel', () => ({
  defaultExportLimit: 1000,
  miningJobBalanceDetails: {
    name: 'SampleOfExcel',
    specification: [{ keyName: 'username', displayName: 'Username' }],
  },
  
  // 用户相关配置
  ...usersConfig,
  
  // 学校相关配置
  ...schoolsConfig,
  
  // 出版社相关配置
  ...publishersConfig,
  
  // 书籍相关配置
  ...booksConfig,
  
  // 学科相关配置
  ...subjectsConfig,
  
  // 报表相关配置
  ...reportsConfig(referenceReadColumns, referenceCopiesColumns),

  // 用户管理相关配置
  ...managementConfig,

  // 科学活动室相关配置
  ...scienceRoomConfig,

  // AI助手相关配置
  ...assistantConfig,
}))
