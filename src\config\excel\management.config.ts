// 用户管理和密码重置相关配置
export const managementConfig = {
  resetUserPassword: {
    zh_HK: {
      name: '用戶',
      specification: [
        { keyName: 'name', displayName: '姓名' },
        { keyName: 'serialNo', displayName: '學號/教職員編號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'className', displayName: '班級名稱' },
        { keyName: 'gradeName', displayName: '年级' },
        { keyName: 'type', displayName: '類型 (學生/教職員)' },
        { keyName: 'password', displayName: '新密碼' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'region', displayName: '學校地區' },
        { keyName: 'familyName', displayName: '姓' },
        { keyName: 'givenName', displayName: '名' },
        { keyName: 'lastLoginAt', displayName: '最後一次登錄時間' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: 'Users',
      specification: [
        { keyName: 'name', displayName: 'Name' },
        { keyName: 'serialNo', displayName: 'Student number/Staff number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'className', displayName: 'Curriculum stages' },
        { keyName: 'gradeName', displayName: 'Year' },
        { keyName: 'type', displayName: 'Type (Student/Staff)' },
        { keyName: 'password', displayName: 'New Password' },
        { keyName: 'schoolName', displayName: 'School' },
        { keyName: 'region', displayName: 'School area (Hong Kong/Macao/Other)' },
        { keyName: 'familyName', displayName: 'Surname' },
        { keyName: 'givenName', displayName: 'Name' },
        { keyName: 'lastLoginAt', displayName: 'Last login time' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
  resetMultipleSchoolUserPassword: {
    zh_HK: {
      name: '用戶',
      specification: [
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'password', displayName: '新密碼' },
      ],
    },
    en_uk: {
      name: 'Users',
      specification: [
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'password', displayName: 'New Password' },
      ],
    },
  },
  passwordResetSample: {
    zh_HK: {
      name: '重置密码模板',
      specification: [
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'password', displayName: '新密碼' },
      ],
    },
    en_uk: {
      name: 'Password Reset Sample',
      specification: [
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'password', displayName: 'New Password' },
      ],
    },
  },
  patchMultipleGradesSample: {
    zh_HK: {
      name: '批量调整学生年级',
      specification: [
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'gradeName', displayName: '年级' },
        { keyName: 'className', displayName: '班級名稱' },
      ],
    },
    en_uk: {
      name: 'Patch multiple grades',
      specification: [
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'gradeName', displayName: 'Year' },
        { keyName: 'className', displayName: 'Curriculum stages' },
      ],
    },
  },
  resetMultiplePassword: {
    zh_HK: {
      name: '批量重設密碼',
      specification: [
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'password', displayName: '新密碼' },
      ],
    },
    en_uk: {
      name: 'Reset Multiple Password',
      specification: [
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'password', displayName: 'New Password' },
      ],
    },
  },
  readingTime: {
    zh_HK: {
      name: '時數分配',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'region', displayName: '學校地區' },
        { keyName: 'readingTime', displayName: '閱讀時數' },
        { keyName: 'leftTime', displayName: '剩餘時數' },
      ],
    },
    en_uk: {
      name: 'Reading Hour Allocation',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'schoolName', displayName: 'School' },
        { keyName: 'region', displayName: 'School area (Hong Kong/Macao/Other)' },
        { keyName: 'readingTime', displayName: 'Reading Hours' },
        { keyName: 'leftTime', displayName: 'Left Hours' },
      ],
    },
  },
  usersByAdmin: {
    zh_HK: {
      name: '用戶管理列表',
      specification: [
        { keyName: 'name', displayName: '姓名' },
        { keyName: 'serialNo', displayName: '學號/教職員編號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'className', displayName: '班級名稱' },
        { keyName: 'gradeName', displayName: '年级' },
        { keyName: 'type', displayName: '類型 (學生/教職員)' },
      ],
    },
    en_uk: {
      name: '用戶管理列表',
      specification: [
        { keyName: 'name', displayName: 'Name' },
        { keyName: 'serialNo', displayName: 'Student number/Staff number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'className', displayName: 'Curriculum stages' },
        { keyName: 'gradeName', displayName: 'Year' },
        { keyName: 'type', displayName: 'Type (Student/Staff)' },
      ],
    },
  },
}
