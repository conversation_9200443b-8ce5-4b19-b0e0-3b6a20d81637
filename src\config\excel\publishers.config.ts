export const publishersConfig = {
  publishers: {
    zh_HK: {
      name: '出版社信息',
      specification: [
        { keyName: 'name', displayName: '出版社名稱' },
        { keyName: 'publisherGroupName', displayName: '所屬出版集團' },
        { keyName: 'description', displayName: '簡介' },
        { keyName: 'contactUserName', displayName: '聯絡人' },
        { keyName: 'email', displayName: '聯絡電郵' },
        { keyName: 'address', displayName: '地址' },
      ],
    },
    en_uk: {
      name: 'Publishers Info',
      specification: [
        { keyName: 'name', displayName: 'Publisher name' },
        { keyName: 'publisherGroupName', displayName: 'Publisher group' },
        { keyName: 'description', displayName: 'Description' },
        { keyName: 'contactUserName', displayName: 'Contact person' },
        { keyName: 'email', displayName: 'Contact e-mail' },
        { keyName: 'address', displayName: 'Address' },
      ],
    },
  },
  publisherReadingTime: {
    zh_HK: {
      name: '被閱讀時間最長的出版社',
      specification: [
        { keyName: 'name', displayName: '出版社名稱' },
        { keyName: 'publisherGroupName', displayName: '所屬出版集團' },
        { keyName: 'description', displayName: '簡介' },
        { keyName: 'contactUserName', displayName: '聯絡人' },
        { keyName: 'email', displayName: '聯絡電郵' },
        { keyName: 'address', displayName: '地址' },
      ],
    },
    en_uk: {
      name: 'Publishers with most readers',
      specification: [
        { keyName: 'name', displayName: 'Publisher name' },
        { keyName: 'publisherGroupName', displayName: 'Publisher group' },
        { keyName: 'description', displayName: 'Description' },
        { keyName: 'contactUserName', displayName: 'Contact person' },
        { keyName: 'email', displayName: 'Contact e-mail' },
        { keyName: 'address', displayName: 'Address' },
      ],
    },
  },
}
