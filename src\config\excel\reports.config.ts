export const reportsConfig = (referenceReadColumns: any, referenceCopiesColumns: any) => ({
  platformPublisherReference: {
    zh_HK: {
      name: '出版社管理-出版社報表',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'publisherName', displayName: '出版社名稱' },
        { keyName: 'publisherGroup', displayName: '所屬出版集團' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'name', displayName: '書籍名稱' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'publishedAt', displayName: '出版年份(YYYY)' },
        { keyName: 'isAdded', displayName: '報表時期新增銷售記錄 (Yes/No)' },
        ...referenceCopiesColumns.zh,
        { keyName: 'price', displayName: '價格(港幣)' },
      ],
    },
    en_uk: {
      name: 'Publishers Report',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'publisherName', displayName: 'Publisher Name' },
        { keyName: 'publisherGroup', displayName: 'Publisher Group' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'name', displayName: 'Book Name' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'publishedAt', displayName: 'Published Year(YYYY)' },
        { keyName: 'isAdded', displayName: 'New sales record added during report period (Yes/No)' },
        ...referenceCopiesColumns.en,
        { keyName: 'price', displayName: 'Price(HKD)' },
      ],
    },
  },
  platformPublisherSchoolReference: {
    zh_HK: {
      name: '出版社詳情-學校報告',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'school', displayName: '學校' },
        { keyName: 'isAdded', displayName: '報表時期新增銷售記錄 (Yes/No)' },
        { keyName: 'HKBookCount', displayName: '新增銷售書籍數量(香港)' },
        { keyName: 'MOBookCount', displayName: '新增銷售書籍數量(澳門)' },
        { keyName: 'bookCount', displayName: '新增銷售書籍總數量' },
        { keyName: 'allHKBookCount', displayName: '累積銷售書籍數量(香港)' },
        { keyName: 'allMOBookCount', displayName: '累積銷售書籍數量(澳門)' },
        { keyName: 'allBookCount', displayName: '累積銷售書籍總數量' },
        ...referenceCopiesColumns.zh,
      ],
    },
    en_uk: {
      name: 'Publisher Details - School Report',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'school', displayName: 'School' },
        { keyName: 'isAdded', displayName: 'New sales record added during report period (Yes/No)' },
        { keyName: 'HKBookCount', displayName: 'New sales book count (Hong Kong)' },
        { keyName: 'MOBookCount', displayName: 'New sales book count (Macao)' },
        { keyName: 'bookCount', displayName: 'Total new sales book count' },
        { keyName: 'allHKBookCount', displayName: 'Cumulative sales book count (Hong Kong)' },
        { keyName: 'allMOBookCount', displayName: 'Cumulative sales book count (Macao)' },
        { keyName: 'allBookCount', displayName: 'Total cumulative sales book count' },
        ...referenceCopiesColumns.en,
      ],
    },
  },
  readingOfStudents: {
    zh_HK: {
      name: '閱讀詳情-用戶',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'name', displayName: '學生姓名' },
        { keyName: 'serialNo', displayName: '學號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'currentGradeClass', displayName: '當前所屬年級/班別' },
        { keyName: 'readRecordGradeClass', displayName: '閱讀時所屬年級/班别' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '已閱讀書籍名稱' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'totalReadingTime', displayName: '已閱讀時長（小時)' },
        { keyName: 'allBooksTotalReadingTime', displayName: ' 該學生總閱讀時長（小時)' },
        { keyName: 'readingBooks', displayName: '該學生已閱讀總書量' },
        {
          keyName: 'readingTimeGradeRatio',
          displayName: '閱讀時長佔比【該學生已閱讀時長/年級總閱讀時長】',
        },
      ],
    },
    en_uk: {
      name: 'Reading Details - Users',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'name', displayName: 'Student Name' },
        { keyName: 'serialNo', displayName: 'Student Number' },
        { keyName: 'email', displayName: 'Email' },
        { keyName: 'currentGradeClass', displayName: 'Current Grade/Class' },
        { keyName: 'readRecordGradeClass', displayName: 'Grade/Class when reading' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name Read' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'totalReadingTime', displayName: 'Total Reading Time (Hours)' },
        { keyName: 'allBooksTotalReadingTime', displayName: 'Student Total Reading Time (Hours)' },
        { keyName: 'readingBooks', displayName: 'Total Books Read by Student' },
        {
          keyName: 'readingTimeGradeRatio',
          displayName: 'Reading Time Ratio [Student Reading Time/Grade Total Reading Time]',
        },
      ],
    },
  },
  referenceReadingOfUsers: {
    zh_HK: {
      name: '閱讀詳情-用戶',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'name', displayName: '學生姓名' },
        { keyName: 'serialNo', displayName: '學號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'grade', displayName: '所屬年級' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '已閱讀書籍名稱' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'counts', displayName: '已閱讀次數' },
        { keyName: 'totalCounts', displayName: '⁠該學生閱讀總次數' },
        { keyName: 'bookCounts', displayName: '該學生閱讀總書量' },
        {
          keyName: 'ratio',
          displayName: '閱讀數量佔比【該學生已閱讀書籍本數/年級總閱讀書籍本數】',
        },
      ],
    },
    en_uk: {
      name: 'Reading Details - Users',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'name', displayName: 'Student Name' },
        { keyName: 'serialNo', displayName: 'Student Number' },
        { keyName: 'email', displayName: 'Email' },
        { keyName: 'class', displayName: 'Class' },
        { keyName: 'grade', displayName: 'Grade' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name Read' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'counts', displayName: 'Reading Count' },
        { keyName: 'totalCounts', displayName: 'Student Total Reading Count' },
        { keyName: 'bookCounts', displayName: 'Student Total Books Read' },
        {
          keyName: 'ratio',
          displayName: 'Reading Ratio [Student Books Read/Grade Total Books Read]',
        },
      ],
    },
  },
  readRefectionDetailSubscription: {
    zh_HK: {
      name: '閱讀感想詳情',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'currentGradeClassName', displayName: '當前所屬年級/班別' },
        { keyName: 'submitGradeClassName', displayName: '提交感想時所屬年級/班別' },
        { keyName: 'studentSerialNo', displayName: '學號' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'bookName', displayName: '閱讀書籍' },
        { keyName: 'readingReflectionCount', displayName: '此時段提交數量' },
        { keyName: 'totalReadingTime', displayName: '該書的總閱讀時數(小時)' },
        { keyName: 'latest_created_at', displayName: '最後提交時間' },
        { keyName: 'reflections1', displayName: '感想1 (最後提交)' },
        { keyName: 'reflections2', displayName: '感想2 (最後提交)' },
        { keyName: 'reflections3', displayName: '感想3 (最後提交)' },
        { keyName: 'reflections4', displayName: '感想4 (最後提交)' },
        { keyName: 'reflections5', displayName: '感想5 (最後提交)' },
      ],
    },
    en_uk: {
      name: 'Reading Reflection Details',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'currentGradeClassName', displayName: 'Current Grade/Class' },
        { keyName: 'submitGradeClassName', displayName: 'Grade/Class when submitting reflection' },
        { keyName: 'studentSerialNo', displayName: 'Student Number' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'bookName', displayName: 'Book Read' },
        { keyName: 'readingReflectionCount', displayName: 'Submissions in this period' },
        { keyName: 'totalReadingTime', displayName: 'Total reading time for this book (hours)' },
        { keyName: 'latest_created_at', displayName: 'Last submission time' },
        { keyName: 'reflections1', displayName: 'Reflection 1 (latest submission)' },
        { keyName: 'reflections2', displayName: 'Reflection 2 (latest submission)' },
        { keyName: 'reflections3', displayName: 'Reflection 3 (latest submission)' },
        { keyName: 'reflections4', displayName: 'Reflection 4 (latest submission)' },
        { keyName: 'reflections5', displayName: 'Reflection 5 (latest submission)' },
      ],
    },
  },
  readRefectionDetailReference: {
    zh_HK: {
      name: '閱讀感想詳情',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'currentGradeClassName', displayName: '當前所屬年級/班別' },
        { keyName: 'submitGradeClassName', displayName: '提交感想時所屬年級/班別' },
        { keyName: 'studentSerialNo', displayName: '學號' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'bookName', displayName: '閱讀書籍' },
        { keyName: 'readingReflectionCount', displayName: '此時段提交數量' },
        { keyName: 'readingCount', displayName: '該書的總閱讀次數' },
        { keyName: 'latest_created_at', displayName: '最後提交時間' },
        { keyName: 'reflections1', displayName: '感想1 (最後提交)' },
        { keyName: 'reflections2', displayName: '感想2 (最後提交)' },
        { keyName: 'reflections3', displayName: '感想3 (最後提交)' },
        { keyName: 'reflections4', displayName: '感想4 (最後提交)' },
        { keyName: 'reflections5', displayName: '感想5 (最後提交)' },
      ],
    },
    en_uk: {
      name: 'Reading Reflection Details',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'currentGradeClassName', displayName: 'Current Grade/Class' },
        { keyName: 'submitGradeClassName', displayName: 'Grade/Class when submitting reflection' },
        { keyName: 'studentSerialNo', displayName: 'Student Number' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'bookName', displayName: 'Book Read' },
        { keyName: 'readingReflectionCount', displayName: 'Submissions in this period' },
        { keyName: 'readingCount', displayName: 'Total reading count for this book' },
        { keyName: 'latest_created_at', displayName: 'Last submission time' },
        { keyName: 'reflections1', displayName: 'Reflection 1 (latest submission)' },
        { keyName: 'reflections2', displayName: 'Reflection 2 (latest submission)' },
        { keyName: 'reflections3', displayName: 'Reflection 3 (latest submission)' },
        { keyName: 'reflections4', displayName: 'Reflection 4 (latest submission)' },
        { keyName: 'reflections5', displayName: 'Reflection 5 (latest submission)' },
      ],
    },
  },
  readingOfStudentsAllSchool: {
    zh_HK: {
      name: '閱讀詳情-用戶',
      specification: [
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'schoolType', displayName: '學校類別' },
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'name', displayName: '學生姓名' },
        { keyName: 'serialNo', displayName: '學號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'currentGradeClass', displayName: '當前所屬年級/班別' },
        { keyName: 'readRecordGradeClass', displayName: '閱讀時所屬年級/班别' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '已閱讀書籍名稱' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'totalReadingTime', displayName: '已閱讀時長（小時)' },
        { keyName: 'allBooksTotalReadingTime', displayName: ' 該學生總閱讀時長（小時)' },
        { keyName: 'readingBooks', displayName: '該學生已閱讀總書量' },
        {
          keyName: 'readingTimeGradeRatio',
          displayName: '閱讀時長佔比【該學生已閱讀總時長/年級總時長】',
        },
        {
          keyName: 'readingTimeClassRatio',
          displayName: '閱讀時長佔比【該學生已閱讀總時長/班別總時長】',
        },
      ],
    },
    en_uk: {
      name: 'reading detail-users',
      specification: [
        { keyName: 'schoolName', displayName: 'School Name' },
        { keyName: 'schoolType', displayName: 'School Type' },
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'name', displayName: 'Student name' },
        { keyName: 'serialNo', displayName: 'Student ID' },
        { keyName: 'class', displayName: 'Current grade/class' },
        { keyName: 'grade', displayName: 'The grade/class you belong to when reading' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Books read' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'totalReadingTime', displayName: 'Reading hours (hours)' },
        {
          keyName: 'allBooksTotalReadingTime',
          displayName: 'Total Reading hours of student',
        },
        {
          keyName: 'readingBooks',
          displayName: 'Number of books the student has read',
        },
        {
          keyName: 'readingTimeGradeRatio',
          displayName:
            'Percentage of reading hours【Reading hours of student/Grade reading hours】',
        },
        {
          keyName: 'readingTimeClassRatio',
          displayName:
            'Percentage of reading hours【Reading hours of student/class reading hours】',
        },
      ],
    },
  },
  readingOfBooks: {
    zh_HK: {
      name: '閱讀詳情-書籍',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '書籍名稱' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'totalUser', displayName: '閱讀人數(按每本書籍總數)' },
        { keyName: 'totalReadingTime', displayName: '已閱讀時長（小時)(按每本書籍總數)' },
        { keyName: 'totalReadingTimes', displayName: '已閱讀時長（秒)(按每本書籍總數)' },
      ],
    },
    en_uk: {
      name: 'reading detal-books',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'totalUser', displayName: 'Number of readers' },
        {
          keyName: 'totalReadingTime',
          displayName: 'Reading hours（Hour)',
        },
      ],
    },
  },
  readingOfBooksV2: {
    zh_HK: {
      name: '閱讀詳情-書籍',
      specification: [
        { keyName: 'schoolType', displayName: '學校類別' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '書籍名稱' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'totalUser', displayName: '閱讀人數(按每本書籍總數)' },
        { keyName: 'totalReadingTime', displayName: '已閱讀時長（小時)(按每本書籍總數)' },
        { keyName: 'totalReadingTimes', displayName: '已閱讀時長（秒)(按每本書籍總數)' },
      ],
    },
    en_uk: {
      name: 'reading detal-books',
      specification: [
        { keyName: 'schoolType', displayName: 'School Type' },
        { keyName: 'schoolName', displayName: 'School Name' },
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'totalUser', displayName: 'Number of readers' },
        {
          keyName: 'totalReadingTime',
          displayName: 'Reading hours（Hour)',
        },
      ],
    },
  },
  readingTimeInClass: {
    zh_HK: {
      name: '閱讀時數分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeReadingTime',
          displayName: '閱讀總時數(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeReadingTimes',
          displayName: '閱讀總時數(秒)(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeReadingTimeRatio',
          displayName:
            '閱讀時數佔比【閱讀時數(年級/教職員)/閱讀總時數(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classReadingTime', displayName: '閱讀總時數(班別)' },
        { keyName: 'classReadingTimes', displayName: '閱讀總時數(秒)(班別)' },
        {
          keyName: 'classReadingTimeRatio',
          displayName: '閱讀時長佔比【閱讀時數(班別)/閱讀總時數(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Reading Hours In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeReadingTime',
          displayName: 'Total Reading hours (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeReadingTimeRatio',
          displayName:
            'Percentage of reading hours【Total Reading hours (Curriculum stages/Stuff)/Total Reading hours(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classReadingTime', displayName: 'Total Reading hours(Classes)' },
        {
          keyName: 'classReadingTimeRatio',
          displayName:
            'Percentage of reading hours【Total Reading hours(Classes)/Total Reading hours(Curriculum stages)】',
        },
      ],
    },
  },
  top10Books: {
    zh_HK: {
      name: '閱讀時間TOP 10',
      specification: [
        { keyName: 'type', displayName: '用戶類別 (全部／ 學生 / 教職員)' },
        // { keyName: 'startTime', displayName: '開始日期' },
        // { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '書名' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'readingUsers', displayName: '閱讀人數' },
        { keyName: 'readingTime', displayName: '已閱讀時數' },
      ],
    },
    en_uk: {
      name: 'Reading Hours TOP 10',
      specification: [
        {
          keyName: 'type',
          displayName: `Type *(STUDENT/TEACHER/ALL, "STUDENT" for students, "TEACHER" ​​for Stuff)`,
        },
        // { keyName: 'startTime', displayName: 'Start Date' },
        // { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'readingUsers', displayName: 'Number of readers' },
        { keyName: 'readingTime', displayName: 'Reading Hours' },
      ],
    },
  },
  top10ReferenceBooks: {
    zh_HK: {
      name: '閱讀數量 (人次)TOP 10',
      specification: [
        { keyName: 'type', displayName: '用戶類別 (全部／ 學生 / 教職員)' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: '書名' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'publisher', displayName: '出版社' },
        { keyName: 'readingUsers', displayName: '閱讀人數' },
        { keyName: 'readingCounts', displayName: '閱讀人次' },
      ],
    },
    en_uk: {
      name: 'Number of readers TOP 10',
      specification: [
        {
          keyName: 'type',
          displayName: `Type *(STUDENT/TEACHER/ALL, "STUDENT" for students, "TEACHER" ​​for Stuff)`,
        },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'publisher', displayName: 'Publisher' },
        { keyName: 'readingUsers', displayName: 'Number of readers' },
        { keyName: 'readingCounts', displayName: 'Number of times' },
      ],
    },
  },
  readingUserCountInClass: {
    zh_HK: {
      name: '閱讀人數分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeUserCount',
          displayName: '閱讀人數(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            '閱讀人數佔比【閱讀人數(年級/教職員)/閱讀總人數(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classUserCount', displayName: '閱讀人數(班別)' },
        {
          keyName: 'classUserCountRatio',
          displayName: '閱讀人數佔比【閱讀人數(班別)/閱讀總人數(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of readers In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeUserCount',
          displayName: 'Total readers (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            'Percentage of readers【Total readers (Curriculum stages/Stuff)/Total readers(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classUserCount', displayName: 'Total readers(Classes)' },
        {
          keyName: 'classUserCountRatio',
          displayName:
            'Percentage of readers【Total readers(Classes)/Total readers(Curriculum stages)】',
        },
      ],
    },
  },
  schoolReferenceUserCount: {
    zh_HK: {
      name: '閱讀人數分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeUserCount',
          displayName: '閱讀人數(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            '閱讀人數佔比【閱讀人數(年級/教職員)/閱讀總人數(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classUserCount', displayName: '閱讀人數(班別)' },
        {
          keyName: 'classUserCountRatio',
          displayName: '閱讀人數佔比【閱讀人數(班別)/閱讀總人數(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of readers In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeUserCount',
          displayName: 'Total readers (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            'Percentage of readers【Total readers (Curriculum stages/Stuff)/Total readers(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classUserCount', displayName: 'Total readers(Classes)' },
        {
          keyName: 'classUserCountRatio',
          displayName:
            'Percentage of readers【Total readers(Classes)/Total readers(Curriculum stages)】',
        },
      ],
    },
  },
  readingTimeAndCountByDate: {
    zh_HK: {
      name: '閱讀時數分佈',
      specification: [
        { keyName: 'date', displayName: '日期' },
        { keyName: 'readingTime', displayName: '閱讀時數' },
        { keyName: 'readingCount', displayName: '閱讀人次' },
      ],
    },
    en_uk: {
      name: 'Reading Hours',
      specification: [
        { keyName: 'date', displayName: 'Date' },
        { keyName: 'readingTime', displayName: 'Reading Hours' },
        { keyName: 'readingCount', displayName: 'Reading Count' },
      ],
    },
  },
  top10Subjects: {
    zh_HK: {
      name: '最受歡迎課題 TOP 10',
      specification: [
        { keyName: 'level', displayName: '適用年級' },
        { keyName: 'subjectName', displayName: '課題名稱' },
        { keyName: 'participantCount', displayName: '參與人數' },
        { keyName: 'answerCount', displayName: '回答總數' },
      ],
    },
    en_uk: {
      name: 'Top 10 most popular topics',
      specification: [
        { keyName: 'level', displayName: 'Level' },
        { keyName: 'subjectName', displayName: 'Subject Name' },
        { keyName: 'participantCount', displayName: 'Participant Count' },
        { keyName: 'answerCount', displayName: 'Answer Count' },
      ],
    },
  },
  publisherReading: {
    zh_HK: {
      name: '出版社信息',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'publisherName', displayName: '出版社名稱' },
        { keyName: 'readingUsers', displayName: '閱讀人數' },
        { keyName: 'readingTime', displayName: '閱讀時數' },
        { keyName: 'bookCount', displayName: '書籍數量' },
      ],
    },
    en_uk: {
      name: 'Publishers Info',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'publisherName', displayName: 'Publisher Name' },
        { keyName: 'readingUsers', displayName: 'Reading Users' },
        { keyName: 'readingTime', displayName: 'Reading Time' },
        { keyName: 'bookCount', displayName: 'Book Count' },
      ],
    },
  },
  AllPublisherReading: {
    zh_HK: {
      name: '出版社信息',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'publisherName', displayName: '出版社名稱' },
        { keyName: 'readingUsers', displayName: '閱讀人數' },
        { keyName: 'readingTime', displayName: '閱讀時數' },
        { keyName: 'bookCount', displayName: '書籍數量' },
      ],
    },
    en_uk: {
      name: 'Publishers Info',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'publisherName', displayName: 'Publisher Name' },
        { keyName: 'readingUsers', displayName: 'Reading Users' },
        { keyName: 'readingTime', displayName: 'Reading Time' },
        { keyName: 'bookCount', displayName: 'Book Count' },
      ],
    },
  },
  readRefectionTeacherCheckSubscription: {
    zh_HK: {
      name: '老師檢閱狀態',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'bookName', displayName: '閱讀書籍' },
        { keyName: 'reflectionContent', displayName: '感想內容' },
        { keyName: 'teacherComment', displayName: '老師評語' },
        { keyName: 'checkStatus', displayName: '檢閱狀態' },
        { keyName: 'checkedAt', displayName: '檢閱時間' },
      ],
    },
    en_uk: {
      name: 'Teacher check state',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'reflectionContent', displayName: 'Reflection Content' },
        { keyName: 'teacherComment', displayName: 'Teacher Comment' },
        { keyName: 'checkStatus', displayName: 'Check Status' },
        { keyName: 'checkedAt', displayName: 'Checked At' },
      ],
    },
  },
  readRefectionTeacherCheckReference: {
    zh_HK: {
      name: '老師檢閱狀態',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'bookName', displayName: '閱讀書籍' },
        { keyName: 'reflectionContent', displayName: '感想內容' },
        { keyName: 'teacherComment', displayName: '老師評語' },
        { keyName: 'checkStatus', displayName: '檢閱狀態' },
        { keyName: 'checkedAt', displayName: '檢閱時間' },
      ],
    },
    en_uk: {
      name: 'Teacher check state',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'bookName', displayName: 'Book Name' },
        { keyName: 'reflectionContent', displayName: 'Reflection Content' },
        { keyName: 'teacherComment', displayName: 'Teacher Comment' },
        { keyName: 'checkStatus', displayName: 'Check Status' },
        { keyName: 'checkedAt', displayName: 'Checked At' },
      ],
    },
  },
  platformSchoolReading: {
    zh_HK: {
      name: '書籍點擊數量及人數-學校',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'address', displayName: '學校地址' },
        { keyName: 'joinedAt', displayName: '加入日期' },
        ...referenceReadColumns.zh,
      ],
    },
    en_uk: {
      name: 'Book Clicks and Readers-School',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'schoolName', displayName: 'School Name' },
        { keyName: 'address', displayName: 'School Address' },
        { keyName: 'joinedAt', displayName: 'Join At' },
        ...referenceReadColumns.en,
      ],
    },
  },
  platformPublisherReading: {
    zh_HK: {
      name: '書籍點擊數量及人數-出版社',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'publisherName', displayName: '出版社名稱' },
        { keyName: 'publisherGroup', displayName: '所屬出版集團' },
        ...referenceReadColumns.zh,
      ],
    },
    en_uk: {
      name: 'Book Clicks and Readers-Publisher',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'publisherName', displayName: 'Publisher Name' },
        { keyName: 'publisherGroup', displayName: 'Publisher Group' },
        ...referenceReadColumns.en,
      ],
    },
  },
  platformAuthorReading: {
    zh_HK: {
      name: '書籍點擊數量及人數-作者',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'author', displayName: '作者' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'name', displayName: '書名' },
        { keyName: 'price', displayName: '定價' },
        { keyName: 'copies', displayName: '複本數量' },
        { keyName: 'publisherName', displayName: '出版社名稱' },
        { keyName: 'publisherGroup', displayName: '所屬出版集團' },
        ...referenceReadColumns.zh,
      ],
    },
    en_uk: {
      name: 'Book Clicks and Readers-Book',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'author', displayName: 'Author' },
        { keyName: 'isbn', displayName: 'ISBN' },
        { keyName: 'name', displayName: 'Book Name' },
        { keyName: 'price', displayName: 'Price' },
        { keyName: 'copies', displayName: 'Number of replicas' },
        { keyName: 'publisherName', displayName: 'Publisher Name' },
        { keyName: 'publisherGroup', displayName: 'Publisher Group' },
        ...referenceReadColumns.en,
      ],
    },
  },
})
