export const schoolsConfig = {
  schools: {
    zh_HK: {
      name: '學校信息',
      specification: [
        { keyName: 'hasSubVer', displayName: '訂閱版' },
        { keyName: 'hasRefVer', displayName: '參考館' },
        { keyName: 'hasSrVer', displayName: '科學活動室' },
        { keyName: 'hasAIVer', displayName: '文心智友' },
        { keyName: 'name', displayName: '學校名稱' },
        { keyName: 'region', displayName: '地區 (香港/澳門/其他)' },
        { keyName: 'principalName', displayName: '聯絡人' },
        { keyName: 'principalNo', displayName: '聯絡電話' },
        { keyName: 'principalEmail', displayName: '聯絡電郵' },
        { keyName: 'joinedAt', displayName: '加入日期' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: `School's information`,
      specification: [
        { keyName: 'hasSubVer', displayName: 'SJRC' },
        { keyName: 'hasRefVer', displayName: 'SJRC+' },
        { keyName: 'hasSrVer', displayName: 'PSAR' },
        { keyName: 'hasAIVer', displayName: 'AI Wisereader' },
        { keyName: 'name', displayName: 'School name' },
        { keyName: 'region', displayName: 'Region (Hong Kong/Macau/Others)' },
        { keyName: 'principalName', displayName: 'Contact Person' },
        { keyName: 'principalNo', displayName: 'Contact number' },
        { keyName: 'principalEmail', displayName: 'Contact e-mail' },
        { keyName: 'joinedAt', displayName: 'Join date' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
  schoolReadingTime: {
    zh_HK: {
      name: '閱讀時間最長的學校',
      specification: [
        { keyName: 'name', displayName: '學校名稱' },
        { keyName: 'address', displayName: '學校地址' },
        { keyName: 'description', displayName: '簡介' },
        { keyName: 'principalName', displayName: '聯絡人' },
        { keyName: 'principalNo', displayName: '聯絡電話' },
        { keyName: 'principalEmail', displayName: '聯絡電郵' },
        { keyName: 'joinedAt', displayName: '加入日期' },
        { keyName: 'studentEmailSuffix', displayName: '學生電郵網域' },
        { keyName: 'teacherEmailSuffix', displayName: '教職員電郵網域' },
      ],
    },
    en_uk: {
      name: `Schools consumes most reading time`,
      specification: [
        { keyName: 'name', displayName: 'School name' },
        { keyName: 'address', displayName: 'School address' },
        { keyName: 'description', displayName: 'Brief introduction' },
        { keyName: 'principalName', displayName: 'Contact Person' },
        { keyName: 'principalNo', displayName: 'Contact number' },
        { keyName: 'principalEmail', displayName: 'Contact e-mail' },
        { keyName: 'joinedAt', displayName: 'Join date' },
        { keyName: 'studentEmailSuffix', displayName: 'Student e-mail domain' },
        { keyName: 'teacherEmailSuffix', displayName: 'Staff e-mail domain' },
      ],
    },
  },
  schoolLeftTime: {
    zh_HK: {
      name: '剩餘閱讀時間最多的學校',
      specification: [
        { keyName: 'name', displayName: '學校名稱' },
        { keyName: 'address', displayName: '學校地址' },
        { keyName: 'description', displayName: '簡介' },
        { keyName: 'principalName', displayName: '聯絡人' },
        { keyName: 'principalNo', displayName: '聯絡電話' },
        { keyName: 'principalEmail', displayName: '聯絡電郵' },
        { keyName: 'joinedAt', displayName: '加入日期' },
        { keyName: 'studentEmailSuffix', displayName: '學生電郵網域' },
        { keyName: 'teacherEmailSuffix', displayName: '教職員電郵網域' },
      ],
    },
    en_uk: {
      name: `Schools with most time`,
      specification: [
        { keyName: 'name', displayName: 'School name' },
        { keyName: 'address', displayName: 'School address' },
        { keyName: 'description', displayName: 'Brief introduction' },
        { keyName: 'principalName', displayName: 'Contact Person' },
        { keyName: 'principalNo', displayName: 'Contact number' },
        { keyName: 'principalEmail', displayName: 'Contact e-mail' },
        { keyName: 'joinedAt', displayName: 'Join date' },
        { keyName: 'studentEmailSuffix', displayName: 'Student e-mail domain' },
        { keyName: 'teacherEmailSuffix', displayName: 'Staff e-mail domain' },
      ],
    },
  },
  schoolPublisherReadingTime: {
    zh_HK: {
      name: '阅读时间',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        // { keyName: 'name', displayName: '出版社名稱' },
        { keyName: 'schoolName', displayName: '學校' },
        // { keyName: 'readingTime', displayName: '校內總閱讀時數' },
        // { keyName: 'readingTimes', displayName: '校內總閱讀時數(秒)' },
      ],
    },
  },
}
