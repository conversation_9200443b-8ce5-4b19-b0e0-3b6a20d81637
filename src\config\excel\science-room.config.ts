// 科学活动室相关配置
export const scienceRoomConfig = {
  scienceRoomUV: {
    zh_HK: {
      name: '互動人數分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeUserCount',
          displayName: '互動人數(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            '互動人數佔比【互動人數(年級/教職員)/互動總人數(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classUserCount', displayName: '互動人數(班別)' },
        {
          keyName: 'classUserCountRatio',
          displayName: '互動人數佔比【互動人數(班別)/互動總人數(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of engagement (Counts) In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeUserCount',
          displayName: 'Total engagement users (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeUserCountRatio',
          displayName:
            'Percentage of engagement users【Total engagement users (Curriculum stages/Stuff)/Total engagement users(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classUserCount', displayName: 'Total engagement users(Classes)' },
        {
          keyName: 'classUserCountRatio',
          displayName:
            'Percentage of engagement users【Total engagement users(Classes)/Total engagement users(Curriculum stages)】',
        },
      ],
    },
  },
  scienceRoomPV: {
    zh_HK: {
      name: '互動人次分佈',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'type', displayName: '用戶類別 (學生/教職員)' },
        { keyName: 'grade', displayName: '所屬年級' },
        {
          keyName: 'gradeEngagementCount',
          displayName: '互動人次(年級/教職員)[所有用戶適用]',
        },
        {
          keyName: 'gradeEngagementCountRatio',
          displayName:
            '互動人次佔比【互動人次(年級/教職員)/互動總人次(所有用戶)】[所有用戶適用]',
        },
        { keyName: 'class', displayName: '所屬班別' },
        { keyName: 'classEngagementCount', displayName: '互動人次(班別)' },
        {
          keyName: 'classEngagementCountRatio',
          displayName: '互動人次佔比【互動人次(班別)/互動總人次(全級)】',
        },
      ],
    },
    en_uk: {
      name: 'Number of engagement (Counts) In Class',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT/TEACHER), "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'grade', displayName: 'Curriculum stages' },
        {
          keyName: 'gradeEngagementCount',
          displayName: 'Total engagement counts (Curriculum stages/Stuff)',
        },
        {
          keyName: 'gradeEngagementCountRatio',
          displayName:
            'Percentage of engagement counts【Total engagement counts (Curriculum stages/Stuff)/Total engagement counts(ALL User)】',
        },
        { keyName: 'class', displayName: 'Classes' },
        { keyName: 'classEngagementCount', displayName: 'Total engagement counts(Classes)' },
        {
          keyName: 'classEngagementCountRatio',
          displayName:
            'Percentage of engagement counts【Total engagement counts(Classes)/Total engagement counts(Curriculum stages)】',
        },
      ],
    },
  },
  schoolSubjectList: {
    zh_HK: {
      name: '課題列表',
      specification: [
        { keyName: 'serialNo', displayName: '學習重點編號' },
        { keyName: 'subjectName', displayName: '課題名稱' },
        { keyName: 'level', displayName: '適用年級' },
        { keyName: 'description', displayName: '課題描述' },
        { keyName: 'createdAt', displayName: '創建時間' },
        { keyName: 'updatedAt', displayName: '更新時間' },
        { keyName: 'isActive', displayName: '是否啟用' },
      ],
    },
    en_uk: {
      name: 'Subject List',
      specification: [
        { keyName: 'serialNo', displayName: 'Learning objective code' },
        { keyName: 'subjectName', displayName: 'Subject Name' },
        { keyName: 'level', displayName: 'Level' },
        { keyName: 'description', displayName: 'Description' },
        { keyName: 'createdAt', displayName: 'Created At' },
        { keyName: 'updatedAt', displayName: 'Updated At' },
        { keyName: 'isActive', displayName: 'Is Active' },
      ],
    },
  },
  adminSubjectList: {
    zh_HK: {
      name: '課題列表',
      specification: [
        { keyName: 'serialNo', displayName: '學習重點編號' },
        { keyName: 'subjectName', displayName: '課題名稱' },
        { keyName: 'level', displayName: '適用年級' },
        { keyName: 'description', displayName: '課題描述' },
        { keyName: 'createdAt', displayName: '創建時間' },
        { keyName: 'updatedAt', displayName: '更新時間' },
        { keyName: 'isActive', displayName: '是否啟用' },
      ],
    },
    en_uk: {
      name: 'Subject List',
      specification: [
        { keyName: 'serialNo', displayName: 'Learning objective code' },
        { keyName: 'subjectName', displayName: 'Subject Name' },
        { keyName: 'level', displayName: 'Level' },
        { keyName: 'description', displayName: 'Description' },
        { keyName: 'createdAt', displayName: 'Created At' },
        { keyName: 'updatedAt', displayName: 'Updated At' },
        { keyName: 'isActive', displayName: 'Is Active' },
      ],
    },
  },
  subjectUserAnswerDetail: {
    zh_HK: {
      name: '互動詳情(用戶)',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'userEmail', displayName: '電郵' },
        { keyName: 'userName', displayName: '學生姓名' },
        { keyName: 'subjectName', displayName: '課題名稱' },
        { keyName: 'answerCount', displayName: '回答數量' },
        { keyName: 'correctCount', displayName: '正確數量' },
        { keyName: 'accuracy', displayName: '正確率' },
      ],
    },
    en_uk: {
      name: 'QA Detail(User)',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'userEmail', displayName: 'Email' },
        { keyName: 'userName', displayName: 'Student Name' },
        { keyName: 'subjectName', displayName: 'Subject Name' },
        { keyName: 'answerCount', displayName: 'Answer Count' },
        { keyName: 'correctCount', displayName: 'Correct Count' },
        { keyName: 'accuracy', displayName: 'Accuracy' },
      ],
    },
  },
  subjectUserAnswerDetailSubject: {
    zh_HK: {
      name: '互動詳情(課題)',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'subjectName', displayName: '課題名稱' },
        { keyName: 'totalUsers', displayName: '參與用戶數' },
        { keyName: 'totalAnswers', displayName: '總回答數' },
        { keyName: 'correctAnswers', displayName: '正確回答數' },
        { keyName: 'averageAccuracy', displayName: '平均正確率' },
      ],
    },
    en_uk: {
      name: 'Interaction details (topic)',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'subjectName', displayName: 'Subject Name' },
        { keyName: 'totalUsers', displayName: 'Total Users' },
        { keyName: 'totalAnswers', displayName: 'Total Answers' },
        { keyName: 'correctAnswers', displayName: 'Correct Answers' },
        { keyName: 'averageAccuracy', displayName: 'Average Accuracy' },
      ],
    },
  },
  subjectUserAnswerGroupBySchool: {
    zh_HK: {
      name: '課題參與數量(學校) ',
      specification: [
        { keyName: 'startTime', displayName: '開始日期' },
        { keyName: 'endTime', displayName: '結束日期' },
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'participantCount', displayName: '參與人數' },
        { keyName: 'answerCount', displayName: '回答總數' },
      ],
    },
    en_uk: {
      name: '課題參與數量(學校) ',
      specification: [
        { keyName: 'startTime', displayName: 'Start Date' },
        { keyName: 'endTime', displayName: 'End Date' },
        { keyName: 'schoolName', displayName: 'School Name' },
        { keyName: 'participantCount', displayName: 'Participant Count' },
        { keyName: 'answerCount', displayName: 'Answer Count' },
      ],
    },
  },
}
