export const subjectsConfig = {
  referenceCategories: {
    zh_HK: {
      name: '課程綱要分類',
      specification: [
        {keyName: 'firstCategoryName', displayName: '課程綱要分類(主項)'},
        // { keyName: 'secondCategoryName', displayName: '課程綱要分類(次項)' },
      ],
    },
    en_uk: {
      name: 'Curriculum Guidelines Category',
      specification: [
        {
          keyName: 'firstCategoryName',
          displayName: 'Curriculum Guidelines Category (Main item)',
        },
        // {
        //   keyName: 'secondCategoryName',
        //   displayName: 'Curriculum Guidelines Category (adjacent item)',
        // },
      ],
    },
  },
  addAuthors: {
    zh_HK: {
      name: '批量增加作者',
      specification: [
        {keyName: 'name', displayName: '姓名*（中文繁體）'},
        {keyName: 'nameEn', displayName: '姓名(英文, 如有)'},
        {keyName: 'nameCn', displayName: '姓名(中文简体, 如有)'},
        {
          keyName: 'description',
          displayName:
              '作者簡介\n（中文繁體）(簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionEn',
          displayName:
              '作者簡介\n（英文, 如有）(簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {
          keyName: 'descriptionCn',
          displayName:
              '作者簡介(中文简体, 如有)(簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
      ],
    },
    en_uk: {
      name: 'Add authors in batches',
      specification: [
        {keyName: 'name', displayName: 'Name*'},
        {
          keyName: 'description',
          displayName: 'Author Introduction* (DONOT include information such as \'signed copies\', \'special gift\', \'limited\' or any other marketing information.',
        },
      ],
    },
  },
  top50Authors: {
    zh_HK: {
      name: 'Top50Author',
      specification: [
        {keyName: 'name', displayName: '姓名*'},
        {
          keyName: 'description',
          displayName: '作者簡介\n(簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        // { keyName: 'totalReadingTime', displayName: '閱讀總時長' },
      ],
    },
    en_uk: {
      name: 'Top50Author',
      specification: [
        {keyName: 'name', displayName: 'Name*'},
        {
          keyName: 'description',
          displayName: 'Author Introduction* (DONOT include information such as \'signed copies\', \'special gift\', \'limited\' or any other marketing information.',
        },
        // { keyName: 'totalReadingTime', displayName: 'total reading time' },
      ],
    },
  },
  authors: {
    zh_HK: {
      name: '作者',
      specification: [
        {keyName: 'name', displayName: '姓名*'},
        {keyName: 'bookName', displayName: '書籍名稱'},
        {
          keyName: 'description',
          displayName: '作者簡介\n(簡介內容請勿包含任何推廣如簽名版、贈品等其它資訊)',
        },
        {keyName: 'publisher', displayName: '出版社'},
        {keyName: 'companyName', displayName: '出版集團'},
        {keyName: 'totalUser', displayName: '閱讀人數(按每本書籍總數)'},
        {keyName: 'totalReadingTime', displayName: '已閱讀時長（小時)(按每本書籍總數)'},
      ],
    },
    en_uk: {
      name: 'authors',
      specification: [
        {keyName: 'name', displayName: 'Name*'},
        {keyName: 'bookName', displayName: 'Book Name'},
        {
          keyName: 'description',
          displayName: 'Author Introduction* (DONOT include information such as \'signed copies\', \'special gift\', \'limited\' or any other marketing information.',
        },
        {keyName: 'publisher', displayName: 'Publisher'},
        {keyName: 'companyName', displayName: 'Publishing Group*'},
        {keyName: 'totalUser', displayName: 'Number of readers'},
        {
          keyName: 'totalReadingTime',
          displayName: 'Reading hours（Hour)',
        },
      ],
    },
  },
}