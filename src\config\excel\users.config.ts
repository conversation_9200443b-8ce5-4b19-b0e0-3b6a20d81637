export const usersConfig = {
  addUsers: {
    zh_HK: {
      name: '批量增加用戶',
      specification: [
        {
          keyName: 'type',
          displayName:
            '類型*（STUDENT或TEACHER，"STUDENT"表示學生，"TEACHER"，表示教職員 ）',
        },
        { keyName: 'name', displayName: '姓名' },
        { keyName: 'serialNo', displayName: '學生學號／教職員編號(如有)' },
        { keyName: 'email', displayName: '登入電郵*' },
        // { keyName: 'familyName', displayName: '姓*' },
        // { keyName: 'givenName', displayName: '名' },
        { keyName: 'className', displayName: '班别(如有)' },
        { keyName: 'gradeName', displayName: '年級(如有)' },
        { keyName: 'password', displayName: '密碼' },
      ],
    },
    en_uk: {
      name: 'Add users in batches',
      specification: [
        {
          keyName: 'type',
          displayName: 'Type *(STUDENT or TEACHER, "STUDENT" for students, "TEACHER" ​​for Stuff)',
        },
        { keyName: 'name', displayName: 'Name' },
        { keyName: 'serialNo', displayName: 'User number' },
        { keyName: 'email', displayName: 'Email' },
        // { keyName: 'familyName', displayName: 'Surname*' },
        // { keyName: 'givenName', displayName: 'Name' },
        { keyName: 'className', displayName: 'Class' },
        { keyName: 'gradeName', displayName: 'Grade' },
        { keyName: 'password', displayName: 'Password' },
      ],
    },
  },
  users: {
    zh_HK: {
      name: '平台總用戶(學生用戶)',
      specification: [
        { keyName: 'schoolName', displayName: '所屬學校' },
        { keyName: 'region', displayName: '學校地區' },
        { keyName: 'serialNo', displayName: '學號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'className', displayName: '班級名稱' },
        { keyName: 'gradeName', displayName: '年级' },
        { keyName: 'familyName', displayName: '姓' },
        { keyName: 'givenName', displayName: '名' },
        { keyName: 'lastLoginAt', displayName: '最後一次登錄時間' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: 'Total platform users (student users)',
      specification: [
        { keyName: 'schoolName', displayName: 'School' },
        { keyName: 'region', displayName: 'School area (Hong Kong/Macao/Other)' },
        { keyName: 'serialNo', displayName: 'Student number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'className', displayName: 'Curriculum stages' },
        { keyName: 'gradeName', displayName: 'Year' },
        { keyName: 'familyName', displayName: 'Surname' },
        { keyName: 'givenName', displayName: 'Name' },
        { keyName: 'lastLoginAt', displayName: 'Last login time' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
  teachers: {
    zh_HK: {
      name: '平台總用戶(教職員用戶)',
      specification: [
        { keyName: 'schoolName', displayName: '學校名稱' },
        { keyName: 'region', displayName: '學校地區' },
        { keyName: 'serialNo', displayName: '教職員編號' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'familyName', displayName: '姓' },
        { keyName: 'givenName', displayName: '名' },
        { keyName: 'lastLoginAt', displayName: '最後一次登錄時間' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: 'Total platform users (teacher users)',
      specification: [
        { keyName: 'schoolName', displayName: 'School' },
        { keyName: 'region', displayName: 'School area (Hong Kong/Macao/Other)' },
        { keyName: 'serialNo', displayName: 'Staff number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'familyName', displayName: 'Surname' },
        { keyName: 'givenName', displayName: 'Name' },
        { keyName: 'lastLoginAt', displayName: 'Last login time' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
  admins: {
    zh_HK: {
      name: '平台管理員數量',
      specification: [
        { keyName: 'name', displayName: '姓名' },
        { keyName: 'phone', displayName: '聯絡電話' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'lastLoginAt', displayName: '最後一次登錄時間' },
        { keyName: 'isRoot', displayName: '超級管理員（是/否）' },
        { keyName: 'publisherName', displayName: '所屬出版社名稱' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: 'Platform Admins',
      specification: [
        { keyName: 'name', displayName: 'Name' },
        { keyName: 'phone', displayName: 'Mobile number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'lastLoginAt', displayName: 'Last log-in time' },
        { keyName: 'isRoot', displayName: 'Super adminstrator (Y/N)' },
        { keyName: 'publisherName', displayName: 'Publisher name' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
  schoolAdmins: {
    zh_HK: {
      name: '學校管理員數量',
      specification: [
        { keyName: 'hasSubVer', displayName: '訂閱版' },
        { keyName: 'hasRefVer', displayName: '參考館' },
        { keyName: 'hasSrVer', displayName: '科學活動室' },
        { keyName: 'hasAIVer', displayName: '文心智友' },
        { keyName: 'name', displayName: '姓名' },
        { keyName: 'phone', displayName: '聯絡電話' },
        { keyName: 'email', displayName: '電郵' },
        { keyName: 'lastLoginAt', displayName: '最後一次登錄時間' },
        { keyName: 'isRoot', displayName: '超級管理員（是/否）' },
        { keyName: 'schoolName', displayName: '所屬學校' },
        { keyName: 'disable', displayName: '是否禁用(是/否)' },
      ],
    },
    en_uk: {
      name: 'School Admins',
      specification: [
        { keyName: 'hasSubVer', displayName: 'SJRC' },
        { keyName: 'hasRefVer', displayName: 'SJRC+' },
        { keyName: 'hasSrVer', displayName: 'PSAR' },
        { keyName: 'hasAIVer', displayName: 'AI Wisereader' },
        { keyName: 'name', displayName: 'Name' },
        { keyName: 'phone', displayName: 'Mobile number' },
        { keyName: 'email', displayName: 'e-mail' },
        { keyName: 'lastLoginAt', displayName: 'Last log-in time' },
        { keyName: 'isRoot', displayName: 'Super adminstrator (Y/N)' },
        { keyName: 'schoolName', displayName: 'School name' },
        { keyName: 'disable', displayName: 'Disable(Yes/No)' },
      ],
    },
  },
}
