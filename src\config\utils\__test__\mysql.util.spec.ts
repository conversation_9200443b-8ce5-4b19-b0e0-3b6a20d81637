import { TypeOrmNamingStrategy } from '../mysq.util'

describe('TypeOrmNamingStrategy', () => {
  const strategy = new TypeOrmNamingStrategy()

  it('tableName', () => {
    expect(strategy.tableName('PaymentAccount', null)).toBe('payment_account')
    expect(strategy.tableName('PaymentAccount', 'accounts')).toBe('accounts')
    expect(strategy.tableName('People', null)).toBe('people')
  })

  it('columnName', () => {
    expect(strategy.columnName('externalId', null, [])).toBe('external_id')
    expect(strategy.columnName('externalId', 'customer_id', [])).toBe('customer_id')
    expect(strategy.columnName('externalId', null, ['stripe'])).toBe('stripe_external_id')
  })
})
