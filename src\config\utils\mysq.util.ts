import { snakeCase } from 'change-case'
import * as R from 'ramda'
import { DefaultNamingStrategy } from 'typeorm'

export class TypeOrmNamingStrategy extends DefaultNamingStrategy {
  tableName(targetName: string, userSpecifiedName: string | undefined): string {
    return userSpecifiedName || snakeCase(targetName)
  }

  columnName(
    propertyName: string,
    customName: string,
    embeddedPrefixes: string[],
  ): string {
    const prefixes = R.ifElse(
      R.isEmpty,
      R<PERSON>always(''),
      R.pipe(R.join('_'), snakeCase, R.concat(R.__, '_')),
    )(embeddedPrefixes)

    const name = snakeCase(customName || propertyName)
    return `${prefixes}${name}`
  }
}
