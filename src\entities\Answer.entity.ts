import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Subject } from './subject.entity'

// export class AnswerItem {
//   @ApiProperty()
//   questionId: number

//   @ApiProperty()
//   selectedOptionIds: number[]
// }

// @Entity('answers')
// export class Answers extends BaseEntity<Answers> {
//   @PrimaryGeneratedColumn()
//   @ApiProperty()
//   id: number

//   @Column()
//   @ApiProperty()
//   score: number

//   @Column()
//   @ApiProperty()
//   time: number

//   @ManyToOne(() => Subject, (subject) => subject.questions)
//   @JoinColumn()
//   subject: Subject

//   @Column({ type: 'json' })
//   answers: AnswerItem
// }
