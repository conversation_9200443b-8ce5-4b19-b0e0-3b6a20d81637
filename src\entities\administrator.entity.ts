import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsBase64,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator'
import { Column, Entity, JoinTable, ManyToMany } from 'typeorm'
import { EAdministratorStatus } from '../enums'
import { BaseUser } from './baseUser.entity'
import { Publisher } from './common/publisher.entity'
import { Role } from './role.entity'

@Entity({ name: 'administrators' })
export class Administrator extends BaseUser<Administrator> {
  @Column({ nullable: true, comment: '密码' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsBase64()
  // @IsString()
  password?: string

  @Column({ nullable: true, comment: '密码加盐' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  salt?: string

  @Column({ nullable: true, comment: 'google unique id' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  googleProviderId?: string

  @Column({ nullable: true, comment: 'google 邮箱' })
  @ApiPropertyOptional({
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  googleEmail?: string

  @Column({ nullable: true, comment: '状态' })
  @ApiPropertyOptional({
    example: EAdministratorStatus.ACTIVE,
    enum: EAdministratorStatus,
  })
  @IsOptional()
  @IsEnum(EAdministratorStatus)
  status?: EAdministratorStatus

  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  @Column({ nullable: true, comment: '最后登陆时间' })
  lastLoginAt?: Date

  @Column({ nullable: true, comment: '是否是root用户' })
  @ApiPropertyOptional({
    description: '是否是root用户',
  })
  @IsBoolean()
  @IsOptional()
  isRoot?: boolean

  @ManyToMany(() => Role, (role) => role.adminUsers)
  @JoinTable({
    name: 'admin_user_roles',
    joinColumn: { name: 'admin_user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles?: Role[]

  @ManyToMany(() => Publisher, (publisher) => publisher.admins, { cascade: true })
  @JoinTable({
    name: 'admin_publishers',
    joinColumn: { name: 'admin_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'publisher_id', referencedColumnName: 'id' },
  })
  publishers: Publisher[]

  constructor(partial: Partial<Administrator>) {
    super(partial)
    Object.assign(this, partial)
  }
}
