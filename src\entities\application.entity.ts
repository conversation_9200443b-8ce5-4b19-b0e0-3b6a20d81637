import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { ApplicationStatus } from '@/enums'

@Entity({ name: 'applications' })
export class Application extends BaseEntity<Application> {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column()
  @IsNumber()
  schoolId: number

  @Column()
  @IsNumber()
  userId: number

  @Column()
  @IsNumber()
  @ApiProperty({
    description: '申请当时剩余时间，展示用',
  })
  ctime: number

  @Column()
  @IsNumber()
  @ApiProperty()
  time: number

  @Column()
  @IsNumber()
  @ApiPropertyOptional()
  messageId?: number

  @Column({ default: 0 })
  @IsNumber()
  @IsOptional()
  @ApiProperty()
  approvalTime: number

  @Column({ default: ApplicationStatus.PENDING })
  @IsEnum(ApplicationStatus)
  @ApiProperty({ enum: ApplicationStatus })
  status: ApplicationStatus

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  @ApiPropertyOptional()
  remarks?: string
}
