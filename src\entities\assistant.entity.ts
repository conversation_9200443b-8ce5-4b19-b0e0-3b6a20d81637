import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'
import { Column, Entity, JoinC<PERSON>umn, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EAssistantStatus } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { AssistantContracts } from './assistantContracts.entity'

@Entity('assistant')
export class Assistant extends BaseEntity<Assistant> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助手ID' })
  @ApiProperty()
  @IsString()
  assistantId: string

  @Column({ name: 'vector_store_id', nullable: false, comment: '向量存储ID' })
  @ApiProperty()
  @IsString()
  vectorStoreId: string

  @Column({ nullable: false, comment: '套餐名', type: 'json' })
  @ApiProperty()
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ name: 'preferred_version', nullable: false, comment: '优先跳转版本' })
  @ApiProperty()
  @IsString()
  preferredVersion: string

  @Column({ nullable: true, default: EAssistantStatus.DRAFT })
  @ApiPropertyOptional({
    description: '套餐状态，默认为DRAFT',
    example: EAssistantStatus.DRAFT,
    enum: EAssistantStatus,
  })
  @IsOptional()
  @IsEnum(EAssistantStatus)
  status?: EAssistantStatus

  @OneToMany(
    () => AssistantContracts,
    (assistantContracts) => assistantContracts.assistant,
  )
  @JoinColumn({
    name: 'assistant_id',
    referencedColumnName: 'assistantId',
  })
  assistantContracts: AssistantContracts[]
}
