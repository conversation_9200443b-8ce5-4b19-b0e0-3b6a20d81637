import { ApiProperty } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'assistant_admin_error_response' })
export class AssistantAdminErrorResponse {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助理ID' })
  @ApiProperty()
  @IsNumber()
  assistantId: string

  @Column({ name: 'file_id', nullable: false, comment: '文件ID' })
  @ApiProperty()
  @IsString()
  fileId: string

  @Column({ nullable: false, comment: 'ISBN' })
  @ApiProperty()
  @IsString()
  isbn: string

  @Column({ name: 'error_msg', nullable: false, comment: '错误消息', type: 'json' })
  @ApiProperty()
  errorMsg: any[]

  @Column({ name: 'created_at', nullable: false, comment: '创建时间', type: 'datetime' })
  @ApiProperty()
  @CreateDateColumn()
  createdAt?: Date
}
