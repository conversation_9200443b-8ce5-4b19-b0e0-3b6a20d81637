import { ApiProperty } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm'

@Entity({ name: 'assistant_client_error_response' })
export class AssistantClientErrorResponse {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助理ID' })
  @ApiProperty()
  @IsNumber()
  assistantId: string

  @Column({ name: 'error_msg', nullable: false, comment: '错误消息', type: 'json' })
  @ApiProperty()
  errorMsg: any[]

  @Column({ name: 'thread_id', nullable: false, comment: '线程ID' })
  @ApiProperty()
  @IsString()
  threadId: string

  @Column({ name: 'run_id', nullable: false, comment: 'runID' })
  @ApiProperty()
  @IsString()
  runId: string

  @Column({ name: 'msg_id', nullable: false, comment: '消息ID' })
  @ApiProperty()
  @IsString()
  msgId: string

  @Column({ name: 'created_at', nullable: false, comment: '创建时间', type: 'datetime' })
  @ApiProperty()
  @CreateDateColumn()
  createdAt?: Date
}
