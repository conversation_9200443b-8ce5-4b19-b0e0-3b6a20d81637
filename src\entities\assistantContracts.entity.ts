import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm'
import { Assistant } from './assistant.entity'
import { School } from './school.entity'

@Entity({ name: 'assistant_contracts' })
export class AssistantContracts {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @ApiProperty()
  @IsString()
  contractNo: string

  @ManyToOne(() => School, (school) => school.assistantContracts)
  school: School

  @ManyToOne(() => Assistant, (assistant) => assistant.assistantContracts)
  @JoinColumn({
    name: 'assistant_id',
    referencedColumnName: 'assistantId',
  })
  assistant: Assistant

  @CreateDateColumn()
  createdAt: Date

  @Column({ nullable: true, type: 'json' })
  @ApiProperty()
  createdBy: any

  @Column({ nullable: true, type: 'json' })
  @ApiProperty()
  updatedBy: any

  @UpdateDateColumn()
  updatedAt: Date
}
