import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsDate,
  IsEnum,
  IsJSON,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EBookVersion } from '@/enums'
import { EOpeaiFileStatus } from '@/enums/assistant.enum'
import { MultiLanguage } from '@/interfaces/multiLanguage.interface'
import { Book } from './book.entity'

@Entity({ name: 'assistant_files' })
export class AssistantFiles extends BaseEntity<AssistantFiles> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ nullable: false, comment: 'ISBN' })
  @ApiProperty()
  @IsString()
  isbn?: string

  @Column({ name: 'book_id', nullable: false, comment: 'book_id' })
  @ApiProperty()
  @IsString()
  bookId?: number

  @Column({ name: 'openai_file_id', nullable: false, comment: 'OpenAI文件ID' })
  @ApiProperty()
  @IsString()
  openaiFileId?: string

  @Column({ nullable: false, comment: '文件状态' })
  @ApiProperty()
  @IsString()
  status?: string

  @Column({ nullable: false, comment: '预览文件' })
  @ApiProperty()
  @IsString()
  awsUrl?: string

  @Column({ nullable: true, comment: '书籍名称', type: 'json' })
  @ApiPropertyOptional({
    description: '书籍名称',
    example: {
      zh_cn: '一个人的薄荷茶',
      zh_HK: '一個人的薄荷茶',
      en_uk: '一個人的薄荷茶',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @Type(() => MultiLanguage)
  @ValidateNested()
  fileName?: MultiLanguage

  @Column({ name: 'file_bytes', nullable: false, comment: '文件大小（字节）' })
  @ApiProperty()
  @IsNumber()
  fileBytes?: number

  @Column({
    default: EBookVersion.SUBSCRIPTION,
    type: 'varchar',
  })
  @ApiProperty({ enum: EBookVersion, isArray: true })
  @IsEnum(EBookVersion, { each: true })
  version: any

  @OneToOne(() => Book, (book) => book.assistantFiles)
  book: Book
}
