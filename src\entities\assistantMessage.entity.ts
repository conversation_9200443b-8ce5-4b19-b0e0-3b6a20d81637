import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document } from 'mongoose'

@Schema({ collection: 'assistant_messages', timestamps: { createdAt: 'created_at' } })
export class AssistantMessages extends Document {
  @Prop({ required: true })
  userId?: string

  @Prop({ required: true })
  assistantId?: string

  @Prop({ required: true })
  threadId?: string

  @Prop({ required: true })
  msgId?: string

  @Prop()
  runId?: string

  @Prop({ required: true })
  role?: string

  @Prop({ type: Object })
  content?: Record<string, any>

  @Prop()
  attachments: Array<any>

  @Prop({ type: Object })
  metadata?: Record<string, any>

  @Prop({ required: true })
  object?: string

  @Prop({ type: Date })
  created_at?: Date
}

export const AssistantMessagesSchema = SchemaFactory.createForClass(AssistantMessages)
