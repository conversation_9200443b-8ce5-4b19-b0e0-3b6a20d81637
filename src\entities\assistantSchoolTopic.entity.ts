import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { MultiLanguage } from '@/interfaces'

@Entity({ name: 'assistant_school_topic' })
export class AssistantSchoolTopic extends BaseEntity<AssistantSchoolTopic> {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column({ nullable: true })
  @ApiProperty()
  schoolId: number

  @Column({ type: 'json' })
  @ApiProperty()
  name: MultiLanguage

  @Column({ nullable: true })
  @ApiProperty()
  answer: string

  @Column({ default: 'all', comment: 'all | teacher | student' })
  @ApiProperty()
  userType: string

  @Column({ type: 'json', comment: '[0]=>所有年级' })
  @ApiProperty()
  gradeIds: number[]

  @Column({ type: 'json', comment: '[0]=>所有班级' })
  @ApiProperty()
  classIds: number[]

  @Column({ default: 'online', comment: 'online | offline' })
  @ApiProperty()
  status: string

  @Column({ type: 'datetime', nullable: true, comment: '上架时间' })
  @ApiProperty()
  onlineAt: Date

  @Column({ type: 'datetime', nullable: true, comment: '下架时间' })
  @ApiProperty()
  offlineAt: Date
}
