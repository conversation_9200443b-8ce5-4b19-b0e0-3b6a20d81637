import { ApiProperty } from '@nestjs/swagger'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { School } from './school.entity'
import { User } from './user.entity'
import { UserClass } from './userClass.entity'

@Entity({ name: 'assistant_school_topic_count' })
export class AssistantSchoolTopicCount {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column()
  @ApiProperty()
  assistantId: string

  @Column()
  @ApiProperty()
  threadId: string

  @Column()
  @ApiProperty()
  topicId: number

  @Column()
  @ApiProperty()
  userId: number

  @Column()
  @ApiProperty()
  userType: string

  @Column({ nullable: true })
  @ApiProperty()
  userClassId: number

  @Column({ nullable: true })
  @ApiProperty()
  gradeId: number

  @Column({ nullable: true })
  @ApiProperty()
  schoolId: number

  @CreateDateColumn()
  createdAt: Date

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User

  @ManyToOne(() => UserClass)
  @JoinColumn({ name: 'user_class_id' })
  userClass: UserClass

  @ManyToOne(() => School)
  @JoinColumn({ name: 'school_id' })
  school: School
}
