import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsOptional, IsString } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EUserType } from '../enums'

@Entity({ name: 'assistant_thread' })
export class AssistantThread {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助理ID' })
  @ApiProperty()
  @IsNumber()
  assistantId: string

  @Column({ name: 'thread_id', nullable: false, comment: '线程ID' })
  @ApiProperty()
  @IsString()
  threadId: string

  @Column({ name: 'user_id', nullable: false, comment: '用户ID' })
  @ApiProperty()
  @IsNumber()
  userId: number

  @Column({ name: 'user_type', nullable: false, comment: '用户类型' })
  @ApiProperty()
  @IsString()
  userType: EUserType

  @Column({ name: 'grade_id', nullable: true, comment: '用户年级ID' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  gradeId?: number

  @Column({ name: 'user_class_id', nullable: true, comment: '用户班级ID' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  userClassId?: number

  @Column({ name: 'school_id', nullable: true, comment: '学校ID' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  schoolId?: number

  @Column({ name: 'created_at', nullable: false, comment: '创建时间', type: 'datetime' })
  @ApiProperty()
  @CreateDateColumn()
  createdAt?: Date
}
