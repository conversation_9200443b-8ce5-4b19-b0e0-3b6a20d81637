import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsString, ValidateNested } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities/base.entity'
import { MultiLanguage } from '@/interfaces'

@Entity('assistant_topic')
export class AssistantTopic extends BaseEntity<AssistantTopic> {
  @PrimaryGeneratedColumn()
  id: number

  @Column('json')
  @ApiProperty({
    description: '适用年级',
    example: ['KINDERGARTEN', 'FIFTH_PRIMARY_GRADE'],
  })
  grades: string[]

  @Column('json')
  @ApiProperty({
    description: 'AI套餐',
    example: ['asst_Dwk9hBsH9S82L70YSc6jJb4C', 'asst_Dwk9hBsH9S82L70YSc6jJb4C'],
  })
  assistants: string[]

  @Column('text')
  @ApiProperty({
    description: '话题状态',
    example: 'oline',
  })
  status: string

  @Column({ type: 'json' })
  @ApiProperty({
    description: '话题',
    example: {
      zh_HK: '老師，可惡童話第六章講什麼',
      en_uk: 'Teacher, what is the sixth chapter of the abominable fairy tale about?',
      zh_cn: '老师，可恶童话第六章讲什么',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ type: 'text' })
  @ApiProperty({
    description: '答案内容',
    example:
      '《可惡童話》第六章名為"罪證"。在這一章中，主角JM獲得了關鍵的證據，開始重新審視之前的案件和角色之間的關係。這些證據揭示了一些隱藏的事實及多名嫌疑人的潛在聯繫，特別是那些與黑暗力量有關的人物。章節中增加了懸疑和張力，使得讀者期待接下來的情節發展，如需進一步的詳細信息或分析，請告訴我！',
  })
  @IsString()
  answer: string

  usageCount?: number
}
