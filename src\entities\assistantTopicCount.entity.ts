import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { School } from './school.entity' // Assuming you have a School entity
import { User } from './user.entity' // Assuming you have a User entity
import { UserClass } from './userClass.entity' // Assuming you have a UserClass entity

@Entity('assistant_topic_count')
export class AssistantTopicCount {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ name: 'thread_id', type: 'varchar', length: 255 })
  threadId: string

  @Column({ name: 'topic_id', type: 'int' })
  topicId: number

  @ManyToOne(() => User, (user) => user.assistantTopicCounts)
  @JoinColumn({ name: 'user_id' })
  user: User

  @Column({ name: 'user_type', type: 'varchar', length: 50 })
  userType: string

  @ManyToOne(() => UserClass, (userClass) => userClass.assistantTopicCounts)
  @JoinColumn({ name: 'user_class_id' })
  userClass?: UserClass

  @Column({ name: 'grade_id', type: 'int' })
  gradeId?: number

  @ManyToOne(() => School, (school) => school.assistantTopicCounts)
  @JoinColumn({ name: 'school_id' })
  school?: School

  @CreateDateColumn({ name: 'created_at', type: 'datetime', precision: 6 })
  createdAt: Date
}
