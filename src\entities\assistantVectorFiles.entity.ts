import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsDate, IsEnum, IsJSON, IsNumber, IsOptional, IsString } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { AssistantFiles } from './assistantFiles.entity'

@Entity({ name: 'assistant_vectorstore_files' })
@Unique(['assistantNumberId', 'openaiFileId'])
export class AssistantVectorstoreFiles extends BaseEntity<AssistantVectorstoreFiles> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_number_id', nullable: false, comment: '套餐主键ID' })
  @ApiProperty()
  @IsNumber()
  assistantNumberId?: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助手ID' })
  @ApiProperty()
  @IsNumber()
  assistantId?: string

  @Column({ name: 'vector_store_id', nullable: false, comment: '向量存储ID' })
  @ApiProperty()
  @IsNumber()
  vectorStoreId?: string

  @Column({ name: 'openai_file_id', nullable: false, comment: 'OpenAI文件ID' })
  @ApiProperty()
  @IsString()
  openaiFileId?: string

  @Column({ nullable: false, comment: '文件状态' })
  @ApiProperty()
  @IsString()
  status?: string

  @ManyToOne(() => AssistantFiles)
  @JoinColumn({ name: 'openai_file_id', referencedColumnName: 'openaiFileId' })
  assistantFile: AssistantFiles
}
