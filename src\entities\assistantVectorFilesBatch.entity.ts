import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsDate, IsEnum, IsJSON, IsNumber, IsOptional, IsString } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'assistant_vectorstore_files_batch' })
export class AssistantVectorstoreFilesBatch extends BaseEntity<AssistantVectorstoreFilesBatch> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ name: 'assistant_id', nullable: false, comment: '助手ID' })
  @ApiProperty()
  @IsNumber()
  assistantId?: string

  @Column({ name: 'vector_store_id', nullable: false, comment: '向量存储ID' })
  @ApiProperty()
  @IsNumber()
  vectorStoreId: string

  @Column({ name: 'batch_id', nullable: false, comment: '批量处理id' })
  @ApiProperty()
  @IsString()
  batchId: string
}
