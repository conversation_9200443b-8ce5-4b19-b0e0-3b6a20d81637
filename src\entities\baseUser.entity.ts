import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsOptional, IsString } from 'class-validator'
import { Column, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '../common/entities'

export class BaseUser<T> extends BaseEntity<BaseUser<T>> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: 'userId', unique: true })
  @ApiProperty({
    example: 'CU22022467653489046144',
  })
  @IsString()
  userId: string

  @Column({ nullable: true, comment: 'profile image' })
  @ApiPropertyOptional({
    example: 'https://www.google.cn/landing/cnexp/google-search.png',
  })
  @IsOptional()
  @IsString()
  profileImage?: string

  @Column({ nullable: true })
  @ApiPropertyOptional({
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  displayName?: string

  @Column({ nullable: true })
  @ApiPropertyOptional({
    example: 'Green',
  })
  @IsOptional()
  @IsString()
  familyName?: string

  @Column({ nullable: true })
  @ApiPropertyOptional({
    example: 'Jim',
  })
  @IsOptional()
  @IsString()
  givenName?: string

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: '区号' })
  prefixNo?: string

  @Column({ nullable: true, comment: '电话' })
  @ApiPropertyOptional({
    example: '+8613997616723',
  })
  @IsOptional()
  @IsString()
  phone?: string

  @Column({ nullable: true, unique: true, comment: '电邮' })
  @ApiPropertyOptional({
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  email?: string

  constructor(partial: Partial<BaseUser<T>>) {
    super(partial)
    Object.assign(this, partial)
  }
}
