import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import {
  BookUserType,
  EBookLanguage,
  EBookVersion,
  ECurrency,
  OnlineOfflineStatus,
} from '../enums'
import { MultiLanguage } from '../interfaces'
import { AssistantFiles } from './assistantFiles.entity'
import { BookList } from './bookList.entity'
import { BookNote } from './bookNote.entity'
import { BookOperateApplication } from './bookOperateApplication.entity'
import { BookReadingTime } from './bookReadingTime.entity'
import { BookStatistics } from './bookStatistic.entity'
import { Chapter } from './chapter.entity'
import { Author } from './common/author.entity'
import { BookLevel } from './common/bookLevel.entity'
import { Category } from './common/category.entity'
import { Label } from './common/label.entity'
import { Publisher } from './common/publisher.entity'
import { ContractBook } from './contractBooks.entity'
import { ContractHistories } from './contractHistories.entity'
import { Homepage } from './homepage.entity'
import { ReadingReflection } from './readingReflection.entity'
import { ReadRecord } from './readRecord.entity'
import { ReferenceBook } from './referenceBook.entity'
import { ReferenceReadingReflection } from './referenceReadingReflection.entity'
import { ReferenceReadRecord } from './referenceReadRecord.entity'
import { SchoolHomepage } from './schoolHomepage.entity'
import { Subject } from './subject.entity'
import { SubjectExtendBook } from './subjectExtendBook'
import { ViewBookDetail } from './viewBookDetail.entity'

@Entity({ name: 'books' })
export class Book extends BaseEntity<Book> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  @IsNumber()
  id: number

  @Column({ nullable: false, comment: '书籍bookId', unique: true })
  @ApiProperty({
    description: '书籍bookId',
    example: 'b_B2D3C435B2F048559CAA2A20E42D57F4',
  })
  @IsString()
  bookId: string

  @Column({ nullable: true, comment: '书籍名称', type: 'json' })
  @ApiPropertyOptional({
    description: '书籍名称',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @Type(() => MultiLanguage)
  @ValidateNested()
  name?: MultiLanguage

  @Column({ nullable: true, comment: '书籍简介', type: 'json' })
  @ApiPropertyOptional({
    description: '书籍简介',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  description?: MultiLanguage

  @Column()
  @IsEnum(EBookLanguage)
  @IsOptional()
  @ApiPropertyOptional({ enum: EBookLanguage })
  language?: EBookLanguage

  @Column({ nullable: true, comment: '书籍章节数' })
  @ApiPropertyOptional({
    description: '章节数',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  chapterCount?: number

  @Column({ nullable: true, comment: '书籍页数' })
  @ApiPropertyOptional({
    description: '页数',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  pageCount?: number

  @Column({ nullable: true, comment: '书籍状态' })
  @ApiPropertyOptional({
    description: '书籍状态',
    example: OnlineOfflineStatus.OFFLINE,
    enum: OnlineOfflineStatus,
  })

  @IsOptional()
  @IsEnum(OnlineOfflineStatus)
  status?: OnlineOfflineStatus

  @Column({ nullable: true, comment: '书籍大小' })
  @ApiPropertyOptional({
    description: '书籍大小',
    example: '10MB',
  })

  @IsOptional()
  @IsString()
  size?: string

  @Column({ comment: 'ISBN', unique: true })
  @ApiProperty()
  @IsString()
  isbn: string

  @Column({ nullable: true, comment: '书籍存储url' })
  @ApiPropertyOptional({
    description: '书籍存储url',
  })
  @IsOptional()
  @IsString()
  url?: string

  @Column({ nullable: true, comment: '书籍封面' })
  @ApiPropertyOptional({
    description: '书籍封面',
  })
  @IsOptional()
  @IsString()
  coverUrl?: string

  @Column({ nullable: true, comment: '是否编辑推荐' })
  @ApiPropertyOptional({
    description: '是否编辑推荐+',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isRecommended?: boolean

  @ApiPropertyOptional({ description: '是否为科学活动室书籍', example: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 1 || value === true)
  @Column({ type: 'tinyint', default: 0 })
  isScienceRoom?: boolean

  @Column({ nullable: true, comment: '出版日期' })
  @IsOptional()
  @ApiPropertyOptional({
    description: '出版日期',
  })
  publishedAt?: Date

  @Column({ nullable: true, comment: '书籍content.opf存储地址' })
  @IsString()
  @ApiPropertyOptional({
    description: '书籍content.opf存储地址',
  })
  @IsOptional()
  opfUrl?: string

  @Column({ nullable: true, type: 'json' })
  @IsOptional()
  @ApiPropertyOptional()
  @IsArray()
  level?: number[]

  @Column({ nullable: true, default: null })
  @IsOptional()
  @ApiPropertyOptional()
  @IsNumber()
  pieces: number

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ type: MultiLanguage })
  @ValidateNested()
  @Type(() => MultiLanguage)
  @IsOptional()
  publisherGroupName?: MultiLanguage

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ type: MultiLanguage })
  @ValidateNested()
  @Type(() => MultiLanguage)
  @IsOptional()
  publisherAddress?: MultiLanguage

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsOptional()
  offlineAt?: Date

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsOptional()
  onlineAt?: Date

  @Column({
    default: EBookVersion.SUBSCRIPTION,
    type: 'varchar',
  })
  @ApiProperty({ enum: EBookVersion, isArray: true })
  @IsEnum(EBookVersion, { each: true })
  version: any

  @Column({ type: 'json', default: null }) // just for searching
  authorIds: number[]

  @Column({ type: 'json', default: null }) // just for searching
  labelIds: number[]

  @Column({ type: 'json', default: null }) // just for searching
  categoryIds: number[]

  @Column({ type: 'json', default: null })
  hiddeSchoolIds: number[]

  @Column({ type: 'json', default: null })
  locations: any

  @Column()
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  price: number

  @Column()
  @ApiPropertyOptional({ enum: ECurrency })
  @IsOptional()
  @IsEnum(ECurrency)
  currency: ECurrency

  @ManyToMany(() => Category, (category) => category.books)
  categories: Category[]

  @ManyToMany(() => Author, (author) => author.books, { cascade: true })
  @JoinTable()
  authors: Author[]

  @ManyToOne(() => Publisher, (publisher) => publisher.books)
  publisher: Publisher

  @OneToMany(() => BookNote, (bookNotes) => bookNotes.book)
  bookNotes: BookNote[]

  @ManyToMany(() => Label, (labels) => labels.books, { cascade: true })
  @JoinTable()
  labels: Label[]

  @ManyToMany(() => BookList, (booklist) => booklist.books, { cascade: true })
  @JoinTable()
  bookLists: BookList[]

  @OneToMany(() => Chapter, (chapters) => chapters.book)
  chapters: Chapter[]

  // @OneToMany(() => Bookmark, (bookmark) => bookmark.book)
  // bookmarks: Bookmark[]

  @ManyToMany(() => Homepage, (homepage) => homepage.books, { cascade: true })
  @JoinTable()
  homepages: Homepage[]

  @ManyToMany(() => SchoolHomepage, (homepage) => homepage.books, { cascade: true })
  @JoinTable()
  schoolHomepages: SchoolHomepage[]

  @ManyToMany(() => BookLevel, (bookLevels) => bookLevels.books, { cascade: true })
  bookLevels: BookLevel[]

  @OneToOne(() => BookReadingTime, (readingTime) => readingTime.book)
  readingTime: BookReadingTime

  @ManyToMany(
    () => BookOperateApplication,
    (bookOperateApplication) => bookOperateApplication.books,
    { cascade: false }
  )
  bookOperateApplications: BookOperateApplication[]

  @Column({ nullable: true, comment: '視頻超連結' })
  @ApiPropertyOptional({
    description: '視頻超連結',
  })
  @IsOptional()
  @IsString()
  hyperlink?: string

  @OneToOne(() => AssistantFiles, (file) => file.book) // 一对一关系
  @JoinColumn({ name: 'id', referencedColumnName: 'bookId' })
  assistantFiles: AssistantFiles

  @OneToMany(() => ReferenceBook, (referenceBook) => referenceBook.book)
  referenceBooks: ReferenceBook[]

  @OneToMany(() => ViewBookDetail, (viewBook) => viewBook.book)
  viewBookDetail: ViewBookDetail[]

  @OneToMany(() => ReferenceReadRecord, (referenceReadRecord) => referenceReadRecord.book)
  referenceReadRecords: ReferenceReadRecord[]

  @OneToOne(() => BookStatistics, (bookStatistic) => bookStatistic.book)
  statistics: BookStatistics

  @OneToMany(() => ContractBook, (contractBook) => contractBook.book)
  contractBooks: ContractBook[]

  @OneToMany(() => ContractHistories, (contractHistories) => contractHistories.book)
  contractHistories: ContractHistories[]

  @OneToMany(() => Subject, (subjects) => subjects.book)
  subjects: Subject[]

  @OneToMany(() => ReadingReflection, (readingReflections) => readingReflections.book)
  readingReflections: ReadingReflection[]

  @OneToMany(() => SubjectExtendBook, (extendBook) => extendBook.book)
  subjectExtendBooks: SubjectExtendBook[]

  @OneToMany(
    () => ReferenceReadingReflection,
    (referenceReadingReflections) => referenceReadingReflections.book
  )
  referenceReadingReflections: ReferenceReadingReflection[]

  @OneToMany(() => ReadRecord, (readRecords) => readRecords.book)
  readRecords: ReadRecord[]

  @Column({ nullable: true, comment: '是否删除', default: false })
  @ApiPropertyOptional({
    description: '是否删除',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isDeleted?: boolean

  // @OneToOne(() => Carousel, (carousel) => carousel.book)
  // carousel: Carousel
  isOnShelf?: boolean // not in db,just for type
  pos?: Record<string, any> // not in db,just for type
  userType?: BookUserType[] // not in db,just for type
  isHidden?: boolean // not in db,just for type
  copiesCount?: number // not in db,just for type
  isShown?: boolean // not in db,just for type
}
