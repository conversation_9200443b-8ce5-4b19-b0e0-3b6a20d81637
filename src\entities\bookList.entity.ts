import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EListStatus } from '../enums'
import { MultiLanguage } from '../interfaces'
import { Book } from './book.entity'
import { Homepage } from './homepage.entity'

@Entity({ name: 'book_lists' })
export class BookList extends BaseEntity<BookList> {
  @ApiPropertyOptional()
  total?: number

  @ApiPropertyOptional()
  pageIndex?: number

  @ApiPropertyOptional()
  pageSize?: number

  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, unique: true })
  @ApiProperty()
  @IsString()
  booklistId: string

  @Column({ nullable: false, comment: '标题', type: 'json' })
  @ApiPropertyOptional({
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, unique: true, comment: '链接地址' })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  url?: string

  @IsOptional()
  @ApiPropertyOptional({
    description: '上架时间',
  })
  @Type(() => Date)
  @Column({ nullable: true, comment: '上架时间' })
  onlineAt?: Date

  @IsOptional()
  @ApiPropertyOptional({
    description: '下架时间',
  })
  @Type(() => Date)
  @Column({ nullable: true, comment: '下架时间' })
  offlineAt?: Date

  @Column({ default: EListStatus.PENDING, comment: '状态' })
  @ApiPropertyOptional({
    example: EListStatus.OFFLINE,
    enum: EListStatus,
  })
  @IsEnum(EListStatus)
  @IsOptional()
  status?: EListStatus

  @Column({ nullable: true, comment: 'app和手機網站圖片' })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  appImage?: string

  @Column({ nullable: true, comment: '桌面版網站圖片' })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  webImage?: string

  @Column({ nullable: true, comment: '簡介', type: 'json' })
  @ApiPropertyOptional({ type: MultiLanguage })
  @ValidateNested()
  @IsOptional()
  @Type(() => MultiLanguage)
  description?: MultiLanguage

  @ManyToMany(() => Book, (book) => book.bookLists)
  books: Book[]

  @OneToOne(() => Homepage, (homepage) => homepage.booklist)
  @JoinColumn()
  homepage: Homepage
}
