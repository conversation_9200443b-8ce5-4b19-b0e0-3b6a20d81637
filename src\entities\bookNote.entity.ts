import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Book } from './book.entity'

@Entity({ name: 'book_notes' })
export class BookNote extends BaseEntity<BookNote> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ comment: '用戶userId' })
  @ApiProperty({
    description: '用戶userId',
  })
  @IsString()
  userId: number

  // @Column({ type: 'enum', enum: NoteType })
  // @ApiProperty({ enum: NoteType })
  // @IsEnum(NoteType)
  // noteType: NoteType

  // @Column({ nullable: true, comment: '原始內容' })
  // @ApiPropertyOptional({
  //   description: '原始內容',
  // })
  // @IsString()
  // @IsOptional()
  // originalText?: string

  // @Column({ nullable: true, comment: '筆記內容' })
  // @ApiPropertyOptional({
  //   description: '筆記內容',
  // })
  // @IsString()
  // @IsOptional()
  // noteText?: string

  // @Column({ nullable: true, comment: '筆記顏色' })
  // @ApiProperty({
  //   description: '筆記顏色',
  // })
  // @IsString()
  // @IsOptional()
  // noteColor?: string

  // @Column({ nullable: true, comment: '起始坐標', type: 'json' })
  // @ApiPropertyOptional({
  //   description: '起始坐標',
  //   // example: 100,
  // })
  // @IsNumber()
  // @IsOptional()
  // startLocation?: any // todo

  // @Column({ nullable: true, comment: '結束坐標', type: 'json' })
  // @ApiPropertyOptional({
  //   description: '結束坐標',
  //   // example: 1000,
  // })
  // @IsNumber()
  // @IsOptional()
  // endLocation?: any

  // @Column({ nullable: true, comment: 'epub定位标识' })
  // @ApiPropertyOptional({
  //   description: 'epub定位标识',
  // })
  // @IsString()
  // @IsOptional()
  // epubCfi?: string

  // @Column({ nullable: true, comment: '起始页，pdf用' })
  // @ApiPropertyOptional({
  //   description: '起始页，pdf用',
  //   example: 100,
  // })
  // @IsNumber()
  // @IsOptional()
  // startPage?: number

  // @ManyToOne(() => Chapter, (chapter) => chapter.bookNotes)
  // chapter: Chapter

  @Column({ type: 'json' })
  // @IsObject()
  @ApiProperty()
  note: Record<string, any>

  @ManyToOne(() => Book, (book) => book.bookNotes)
  book: Book
}
