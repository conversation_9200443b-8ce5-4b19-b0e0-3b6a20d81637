import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsOptional } from 'class-validator'
import {
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common'
import { ApplicationStatus, BookOperationType } from '@/enums'
import { Book } from './book.entity'
import { Publisher } from './common/publisher.entity'

@Entity({ name: 'book_operate_applications' })
export class BookOperateApplication extends BaseEntity<BookOperateApplication> {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column({ default: BookOperationType.BATCH_DELETE, comment: '操作类型' })
  @ApiPropertyOptional({
    description: '操作类型',
    enum: BookOperationType,
    example: BookOperationType.BATCH_UNPUBLISH,
  })
  @IsOptional()
  @IsEnum(BookOperationType)
  type?: BookOperationType

  @Column({ type: String, comment: '处理状态', default: ApplicationStatus.PENDING })
  @ApiPropertyOptional({
    description: '处理状态',
    example: ApplicationStatus.PENDING,
    enum: ApplicationStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({
    type: Object,
  })
  @IsOptional()
  metadata?: Record<string, any>

  @ManyToOne(() => Publisher, (publisher) => publisher.bookOperateApplications, {
    cascade: false,
  })
  @ApiPropertyOptional({
    description: '出版社',
    type: () => Publisher,
  })
  @IsOptional()
  @Type(() => Publisher)
  publisher: Publisher

  @ManyToMany(() => Book, (book) => book.bookOperateApplications, {
    cascade: false,
  })
  @JoinTable({
    name: 'book_operate_application_relations',
    joinColumn: { name: 'book_operate_application_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'book_id', referencedColumnName: 'id' },
  })
  @ApiPropertyOptional({
    description: '操作书籍',
    type: () => [Book],
  })
  @IsOptional()
  @Type(() => Publisher)
  books: Book[]
}
