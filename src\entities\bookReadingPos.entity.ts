import { ApiProperty } from '@nestjs/swagger'
import { IsNumber } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'book_reading_pos' })
@Unique(['userId', 'bookId'])
export class BookReadingPos extends BaseEntity<BookReadingPos> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @ApiProperty()
  userId: number

  @Column({ type: 'json' })
  @ApiProperty()
  pos: Record<string, any>

  @Column({ default: null })
  platform?: string

  @Column()
  @IsNumber()
  @ApiProperty()
  bookId: number
}
