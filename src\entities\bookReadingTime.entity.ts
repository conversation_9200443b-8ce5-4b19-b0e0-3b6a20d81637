import { ApiProperty } from '@nestjs/swagger'
import { Column, Entity, JoinColumn, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { Book } from './book.entity'

@Entity({ name: 'book_reading_time' })
export class BookReadingTime extends BaseEntity<BookReadingTime> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ default: 0 })
  count: number

  @Column({ default: 0 })
  readingTime: number

  @Column()
  schoolId: number

  @Column({ type: 'json' })
  userIds: number[]

  @OneToOne(() => Book, (book) => book.readingTime)
  @JoinColumn()
  book: Book
}
