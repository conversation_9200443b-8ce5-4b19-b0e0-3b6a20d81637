import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { Book } from './book.entity'

@Entity('book_statistics')
export class BookStatistics {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  readingTime: number

  @Column()
  copiesCount: number

  @OneToOne(() => Book, (book) => book.statistics)
  @JoinColumn()
  book: Book
}
