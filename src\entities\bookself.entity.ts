import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm'
import { BaseEntity } from '@/common'
import { EBookVersion } from '@/enums'

@Entity({ name: 'bookshelves' })
@Unique(['bookId', 'userId'])
export class Bookshelf extends BaseEntity<Bookshelf> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ comment: '書籍id' })
  @ApiProperty({
    description: '書籍id',
  })
  @IsNumber()
  bookId: number

  @Column({ comment: '用戶userId' })
  @ApiProperty({
    description: '用戶userId',
  })
  @IsNumber()
  userId: number

  @Column({ default: EBookVersion.SUBSCRIPTION })
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version: EBookVersion
}
