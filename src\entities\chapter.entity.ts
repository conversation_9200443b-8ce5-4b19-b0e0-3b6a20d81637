import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsOptional, IsString } from 'class-validator'
import { Column, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Book } from './book.entity'

@Entity({ name: 'chapters' })
export class Chapter extends BaseEntity<Chapter> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, comment: '章节名称' })
  @ApiPropertyOptional({
    description: '章节名称',
    example: '第一章',
  })
  @IsString()
  @IsOptional()
  name?: string

  @Column({ nullable: true, comment: '章节名称' })
  @ApiProperty({
    description: '章节存储url',
  })
  @IsString()
  @IsOptional()
  url?: string

  @Column({ nullable: true, comment: '章节名称' })
  @ApiPropertyOptional({
    description: '章节序号',
    example: 100,
  })
  @IsNumber()
  @IsOptional()
  sort?: number

  @Column({ nullable: true, comment: 'pdf起始页' })
  @ApiPropertyOptional({
    description: 'pdf起始页',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  startPage?: number

  @Column({ nullable: true, comment: 'pdf结束页' })
  @ApiPropertyOptional({
    description: 'pdf结束页',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  endPage?: number

  @ManyToOne(() => Chapter, (chapter) => chapter.children)
  parent?: Chapter

  @OneToMany(() => Chapter, (chapter) => chapter.parent)
  children?: Chapter[]

  // @OneToMany(() => Bookmark, (bookmarks) => bookmarks.chapter)
  // bookmarks: Bookmark[]

  // @OneToMany(() => BookNote, (bookNotes) => bookNotes.chapter)
  // bookNotes: BookNote

  @ManyToOne(() => Book, (book) => book.chapters)
  book: Book
}
