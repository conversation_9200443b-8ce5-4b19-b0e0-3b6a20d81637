import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsOptional, IsString, ValidateNested } from 'class-validator'
import { Column, Entity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { MultiLanguage } from '@/interfaces'
import { Book } from '../book.entity'

@Entity({ name: 'authors' }) // todo how about the field of total books
export class Author extends BaseEntity<Author> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '作者authorId', unique: true })
  @ApiProperty({
    description: '作者authorId',
    example: 'au_B2D3C435B2F048559CAA2A20E42D57F4',
  })
  @IsString()
  authorId: string

  @Column({ nullable: true, comment: '作者名', type: 'json' })
  @ApiPropertyOptional({
    description: '作者名',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: () => MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, comment: '作者简介', type: 'json' })
  @ApiPropertyOptional({
    description: '作者名',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  description?: MultiLanguage

  @Column({ nullable: true, comment: '作者头像' })
  @ApiPropertyOptional({
    description: '作者头像',
  })
  @IsOptional()
  profileImage?: string

  @ManyToMany(() => Book, (books) => books.authors)
  books: Book[]
}
