import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsOptional, ValidateNested } from 'class-validator'
import { Column, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { MultiLanguage } from '@/interfaces'
import { Book } from '../book.entity'

@Entity({ name: 'book_levels' })
export class BookLevel extends BaseEntity<BookLevel> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ type: 'json' })
  @ApiPropertyOptional({
    type: MultiLanguage,
  })
  @IsOptional()
  @Type(() => MultiLanguage)
  @ValidateNested()
  name?: MultiLanguage

  @Column({ default: false })
  @ApiProperty()
  isFixed: boolean

  @Column({ default: 0 })
  sequence?: number

  @ManyToMany(() => Book, (books) => books.bookLevels)
  @JoinTable({
    name: 'book_levels_books',
    joinColumn: { name: 'book_levels_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'book_id', referencedColumnName: 'id' },
  })
  books: Book[]
}
