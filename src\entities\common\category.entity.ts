import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsOptional, IsString, ValidateNested } from 'class-validator'
import { Column, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { MultiLanguage } from '@/interfaces'
import { Book } from '../book.entity'

@Entity({ name: 'categories' })
export class Category extends BaseEntity<Category> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '书籍分类Id', unique: true })
  @ApiProperty()
  @IsString()
  categoryId: string

  // @ManyToOne((type) => Category, (category) => category.children)
  // parent?: Category

  // @OneToMany((type) => Category, (category) => category.parent)
  // children?: Category[]

  @Column({ nullable: true, comment: '分类名称', type: 'json' })
  @ApiPropertyOptional({
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @ManyToMany(() => Book, (book) => book.categories)
  @JoinTable({
    name: 'categories_books',
    joinColumn: { name: 'category_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'book_id', referencedColumnName: 'id' },
  })
  books: Book[]

  constructor(partial: Partial<Category>) {
    super(partial)
    Object.assign(this, partial)
  }
}
