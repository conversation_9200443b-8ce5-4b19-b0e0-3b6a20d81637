import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EGrade } from '@/enums'

@Entity({ name: 'grades' })
@Unique(['grade', 'schoolId'])
export class Grade extends BaseEntity<Grade> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({ description: '平台设定的年级' })
  @Column()
  // @IsOptional()
  @IsEnum(EGrade)
  gradeCode: EGrade

  @Column()
  @IsString()
  @IsOptional()
  @ApiProperty({ description: '学校自定义的年级名称' })
  grade: string

  @Column()
  @ApiProperty()
  @IsNumber()
  sequence: number

  @Column()
  @IsNumber()
  schoolId: number
}
