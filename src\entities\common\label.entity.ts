import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator'
import { Column, Entity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { LabelType } from '@/enums/label.enum'
import { MultiLanguage } from '@/interfaces'
import { Book } from '../book.entity'

@Entity({ name: 'labels' })
export class Label extends BaseEntity<Label> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '书籍标签categoryId', unique: true })
  @ApiProperty()
  @IsString()
  labelId: string

  @Column({ nullable: false, comment: '标签名称', type: 'json' })
  @ApiPropertyOptional({
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ default: LabelType.CATEGORY })
  @IsEnum(LabelType)
  @ApiProperty({ enum: LabelType })
  type: LabelType

  @ManyToMany(() => Book, (books) => books.labels)
  books: Book[]

  constructor(partial: Partial<Label>) {
    super(partial)
    Object.assign(this, partial)
  }
}
