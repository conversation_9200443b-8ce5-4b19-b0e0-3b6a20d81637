import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { Column, Entity, ManyToMany, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EStatus } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { Administrator } from '../administrator.entity'
import { Book } from '../book.entity'
import { BookOperateApplication } from '../bookOperateApplication.entity'

@Entity({ name: 'publishers' })
export class Publisher extends BaseEntity<Publisher> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '出版社Id', unique: true })
  @ApiProperty({
    description: '出版社Id',
  })
  @IsString()
  publisherId: string

  @Column({ nullable: true, comment: '出版社名称', type: 'json' })
  @ApiPropertyOptional({
    description: '出版社名称',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  // @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ type: MultiLanguage })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  publisherGroupName?: MultiLanguage

  @Column({ nullable: true, comment: '出版商简介', type: 'json' })
  @ApiPropertyOptional({
    description: '出版社描述',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  description?: MultiLanguage

  @Column({ nullable: true, comment: ' 出版商logo' })
  @ApiPropertyOptional({
    description: 'logo图',
  })
  @IsOptional()
  @IsString()
  logoImage?: string

  // @Column({ nullable: true, comment: '出版商合作到期日期' })
  // @IsOptional()
  // @IsDate()
  // @ApiPropertyOptional({
  //   description: '合作到期日期',
  // })
  // expiredAt?: Date

  @Column({ default: EStatus.ONLINE, comment: '出版社状态' })
  @ApiPropertyOptional({
    description: '出版社状态',
    enum: EStatus,
  })
  @IsOptional()
  @IsEnum(EStatus)
  status?: EStatus

  @Column({ nullable: true, comment: '聯絡電郵' })
  @ApiPropertyOptional({
    description: '聯絡電郵',
  })
  @IsEmail()
  @IsOptional()
  email?: string

  @Column({ nullable: true, comment: '聯絡人' })
  @ApiPropertyOptional({
    description: '聯絡人',
  })
  @IsString()
  @IsOptional()
  contactUserName?: string

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  address?: string

  @Column({ nullable: true, comment: '是否删除', default: false })
  @ApiPropertyOptional({
    description: '是否删除',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isDeleted?: boolean

  @OneToMany(() => Book, (books) => books.publisher)
  books: Book[]

  @OneToMany(
    () => BookOperateApplication,
    (bookOperateApplication) => bookOperateApplication.publisher,
    { cascade: false }
  )
  bookOperateApplications: BookOperateApplication[]

  @ManyToMany(() => Administrator, (admin) => admin.publishers)
  admins: Administrator[]
}
