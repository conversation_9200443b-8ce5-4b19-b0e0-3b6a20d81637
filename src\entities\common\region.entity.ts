import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { MultiLanguage } from '@/interfaces'

@Entity({ name: 'regions' })
export class Region extends BaseEntity<Region> {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ type: 'json' })
  @IsString()
  @ApiProperty()
  name: MultiLanguage

  @Column({ nullable: false })
  @IsString()
  @ApiProperty()
  prefixNo: string

  @Column({ default: false })
  frequentlyUsed: boolean
}
