import { ApiPropertyOptional } from '@nestjs/swagger'
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common'
import { EContractStatus } from '@/enums'
import { ContractBook } from './contractBooks.entity'
import { ContractHistories } from './contractHistories.entity'
import { School } from './school.entity'

@Entity('contracts')
export class Contract extends BaseEntity<Contract> {
  @ApiPropertyOptional({
    description: 'Auto Increment ID',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @PrimaryGeneratedColumn()
  id?: number

  @Column({ unique: true })
  @ApiPropertyOptional({
    description: '合同编号',
    example: 'abc123',
  })
  @IsOptional()
  @IsString()
  serialNumber: string

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '发布前书籍数量',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  bookCountBeforeUpdated?: number

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '发布前副本数量',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  copiesCountBeforeUpdated?: number

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '当次合约增量书籍数量',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  bookCount?: number

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '当次合约增量副本数量',
    example: 50,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  copiesCount?: number

  @Column({ nullable: true, default: EContractStatus.DRAFT })
  @ApiPropertyOptional({
    description: '合约状态，默认为DRAFT',
    example: EContractStatus.DRAFT,
    enum: EContractStatus,
  })
  @IsOptional()
  @IsEnum(EContractStatus)
  status?: EContractStatus

  @ApiPropertyOptional({
    type: () => [ContractBook],
  })
  @IsOptional()
  @OneToMany(() => ContractBook, (contractBook) => contractBook.contract, {
    eager: false,
    cascade: ['insert', 'update'],
  })
  contractBooks?: ContractBook[]

  @OneToMany(() => ContractHistories, (contractHistories) => contractHistories.contract, {
    eager: false,
  })
  contractHistories?: ContractHistories[]

  @ApiPropertyOptional({
    type: () => School,
  })
  @IsOptional()
  @ManyToOne(() => School, (school) => school.contracts, {
    eager: false,
  })
  @JoinColumn()
  school: School

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Published time',
    example: '2023-07-09T07:48:08.000Z',
  })
  @IsOptional()
  @IsDateString()
  publishedAt?: Date

  @ApiPropertyOptional()
  @IsOptional()
  @Column({ nullable: true, type: 'json', default: null })
  publishedBy?: Record<string, any>

  constructor(partial?: Partial<Contract>) {
    super(partial)
    Object.assign(this, partial)
  }
}
