import { ApiPropertyOptional } from '@nestjs/swagger'
import { Is<PERSON><PERSON>ber, IsOptional, Min } from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm'
import { Book } from './book.entity'
import { Contract } from './contract.entity'

@Entity('contract_books')
@Unique(['book'])
export class ContractBook {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '副本数量',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  copiesCount: number

  @ApiPropertyOptional({
    type: () => Book,
  })
  @IsOptional()
  @ManyToOne(() => Book, (book) => book.contractBooks)
  @JoinColumn()
  book?: Book

  @ApiPropertyOptional({
    type: () => Contract,
  })
  @IsOptional()
  @ManyToOne(() => Contract, (contract) => contract.contractBooks, {
    eager: false,
    orphanedRowAction: 'delete',
  })
  @JoinColumn()
  contract?: Contract

  constructor(partial?: Partial<ContractBook>) {
    Object.assign(this, partial)
  }
}
