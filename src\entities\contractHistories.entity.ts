import {
  <PERSON><PERSON><PERSON>,
  C<PERSON>Date<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { Book } from './book.entity'
import { Contract } from './contract.entity'
import { School } from './school.entity'

@Entity('contract_histories')
export class ContractHistories {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  serialNumber: string

  @ManyToOne(() => Book, (book) => book.contractHistories)
  @JoinColumn()
  book?: Book

  @Column({ nullable: false, default: 0 })
  copiesCount: number

  @Column({ nullable: false, default: 0 })
  bookCount: number

  @ManyToOne(() => School, (school) => school.contractHistories, {
    eager: false,
  })
  @JoinColumn()
  school: School

  @ManyToOne(() => Contract, (contract) => contract.contractHistories, {
    eager: false,
  })
  @JoinColumn()
  contract: Contract

  @CreateDateColumn()
  createdAt?: Date

  @Column({ nullable: true, type: 'json', default: null })
  createdBy: Record<string, any>
}
