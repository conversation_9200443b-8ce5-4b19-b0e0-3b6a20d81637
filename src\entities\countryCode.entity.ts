import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsBoolean, IsOptional, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'country_codes' })
export class CountryCode extends BaseEntity<CountryCode> {
  @ApiProperty({
    example: 1,
  })
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, comment: '國家名稱' })
  @ApiPropertyOptional()
  @IsOptional()
  countryName?: string

  @Column({ nullable: true, comment: 'alpha-2 code' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  code?: string

  @Column({ nullable: true, comment: '是否經常使用', default: false })
  @ApiPropertyOptional({
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isOftenUsed?: boolean
}
