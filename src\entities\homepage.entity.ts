import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, IsEnum, IsNumber, IsOptional, ValidateNested } from 'class-validator'
import { Column, Entity, ManyToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EListStatus, EListStyle } from '../enums'
import { MultiLanguage } from '../interfaces'
import { Book } from './book.entity'
import { BookList } from './bookList.entity'

@Entity({ name: 'homepage' })
export class Homepage extends BaseEntity<Homepage> {
  @ApiProperty()
  @IsOptional()
  total?: any

  @ApiProperty()
  @IsOptional()
  pageIndex?: any

  @ApiProperty()
  @IsOptional()
  pageSize?: any

  @ApiProperty()
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, comment: '标题', type: 'json' })
  @ApiPropertyOptional({
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, comment: 'app样式' })
  @ApiPropertyOptional({
    description: 'app样式',
    enum: EListStyle,
    example: EListStyle.VERTICAL_DOUBLE,
  })
  @IsEnum(EListStyle)
  @IsOptional()
  appStyle?: EListStyle

  @Column({ nullable: true, comment: 'web样式' })
  @ApiPropertyOptional({
    description: 'web样式',
    enum: EListStyle,
    example: EListStyle.VERTICAL_DOUBLE,
  })
  @IsEnum(EListStyle)
  @IsOptional()
  webStyle?: EListStyle

  @IsOptional()
  @ApiPropertyOptional({
    description: '上架时间',
  })
  @Column({ nullable: true, comment: '上架时间' })
  onlineAt?: Date

  @IsOptional()
  @ApiPropertyOptional({
    description: '下架时间',
  })
  @Column({ nullable: true, comment: '下架时间' })
  offlineAt?: Date

  @Column({ default: EListStatus.PENDING, comment: '状态' })
  @ApiPropertyOptional({
    example: EListStatus.OFFLINE,
    enum: EListStatus,
  })
  @IsEnum(EListStatus)
  @IsOptional()
  status?: EListStatus

  @Column({ nullable: true, comment: '排序（升序）' })
  @ApiPropertyOptional({
    description: '排序（升序）',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  sort?: number

  @Column({ default: false, comment: '是否是固定模块' })
  @ApiPropertyOptional({
    description: '是否是固定模块',
  })
  @IsBoolean()
  @IsOptional()
  isPermanent?: boolean

  @Column({ default: false, comment: '是否是固定排序' })
  @ApiPropertyOptional({
    description: '是否是固定排序',
  })
  @IsBoolean()
  @IsOptional()
  isFixedSort?: boolean

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: '今日推荐的子推荐栏位',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number

  @ManyToMany(() => Book, (books) => books.homepages)
  books?: Book[]

  @OneToOne(() => BookList, (booklist) => booklist.homepage)
  booklist?: BookList
}
