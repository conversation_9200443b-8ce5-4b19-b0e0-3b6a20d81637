import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'hot_search_words' })
export class HotSearchWord extends BaseEntity<HotSearchWord> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '搜索关键词', unique: true })
  @ApiProperty()
  @IsString()
  keyword: string

  @Column({ nullable: false, comment: '搜索次数', default: 1 })
  @ApiPropertyOptional()
  @IsNumber()
  searchCount: number
}
