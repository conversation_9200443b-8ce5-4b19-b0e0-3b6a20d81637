import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EUserType } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { School } from './school.entity'

@Entity({ name: 'messages' })
export class Message extends BaseEntity<Message> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ type: 'json' })
  @ApiPropertyOptional({
    description: '标题',
    type: () => MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  title?: MultiLanguage

  @Column({ type: 'json' })
  @ApiPropertyOptional({
    description: '消息内容',
    type: () => MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  message?: MultiLanguage

  @ManyToOne(() => School, (school) => school.messages, {
    eager: false,
  })
  school?: School

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  toUserIds?: number[]

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  readUserIds?: number[]

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  type?: string

  @Column({ nullable: true })
  @IsEnum(EUserType)
  @IsOptional()
  @ApiPropertyOptional({ enum: EUserType })
  userType?: EUserType

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsArray()
  // @ArrayMinSize(1)
  @ApiPropertyOptional({ type: [Number] })
  gradeIds?: number[]

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsArray()
  // @ArrayMinSize(1)
  @ApiPropertyOptional({ type: [Number] })
  classIds?: number[]

  @Column()
  @IsNumber()
  @ApiPropertyOptional()
  applicationId?: number

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional()
  meta?: Record<string, any>
}
