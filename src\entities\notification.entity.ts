import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, IsDate, IsEnum, IsNumber, IsOptional } from 'class-validator'
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { AuthSchema } from '@/common'

@Entity({ name: 'notifications' })
export class Notification {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  @IsNumber()
  id: number

  @ApiPropertyOptional({
    example: AuthSchema.SCHOOL_ADMIN,
    enum: AuthSchema,
  })
  @IsOptional()
  @Column({
    nullable: true,
    comment: 'notification type',
    type: 'varchar',
  })
  @IsEnum(AuthSchema)
  authSchema?: AuthSchema

  @ApiPropertyOptional({
    type: () => Number,
    example: 12,
  })
  @IsOptional()
  @Column({
    nullable: true,
    comment: 'user id',
  })
  userId?: number

  @ApiPropertyOptional({
    description: '通知参数',
  })
  @IsOptional()
  @Column({
    nullable: true,
    type: 'json',
    comment: 'data',
  })
  title?: Record<string, any>

  @ApiPropertyOptional({
    description: '通知参数',
  })
  @IsOptional()
  @Column({
    nullable: true,
    type: 'json',
    comment: 'data',
  })
  content?: Record<string, any>

  @Column({ nullable: true, comment: '已读/未读', default: false })
  @ApiPropertyOptional({
    description: '已读/未读',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isRead?: boolean

  @CreateDateColumn()
  @IsDate()
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Date)
  createdAt?: Date

  constructor(partial?: Partial<Notification>) {
    Object.assign(this, partial)
  }
}
