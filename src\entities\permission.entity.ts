import { IsString } from 'class-validator'
import {
  Column,
  <PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm'
import { BaseEntity } from '@/common'
import { Resource } from './resource.entity'
import { Role } from './role.entity'

@Entity({ name: 'permissions' })
@Unique(['code'])
export class Permission extends BaseEntity<Permission> {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @IsString()
  name: string

  @Column()
  @IsString()
  code: string

  @Column({ nullable: true })
  @IsString()
  description: string

  @ManyToMany(() => Role, (role) => role.permissions)
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'permission_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles?: Role[]

  @ManyToOne(() => Resource, (resource) => resource.permissions)
  @JoinColumn()
  resource: Resource
}
