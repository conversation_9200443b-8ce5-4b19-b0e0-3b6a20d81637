import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator'
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { EAbility, EAnswerType, EQuestionType } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { Subject } from './subject.entity'

export class Options {
  @ApiPropertyOptional()
  @IsInt()
  @IsOptional()
  id: number

  @ApiProperty({
    description: '問題答案',
    example: {
      zh_HK: '健康的生活方式',
      en_uk: '健康的生活方式',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: '答案图片',
    example: 'https://img.iread.com.tw/theme/1.png',
  })
  @IsString()
  @IsOptional()
  image?: string

  @ApiProperty()
  @IsBoolean()
  isCorrect: boolean

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  reason?: string
}

export class QuestionAbility {
  @ApiProperty({ enum: EAbility })
  @IsEnum(EAbility)
  ability: EAbility

  @ApiProperty()
  @IsInt()
  score: number
}

@Entity({ name: 'questions' })
export class Question extends BaseEntity<Question> {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({
    description: '題目問題',
    example: {
      zh_HK: '健康的生活方式',
      en_uk: '健康的生活方式',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ default: EQuestionType.MULTIPLE_CHOICE_QUESTIONS, type: 'varchar' })
  @IsEnum(EQuestionType)
  @ApiPropertyOptional({ description: '类型', enum: EQuestionType })
  @IsOptional()
  questionType: EQuestionType

  @Column({ default: EAnswerType.SINGLE_CHOICE, type: 'varchar' })
  @IsEnum(EAnswerType)
  @ApiPropertyOptional({ description: '答案选项', enum: EAnswerType })
  @ValidateIf((obj) => obj.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS)
  @IsOptional()
  answerType: EAnswerType

  @Column({ type: 'json' })
  @ApiPropertyOptional({
    description: '考察能力',
    type: () => [QuestionAbility],
    isArray: true,
  })
  @ValidateIf((obj) => obj.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS)
  @ValidateNested()
  ability: QuestionAbility[]

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: '題目图片',
    example: 'https://img.iread.com.tw/theme/1.png',
  })
  @IsString()
  @IsOptional()
  image: string

  @Column({ default: 1 })
  @ApiPropertyOptional({ description: '題目顺序' })
  @IsOptional()
  @IsInt()
  sequence: number

  @ApiPropertyOptional({ description: '題目详细描述' })
  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  description?: string

  @Column({ type: 'json', default: [] })
  @ApiPropertyOptional({ type: [Options] })
  @Type(() => Options)
  @ValidateNested()
  @IsArray()
  @ArrayMinSize(1)
  @ValidateIf((obj) => obj.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS)
  options: Options[]

  @ManyToOne(() => Subject, (subject) => subject.questions)
  @JoinColumn()
  subject: Subject
}
