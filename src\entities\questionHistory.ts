import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { UserAnswer } from './userAnswer.entity'

@Entity({ name: 'questions_history' })
export class QuestionHistory {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  subjectId: number

  @Column({ type: 'json', default: [] })
  questions: any[]

  @CreateDateColumn()
  createdAt?: number

  @OneToMany(() => UserAnswer, (userAnswer) => userAnswer.user)
  userAnswers: UserAnswer[]
}
