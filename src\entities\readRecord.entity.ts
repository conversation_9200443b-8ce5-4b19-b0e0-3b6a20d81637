import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { EUserType } from '@/enums'
import { Book } from './book.entity'
import { User } from './user.entity'

@Entity()
export class ReadRecord extends BaseEntity<ReadRecord> {
  @Column()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  userType: EUserType

  @Column()
  bookId: number

  @Column()
  userId: number

  @Column({ type: 'json' })
  authorId: number[]

  @Column()
  schoolId: number

  @Column({ nullable: true })
  class?: number

  @Column()
  publisherId: number

  @Column({ nullable: true })
  grade?: number

  @Column({ default: 0 })
  readingTime: number

  @Column({ nullable: true })
  location: string

  @Column({ nullable: true })
  chapterLocation: string

  @Column()
  region: string

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>

  @ManyToOne(() => Book, (book) => book.readRecords)
  @JoinColumn({ name: 'book_id' })
  book: Book
}
