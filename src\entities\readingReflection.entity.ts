import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm'
import { Book } from './book.entity'
import { School } from './school.entity'
import { User } from './user.entity'
import { UserClass } from './userClass.entity'

@Entity('reading_reflection')
export class ReadingReflection {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ name: 'grade_id', nullable: true })
  gradeId: number

  @ManyToOne(() => Book, (book) => book.readingReflections)
  @JoinColumn({ name: 'book_id' })
  book: Book

  @ManyToOne(() => User, (user) => user.readingReflections)
  @JoinColumn({ name: 'user_id' })
  user: User

  @ManyToOne(() => UserClass, (userClass) => userClass.readingReflections)
  @JoinColumn({ name: 'user_class_id' })
  userClass: UserClass

  @ManyToOne(() => School, (school) => school.readingReflections)
  @JoinColumn({ name: 'school_id' })
  school: School

  @Column({ name: 'user_type', type: 'varchar', length: 255 })
  userType: string

  @Column({ name: 'review_state', type: 'int', default: 0, nullable: true })
  reviewState: number

  @Column({ name: 'flag_state', type: 'int', default: 0, nullable: true })
  flagState: number

  @Column({ name: 'audio_time', type: 'int', nullable: true })
  audioTime: number

  @Column({ name: 'audio_url', type: 'varchar', length: 255, nullable: true })
  audioUrl: string

  @Column({ type: 'text', nullable: true })
  content: string

  @CreateDateColumn({ name: 'created_at', type: 'datetime', precision: 6 })
  createdAt: Date

  @UpdateDateColumn({
    name: 'updated_at',
    type: 'datetime',
    precision: 6,
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date

  @DeleteDateColumn({
    name: 'deleted_at',
    type: 'datetime',
    precision: 6,
    nullable: true,
  })
  deletedAt: Date

  @Column({ name: 'created_by', type: 'json', nullable: true })
  createdBy: Record<string, any>

  @Column({ name: 'updated_by', type: 'json', nullable: true })
  updatedBy: Record<string, any>

  @Column({ name: 'deleted_by', type: 'json', nullable: true })
  deletedBy: Record<string, any>
}
