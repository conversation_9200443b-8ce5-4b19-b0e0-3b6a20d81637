import { ApiProperty } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { RechargeType } from '@/enums'
import { School } from './school.entity'

@Entity({ name: 'recharges' })
export class Recharge extends BaseEntity<Recharge> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @ApiProperty()
  buyOrRevokeTime: number

  @Column()
  @ApiProperty()
  balance: number

  @Column()
  @ApiProperty()
  previousBalance: number

  @ApiProperty()
  @IsString()
  @Column()
  contractNumber: string

  @Column()
  @ApiProperty({ enum: RechargeType })
  type: RechargeType

  @ManyToOne(() => School, (school) => school.recharge)
  school: School
}
