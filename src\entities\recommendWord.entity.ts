import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { OnlineOfflineStatus } from '../enums'

@Entity({ name: 'recommend_search_words' })
export class RecommendSearchWord extends BaseEntity<RecommendSearchWord> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '搜索关键词', unique: true })
  @ApiProperty()
  @IsString()
  keyword: string

  @Column({ default: 0, comment: '搜索次数' })
  @ApiPropertyOptional()
  @IsNumber()
  searchCount: number

  @Column({ nullable: true, comment: '上架时间' })
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  onlineAt?: Date

  @Column({ nullable: true, comment: '下架时间' })
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  offlineAt?: Date

  @Column({ default: OnlineOfflineStatus.OFFLINE, comment: '关键词状态' })
  @IsEnum(OnlineOfflineStatus)
  @ApiPropertyOptional({
    description: '状态',
    example: OnlineOfflineStatus.OFFLINE,
    enum: OnlineOfflineStatus,
  })
  @IsOptional()
  status?: OnlineOfflineStatus
}
