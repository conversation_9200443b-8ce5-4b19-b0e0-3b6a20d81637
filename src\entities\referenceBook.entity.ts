import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsNumber, IsOptional } from 'class-validator'
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { Book } from './book.entity'
import { School } from './school.entity'

@Entity('reference_books')
export class ReferenceBook {
  @PrimaryGeneratedColumn()
  id: number

  @ApiPropertyOptional({
    type: () => School,
  })
  @IsOptional()
  @ManyToOne(() => School, (school) => school.referenceBooks, {
    eager: false,
    orphanedRowAction: 'delete',
  })
  @JoinColumn()
  school?: School

  @ApiPropertyOptional({
    type: () => Book,
  })
  @IsOptional()
  @ManyToOne(() => Book, (book) => book.referenceBooks)
  @JoinColumn()
  book?: Book

  @Column({ nullable: true, default: 0 })
  @ApiPropertyOptional({
    description: '副本数量',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  copiesCount?: number

  constructor(partial?: Partial<ReferenceBook>) {
    Object.assign(this, partial)
  }
}
