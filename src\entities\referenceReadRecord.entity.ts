import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Book } from './book.entity'
import { School } from './school.entity'
import { User } from './user.entity'

@Entity('reference_read_record')
export class ReferenceReadRecord extends BaseEntity<ReferenceReadRecord> {
  @PrimaryGeneratedColumn()
  id: number

  @ManyToOne(() => User, (user) => user.referenceReadRecords)
  @JoinColumn()
  user: User

  @ManyToOne(() => Book, (book) => book.referenceReadRecords)
  @JoinColumn({ name: 'book_id' })
  book: Book

  @ManyToOne(() => School, (school) => school.referenceReadRecords)
  school: School
}
