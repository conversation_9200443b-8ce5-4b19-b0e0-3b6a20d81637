import { IsString } from 'class-validator'
import { Column, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Permission } from './permission.entity'

@Entity({ name: 'resources' })
export class Resource extends BaseEntity<Resource> {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @IsString()
  name: string

  @ManyToOne(() => Resource, (resource) => resource.children)
  parent?: Resource

  @OneToMany(() => Resource, (resource) => resource.parent)
  children?: Resource[]

  @Column()
  @IsString()
  key: string

  @Column({ nullable: true })
  @IsString()
  description: string

  @OneToMany(() => Permission, (permission) => permission.resource, {
    eager: true,
  })
  permissions?: Permission[]
}
