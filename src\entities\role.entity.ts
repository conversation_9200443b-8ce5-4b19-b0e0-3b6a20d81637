import { IsString } from 'class-validator'
import { Column, <PERSON><PERSON><PERSON>, JoinT<PERSON>, ManyToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common'
import { Administrator } from './administrator.entity'
import { Permission } from './permission.entity'

@Entity({ name: 'roles' })
export class Role extends BaseEntity<Role> {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @IsString()
  name: string

  @Column({ nullable: false })
  @IsString()
  description: string

  @Column({ nullable: false, default: 'yes' })
  @IsString()
  visible: 'yes' | 'no'

  @ManyToMany(() => Administrator, (adminUser) => adminUser.roles, {
    cascade: true,
  })
  @JoinTable({
    name: 'admin_user_roles',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'admin_user_id', referencedColumnName: 'id' },
  })
  adminUsers?: Administrator[]

  @ManyToMany(() => Permission, (permission) => permission.roles, {
    cascade: true,
  })
  permissions?: Permission[]
}
