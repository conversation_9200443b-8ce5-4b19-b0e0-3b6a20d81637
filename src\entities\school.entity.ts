import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { Column, Entity, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { ESchoolStatus, ESchoolVersion, SchoolType } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { AssistantContracts } from './assistantContracts.entity'
import { AssistantSessionCount } from './assistantSessionCount.entity'
import { AssistantTopicCount } from './assistantTopicCount.entity'
import { Contract } from './contract.entity'
import { ContractHistories } from './contractHistories.entity'
import { Message } from './message.entity'
import { ReadingReflection } from './readingReflection.entity'
import { Recharge } from './recharge.entity'
import { ReferenceBook } from './referenceBook.entity'
import { ReferenceReadingReflection } from './referenceReadingReflection.entity'
import { ReferenceReadRecord } from './referenceReadRecord.entity'
import { SchoolAdministrator } from './schoolAdministrator.entity'
import { SchoolBalance } from './schoolBalance.entity'
import { SchoolHomepage } from './schoolHomepage.entity'
import { SchoolRole } from './schoolRole.entity'
import { SchoolSubject } from './schoolSubject.entity'
import { ScienceContracts } from './scienceContract.entity'
import { User } from './user.entity'
import { UserAnswer } from './userAnswer.entity'
import { UserAnswerCount } from './userAnswerCount.entity'
import { UserClass } from './userClass.entity'
import { ViewBookDetail } from './viewBookDetail.entity'

@Entity({ name: 'schools' })
export class School extends BaseEntity<School> {
  @ApiProperty()
  assistantPreferredVersion?: string

  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: '学校ID', unique: true })
  @ApiProperty({
    description: '学校ID',
  })
  @IsString()
  schoolId: string

  @Column({ nullable: true, comment: '学校名字', type: 'json' })
  @ApiPropertyOptional({
    description: '学校名字',
    type: MultiLanguage,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, comment: '学校地址', type: 'json' })
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  address?: MultiLanguage

  @Column({ nullable: true, comment: '学校地区' })
  @ApiPropertyOptional()
  @IsOptional()
  // @ValidateNested()
  // @Type(() => MultiLanguage)
  region?: string

  @Column({ nullable: true, comment: '简介', type: 'json' })
  @ApiPropertyOptional({ type: MultiLanguage })
  @IsOptional()
  @ValidateNested()
  @Type(() => MultiLanguage)
  description?: MultiLanguage

  @Column({ nullable: true, comment: '学校logo' })
  @ApiPropertyOptional({
    description: '学校logo',
    example: 'http://s3-cdn/sample.png',
  })
  @IsOptional()
  @IsString()
  logo?: string

  @Column({ nullable: true, comment: '联络人' })
  @ApiPropertyOptional({
    description: '联络人',
    example: 'Cindy Yang',
  })
  @IsOptional()
  @IsString()
  principalName?: string

  @Column({ nullable: true })
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: '区号' })
  prefixNo?: string

  @Column({ nullable: true, comment: '联系人电话' })
  @ApiPropertyOptional({
    description: '联系人电话',
    example: '+8618612345678',
  })
  @IsOptional()
  @IsString()
  principalNo?: string

  @Column({ nullable: true, comment: '联系人电邮' })
  @ApiPropertyOptional({
    description: '联系人电邮',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  principalEmail?: string

  @Column({
    nullable: true,
    comment: '加入时间',
  })
  @ApiPropertyOptional({
    description: '加入时间',
    example: 1657789890,
  })
  @IsOptional()
  @IsNumber()
  joinedAt?: number

  @Column({ nullable: true, comment: 'Web url' })
  @ApiPropertyOptional({
    description: 'Web url',
    example: 'https://web.sjrc.com/<shcooleId>',
  })
  @IsOptional()
  @IsString()
  webUrl?: string

  @Column({ nullable: true, comment: 'Admin panel url' })
  @ApiPropertyOptional({
    description: 'Admin panel url',
    example: 'https://admin-panel.sjrc.com/<shcooleId>',
  })
  @IsOptional()
  @IsString()
  adminPanelUrl?: string

  @Column({ nullable: true, comment: 'Student email suffix' })
  @ApiPropertyOptional({
    description: 'Student email suffix',
    example: '@student.com',
  })
  @IsOptional()
  @IsString()
  studentEmailSuffix?: string

  @Column({ nullable: true, comment: 'Teacher email suffix' })
  @ApiPropertyOptional({
    description: 'Teacher email suffix',
    example: '@teacher.com',
  })
  @IsOptional()
  @IsString()
  teacherEmailSuffix?: string

  @Column({ nullable: true, default: ESchoolStatus.ACTIVE, comment: '状态' })
  @ApiProperty({
    enum: ESchoolStatus,
    example: ESchoolStatus.ACTIVE,
  })
  @IsEnum(ESchoolStatus)
  status: ESchoolStatus

  @Column({ nullable: false, default: ESchoolVersion.SUBSCRIPTION })
  @ApiProperty({ enum: ESchoolVersion })
  @IsEnum(ESchoolVersion)
  version: ESchoolVersion

  @Column({ nullable: true, default: true })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  hasOTP?: boolean

  @Column({ nullable: true, default: false })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isSharingTime?: boolean

  @Column({ nullable: true, default: false })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  hasScienceRoom?: boolean

  @Column({ nullable: true, default: false })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  hasAssistant?: boolean

  @Column({ nullable: true })
  @ApiPropertyOptional({ enum: SchoolType })
  @IsEnum(SchoolType)
  @IsOptional()
  type?: SchoolType

  @OneToMany(() => ScienceContracts, (scienceContracts) => scienceContracts.school)
  scienceContracts: ScienceContracts[]

  @ValidateNested()
  @ApiProperty({
    type: () => SchoolBalance,
  })
  @Type(() => SchoolBalance)
  @OneToOne(() => SchoolBalance, (schoolBalance) => schoolBalance.school, {
    eager: false,
    cascade: ['insert'],
  })
  balance: SchoolBalance

  @ValidateNested()
  @ApiPropertyOptional({
    type: () => [SchoolAdministrator],
  })
  @IsOptional()
  @Type(() => SchoolAdministrator)
  @OneToMany(
    () => SchoolAdministrator,
    (schoolAdministrator) => schoolAdministrator.school,
    {
      eager: false,
    },
  )
  adnimistrators?: SchoolAdministrator[]

  @OneToMany(() => UserClass, (userClass) => userClass.school)
  classes: UserClass[]

  @OneToMany(() => Recharge, (recharge) => recharge.school)
  recharge: Recharge[]

  @OneToMany(() => User, (user) => user.school, {
    eager: false,
  })
  users?: User[]

  @OneToMany(() => Message, (message) => message.school, {
    eager: false,
  })
  messages?: Message[]

  @OneToOne(() => SchoolHomepage, (hompage) => hompage.school)
  homepage: SchoolHomepage

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional()
  @IsArray()
  @IsNumber(undefined, { each: true })
  @IsOptional()
  staffLevelIds?: number[]

  @Column({ nullable: true, default: false })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isAllLevelForStaff?: boolean

  @Column({ nullable: true, default: false })
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isAllLevelForStudent?: boolean

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional()
  @IsArray()
  @IsNumber(undefined, { each: true })
  @IsOptional()
  studentLevelIds?: number[]

  @ApiPropertyOptional({
    type: () => [SchoolRole],
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SchoolRole)
  @OneToMany(() => SchoolRole, (schoolRole) => schoolRole.school, {
    eager: false,
  })
  roles?: SchoolRole[]

  @Column({ default: false })
  hasNotified?: boolean

  @OneToMany(() => ReferenceBook, (schoolReferenceBook) => schoolReferenceBook.school, {
    eager: false,
    cascade: ['insert', 'update'],
  })
  referenceBooks: ReferenceBook[]

  @OneToMany(() => Contract, (contracts) => contracts.school)
  contracts: Contract[]

  @OneToMany(() => ContractHistories, (contractHistories) => contractHistories.school)
  contractHistories: ContractHistories[]

  @OneToMany(
    () => ReferenceReadRecord,
    (referenceReadRecords) => referenceReadRecords.school,
  )
  referenceReadRecords: ReferenceReadRecord[]

  @OneToMany(
    () => ReferenceReadingReflection,
    (referenceReadingReflections) => referenceReadingReflections.school,
  )
  referenceReadingReflections: ReferenceReadingReflection[]

  @OneToMany(() => ReadingReflection, (readingReflections) => readingReflections.school)
  readingReflections: ReadingReflection[]

  @OneToMany(() => ViewBookDetail, (viewBookDetails) => viewBookDetails.school)
  viewBookDetails: ViewBookDetail[]

  @OneToMany(() => SchoolSubject, (schoolSubjects) => schoolSubjects.school)
  schoolSubjects: SchoolSubject[]

  @OneToMany(() => UserAnswer, (userAnswer) => userAnswer.user)
  userAnswers: UserAnswer[]

  @OneToMany(() => UserAnswerCount, (UserAnswerCount) => UserAnswerCount.user)
  userAnswerCounts: UserAnswerCount[]

  @OneToMany(
    () => AssistantSessionCount,
    (AssistantSessionCount) => AssistantSessionCount.user,
  )
  assistantSessionCounts: AssistantSessionCount[]

  @OneToMany(() => AssistantTopicCount, (AssistantTopicCount) => AssistantTopicCount.user)
  assistantTopicCounts: AssistantTopicCount[]

  @OneToMany(() => AssistantContracts, (assistantContracts) => assistantContracts.school)
  assistantContracts: AssistantContracts[]

  constructor(partial: Partial<School>) {
    super(partial)
    Object.assign(this, partial)
  }
}
