import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsBase64,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import { Column, Entity, JoinColumn, ManyToMany, ManyToOne } from 'typeorm'
import { EAdministratorStatus } from '../enums'
import { BaseUser } from './baseUser.entity'
import { School } from './school.entity'
import { SchoolRole } from './schoolRole.entity'

@Entity({ name: 'school_administrators' })
export class SchoolAdministrator extends BaseUser<SchoolAdministrator> {
  @Column({ nullable: true, comment: '密码' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsBase64()
  // @IsString()
  password?: string

  @Column({ nullable: true, comment: '密码加盐' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  salt?: string

  @Column({ nullable: true, comment: '状态' })
  @ApiPropertyOptional({
    example: EAdministratorStatus.ACTIVE,
    enum: EAdministratorStatus,
  })
  @IsOptional()
  @IsEnum(EAdministratorStatus)
  status?: EAdministratorStatus

  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  @Column({ nullable: true, comment: '最后登陆时间' })
  lastLoginAt?: Date

  @ValidateNested()
  @ApiPropertyOptional({
    type: () => School,
  })
  @IsOptional()
  @Type(() => School)
  @ManyToOne(() => School, (school) => school.adnimistrators, {
    eager: false,
  })
  @JoinColumn()
  school?: School

  @Column({ nullable: true, comment: '是否是root用户', default: false })
  @ApiPropertyOptional({
    description: '是否是root用户',
  })
  @IsBoolean()
  @IsOptional()
  isRoot?: boolean

  @ManyToMany(() => SchoolRole, (schoolRole) => schoolRole.users, {
    eager: false,
    cascade: false,
  })
  @ApiPropertyOptional({
    type: () => [SchoolRole],
  })
  @IsOptional()
  roles?: SchoolRole[]

  constructor(partial: Partial<SchoolAdministrator>) {
    super(partial)
    Object.assign(this, partial)
  }
}
