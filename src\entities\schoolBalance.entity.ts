import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNumber, ValidateNested } from 'class-validator'
import { Column, Entity, JoinColumn, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { School } from './school.entity'

@Entity({ name: 'school_balances' })
export class SchoolBalance extends BaseEntity<SchoolBalance> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({ description: 'Total bought quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  totalBoughtQuota: number

  @ApiProperty({ description: 'Recently bought quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  distributionQuota: number

  @ApiProperty({ description: 'total distribution quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  totalDistributionQuota: number

  @ApiProperty({ description: 'Used quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  usedQuota: number

  @ApiProperty({ description: '资产版本号, 每次更新需要携带此版本' })
  @IsNumber()
  @Column({ nullable: false, default: 0 })
  version: number

  @ValidateNested()
  @ApiProperty({
    type: () => School,
  })
  @Type(() => School)
  @OneToOne(() => School, (school) => school.balance, {
    eager: false,
    cascade: ['insert'],
  })
  @JoinColumn()
  school: School
}
