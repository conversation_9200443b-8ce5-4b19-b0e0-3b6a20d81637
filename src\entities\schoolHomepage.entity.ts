import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EBookVersion, EListStatus, EListStyle, EUserType } from '../enums'
import { MultiLanguage } from '../interfaces'
import { School } from '.'
import { Book } from './book.entity'

@Entity({ name: 'school_homepage' })
export class SchoolHomepage extends BaseEntity<SchoolHomepage> {
  @ApiProperty()
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, comment: '标题', type: 'json' })
  @ApiPropertyOptional({
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: MultiLanguage,
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MultiLanguage)
  name?: MultiLanguage

  @Column({ nullable: true, comment: 'app样式' })
  @ApiPropertyOptional({
    description: 'app样式',
    enum: EListStyle,
    example: EListStyle.VERTICAL_DOUBLE,
  })
  @IsEnum(EListStyle)
  @IsOptional()
  appStyle?: EListStyle

  @Column({ nullable: true, comment: 'web样式' })
  @ApiPropertyOptional({
    description: 'web样式',
    enum: EListStyle,
    example: EListStyle.VERTICAL_DOUBLE,
  })
  @IsEnum(EListStyle)
  @IsOptional()
  webStyle?: EListStyle

  @IsOptional()
  @ApiPropertyOptional({
    description: '上架时间',
  })
  @Column({ nullable: true, comment: '上架时间' })
  onlineAt?: Date

  @IsOptional()
  @ApiPropertyOptional({
    description: '下架时间',
  })
  @Column({ nullable: true, comment: '下架时间' })
  offlineAt?: Date

  @Column({ default: EListStatus.PENDING, comment: '状态' })
  @ApiPropertyOptional({
    example: EListStatus.OFFLINE,
    enum: EListStatus,
  })
  @IsEnum(EListStatus)
  @IsOptional()
  status?: EListStatus

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: '今日推荐的子推荐栏位',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number

  @Column({ default: EUserType.STUDENT, comment: '状态' })
  @ApiPropertyOptional({
    example: EUserType.STUDENT,
    enum: EUserType,
  })
  @IsEnum(EUserType)
  @IsOptional()
  type?: EUserType

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({
    description: '班级Id',
    example: [1, 2, 4],
  })
  @IsNumber(undefined, { each: true })
  @IsOptional()
  classIds?: number[]

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({
    description: '年级Id',
    example: [1, 2, 4],
  })
  @IsNumber(undefined, { each: true })
  @IsOptional()
  gradeIds?: number[]

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  optionName?: string

  @Column({ default: EBookVersion.SUBSCRIPTION })
  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version: EBookVersion

  @ManyToMany(() => Book, (books) => books.schoolHomepages)
  books: Book[]

  @OneToOne(() => School, (school) => school.homepage)
  @JoinColumn()
  school: School
}
