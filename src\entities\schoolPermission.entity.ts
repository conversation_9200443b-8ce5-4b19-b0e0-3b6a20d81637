import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'
import {
  Column,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity, MultilingualContent } from '@/common'
import { EPermissionStatus } from '../enums'
import { SchoolRole } from './schoolRole.entity'

@Entity({ name: 'school_permissions' })
export class SchoolPermission extends BaseEntity<SchoolPermission> {
  @ApiPropertyOptional({
    description: 'id',
    example: 1,
  })
  @IsOptional()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: false, comment: 'Permission level' })
  @ApiPropertyOptional({
    description: '权限等级，1、2、3',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  level?: number

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Permission code, generated by backend',
    example: 'copy from title',
  })
  @IsOptional()
  @IsString()
  key?: string

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Permission id, generated by backend',
    example: 'PE00001',
  })
  @IsOptional()
  @IsString()
  permissionId?: string

  @Column({
    nullable: true,
    type: 'json',
    comment: 'Permission name',
  })
  @ApiPropertyOptional({
    type: () => MultilingualContent,
  })
  @Type(() => MultilingualContent)
  @ValidateNested()
  @IsOptional()
  title?: any

  @Column({ nullable: true, default: EPermissionStatus.ACTIVE })
  @ApiPropertyOptional({
    description: 'Permission status',
    example: EPermissionStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EPermissionStatus)
  status?: EPermissionStatus

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Permission description',
    example: 'Permission description example',
  })
  @IsOptional()
  @IsString()
  description?: string

  @ManyToMany(() => SchoolRole, (schoolRole) => schoolRole.permissions, {
    eager: false,
    onDelete: 'CASCADE',
  })
  roles?: SchoolRole[]

  @ApiPropertyOptional({
    type: () => SchoolPermission,
  })
  @IsOptional()
  @Type(() => SchoolPermission)
  @ManyToOne(() => SchoolPermission, (schoolPermission) => schoolPermission.children, {
    eager: false,
  })
  parent?: SchoolPermission

  @ApiPropertyOptional({
    type: () => [SchoolPermission],
  })
  @IsOptional()
  @Type(() => SchoolPermission)
  @OneToMany(() => SchoolPermission, (schoolPermission) => schoolPermission.parent, {
    eager: false,
    onDelete: 'CASCADE',
  })
  children?: SchoolPermission[]

  constructor(partial: Partial<SchoolPermission>) {
    super(partial)
    Object.assign(this, partial)
  }
}
