import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import {
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common'
import { ERoleStatus } from '../enums'
import { School } from './school.entity'
import { SchoolAdministrator } from './schoolAdministrator.entity'
import { SchoolPermission } from './schoolPermission.entity'

@Entity({ name: 'school_roles' })
export class SchoolRole extends BaseEntity<SchoolRole> {
  @ApiPropertyOptional({
    description: 'id',
    example: 1,
  })
  @IsOptional()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Role id, generated by backend',
    example: 'RL0001',
  })
  @IsOptional()
  @IsString()
  roleId?: string

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Role title ',
    example: 'Manager',
  })
  @IsOptional()
  @IsString()
  title?: string

  @Column({ nullable: true, default: ERoleStatus.ACTIVE })
  @ApiPropertyOptional({
    description: 'Role status',
    example: ERoleStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ERoleStatus)
  status?: ERoleStatus

  @Column({ nullable: true })
  @ApiPropertyOptional({
    description: 'Role description ',
    example: 'role description example',
  })
  @IsOptional()
  @IsString()
  description?: string

  @ManyToMany(
    () => SchoolAdministrator,
    (schoolAdministrator) => schoolAdministrator.roles,
    {
      eager: false,
      cascade: false,
    },
  )
  @JoinTable({
    name: 'school_administrator_roles',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  users?: SchoolAdministrator[]

  @ManyToMany(() => SchoolPermission, (schoolPermission) => schoolPermission.roles, {
    eager: false,
    onDelete: 'CASCADE',
  })
  @JoinTable({
    name: 'school_role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
  })
  permissions?: SchoolPermission[]

  @ApiPropertyOptional({
    type: () => School,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => School)
  @ManyToOne(() => School, (school) => school.roles, {
    eager: false,
    onDelete: 'CASCADE',
  })
  school?: School

  @ApiPropertyOptional({
    description: 'Account quantity',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  accountQuantity?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Column({ default: false })
  isEditable: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Column({ default: false })
  isSuper: boolean

  constructor(partial: Partial<SchoolRole>) {
    super(partial)
    Object.assign(this, partial)
  }
}
