import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional } from 'class-validator'
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { EOnlineType, ESchoolSubjectStatus } from '@/enums'
import { School } from './school.entity'
import { Subject } from './subject.entity'

@Entity({ name: 'school_subjects' })
export class SchoolSubject {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @IsEnum(ESchoolSubjectStatus)
  @IsOptional()
  @ApiProperty({ enum: ESchoolSubjectStatus })
  status?: ESchoolSubjectStatus

  @Column()
  @IsEnum(EOnlineType)
  @IsOptional()
  @ApiProperty({ enum: EOnlineType })
  onlineType?: EOnlineType

  @Column()
  @IsOptional()
  @ApiProperty()
  @IsNumber()
  onlineAt?: number

  @ManyToOne(() => Subject, (subject) => subject.schoolSubjects)
  @JoinColumn()
  subject: Subject

  @ManyToOne(() => School, (school) => school.schoolSubjects)
  @JoinColumn()
  school: School

  constructor(partial: Partial<SchoolSubject>) {
    Object.assign(this, partial)
  }
}
