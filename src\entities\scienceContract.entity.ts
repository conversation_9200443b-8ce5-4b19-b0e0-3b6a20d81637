import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm'
import { School } from './school.entity'

@Entity({ name: 'science_contracts' })
export class ScienceContracts {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @ApiProperty()
  @IsString()
  contractNo: string

  @ManyToOne(() => School, (school) => school.scienceContracts)
  school: School

  @CreateDateColumn()
  createdAt: Date

  @Column({ nullable: true, type: 'json' })
  @ApiProperty()
  gradeCodes: string[]

  @Column({ nullable: true, type: 'json' })
  @ApiProperty()
  createdBy: any

  @Column({ nullable: true, type: 'json' })
  @ApiProperty()
  updatedBy: any

  @UpdateDateColumn()
  updatedAt: Date
}
