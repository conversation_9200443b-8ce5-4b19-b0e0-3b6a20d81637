import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsArray,
  IsDate,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { BaseEntity } from '@/common'
import { EGrade, ELessonDocumentType, ESubjectNo, ESubjectStatus } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { Book } from './book.entity'
import { Question } from './question.entity'
import { SchoolSubject } from './schoolSubject.entity'
import { SubjectCategory } from './subjectCategory.entity'
import { SubjectExtendBook } from './subjectExtendBook'
import { Theme } from './theme.entity'
import { UserAnswer } from './userAnswer.entity'
import { UserAnswerCount } from './userAnswerCount.entity'

export class LessonDocument {
  @ApiProperty({ description: '教案文件类型', enum: ELessonDocumentType })
  @IsEnum(ELessonDocumentType)
  type: ELessonDocumentType

  @ApiPropertyOptional({ description: '教案文件pdf' })
  @IsString()
  @ValidateIf((o) =>
    [
      ELessonDocumentType.INTRODUCTION,
      ELessonDocumentType.PDAR,
      ELessonDocumentType.ASSESSMENT_QUESTIONS,
      ELessonDocumentType.SIMULATION_EXPERIMENT_VIDEO,
    ].includes(o.type),
  )
  pdfUrl?: string

  @ApiPropertyOptional({ description: '教案文件（其他格式）' })
  @IsString()
  @IsOptional()
  url?: string
}

@Entity({ name: 'subjects' })
export class Subject extends BaseEntity<Subject> {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '课题ID' })
  id: number

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({
    description: '课题名称',
    example: {
      zh_HK: '健康的生活方式',
      en_uk: '健康的生活方式',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ type: 'varchar' })
  @ApiProperty({ description: '课题编号', enum: ESubjectNo, isArray: true })
  @IsEnum(ESubjectNo, { each: true })
  serialNo: ESubjectNo[]

  @Column({ default: 1 })
  @ApiPropertyOptional({ description: '课题顺序' })
  @IsOptional()
  @IsInt()
  sequence: number

  @Column({ nullable: true })
  @ApiProperty({
    description: '课题图片',
    example: 'https://img.iread.com.tw/theme/1.png',
  })
  @IsString()
  @IsOptional()
  image?: string

  @Column({ default: 0 })
  @ApiPropertyOptional({ description: '课题倒计时' })
  @IsInt()
  @IsOptional()
  countdown: number

  @Column()
  @ApiProperty({ description: '课题背景图片' })
  @IsString()
  bgImage: string

  @Column()
  @ApiPropertyOptional({ description: '课题背景音乐' })
  @IsString()
  @IsOptional()
  bgMusic?: string

  @Column({ nullable: true, type: 'json' })
  @ApiProperty({
    description: '课题视频列表',
    example: '["https://img.iread.com.tw/theme/1.mp4"]',
  })
  @IsArray()
  videos?: string[]

  @Column({ nullable: true })
  @ApiProperty({
    description: '课题视频',
    example: 'https://img.iread.com.tw/theme/1.mp4',
  })
  @IsString()
  video?: string

  @Column({ type: 'varchar' })
  @ApiProperty({ description: '试用年级', enum: EGrade })
  @IsEnum(EGrade)
  @IsOptional()
  grade?: EGrade

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ description: '延伸阅读书籍起始位置' })
  @IsOptional()
  fromBookPos?: any

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ description: '延伸阅读书籍截止位置' })
  @IsOptional()
  toBookPos: any

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ description: '教案文件', type: [LessonDocument] })
  @IsOptional()
  @ValidateNested({ each: true })
  documents?: LessonDocument[]

  @Column({ type: String })
  @ApiProperty({ description: '课题状态', enum: ESubjectStatus })
  @IsEnum(ESubjectStatus)
  status: ESubjectStatus

  @Column({ name: 'online_at', nullable: true, type: 'datetime' })
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  onlineAt?: Date

  @Column({ name: 'offline_at', nullable: true, type: 'datetime' })
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  offlineAt?: Date

  @ManyToOne(() => SubjectCategory, (subjectCategory) => subjectCategory.subjects)
  @JoinColumn()
  subjectCategory: SubjectCategory

  @ManyToOne(() => Book, (book) => book.subjects)
  @JoinColumn()
  book: Book

  @ManyToOne(() => Theme, (theme) => theme.subjects)
  @JoinColumn()
  theme: Theme

  @OneToMany(() => Question, (questions) => questions.subject, { cascade: true })
  questions: Question[]

  @OneToMany(() => SchoolSubject, (schoolSubject) => schoolSubject.subject)
  schoolSubjects: SchoolSubject[]

  @OneToMany(() => UserAnswer, (userAnswer) => userAnswer.user)
  userAnswers: UserAnswer[]

  @OneToMany(() => UserAnswerCount, (userAnswerCount) => userAnswerCount.user)
  userAnswerCounts: UserAnswerCount[]

  @OneToMany(() => SubjectExtendBook, (extendBook) => extendBook.subject, {
    cascade: true,
  })
  subjectExtendBooks: SubjectExtendBook[]
}
