import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ValidateNested } from 'class-validator'
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { MultiLanguage } from '@/interfaces'
import { Subject } from './subject.entity'
import { Theme } from './theme.entity'

@Entity({ name: 'subject_categories' })
export class SubjectCategory {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, comment: '书籍名称', type: 'json' })
  @ApiProperty({
    description: '范畴名称',
    example: {
      zh_HK: '健康的生活方式',
      en_uk: '健康的生活方式',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ nullable: true, comment: 'image url' })
  @ApiProperty({
    description: 'image url',
    type: String,
  })
  image: string

  @Column({ nullable: true, comment: 'UI bg colors', type: 'json' })
  @ApiProperty({
    description: 'UI bg colors',
    isArray: true,
  })
  colors: string[]

  @OneToMany(() => Theme, (themes) => themes.subjectCategory)
  themes: Theme[]

  @OneToMany(() => Subject, (subject) => subject.subjectCategory)
  subjects: Subject[]
}
