import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm'
import { Book } from './book.entity'
import { Subject } from './subject.entity'

@Entity('subject_extend_books')
export class SubjectExtendBook {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ description: '延伸阅读书籍起始位置' })
  @IsOptional()
  fromBookPos?: any

  @Column({ nullable: true, type: 'json' })
  @ApiPropertyOptional({ description: '延伸阅读书籍截止位置' })
  @IsOptional()
  toBookPos: any

  @ManyToOne(() => Subject, (subject) => subject.subjectExtendBooks)
  subject: Subject

  @ManyToOne(() => Book, (book) => book.subjectExtendBooks)
  book: Book
}
