import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsInt, IsOptional, ValidateNested } from 'class-validator'
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { MultiLanguage } from '@/interfaces'
import { Subject } from './subject.entity'
import { SubjectCategory } from './subjectCategory.entity'

@Entity({ name: 'themes' })
export class Theme {
  @PrimaryGeneratedColumn()
  @ApiProperty({ description: '主题ID' })
  id: number

  @Column({ nullable: true, comment: '书籍名称', type: 'json' })
  @ApiProperty({
    description: '主题名称',
    example: {
      zh_HK: '生命與環境',
      en_uk: '生命與環境',
    },
    type: MultiLanguage,
  })
  @Type(() => MultiLanguage)
  @ValidateNested()
  name: MultiLanguage

  @Column({ default: 1 })
  @ApiPropertyOptional({ description: '主题顺序' })
  @IsOptional()
  @IsInt()
  sequence: number

  @Column({ nullable: true })
  @ApiProperty({
    description: '主题图片',
    example: 'https://img.iread.com.tw/theme/1.png',
  })
  image?: string

  @ManyToOne(() => SubjectCategory, (subjectCategory) => subjectCategory.themes)
  @JoinColumn()
  subjectCategory: SubjectCategory

  @OneToMany(() => Subject, (subjects) => subjects.theme)
  subjects: Subject[]
}
