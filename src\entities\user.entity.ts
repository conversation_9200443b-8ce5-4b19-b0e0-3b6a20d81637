import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  IsBase64,
  IsBoolean,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator'
import { Column, Entity, ManyToOne, OneToMany, OneToOne } from 'typeorm'
import { ELocaleType } from '@/common'
import { EUserType } from '@/enums'
import { AssistantSessionCount } from './assistantSessionCount.entity'
import { AssistantTopicCount } from './assistantTopicCount.entity'
import { BaseUser } from './baseUser.entity'
import { ReadingReflection } from './readingReflection.entity'
import { ReadRecord } from './readRecord.entity'
import { ReferenceReadingReflection } from './referenceReadingReflection.entity'
import { ReferenceReadRecord } from './referenceReadRecord.entity'
import { School } from './school.entity'
import { UserAnswer } from './userAnswer.entity'
import { UserAnswerCount } from './userAnswerCount.entity'
import { UserBalance } from './userBalance.entity'
import { UserClass } from './userClass.entity'
import { ViewBookDetail } from './viewBookDetail.entity'

@Entity({ name: 'users' })
export class User extends BaseUser<User> {
  @Column({ nullable: true, comment: 'Password' })
  @ApiPropertyOptional()
  @IsOptional()
  // @IsString()
  // @Length(8, 8)
  @IsBase64()
  password?: string

  @Column({ nullable: true, comment: 'Salt' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  salt?: string

  @Column({ nullable: true, comment: 'Student serial no.' })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  serialNo?: string

  @Column({ nullable: true, comment: 'App playerId' })
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  playerId?: string

  @Column({ nullable: true, comment: 'User type' })
  @ApiPropertyOptional({
    example: EUserType.STUDENT,
    enum: EUserType,
  })
  @IsEnum(EUserType)
  type: EUserType

  @IsDate()
  @IsOptional()
  @ApiPropertyOptional()
  @Type(() => Date)
  @Column({ nullable: true, comment: '最后登陆时间' })
  lastLoginAt?: Date

  @Column({ default: true })
  @IsBoolean()
  @ApiProperty()
  isEnabled: boolean

  @Column({ default: ELocaleType.ZH_HK, nullable: true })
  @ApiPropertyOptional()
  @IsEnum(ELocaleType)
  @IsOptional()
  locale?: ELocaleType

  @Column({ default: false })
  hasNotifed: boolean

  @Column({ nullable: true, comment: 'google unique id' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  googleProviderId?: string

  @Column({ nullable: true, comment: 'apple unique id' })
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  appleProviderId?: string

  // @ValidateNested()
  // @ApiPropertyOptional({
  //   type: () => UserClass,
  // })
  // @IsOptional()
  // @Type(() => UserClass)
  @ManyToOne(() => UserClass, (grade) => grade.users, {
    eager: false,
  })
  userClass?: UserClass

  @OneToOne(() => UserBalance, (blance) => blance.user, {
    cascade: ['insert'],
  })
  balance: UserBalance

  @ManyToOne(() => School, (school) => school.users, {
    eager: false,
  })
  school?: School

  @OneToMany(() => ReferenceReadRecord, (referenceReadRecord) => referenceReadRecord.user)
  referenceReadRecords: ReferenceReadRecord[]

  @OneToMany(
    () => ReferenceReadingReflection,
    (referenceReadingReflections) => referenceReadingReflections.user,
  )
  referenceReadingReflections: ReferenceReadingReflection[]

  @OneToMany(() => ReadingReflection, (readingReflections) => readingReflections.user)
  readingReflections: ReadingReflection[]

  @OneToMany(() => ViewBookDetail, (viewBookDetail) => viewBookDetail.user)
  viewBookDetail: ViewBookDetail[]

  @OneToMany(() => UserAnswer, (userAnswer) => userAnswer.user)
  userAnswers: UserAnswer[]

  @OneToMany(() => UserAnswerCount, (UserAnswerCount) => UserAnswerCount.user)
  userAnswerCounts: UserAnswerCount[]

  @OneToMany(
    () => AssistantSessionCount,
    (AssistantSessionCount) => AssistantSessionCount.user,
  )
  assistantSessionCounts: AssistantSessionCount[]

  @OneToMany(
    () => AssistantSessionCount,
    (AssistantSessionCount) => AssistantSessionCount.user,
  )
  assistantTopicCounts: AssistantTopicCount[]

  constructor(partial: Partial<User>) {
    super(partial)
    Object.assign(this, partial)
  }
}
