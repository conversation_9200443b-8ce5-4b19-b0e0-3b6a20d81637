import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNumber, ValidateNested } from 'class-validator'
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { EBadge } from '@/enums'
import { QuestionHistory } from './questionHistory'
import { School } from './school.entity'
import { Subject } from './subject.entity'
import { User } from './user.entity'
import { UserClass } from './userClass.entity'

export class AnswerItem {
  @ApiProperty()
  @IsNumber()
  questionId: number

  @ApiProperty({ type: [Number] })
  @IsNumber(undefined, { each: true })
  selectedOptionIds: number[]

  @ApiProperty()
  @IsNumber()
  usedTime: number
}

@Entity('user_answer')
export class UserAnswer {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column()
  @ApiProperty({ description: '答题耗时' })
  time: number

  @Column()
  @ApiProperty({ description: '答题得分' })
  scores: number

  @Column({ nullable: true })
  @ApiProperty({ description: '回答正确数' })
  correctCount: number

  @Column({ nullable: true })
  @ApiProperty({ description: '题目总数' })
  questionCount: number

  @Column({ type: 'json' })
  @ApiProperty({ type: [AnswerItem] })
  @ValidateNested()
  answers: AnswerItem[]

  @Column({ type: 'json', nullable: true })
  @ApiProperty({ description: '答题徽章', enum: EBadge, isArray: true })
  @IsEnum(EBadge, { each: true })
  badge: EBadge[]

  @Column({ type: 'int', nullable: true })
  @ApiProperty({ description: '最高答题徽章', enum: EBadge })
  @IsEnum(EBadge)
  maxBadge: EBadge

  @ManyToOne(() => User, (user) => user.userAnswers)
  @JoinColumn()
  user: User

  @ManyToOne(() => UserClass, (userClass) => userClass.userAnswers)
  @JoinColumn()
  userClass: UserClass

  @Column({ nullable: true })
  gradeId: number

  @ManyToOne(() => School, (school) => school.userAnswers)
  @JoinColumn()
  school: School

  @ManyToOne(() => QuestionHistory, (questionsHistory) => questionsHistory.userAnswers)
  @JoinColumn()
  questionsHistory: QuestionHistory

  @ManyToOne(() => Subject, (subject) => subject.userAnswers)
  @JoinColumn()
  subject: Subject

  @CreateDateColumn()
  createdAt?: Date
}
