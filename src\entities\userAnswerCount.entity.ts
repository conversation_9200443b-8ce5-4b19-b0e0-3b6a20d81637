import { ApiProperty } from '@nestjs/swagger'
import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { School } from './school.entity'
import { Subject } from './subject.entity'
import { User } from './user.entity'
import { UserClass } from './userClass.entity'

@Entity('user_answer_count')
export class UserAnswerCount {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  id: number

  @Column({ nullable: true })
  @ApiProperty()
  gradeId: number

  @Column()
  @ApiProperty()
  userType: string

  @ManyToOne(() => User, (user) => user.userAnswerCounts)
  @JoinColumn()
  user: User

  @ManyToOne(() => UserClass, (userClass) => userClass.userAnswerCounts)
  @JoinColumn()
  userClass: UserClass

  @ManyToOne(() => School, (school) => school.userAnswerCounts)
  @JoinColumn()
  school: School

  @ManyToOne(() => Subject, (subject) => subject.userAnswerCounts)
  @JoinColumn()
  subject: Subject

  @CreateDateColumn()
  createdAt?: Date
}
