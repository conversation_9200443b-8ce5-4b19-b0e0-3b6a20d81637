import { ApiProperty } from '@nestjs/swagger'
import { IsNumber } from 'class-validator'
import { Column, Entity, JoinColumn, OneToOne, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { User } from './user.entity'

@Entity({ name: 'user_balances' })
export class UserBalance extends BaseEntity<UserBalance> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({ description: 'Total quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  totalQuota: number

  // @ApiProperty({ description: 'Available quota' })
  // @IsNumber()
  // @Column({ nullable: false, default: 0, type: 'double' })
  // availableQuota: number

  @ApiProperty({ description: 'Used quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  usedQuota: number

  @ApiProperty({ description: 'Total used quota' })
  @IsNumber()
  @Column({ nullable: false, default: 0, type: 'double' })
  totalUsedQuota: number

  @ApiProperty({ description: '资产版本号, 每次更新需要携带此版本' })
  @IsNumber()
  @Column({ nullable: false, default: 0 })
  version?: number

  @ApiProperty()
  @Column({ default: 0 })
  viewReferenceBooks: number

  @ApiProperty()
  @Column({ nullable: true, type: 'varchar' })
  readReferenceBooks: any

  @OneToOne(() => User, (user) => user.balance, {
    eager: false,
    cascade: ['insert'],
  })
  @JoinColumn()
  user: User
}
