import { ApiProperty } from '@nestjs/swagger'
import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator'
import { Column, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { AssistantSessionCount } from './assistantSessionCount.entity'
import { AssistantTopicCount } from './assistantTopicCount.entity'
import { ReadingReflection } from './readingReflection.entity'
import { ReferenceReadingReflection } from './referenceReadingReflection.entity'
import { School } from './school.entity'
import { User } from './user.entity'
import { UserAnswer } from './userAnswer.entity'
import { UserAnswerCount } from './userAnswerCount.entity'

@Entity({ name: 'user_class' })
export class UserClass extends BaseEntity<UserClass> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  @ApiProperty()
  @IsNumber()
  gradeId: number

  @Column()
  @ApiProperty({ description: 'class' })
  @IsString()
  class: string

  @Column()
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  sequence: number

  @ApiProperty({
    type: () => User,
  })
  // @Type(() => User)
  @OneToMany(() => User, (user) => user.userClass, {
    eager: false,
  })
  users: User[]

  @OneToMany(() => UserAnswer, (userAnswers) => userAnswers.userClass, {
    eager: false,
  })
  userAnswers: UserAnswer[]

  @OneToMany(() => UserAnswerCount, (userAnswerCount) => userAnswerCount.userClass, {
    eager: false,
  })
  userAnswerCounts: UserAnswerCount[]

  @OneToMany(
    () => AssistantSessionCount,
    (AssistantSessionCount) => AssistantSessionCount.userClass,
    {
      eager: false,
    },
  )
  assistantSessionCounts: AssistantSessionCount[]

  @OneToMany(
    () => AssistantTopicCount,
    (AssistantTopicCount) => AssistantTopicCount.userClass,
    {
      eager: false,
    },
  )
  assistantTopicCounts: AssistantTopicCount[]

  @OneToMany(
    () => ReferenceReadingReflection,
    (ReferenceReadingReflection) => ReferenceReadingReflection.userClass,
    {
      eager: false,
    },
  )
  referenceReadingReflections: ReferenceReadingReflection[]

  @OneToMany(
    () => ReadingReflection,
    (readingReflections) => readingReflections.userClass,
    {
      eager: false,
    },
  )
  readingReflections: ReadingReflection[]

  @ManyToOne(() => School, (school) => school.classes)
  school: School
}
