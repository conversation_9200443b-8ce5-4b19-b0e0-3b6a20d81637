import {
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm'
import { Book } from './book.entity'
import { School } from './school.entity'
import { User } from './user.entity'

@Entity('view_book_detail')
export class ViewBookDetail {
  @PrimaryGeneratedColumn()
  id: number

  @ManyToOne(() => User, (user) => user.viewBookDetail)
  @JoinColumn()
  user: User

  @ManyToOne(() => Book, (book) => book.viewBookDetail)
  @JoinColumn()
  book: Book

  @ManyToOne(() => School, (school) => school.viewBookDetails)
  school: School

  @CreateDateColumn()
  createdAt?: Date
}
