export enum EUserStatus {
  INACTIVE = 'inactive',
  ACTIVE = 'active',
}

export enum EGoogleLoginType {
  WEB = 'web',
  ADMIN = 'admin',
  IOS = 'ios',
  ANDROID = 'android',
}

export enum EVerificationCodeType {
  LOGIN = 'login',
  FORGET_PASSCODE = 'forget_passcode',
  RECHANGE_EMAIL = 'rechange_email',
  RECHANGE_PHONE = 'rechange_phone',
  RESET_PASSCODE = 'reset_passcode',
  VERIFY_EMAIL = 'verify_email',
}

export enum EClientVerificationCodeType {
  RESET_PASSWORD = 'reset_password',
}

export enum EPublicVerificationCodeType {
  REGISTER = 'register',
  LOGIN = 'login',
}

export enum EProviderType {
  GOOGLE = 'google',
  FACEBOOK = 'facebook',
  APPLE = 'apple',
}

export enum EAdministratorType {
  SUPER_ADMIN = 'super_admin',
  PLATFORM_ADMIN = 'platform_admin',
  SCHOOL_ADMIN = 'school_admin',
}

export enum EAdministratorStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
}

export const PublicVerificationCodeTypes = [
  EVerificationCodeType.LOGIN,
  EVerificationCodeType.FORGET_PASSCODE,
]

export const ClientVerificationCodeTypes = [EClientVerificationCodeType.RESET_PASSWORD]

export enum ETicketType {
  LOGIN_BY_EMAIL = 'login_by_email',
  RESET_PASSWORD = 'reset_password',
  FORGET_PASSWORD = 'forget_password',
  MODIFY_USER = 'modify_user',
}

export enum ECredentialType {
  FINGERPRINT = 'fingerprint',
  FACE = 'face',
  PASSCODE = 'passcode',
}

export enum EChallengeType {
  FINGERPRINT = 'fingerprint',
  FACE = 'face',
}

export enum EDynamicLinkScenario {
  LOGIN = 'login',
  SHARE = 'share',
}

export enum EUserType {
  ALL = 'all',
  STUDENT = 'student',
  TEACHER = 'teacher',
}

export enum ESchoolAdminType {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  USER = 'USER',
}
