export enum BookType {
  EPUB = 'epub',
  PDF = 'pdf',
}

export const bookTypeValues = Object.values(BookType)

export enum BookOrderType {
  NEWESE = 'newest',
  HOT = 'hot',
}

// export enum BookLevel {
//   PRIMARY_ONE_TO_THREE = 'PRIMARY_ONE_TO_THREE',
//   PRIMARY_FOUR_TO_SIX = 'PRIMARY_FOUR_TO_SIX',
//   // PRIMARY_SCHOOL_LEVEL = 'PRIMARY_SCHOOL_LEVEL',
//   JUNIOR_HIGH_SCHOOL = 'JUNIOR_HIGH_SCHOOL',
//   HIGH_SCHOOL_LEVEL = 'HIGH_SCHOOL_LEVEL',
//   TEACHER_LEVEL = 'TEACHER_LEVEL',
// }

export enum BookOperationType {
  BATCH_UNPUBLISH = 'batch_unpublish',
  BATCH_DELETE = 'batch_delete',
}

export enum BookUserType {
  STUDENT = 'STUDENT',
  TEACHER = 'TEACHER',
}

export enum EBookVersion {
  SUBSCRIPTION = 'SUBSCRIPTION',
  REFERENCE = 'REFERENCE',
  // SUBSCRIPTION_AND_REFERENCE = 'SUBSCRIPTION_AND_REFERENCE',
}

export enum ECurrency {
  HKD = 'HKD',
  // RMB = 'RMB',
  // USD = 'USD',
}

export enum EBookLanguage {
  ZH_AND_EN = 'zh_en',
  ZH = 'zh',
  EN = 'en',
}

export enum EBookOrderType {
  NEWEST = 'newest',
  TIMES_OF_SCHOOL_READING = 'times_of_school_reading',
}

export enum EBookReadRefectionType {
  TEXT = 'TEXT',
  AUDIO = 'AUDIO',
}

export enum ETextToSpeechLanguage {
  ZH_EN = 'cmn-CN',
  ZH_HK = 'yue-HK',
  EN = 'en-GB',
}

export enum ETextToSpeechSsmlGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
}
