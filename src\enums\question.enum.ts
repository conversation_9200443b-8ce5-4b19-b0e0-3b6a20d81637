export enum EQuestionType {
  MU<PERSON><PERSON><PERSON>_CHOICE_QUESTIONS = 'MULTIPLE_CHOICE_QUESTIONS',
  QUESTION_INTRODUCTION = 'QUESTION_INTRODUCTION',
}

export enum EAnswerType {
  SINGLE_CHOICE = 'SINGLE_CHOICE',
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
}

export enum EAbility {
  COMPREHENSION = 'COMPREHENSION', // 理解能力
  SKILLS_OF_ANALYZE = 'SKILLS_OF_ANALYZE', // 分析能力
  PROBLEM_SOLVING_ABILITY = 'PROBLEM_SOLVING_ABILITY', // 解难能力
  EXPRESSION_ABILITY = 'EXPRESSION_ABILITY', // 表达能力
  INQUIRY_ABILITY = 'INQUIRY_ABILITY', // 探究能力
  JUDGMENT_ABILITY = 'JUDGMENT_ABILITY', // 判断能力
}
