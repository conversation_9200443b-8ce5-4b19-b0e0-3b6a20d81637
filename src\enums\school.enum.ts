export enum ESchoolStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum SchoolType {
  KINDERGARTEN = 'KIN<PERSON>RGARTEN',
  PRIMARY = 'PRIMARY',
  MIDDLE = 'MIDDLE',
  COLLEGES = 'COLLEGES',
}

export enum EPermissionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum ERoleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum EGlobalSummarySplitType {
  HOUR = 'hour',
  DAY = 'day',
  MONTH = 'month',
  YEAR = 'year',
}

export enum ESchoolVersion {
  SUBSCRIPTION = 'SUBSCRIPTION',
  REFERENCE = 'REFERENCE',
  SUBSCRIPTION_REFERENCE = 'SUBSCRIPTION_REFERENCE',
}

export enum EGrade {
  KINDERGARTEN = 'KINDERGARTEN',
  FIRST_PRIMARY_GRADE = 'FIRST_PRIMARY_GRADE',
  SECOND_PRIMARY_GRADE = 'SECOND_PRIMARY_GRADE',
  THIRD_PRIMARY_GRADE = 'THIRD_PRIMARY_GRADE',
  FOURTH_PRIMARY_GRADE = 'FOURTH_PRIMARY_GRADE',
  FIFTH_PRIMARY_GRADE = 'FIFTH_PRIMARY_GRADE',
  SIXTH_PRIMARY_GRADE = 'SIXTH_PRIMARY_GRADE',
  FIRST_MIDDLE_GRADE = 'FIRST_MIDDLE_GRADE',
  SECOND_MIDDLE_GRADE = 'SECOND_MIDDLE_GRADE',
  THIRD_MIDDLE_GRADE = 'THIRD_MIDDLE_GRADE',
  FOURTH_MIDDLE_GRADE = 'FOURTH_MIDDLE_GRADE',
  FIFTH_MIDDLE_GRADE = 'FIFTH_MIDDLE_GRADE',
  SIXTH_MIDDLE_GRADE = 'SIXTH_MIDDLE_GRADE',
}
