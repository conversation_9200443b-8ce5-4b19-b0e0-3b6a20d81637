export enum ESubjectStatus {
  DRAFT = 'DRAFT',
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
}

export enum ESchoolSubjectStatus {
  PRE_ONLINE = 'PRE_ONLINE',
  ONLINE = 'ONLINE',
  HIDDEN = 'HIDDEN',
}

export enum EOnlineType {
  IMMEDIATELY = 'IMMEDIATELY',
  TIMING = 'TIMING',
}

export enum ESubjectNo {
  LA11 = '1LA1',
  LA12 = '1LA2',
  LA13 = '1LA3',
  LA14 = '1LA4',
  LB11 = '1LB1',
  LB12 = '1LB2',
  LB13 = '1LB3',
  LB14 = '1LB4',
  LB15 = '1LB5',
  LC11 = '1LC1',
  MA11 = '1MA1',
  MA12 = '1MA2',
  MB11 = '1MB1',
  MB12 = '1MB2',
  MB13 = '1MB3',
  MB14 = '1MB4',
  MC11 = '1MC1',
  MC12 = '1MC2',
  MC13 = '1MC3',
  MC14 = '1MC4',
  EA11 = '1EA1',
  EA12 = '1EA2',
  EA13 = '1EA3',
  EA14 = '1EA4',
  EC11 = '1EC1',
  EC12 = '1EC2',
  SB11 = '1SB1',
  SB12 = '1SB2',
  SB13 = '1SB3',
  SC11 = '1SC1',
  SC12 = '1SC2',
  SC13 = '1SC3',
  LB21 = '2LB1',
  LB22 = '2LB2',
  LD21 = '2LD1',
  LD22 = '2LD2',
  LD23 = '2LD3',
  LE21 = '2LE1',
  LE22 = '2LE2',
  LE23 = '2LE3',
  MA21 = '2MA1',
  MA22 = '2MA2',
  MA23 = '2MA3',
  MA24 = '2MA4',
  MA25 = '2MA5',
  MA26 = '2MA6',
  MB21 = '2MB1',
  MB22 = '2MB2',
  MB23 = '2MB3',
  MC21 = '2MC1',
  MC22 = '2MC2',
  MC23 = '2MC3',
  EB21 = '2EB1',
  EB22 = '2EB2',
  EC21 = '2EC1',
  SA21 = '2SA2',
  SA22 = '2SA3',
  SA23 = '2SA1',
  SA24 = '2SA4',
  SC21 = '2SC1',
  SC22 = '2SC2',
  SC23 = '2SC3',
  SC24 = '2SC4',
  LA31 = '3LA1',
  LA32 = '3LA2',
  LA33 = '3LA3',
  LA34 = '3LA4',
  LA35 = '3LA5',
  LB31 = '3LB1',
  LB32 = '3LB2',
  LB33 = '3LB3',
  LB34 = '3LB4',
  LB35 = '3LB5',
  LB36 = '3LB6',
  LB37 = '3LB7',
  LC31 = '3LC1',
  LC32 = '3LC2',
  LC33 = '3LC3',
  LC34 = '3LC4',
  LC35 = '3LC5',
  MA31 = '3MA1',
  MA32 = '3MA2',
  MA33 = '3MA3',
  MA34 = '3MA4',
  MA35 = '3MA5',
  MA36 = '3MA6',
  MA37 = '3MA7',
  MA38 = '3MA8',
  MB31 = '3MB1',
  MB32 = '3MB2',
  MB33 = '3MB3',
  MB34 = '3MB4',
  MC31 = '3MC1',
  MC32 = '3MC2',
  EA31 = '3EA1',
  EA32 = '3EA2',
  EA33 = '3EA3',
  EA34 = '3EA4',
  EB31 = '3EB1',
  EB32 = '3EB2',
  EB33 = '3EB3',
  EC31 = '3EC1',
  EC32 = '3EC2',
  EC33 = '3EC3',
  EC34 = '3EC4',
  SA31 = '3SA1',
  SA32 = '3SA2',
  SA33 = '3SA3',
  SB31 = '3SB1',
  SB32 = '3SB2',
  SB33 = '3SB3',
  SC31 = '3SC1',
  SC32 = '3SC2',
  SC33 = '3SC3',
  SC34 = '3SC4',
  SC35 = '3SC5',
  LA41 = '4LA1',
  LA42 = '4LA2',
  LA43 = '4LA3',
  LA44 = '4LA4',
  LC41 = '4LC1',
  LC42 = '4LC2',
  LC43 = '4LC3',
  LC44 = '4LC4',
  LC45 = '4LC5',
  LC46 = '4LC6',
  LD41 = '4LD1',
  LD42 = '4LD2',
  LD43 = '4LD3',
  LE41 = '4LE1',
  LE42 = '4LE2',
  LE43 = '4LE3',
  LE44 = '4LE4',
  LE45 = '4LE5',
  MA41 = '4MA1',
  MA42 = '4MA2',
  MA43 = '4MA3',
  MA44 = '4MA4',
  MA45 = '4MA5',
  MB41 = '4MB1',
  MB42 = '4MB2',
  MB43 = '4MB3',
  MB44 = '4MB4',
  MB45 = '4MB5',
  MB46 = '4MB6',
  MB47 = '4MB7',
  MB48 = '4MB8',
  MC41 = '4MC1',
  MC42 = '4MC2',
  MC43 = '4MC3',
  EA41 = '4EA1',
  EA42 = '4EA2',
  EA43 = '4EA3',
  EB41 = '4EB1',
  EB42 = '4EB2',
  EB43 = '4EB3',
  EB44 = '4EB4',
  EB45 = '4EB5',
  EC41 = '4EC1',
  EC42 = '4EC2',
  EC43 = '4EC3',
  SA41 = '4SA1',
  SA42 = '4SA2',
  SA43 = '4SA3',
  SB41 = '4SB1',
  SB42 = '4SB2',
  SB43 = '4SB3',
  SB44 = '4SB4',
  SB45 = '4SB5',
  SC41 = '4SC1',
  SC42 = '4SC2',
  LB51 = '5LB1',
  LB52 = '5LB2',
  LB53 = '5LB3',
  LC51 = '5LC1',
  LC52 = '5LC2',
  LC53 = '5LC3',
  LC54 = '5LC4',
  LD51 = '5LD1',
  LD52 = '5LD2',
  LD53 = '5LD3',
  LF51 = '5LF1',
  LF52 = '5LF2',
  LF53 = '5LF3',
  MA51 = '5MA1',
  MA52 = '5MA2',
  MA53 = '5MA3',
  MA54 = '5MA4',
  MA55 = '5MA5',
  MA56 = '5MA6',
  MA57 = '5MA7',
  MB51 = '5MB1',
  MB52 = '5MB2',
  MB53 = '5MB3',
  MB54 = '5MB4',
  MB55 = '5MB5',
  MB56 = '5MB6',
  MB57 = '5MB7',
  MB58 = '5MB8',
  MB59 = '5MB9',
  MC51 = '5MC1',
  MC52 = '5MC2',
  MC53 = '5MC3',
  EA51 = '5EA1',
  EA52 = '5EA2',
  EA53 = '5EA3',
  EA54 = '5EA4',
  EB51 = '5EB1',
  EB52 = '5EB2',
  EC51 = '5EC1',
  EC52 = '5EC2',
  EC53 = '5EC3',
  EC54 = '5EC4',
  EC55 = '5EC5',
  SA51 = '5SA1',
  SA52 = '5SA2',
  SA53 = '5SA3',
  SB51 = '5SB1',
  SB52 = '5SB2',
  SC51 = '5SC1',
  LA61 = '6LA1',
  LA62 = '6LA2',
  LA63 = '6LA3',
  LB61 = '6LB1',
  LB62 = '6LB2',
  LB63 = '6LB3',
  LB64 = '6LB4',
  LD61 = '6LD1',
  LD62 = '6LD2',
  LD63 = '6LD3',
  LD64 = '6LD4',
  LE61 = '6LE1',
  LE62 = '6LE2',
  LE63 = '6LE3',
  LF61 = '6LF1',
  LF62 = '6LF2',
  LF63 = '6LF3',
  MA61 = '6MA1',
  MA62 = '6MA2',
  MA63 = '6MA3',
  MA64 = '6MA4',
  MA65 = '6MA5',
  MB61 = '6MB1',
  MB62 = '6MB2',
  MB63 = '6MB3',
  MB64 = '6MB4',
  MB65 = '6MB5',
  MB66 = '6MB6',
  MB67 = '6MB7',
  MC61 = '6MC1',
  MC62 = '6MC2',
  MC63 = '6MC3',
  MC64 = '6MC4',
  EA61 = '6EA1',
  EA62 = '6EA2',
  EA63 = '6EA3',
  EC61 = '6EC1',
  EC62 = '6EC2',
  EC63 = '6EC3',
  EC64 = '6EC4',
  EC65 = '6EC5',
  SA61 = '6SA1',
  SA62 = '6SA2',
  SB61 = '6SB1',
  SB62 = '6SB2',
  SB63 = '6SB3',
  SB64 = '6SB4',
  SB65 = '6SB5',
  SC61 = '6SC1',
}

export enum EBadge {
  ANSWER_THREE_QUESTION_CORRECTLY = 1,
  ANSWER_FIVE_QUESTION_CORRECTLY = 2,
  ANSWER_EIGHT_QUESTION_CORRECTLY = 3,
  ANSWER_QUESTION_CORRECTLY_AND_QUICKLY = 4,
}

export enum ELessonDocumentType {
  INTRODUCTION = 'INTRODUCTION', //简介
  PDAR = 'PDAR', //PDAR
  ASSESSMENT_QUESTIONS = 'ASSESSMENT_QUESTIONS', //评估题目
  SIMULATION_EXPERIMENT_VIDEO = 'SIMULATION_EXPERIMENT_VIDEO', //模拟实验视频
  SUGGESTED_CLASSROOM_EXPERIMENTS = 'SUGGESTED_CLASSROOM_EXPERIMENTS', //建议课堂实验
  SUGGESTED_CLASSROOM_ACTIVITIES = 'SUGGESTED_CLASSROOM_ACTIVITIES', //建议课堂活动
  TEACHING_MATERIAL = 'TEACHING_MATERIAL', //教材
  EXTENDED_EXPERIMENT = 'EXTENDED_EXPERIMENT', //延伸實驗
  EXTENDED_ACTIVITIES = 'EXTENDED_ACTIVITIES', //延伸活动
}
