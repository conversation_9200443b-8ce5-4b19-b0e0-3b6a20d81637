import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'

// export type MultiLanauge = Record<ELocaleType, string>

// /* eslint-disable */
// export interface Lanauge extends MultiLanauge {}

export class MultiLanguage {
  @ApiProperty()
  @IsString()
  @IsOptional()
  en_uk: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  zh_HK: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  zh_cn: string
}
