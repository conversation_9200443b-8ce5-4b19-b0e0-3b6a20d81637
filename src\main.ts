import { ConfigService } from '@nestjs/config'
import { NestFactory } from '@nestjs/core'
import { OpenAPIObject, SwaggerModule } from '@nestjs/swagger'
import { json } from 'express'
import { Logger } from 'nestjs-pino'
import R from 'ramda'
import { attachRawBodyMiddleware } from './middlewares/attachRawBody.middleware'
import { AppModule } from './modules'
import { InitLeaderBoardService } from './modules/books/services/initLeaderBoard.service'
import { RedisIoAdapter } from './modules/websocket'
import { runMigration } from './runMigration'

import 'source-map-support/register'
console.log(
  'main.ts loaded at',
  new Date().toISOString(),
  'process.uptime:',
  process.uptime(),
)
const bootstrap = async () => {
  const app = await NestFactory.create(AppModule, { cors: true })

  // try {
  //   await runMigration(app)
  // } catch (error) {
  //   console.log(error)
  // }

  app.enableCors()

  app.use(json({ limit: '50mb', verify: attachRawBodyMiddleware }))

  const logger = app.get(Logger)
  app.useLogger(logger)

  const redisIoAdapter = new RedisIoAdapter(app)
  await redisIoAdapter.connectToRedis()
  app.useWebSocketAdapter(redisIoAdapter)

  process.on('unhandledRejection', (err) => {
    logger.error('DEBUG: unhandledRejection....', err)
    throw err
  })

  logger.log(`process.env.APP_ENV: ${process.env.APP_ENV}`)

  const configService = app.get(ConfigService)

  const options: OpenAPIObject = configService.get('application.swagger')
  const document = SwaggerModule.createDocument(app, options)
  const pathname = 'swagger'

  SwaggerModule.setup(pathname, app, document)

  const swaggerMsg = `Please visit swagger document at ${
    R.last(options.servers).url
  }/${pathname}`
  logger.log(swaggerMsg)
  console.log(swaggerMsg)
  // console.log(
  //   app.get(JwtService).sign(
  //     {
  //       userId: 2,
  //       email: '<EMAIL>',
  //       familyName: 'Yang',
  //       givenName: 'cindy',
  //       schoolId: 1,
  //     },
  //     { authSchema: AuthSchema.CLIENT },
  //   ),
  // )

  // if (process.env.APP_ENV !== 'local')
  //   await app.get(InitLeaderBoardService).synchronizeReadingTime()
  await app.listen(configService.get('application.port'))
  const url = await app.getUrl()
  const appMsg = `Application is running on: ${url}`
  logger.log(appMsg)
  console.log(appMsg)
}

bootstrap()
