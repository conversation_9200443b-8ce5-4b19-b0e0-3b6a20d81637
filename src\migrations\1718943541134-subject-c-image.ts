import { MigrationInterface, QueryRunner } from 'typeorm'

export class SubjectCImage1718943541134 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const iconPrefix = 'https://images-uat.trusive.hk/public/images/'
    await queryRunner.query(
      `alter table subject_categories add column \`image\` varchar(255);`,
    )
    await queryRunner.query(
      `alter table subject_categories add column \`colors\` json  DEFAULT ('[]');`,
    )

    await queryRunner.query(
      `update subject_categories set image = '${iconPrefix}category-env.svg', 
        colors='["#00BDAA","#32C2FF"]'
        where id = 1;`,
    )

    await queryRunner.query(
      `update subject_categories set image = '${iconPrefix}category-energy.svg', 
        colors='["#FFCC00","#FFA800"]'
        where id = 2;`,
    )

    await queryRunner.query(
      `update subject_categories set image = '${iconPrefix}category-space.svg', 
        colors='["#2EC2F7","#5E8BFF"]'
        where id = 3;`,
    )

    await queryRunner.query(
      `update subject_categories set image = '${iconPrefix}category-science.svg', 
        colors='["#A961FF","#FF8DD8"]'
        where id = 4;`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
