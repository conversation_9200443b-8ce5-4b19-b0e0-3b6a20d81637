import { MigrationInterface, QueryRunner } from 'typeorm'

export class UserAnswerCorrectCount1719384101425 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `alter table user_answer add column \`question_count\` INT DEFAULT NULL;`,
    )
    await queryRunner.query(
      `alter table user_answer add column \`correct_count\` INT DEFAULT NULL;`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
