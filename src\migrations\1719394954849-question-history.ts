import { MigrationInterface, QueryRunner } from 'typeorm'

export class QuestionHistory1719394954849 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE questions_history  (
  \`questions\` json NOT NULL,
  \`subject_id\` int NOT NULL,
  \`id\` int NOT NULL AUTO_INCREMENT,
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (\`id\`)
);`,
    )

    await queryRunner.query(
      `alter table user_answer
      ADD COLUMN \`questions_history_id\` int NULL,
ADD FOREIGN KEY (\`questions_history_id\`) REFERENCES \`questions_history\` (\`id\`) ON DELETE RESTRICT ON UPDATE RESTRICT;`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
