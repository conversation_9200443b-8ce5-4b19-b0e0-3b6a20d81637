import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTheme1720704965303 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE subject_categories SET name = '{\"en_uk\": \"Life and environment\", \"zh_cn\": \"生命与环境\", \"zh_HK\": \"生命與環境\"}' WHERE id = 1`,
    )

    await queryRunner.query(
      `UPDATE subject_categories SET name = '{\"en_uk\": \"Matter, energy and changes\", \"zh_cn\": \"物质、能量和变化\", \"zh_HK\": \"物質、能量和變化\"}' WHERE id = 2`,
    )

    await queryRunner.query(
      `UPDATE subject_categories SET name = '{\"en_uk\": \"Earth and Space\", \"zh_HK\": \"地球與太空\", \"zh_cn\": \"地球与太空\"}' WHERE id = 3;`,
    )

    await queryRunner.query(
      `UPDATE subject_categories SET name = '{\"en_uk\": \"Science, Technology, Engineering and Society\", \"zh_HK\": \"科學、科技、工程與社會\", \"zh_cn\": \"科学、科技、工程与社会\"}' WHERE id = 4`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Human health\", \"zh_HK\": \"人體健康\",\"zh_cn\":\"人体健康\"}' WHERE id = 1`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Characteristics of living things\", \"zh_HK\": \"生物的特性\",\"zh_cn\":\"生物的特性\"}' WHERE id = 2`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Life continuation\", \"zh_HK\": \"生命的延續\",\"zh_cn\":\"生命的延续\"}' WHERE id = 3`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Ecosystem\", \"zh_HK\": \"生態系統\",\"zh_cn\":\"生态系统\"}' WHERE id = 4`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"World under microscope\", \"zh_HK\": \"顯微鏡下的世界\",\"zh_cn\":\"显微镜下的世界\"}' WHERE id = 5`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Properties and transformation ( changes) of matter\", \"zh_HK\": \"物質的特性和變化\",\"zh_cn\":\"物质的特性和变化\"}' WHERE id = 6`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Different form of energy and transmission\", \"zh_HK\": \"能量的不同形式和傳遞\",\"zh_cn\":\"能量的不同形式和传递\"}' WHERE id = 7`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Force and motion\", \"zh_HK\": \"力和運動\",\"zh_cn\":\"力和运动\"}' WHERE id = 8`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Earth\\'s features and resources\", \"zh_HK\": \"地球的特徵和資源\",\"zh_cn\":\"地球的特征和资源\"}' WHERE id = 9`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Climate and seasons\", \"zh_HK\": \"氣候與季節\",\"zh_cn\":\"气候与季节\"}' WHERE id = 10`,
    )

    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Solar system in the Universe\", \"zh_HK\": \"宇宙中的太陽系\",\"zh_cn\":\"宇宙中的太阳系\"}' WHERE id = 11`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Scientific procedures and science spirit\", \"zh_HK\": \"科學過程和科學精神\",\"zh_cn\":\"科学过程和科学精神\"}' WHERE id = 12`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Aerospace and innovation technology\", \"zh_HK\": \"航天與創新科技\",\"zh_cn\":\"航天与创新科技\"}' WHERE id = 13`,
    )
    await queryRunner.query(
      `UPDATE themes SET name = '{\"en_uk\": \"Engineering and design\", \"zh_HK\": \"工程與設計\",\"zh_cn\":\"工程与设计\"}' WHERE id = 14`,
    )
  }

  public async down(): Promise<any> {}
}
