import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterScienceContracts1720734965303 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE science_contracts 
      ADD COLUMN grade_codes json NULL,
      ADD COLUMN updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      ADD COLUMN updated_by json NULL;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
      ALTER TABLE science_contracts 
      DROP COLUMN updated_by,
      DROP COLUMN updated_at,
      DROP COLUMN grade_codes;
    `)
  }
}
