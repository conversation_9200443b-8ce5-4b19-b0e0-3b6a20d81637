import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateUserAnswerCount1721182333883 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`user_answer_count\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`grade_id\` int NULL,
              \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
              \`user_id\` int NULL,
              \`user_class_id\` int NULL,
              \`school_id\` int NULL,
              \`subject_id\` int NULL,
              \`user_type\` varchar(255) NOT NULL,
              PRIMARY KEY (\`id\`),
              CONSTRAINT \`FK_007884ab9dff41bba65m621185e_users\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
              CONSTRAINT \`FK_8a2f1fbb37d91f64d68b673a9f8_user_class\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
              CONSTRAINT \`FK_6abf1e8f00d7d5c9fb917bb84f5_school\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`),
              CONSTRAINT \`FK_5e9d5a2d7f56d0c91b8922d5733_subject\` FOREIGN KEY (\`subject_id\`) REFERENCES \`subjects\`(\`id\`)
            ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
          `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`user_answer_count\``)
  }
}
