import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddPermissions1721717655626 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // ## 查看角色是否有设置过主页权限以及打勾过 查看全平台閱讀時數resource_id=18 过滤已经设置过的
    const roleIds = await queryRunner.query(`
          SELECT role_id
          FROM role_permissions
          WHERE permission_id IN (
              SELECT id FROM permissions WHERE resource_id = 18
          )
          GROUP BY role_id
          HAVING FIND_IN_SET(116, GROUP_CONCAT(permission_id)) = 0;
      `)

    // 构造批量插入语句
    if (roleIds.length > 0) {
      const values = roleIds
        .map((row: { role_id: number }) => `(116, ${row.role_id})`)
        .join(', ')

      const insertQuery = `INSERT INTO role_permissions (permission_id, role_id) VALUES ${values};`

      await queryRunner.query(insertQuery)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
