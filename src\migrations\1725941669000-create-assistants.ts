import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateAssistantTablesMigration1725941669000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`assistant\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`assistant_id\` varchar(255) NOT NULL,
                \`vector_store_id\` varchar(255) NOT NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        `)

    await queryRunner.query(`
      
          CREATE TABLE \`assistant_files\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`assistant_id\` varchar(255) NOT NULL,
              \`isbn\` varchar(255) NOT NULL,
              \`book_id\` int NOT NULL,
              \`aws_url\` varchar(255) NOT NULL,
              \`openai_file_id\` varchar(255) NOT NULL,
              \`vector_store_id\` varchar(255) NOT NULL,
              \`openai_vector_store_file_id\` varchar(255) NOT NULL,
              \`status\` varchar(50) NOT NULL,
              \`file_bytes\` int(25) NOT NULL,
              \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
              \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
              \`deleted_at\` datetime(6) DEFAULT NULL,
              \`created_by\` json DEFAULT NULL,
              \`updated_by\` json DEFAULT NULL,
              \`deleted_by\` json DEFAULT NULL,
              PRIMARY KEY (\`id\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

        `)
    await queryRunner.query(`
          CREATE TABLE \`assistant_thread\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`assistant_id\` varchar(255) NOT NULL,
            \`thread_id\` varchar(255) NOT NULL,
            \`user_id\` int NOT NULL,
            \`user_type\` varchar(50) NOT NULL,
            \`user_class_id\` int NOT NULL,
            \`grade_id\` int NOT NULL,
            \`school_id\` int NOT NULL,
            \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            PRIMARY KEY (\`id\`),
            CONSTRAINT \`FK_1a2b3c4d5e6f7g8h9i0j1k2l3\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
            CONSTRAINT \`FK_3x4y5z6a7b8c9d0e1f2g3h4g2\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
            CONSTRAINT \`FK_4i5j6k7l8m9n0o1p2q3r4s643\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

        `)
    await queryRunner.query(`
          CREATE TABLE \`assistant_thread_message_runs\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`assistant_id\` varchar(255) NOT NULL,
              \`thread_id\` varchar(255) NOT NULL,
              \`msg_id\` varchar(255) NOT NULL,
              \`run_id\` varchar(255) NOT NULL,
              \`user_id\` int NOT NULL,
              \`user_type\` varchar(50) NOT NULL,
              \`user_class_id\` int NOT NULL,
              \`grade_id\` int NOT NULL,
              \`school_id\` int NOT NULL,
              \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
              PRIMARY KEY (\`id\`),
              CONSTRAINT \`FK_7p8q9r0s1t2u3v4w5x6y7z2hd\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
              CONSTRAINT \`FK_8a9b0c1d2e3f4g5h6i7j8k7uy\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
              CONSTRAINT \`FK_9l0m1n2o3p4q5r6s7t8u9v86y\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        `)
    await queryRunner.query(`
          CREATE TABLE \`assistant_admin_error_response\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`assistant_id\` varchar(255) NOT NULL,
              \`file_id\` varchar(255) NOT NULL,
              \`isbn\` varchar(255) NOT NULL,
              \`error_msg\` json NOT NULL,
              \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
              PRIMARY KEY (\`id\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

        `)
    await queryRunner.query(`
          CREATE TABLE \`assistant_client_error_response\` (
            \`id\` int NOT NULL AUTO_INCREMENT,
            \`assistant_id\` varchar(255) NOT NULL,
            \`thread_id\` varchar(255) NOT NULL,
            \`run_id\` varchar(255) default NULL,
            \`msg_id\` varchar(255) default NULL,
            \`error_msg\` json NOT NULL,
            \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            PRIMARY KEY (\`id\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE \`assistant_client_error_response\`;
            DROP TABLE \`assistant_admin_error_response\`;
            DROP TABLE \`assistant_thread_message_runs\`;
            DROP TABLE \`assistant_thread\`;
            DROP TABLE \`assistant_files\`;
            DROP TABLE \`assistant\`;
        `)
  }
}
