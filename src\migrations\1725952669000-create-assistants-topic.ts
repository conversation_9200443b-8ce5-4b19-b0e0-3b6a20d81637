import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateAssistantTopicTablesMigration1725952669000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE \`assistant_topic\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`name\` json NOT NULL,
        \`all_name\` text GENERATED ALWAYS AS (CONCAT(name->'$.zh_HK', " ", name->'$.en_uk', " ", name->'$.zh_cn')),
        \`answer\` varchar(255) DEFAULT NULL,
        \`grades\` json NOT NULL,
        \`status\` varchar(255) DEFAULT 'online' COMMENT 'online | offline',
        \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        \`deleted_at\` datetime(6) DEFAULT NULL,
        \`online_at\` datetime DEFAULT NULL COMMENT '上架时间',
        \`offline_at\` datetime DEFAULT NULL COMMENT '下架时间',
        \`created_by\` json DEFAULT NULL,
        \`updated_by\` json DEFAULT NULL,
        \`deleted_by\` json DEFAULT NULL,
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE \`assistant_topic\`;
        `)
  }
}
