import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateAssistantSessionCountTablesMigration1725963769100
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE \`assistant_session_count\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`thread_id\` varchar(255) NOT NULL,
        \`user_id\` int NOT NULL,
        \`user_type\` varchar(50) NOT NULL,
        \`user_class_id\` int NOT NULL,
        \`grade_id\` int NOT NULL,
        \`school_id\` int NOT NULL,
        \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        PRIMARY KEY (\`id\`),
        CONSTRAINT \`FK_1a2b3c4d5e6f7g8h9i0j1k2lmn\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
        CONSTRAINT \`FK_2b3c4d5e6f7g8h9i0j1k2lmn3o\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
        CONSTRAINT \`FK_3c4d5e6f7g8h9i0j1k2lmn4op5\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`)
      ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
      `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE \`assistant_session_count\`;
        `)
  }
}
