import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateAssistantContractsTablesMigration1725963769100
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE \`assistant_contracts\` (
          \`id\` int NOT NULL AUTO_INCREMENT,
          \`contract_no\` varchar(255) NOT NULL,
          \`school_id\` int DEFAULT NULL,
          \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
          \`created_by\` json DEFAULT NULL,
          \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
          \`updated_by\` json DEFAULT NULL,
          PRIMARY KEY (\`id\`),
          KEY \`FK_007884ab8euu63bba94f9b7c85e\` (\`school_id\`),
          CONSTRAINT \`FK_007884ab8euu63bba94f9b7c85e\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\` (\`id\`)
        ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
      `)
    await queryRunner.query(
      `ALTER TABLE \`schools\` add column \`has_assistant\` tinyint(1) DEFAULT 0;`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        DROP TABLE \`assistant_contracts\`;

        ALTER TABLE schools DROP COLUMN has_assistant;
    `)
  }
}
