import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterAssistant1745234965303 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE assistant_session_count
        MODIFY grade_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_session_count
        MODIFY user_class_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_session_count
        MODIFY school_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread
        MODIFY grade_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread
        MODIFY user_class_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread
        MODIFY school_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread_message_runs
        MODIFY grade_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread_message_runs
        MODIFY user_class_id INT NULL;
      `)

    await queryRunner.query(`
        ALTER TABLE assistant_thread_message_runs
        MODIFY school_id INT NULL;
      `)
    await queryRunner.query(`
      ALTER TABLE assistant_files
      MODIFY status VARCHAR(50) default 'pending';
    `)
    await queryRunner.query(`
      ALTER TABLE assistant_files
      MODIFY aws_url VARCHAR(255) NULL;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
