import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterAssistantThreadMessage1747934965313 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE assistant_thread_message_runs MODIFY run_id varchar(255) NULL;
    `)

    await queryRunner.query(
      `ALTER TABLE assistant_thread_message_runs add COLUMN msg_type varchar(25) NULL;`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}
