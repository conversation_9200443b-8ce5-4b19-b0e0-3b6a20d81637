import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateReadingThink1764634962112 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`read_think\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`user_id\` int NULL,
              \`grade_id\` int NULL,
              \`user_class_id\` int NULL,
              \`school_id\` int NULL,
              \`book_id\` int NULL,
              \`user_type\` varchar(255) NOT NULL,
              \`review_state\` int NULL DEFAULT 0,
              \`flag_state\` int NULL DEFAULT 0,
              \`content\` text NOT NULL,
              \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
              \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
              \`deleted_at\` datetime(6) DEFAULT NULL,
              \`created_by\` json DEFAULT NULL,
              \`updated_by\` json DEFAULT NULL,
              \`deleted_by\` json DEFAULT NULL,
              PRIMARY KEY (\`id\`),
              CONSTRAINT \`FK_007364ab9df7o1bba65m61jk15e_users\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
              CONSTRAINT \`FK_4a2f1f2b37d91m64d6cb673a9f8_user_class\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
              CONSTRAINT \`FK_6auf1t8f00d7d5n9fb317bk81f6_school\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`),
              CONSTRAINT \`FK_5e3d5k3d7f54d8c91b8362d5y33_book\` FOREIGN KEY (\`book_id\`) REFERENCES \`books\`(\`id\`)
            ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
      `)

    await queryRunner.query(`
        CREATE TABLE \`reference_read_think\` (
          \`id\` int NOT NULL AUTO_INCREMENT,
          \`user_id\` int NULL,
          \`grade_id\` int NULL,
          \`user_class_id\` int NULL,
          \`school_id\` int NULL,
          \`book_id\` int NULL,
          \`user_type\` varchar(255) NOT NULL,
          \`review_state\` int NULL DEFAULT 0,
          \`flag_state\` int NULL DEFAULT 0,
          \`content\` text NOT NULL,
          \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
          \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
          \`deleted_at\` datetime(6) DEFAULT NULL,
          \`created_by\` json DEFAULT NULL,
          \`updated_by\` json DEFAULT NULL,
          \`deleted_by\` json DEFAULT NULL,
          PRIMARY KEY (\`id\`),
          CONSTRAINT \`FK_00ref4ab9df7o1bba65m61jk15e_users\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
          CONSTRAINT \`FK_4a2f1ref37d91m64d6cb673a9f8_user_class\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
          CONSTRAINT \`FK_6auf1t8fref7d5n9fb317bk81f6_school\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`),
          CONSTRAINT \`FK_5e3d5k3ref54d8c91b8362d5y33_book\` FOREIGN KEY (\`book_id\`) REFERENCES \`books\`(\`id\`)
        ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
      `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`read_think\``)
    await queryRunner.query(`DROP TABLE \`reference_read_think\``)
  }
}
