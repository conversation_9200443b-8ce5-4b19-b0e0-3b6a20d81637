import { MigrationInterface, QueryRunner } from 'typeorm'

export class RenameReadingThinkTables1764634963002 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      RENAME TABLE read_think TO reading_reflection;
    `)
    await queryRunner.query(`
      RENAME TABLE reference_read_think TO reference_reading_reflection;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      RENAME TABLE reading_reflection TO read_think;
    `)
    await queryRunner.query(`
      RENAME TABLE reference_reading_reflection TO reference_read_think;
    `)
  }
}
