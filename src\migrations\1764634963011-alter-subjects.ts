import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterSubjectsTables1764634963011 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE subjects ADD COLUMN videos json NULL;
    `)

    await queryRunner.query(`
      UPDATE subjects SET videos = JSON_ARRAY(video) WHERE video IS NOT NULL AND video != '';
    `)

    await queryRunner.query(
      `CREATE TABLE \`subject_extend_books\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`subject_id\` int NOT NULL,
        \`book_id\` int NOT NULL,
        \`from_book_pos\` json DEFAULT NULL,
        \`to_book_pos\` json DEFAULT NULL,
        PRIMARY KEY (\`id\`),
        CONSTRAINT \`FK_5pmo3c4b636f7gek9i0j1k2lmn\` FOREIGN KEY (\`subject_id\`) REFERENCES \`subjects\`(\`id\`),
        CONSTRAINT \`FK_2tyc4u5e6iuy39ki0j1k2lmr3o\` FOREIGN KEY (\`book_id\`) REFERENCES \`books\`(\`id\`)
      ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
      `,
    )

    // 将 `subjects` 表中的数据迁移到 `subjects_extend_books` 表
    await queryRunner.query(`
      INSERT INTO \`subject_extend_books\` (\`subject_id\`, \`book_id\`, \`from_book_pos\`, \`to_book_pos\`)
      SELECT 
        id AS subject_id, 
        book_id, 
        from_book_pos, 
        to_book_pos
      FROM subjects
      WHERE book_id IS NOT NULL;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    `)
  }
}
