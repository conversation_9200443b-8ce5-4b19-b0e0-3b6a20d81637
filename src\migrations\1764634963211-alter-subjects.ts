import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterSubjectsTables1764634963211 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE subjects 
      ADD COLUMN online_at datetime(6) NULL,
      ADD COLUMN offline_at datetime(6) NULL;

    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    `)
  }
}
