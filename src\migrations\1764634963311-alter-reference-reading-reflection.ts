import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterReferenceReadingReflectionTables1764634963311
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE reference_reading_reflection 
      ADD COLUMN audio_url varchar(255) NULL,
      MODIFY content TEXT NULL;
    `)
    await queryRunner.query(`
      ALTER TABLE reading_reflection 
      ADD COLUMN audio_url varchar(255) NULL,
      MODIFY content TEXT NULL;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    `)
  }
}
