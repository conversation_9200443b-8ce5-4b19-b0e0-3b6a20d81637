import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterReferenceReadingReflectionTables1764634963666
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE reference_reading_reflection 
      ADD COLUMN audio_time int(8) NULL;
    `)
    await queryRunner.query(`
      ALTER TABLE reading_reflection 
      ADD COLUMN audio_time int(8) NULL;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    `)
  }
}
