import { MigrationInterface, QueryRunner } from 'typeorm'
export class AlterGradesTables1764634964766 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`grades\` DROP INDEX \`IDX_4e77c388344206307642ed4d0a\`;
    `)

    await queryRunner.query(`
      ALTER TABLE \`grades\` ADD UNIQUE KEY \`IDX_4e77c388344206307642ed4d0a\` (\`grade\`, \`school_id\`, \`grade_code\`);
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(``)
  }
}
