import { MigrationInterface, QueryRunner } from 'typeorm'

export class AlterV2Assistant1764634964866 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Ai套餐
    await queryRunner.query(`
      ALTER TABLE assistant
        ADD COLUMN  \`name\` json DEFAULT NULL,
        MODIFY COLUMN  \`vector_store_id\` varchar(255) DEFAULT NULL,
        MODIFY COLUMN  \`assistant_id\` varchar(255) DEFAULT NULL,
        ADD COLUMN  \`status\` varchar(255) DEFAULT 'DRAFT',
        ADD COLUMN  \`preferred_version\` varchar(255) DEFAULT NULL,
        ADD COLUMN  \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        ADD COLUMN  \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        ADD COLUMN  \`deleted_at\` datetime(6) DEFAULT NULL,
        ADD COLUMN  \`created_by\` json DEFAULT NULL,
        ADD COLUMN  \`updated_by\` json DEFAULT NULL,
        ADD COLUMN  \`deleted_by\` json DEFAULT NULL,
        ADD COLUMN  \`all_name\` varchar(255) GENERATED ALWAYS AS (CONCAT(name->'$.zh_HK', " ", name->'$.en_uk', " ", name->'$.zh_cn'))
    `)
    await queryRunner.query(`
      UPDATE assistant
      SET name = JSON_OBJECT('zh_HK', '文心智友', 'en_uk', '文心智友', 'zh_cn', '文心智友'),
          status = 'ONLINE'
      WHERE id = 1;
    `)
    /**   assistant_files filename字段变更    */
    await queryRunner.query(
      `ALTER TABLE assistant_files ADD COLUMN file_name_tmp JSON DEFAULT NULL;`,
    )
    await queryRunner.query(`
      UPDATE assistant_files af
      INNER JOIN books b ON af.book_id = b.id
      SET af.file_name_tmp = b.name
      WHERE af.book_id IS NOT NULL;
    `)
    await queryRunner.query(`ALTER TABLE assistant_files DROP COLUMN file_name;`)
    await queryRunner.query(
      `ALTER TABLE assistant_files CHANGE COLUMN file_name_tmp file_name JSON DEFAULT NULL;`,
    )

    // 添加 version 字段
    await queryRunner.query(`
      ALTER TABLE assistant_files ADD COLUMN version varchar(255) DEFAULT NULL;
    `)
    await queryRunner.query(`
      UPDATE assistant_files af
      INNER JOIN books b ON af.book_id = b.id
      SET af.version = b.version
      WHERE af.book_id IS NOT NULL;
    `)
    /**   assistant_files filename字段变更    */

    // 更新 assistant_contracts 表
    await queryRunner.query(`
      ALTER TABLE assistant_contracts ADD COLUMN assistant_id varchar(255) DEFAULT NULL;
    `)
    await queryRunner.query(`
      UPDATE assistant_contracts 
      SET assistant_id = (
        SELECT assistant_id 
        FROM assistant 
        WHERE id = 1
      );
    `)

    // 更新 assistant_topic表
    await queryRunner.query(`
      ALTER TABLE assistant_topic ADD COLUMN assistants json NOT NULL;
    `)
    await queryRunner.query(`
      UPDATE assistant_topic
      SET assistants = JSON_ARRAY(
        (
          SELECT assistant_id 
          FROM assistant 
          WHERE id = 1
        )
      );
    `)

    // 更新 assistant_session_count 表
    await queryRunner.query(`
      ALTER TABLE assistant_session_count ADD COLUMN assistant_id varchar(255) DEFAULT NULL;
    `)
    await queryRunner.query(`
      UPDATE assistant_session_count 
      SET assistant_id = (
        SELECT assistant_id 
        FROM assistant 
        WHERE id = 1
      );
    `)

    // 套餐文件
    await queryRunner.query(`
      CREATE TABLE \`assistant_vectorstore_files\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`assistant_number_id\` int NOT NULL,
        \`assistant_id\` varchar(255) DEFAULT NULL,
        \`vector_store_id\` varchar(255) DEFAULT NULL,
        \`openai_file_id\` varchar(255) NOT NULL,
        \`status\` varchar(255) DEFAULT 'DRAFT',
        \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        \`deleted_at\` datetime(6) DEFAULT NULL,
        \`created_by\` json DEFAULT NULL,
        \`updated_by\` json DEFAULT NULL,
        \`deleted_by\` json DEFAULT NULL,
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    `)

    // 套餐文件 批量操作id
    await queryRunner.query(`
      CREATE TABLE \`assistant_vectorstore_files_batch\` (
        \`id\` int NOT NULL AUTO_INCREMENT,
        \`batch_id\` varchar(255) NOT NULL,
        \`assistant_id\` varchar(255) NOT NULL,
        \`vector_store_id\` varchar(255) NOT NULL,
        \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        \`deleted_at\` datetime(6) DEFAULT NULL,
        \`created_by\` json DEFAULT NULL,
        \`updated_by\` json DEFAULT NULL,
        \`deleted_by\` json DEFAULT NULL,
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    `)

    await queryRunner.query(`
      INSERT INTO assistant_vectorstore_files (
        assistant_number_id,
        assistant_id,
        vector_store_id,
        openai_file_id,
        status,
        created_at,
        created_by,
        updated_at,
        updated_by
      )
      SELECT 
        1 as assistant_number_id,
        assistant_id,
        vector_store_id,
        openai_file_id,
        status,
        created_at,
        created_by,
        updated_at,
        updated_by
      FROM assistant_files
      WHERE openai_file_id IS NOT NULL  AND openai_file_id != '';
    `)

    // assistant删除字段并更新状态
    await queryRunner.query(`
      ALTER TABLE assistant_files
        MODIFY COLUMN book_id int DEFAULT NULL,
        MODIFY COLUMN isbn varchar(255) DEFAULT NULL,
        DROP COLUMN assistant_id,
        DROP COLUMN vector_store_id;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(``)
  }
}
