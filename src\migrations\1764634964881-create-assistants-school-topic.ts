import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateAssistantSchoolTopicTablesMigration1764634964881
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE \`assistant_school_topic\` (
          \`id\` int NOT NULL AUTO_INCREMENT,
          \`name\` json NOT NULL,
          \`school_id\` int NOT NULL,
          \`all_name\` text GENERATED ALWAYS AS (CONCAT(name->'$.zh_HK', " ", name->'$.en_uk', " ", name->'$.zh_cn')),
          \`answer\` varchar(255) DEFAULT NULL,
          \`user_type\` varchar(255) DEFAULT 'all',
          \`grade_ids\` json NULL,
          \`class_ids\` json NULL,
          \`status\` varchar(255) DEFAULT 'online' COMMENT 'online | offline',
          \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
          \`updated_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
          \`deleted_at\` datetime(6) DEFAULT NULL,
          \`online_at\` datetime DEFAULT NULL COMMENT '上架时间',
          \`offline_at\` datetime DEFAULT NULL COMMENT '下架时间',
          \`created_by\` json DEFAULT NULL,
          \`updated_by\` json DEFAULT NULL,
          \`deleted_by\` json DEFAULT NULL,
          PRIMARY KEY (\`id\`)
        ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;`)

    await queryRunner.query(
      `CREATE TABLE \`assistant_school_topic_count\` (
          \`id\` int NOT NULL AUTO_INCREMENT,
          \`assistant_id\` varchar(255) NOT NULL,
          \`thread_id\` varchar(255) NOT NULL,
          \`topic_id\` varchar(255) NOT NULL,
          \`user_id\` int NOT NULL,
          \`user_type\` varchar(50) NOT NULL,
          \`user_class_id\` int NULL,
          \`grade_id\` int NULL,
          \`school_id\` int NULL,
          \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
          PRIMARY KEY (\`id\`),
          CONSTRAINT \`FK_698o3c4d5e6f7g8h1bsj1k2lmn\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`),
          CONSTRAINT \`FK_2b3c4d5elv0y559i0j1k2lmn3o\` FOREIGN KEY (\`user_class_id\`) REFERENCES \`user_class\`(\`id\`),
          CONSTRAINT \`FK_3c4d5e4kty7h9i0j1k2l5n4op5\` FOREIGN KEY (\`school_id\`) REFERENCES \`schools\`(\`id\`)
        ) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        `,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(``)
  }
}
