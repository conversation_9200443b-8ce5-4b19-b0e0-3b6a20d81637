import { ELocaleType } from '../../common'

export const VERIFIC<PERSON>IONCODE_PREFIX = 'verification'
export const VERIFICATIONCODE_60_SECONDS_ACESS_CHECK_PREFIX =
  'verification.access.60.check'

export const VERIFICATIONCODE_300_SECONDS_ACESS_CHECK_PREFIX =
  'verification.access.300.check'

export const VERIFICATIONCODE_120_SECONDS_ACESS_CHECK_PREFIX =
  'verification.access.120.check'

export const VERIFICATIONCODE_360_SECONDS_ACESS_CHECK_PREFIX =
  'verification.access.360.check'

export const VERIFICATIONCODE_1800_SECONDS_ACESS_CHECK_PREFIX =
  'verification.access.1800.check'

export const TICKET_PREFIX = 'ticket'

export const PUBLISHER_PERMISSION = 'publisher:permission'

export const wrong_password_message = '密码应是8位至32位，包含数学与字母'

export const SCHOOL_ROLE_ID_SEQ = 'school_role_id_seq'
export const SCHOOL_ROLE_ID_PREFIX = 'RL'
export const SCHOOL_PERMISSION_ID_SEQ = 'school_permission_id_seq'
export const SCHOOL_PERMISSION_ID_PREFIX = 'PE'

export const LANG_YES = {
  [ELocaleType.ZH_HK]: `是`,
  [ELocaleType.ZH_CN]: `是`,
  [ELocaleType.EN_UK]: `Yes`,
}
export const LANG_NO = {
  [ELocaleType.ZH_HK]: `否`,
  [ELocaleType.ZH_CN]: `否`,
  [ELocaleType.EN_UK]: `No`,
}
