import { Body, Controller, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import R from 'ramda'
import {
  ApiBaseResult,
  AuthSchema,
  BooleanResponse,
  CurrentPlatform,
  CurrentPlatformHeader,
  ELoginMethod,
  EPlatform,
  JwtService,
  PublicAuth,
} from '@/common'
import { SchoolAdministrator } from '@/entities'
import { EAdministratorStatus, ETicketType, EVerificationCodeType } from '@/enums'
import { IAssistantContractsService } from '@/modules/shared/interfaces'
import { SchoolNotExistException } from '@/modules/schools/exception'
import { OperationLogService } from '@/modules/system'
import {
  ForgetPasswordRequest,
  GenerateLoginTicketRequest,
  LoginByTicket,
  LoginDto,
  LoginResponse,
  RefreshTokenRequest,
  SchoolAdministratorResponse,
  SendEmailVerificationCodeDto,
  SendVerificationCodeResponse,
  TicketResponse,
} from '../dto'
import {
  ETicketCredentialType,
  GetTicketDto,
  GetTicketResponse,
  TicketEmailCredential,
} from '../dto/ticket.dto'
import { AdministratorStatusError } from '../exception'
import { SchoolAdminRepository } from '../repositories'
import { SchoolAdministratorService, TokenService } from '../services'
import { validateInput, VerificationCodeUtil } from '../utils'
import { TicketUtil } from '../utils/ticket.util'

@ApiTags('Account')
@ApiExtraModels(
  SchoolAdministratorResponse,
  LoginResponse,
  TicketResponse,
  SendVerificationCodeResponse,
  BooleanResponse 
)
@Controller('/v1/public')
export class SchoolAdministratorPublicController {
  constructor(
    private readonly schoolAdministratorService: SchoolAdministratorService,
    private readonly schoolAdminRepository: SchoolAdminRepository,
    private readonly verificationCodeUtil: VerificationCodeUtil,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly ticketUtil: TicketUtil,
    private readonly logService: OperationLogService,
    private readonly assistantContractsService: IAssistantContractsService
  ) {}

  @PublicAuth()
  @ApiBaseResult(LoginDto, 201, 'Pre-login Steps')
  @ApiOperation({ summary: 'Pre-login Steps' })
  @CurrentPlatformHeader()
  @Post('/schools/administrators/email/pre-login')
  async generateLoginTicket(
    @Body() body: GenerateLoginTicketRequest,
    @CurrentPlatform() platform: EPlatform = EPlatform.WEB
  ) {
    const { ticket, user } = await this.schoolAdministratorService.generateLoginTicket(
      body
    )

    if (!ticket) {
      return this.generateToken(user, platform)
    }
    return {
      ticket,
    }
  }

  @PublicAuth()
  @ApiBaseResult(SendVerificationCodeResponse, 201, 'Send verification code by email')
  @ApiOperation({ summary: 'Send verification code by email ' })
  @Post('send-verifications')
  async sendVerificationCodeByEmail(@Body() body: SendEmailVerificationCodeDto) {
    await this.schoolAdminRepository.findOne({ where: { email: body.identify  } })
    await this.verificationCodeUtil.sendVerificationCodeByEmail(body)
    return { status: true }
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Login by ticket')
  @ApiOperation({ summary: 'Login by ticket' })
  @Post('/schools/administrators/email/login')
  async loginByTicket(@Body() body: LoginByTicket) {
    const { ticket, verificationCode } = body

    const payload = this.jwtService.verify(ticket)
    const { email, platform } = payload as any

    await this.verificationCodeUtil.checkVerificationCode(
      EVerificationCodeType.LOGIN,
      email,
      verificationCode
    )
    const user = await this.schoolAdminRepository.findOne({
      where: { email },
      relations: ['school', 'roles'],
    })

    if (user.status === EAdministratorStatus.INACTIVE)
    {throw new AdministratorStatusError()}

    await this.ticketUtil.consume(ETicketType.LOGIN_BY_EMAIL, ticket)

    if (!user.school) {
      throw new SchoolNotExistException()
    }

    return this.generateToken(user, platform)
  }

  @PublicAuth()
  @ApiBaseResult(GetTicketResponse, 201, 'Get access ticket')
  @ApiOperation({
    summary: 'Get access ticket',
    description: `
  access ticket 适用于多步骤鉴权的场景，比如
    - 重置密码前，旧密码鉴权；
    - 忘记密码重置密码前 email + verification code 鉴权；
    - 删除账户前，三方登陆鉴权
    - Email 中链接跳转登陆等
    - 类似场景，均是先拿到ticket， 再用ticket去完成最终的权限校验
  `,
  })
  @Post('/schools/administrators/tickets')
  async getPasswordResetTicket(@Body() body: GetTicketDto): Promise<GetTicketResponse> {
    const { credential, credentialType, ticketType } = body

    const ValidationSchemas = {
      [ETicketCredentialType.EMAIL]: TicketEmailCredential,
    }

    await validateInput(credential, ValidationSchemas[credentialType])

    if (ticketType === ETicketType.FORGET_PASSWORD) {
      const { email } = credential
      const ticket = await this.schoolAdministratorService.generateTicket({
        credential,
        credentialType,
        email,
        ticketType,
      })
      return { ticket }
    }
  }

  @PublicAuth()
  @ApiBaseResult(BooleanResponse, 201, 'Reset password')
  @ApiOperation({ summary: 'Reset password' })
  @Post('/schools/administrators/password/reset')
  async resetPassword(@Body() body: ForgetPasswordRequest) {
    const status = await this.schoolAdministratorService.forgetPassword(body)
    return {
      status,
    }
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Refresh token')
  @ApiOperation({ summary: 'Refresh token' })
  @CurrentPlatformHeader()
  @Post('accounts/school-administrators/token/refresh')
  async refreshToken(
    @Body() body: RefreshTokenRequest,
    @CurrentPlatform() platform: EPlatform
  ) {
    const { refreshToken } = body

    return this.tokenService.retrieveAccessTokenByRefreshToken(
      refreshToken,
      platform,
      AuthSchema.SCHOOL_ADMIN
    )
  }

  private async generateToken(user: SchoolAdministrator, platform: EPlatform) {
    const assistantContract = user.school.hasAssistant
      ? await this.assistantContractsService.getLastContract(user.school.id)
      : null
    const accessToken = await this.tokenService.createAccessToken(
      AuthSchema.SCHOOL_ADMIN,
      platform,
      {
        id: user.userId,
        loginMethod: ELoginMethod.EMAIL,
        userId: user.id,
        profileImage: user.profileImage,
        familyName: user.familyName,
        givenName: user.givenName,
        displayName: user.displayName,
        email: user.email,
        schoolId: user.school.id,
        isRoot: user.isRoot,
        studentEmailSuffix: user.school.studentEmailSuffix,
        teacherEmailSuffix: user.school.teacherEmailSuffix,
        adminPanelUrl: user.school.adminPanelUrl,
        logo: user.school.logo,
        schoolName: user.school.name,
        staffLevelIds: user.school.staffLevelIds,
        studentLevelIds: user.school.studentLevelIds,
        isAllLevelForStaff: user.school.isAllLevelForStaff,
        isAllLevelForStudent: user.school.isAllLevelForStudent,
        webUrl: user.school.webUrl,
        roleName: user?.roles?.pop()?.title,
        version: user.school.version,
        hasScienceRoom: user.school.hasScienceRoom,
        hasAssistant: user.school.hasAssistant,
        assistantId: assistantContract?.assistant?.assistantId,

        // permissions: R.flatten(
        //   user.roles?.map((role) =>
        //     role.permissions?.map((permission) => permission.key),
        //   ),
        // ),
      }
    )
    await this.schoolAdminRepository.updateSchoolAdmin(user.id, {
      lastLoginAt: new Date(),
    })
    if (R.isNil(user.lastLoginAt)) {
      const userId = user.id
      await this.logService.createLog({
        user: { ...user, userId },
        metaData: { adminId: user.id },
        operation: `${user.email}接受邀請，成為(${user.school.name.zh_HK})的${
          user.isRoot ? '超級' : ''
        }管理員`,
      })
    }
    const refreshToken = await this.tokenService.createRefreshToken(
      user.userId,
      accessToken,
      AuthSchema.SCHOOL_ADMIN
    )

    return { accessToken, refreshToken }
  }
}
