import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  CurrentSchoolAdmin,
  CurrentUser,
  SchoolAdminAuth,
} from '@/common'
import { SchoolPermission, SchoolRole } from '@/entities'
import { OperationLogService } from '@/modules/system'
import { ESchoolVersion } from '../../../enums'
import { ISchoolService } from '@/modules/shared/interfaces'
import {
  DeleteMultipleSchoolRolesRequest,
  PatchMultipleSchoolRoleStatusResponse,
} from '../dto'
import {
  CreateSchoolRoleRequest,
  GenerateRoleIdResponse,
  ListSchoolRolesRequest,
  PatchSchoolRoleRequest,
} from '../dto/schoolRole.dto'
import { SchoolRoleService } from '../services'

@ApiTags('ACL SchoolRole - Admin')
@ApiExtraModels(
  SchoolRole,
  SchoolPermission,
  GenerateRoleIdResponse,
  PatchMultipleSchoolRoleStatusResponse,
)
@Controller('v1/school-admin/')
export class SchoolRoleSchoolAdminController {
  constructor(
    private readonly schoolRoleService: SchoolRoleService,
    private readonly logService: OperationLogService,
    private readonly schoolService: ISchoolService,
  ) {}

  // @ApiOperation({ summary: 'Generate role id - School Admin' })
  // @SchoolAdminAuth()
  // @ApiBaseResult(GenerateRoleIdResponse, 201, 'Generate role id - School Admin')
  // @Post('roles/pre-role-id')
  // async getPreRoleId(@CurrentUser('schoolId') schoolId: number) {
  //   const roleId = await this.schoolRoleService.generateRoleId(Number(schoolId))
  //   return { roleId }
  // }

  @ApiOperation({ summary: 'Create school role' })
  @SchoolAdminAuth()
  @ApiBaseResult(SchoolRole, 201, 'Create school role')
  @Post('roles')
  async createSchoolRole(
    @Body() body: CreateSchoolRoleRequest,
    @CurrentUser() user: any,
  ) {
    return this.schoolRoleService.create(body, {
      operator: user,
      schoolId: Number(user.schoolId),
    })
  }

  @ApiOperation({ summary: 'Patch school role' })
  @SchoolAdminAuth()
  @ApiBaseResult(SchoolRole, 200, 'Patch school role')
  @Patch('roles/:id')
  async patchSchoolRole(
    @Param('id') id: number,
    @Body() body: PatchSchoolRoleRequest,
    @CurrentUser() user: any,
  ) {
    return this.schoolRoleService.patch(Number(id), body, {
      operator: user,
    })
  }

  @ApiOperation({ summary: 'Delete school role' })
  @SchoolAdminAuth()
  @ApiBaseResult(SchoolRole, 200, 'Delete school role')
  @Delete('roles/:id')
  async deleteSchoolRole(@Param('id') id: number) {
    return this.schoolRoleService.delete(id)
  }

  @SchoolAdminAuth()
  @ApiBaseResult(
    PatchMultipleSchoolRoleStatusResponse,
    201,
    'Multiple delete school administrator',
  )
  @ApiOperation({ summary: 'Multiple delete school administrator ' })
  @Post('/roles/multiple-delete')
  async deleteUser(
    @CurrentSchoolAdmin() admin: any,
    @Body() data: DeleteMultipleSchoolRolesRequest,
  ) {
    const roles = await this.schoolRoleService.deleteMultipleSchoolRoles(
      admin.schoolId,
      data,
    )

    await this.logService.createLog({
      operation: `${roles.length > 3 ? `批量删除` : '删除角色'}${roles
        .slice(0, 3)
        .map((item) => `“${item.title}”`)
        .join(',')} ${roles.length > 3 ? `等${roles.length}個角色` : ''}`,
      metaData: { input: data, roles },
      user: admin,
    })

    return {
      items: roles,
    }
  }

  @ApiOperation({ summary: 'List school role' })
  @SchoolAdminAuth()
  @ApiPageResult(SchoolRole, 200, 'List school role')
  @Get('roles')
  async listSchoolRoles(
    @Query() query: ListSchoolRolesRequest,
    @CurrentUser() user: any,
  ) {
    return this.schoolRoleService.list(query, {
      schoolId: Number(user.schoolId),
    })
  }

  @ApiOperation({ summary: 'Get school role' })
  @SchoolAdminAuth()
  @ApiBaseResult(SchoolRole, 200, 'Delete school role')
  @Get('roles/:id')
  async getSchoolRole(@Param('id') id: number) {
    return this.schoolRoleService.findOne({
      where: { id },
      relations: ['permissions', 'permissions.parent', 'permissions.parent.parent'],
    })
  }

  @ApiOperation({ summary: 'FindAll school permission' })
  @SchoolAdminAuth()
  @ApiListResult(SchoolPermission, 200, 'FindAll school permission')
  @Get('permissions')
  async findAllSchoolPermissions(@CurrentUser() user: any) {
    let allP = await this.schoolRoleService.findAllPermissions()
    const school = await this.schoolService.findSchools([user.schoolId])
    if (!school[0]?.hasScienceRoom) {
      allP = allP
        .filter((v) => {
          return v.key != 'scienceroom'
        })
        .map((v) => {
          if (v.key == 'dashboard') {
            v.children = v.children.filter(
              (c) =>
                ![
                  'dashboard:hotTopicsTOP10',
                  'dashboard:engagementData',
                  'dashboard:engagementDetails',
                ].includes(c.key),
            )
          }
          return v
        })
    }
    if (!school[0]?.version?.includes(ESchoolVersion.REFERENCE)) {
      allP = allP.map((v) => {
        if (v.key == 'dashboard') {
          v.children = v.children.filter(
            (c) =>
              ![
                'dashboard:reading:counts:top10',
                'dashboard:reading:users',
                'dashboard:reading:detail:user',
              ].includes(c.key),
          )
        }
        return v
      })
    }
    return allP
  }

  @ApiOperation({ summary: 'initSchoolRoles' })
  @AdminAuth()
  @ApiBaseResult(SchoolRole, 201, 'initSchoolRoles')
  @Post('initSchoolRoles')
  async initSchoolRoles() {
    return this.schoolRoleService.initRoles()
  }

  @ApiOperation({ summary: 'InitSchoolPermissions' })
  @AdminAuth()
  @ApiBaseResult(SchoolRole, 201, 'InitSchoolPermissions')
  @Post('initSchoolPermissions')
  async initSchoolPermissions() {
    const data = {
      dashboard: {
        key: 'dashboard',
        name: {
          zh_HK: '主頁',
          en_uk: '主頁',
        },
        children: [
          {
            key: 'dashboard:readingTimeTop10',
            name: {
              zh_HK: '閱讀時間Top10',
              en_uk: '閱讀時間Top10',
            },
            children: [
              {
                key: 'dashboard:readingTimeTop10:view',
                name: {
                  zh_HK: '查看閱讀時間Top10',
                  en_uk: '查看閱讀時間Top10',
                },
              },
              {
                key: 'dashboard:readingTimeTop10:view&dashboard:readingTimeTop10:export',
                name: {
                  zh_HK: '查看閱讀時間Top10並導出數據',
                  en_uk: '查看閱讀時間Top10並導出數據',
                },
              },
            ],
          },
          {
            key: 'dashboard:readingTimeDistribution',
            name: {
              zh_HK: '閱讀時數分佈',
              en_uk: '閱讀時數分佈',
            },
            children: [
              {
                key: 'dashboard:readingTimeDistribution:forUser:view',
                name: {
                  zh_HK: '查看閱讀人數分佈數據',
                  en_uk: '查看閱讀人數分佈數據',
                },
              },
              {
                key: 'dashboard:readingTimeDistribution:forUser:view&dashboard:readingTimeDistribution:forUser:export',
                name: {
                  zh_HK: '查看閱讀人數分佈數據及下載數據',
                  en_uk: '查看閱讀人數分佈數據及下載數據',
                },
              },
              {
                key: 'dashboard:readingTimeDistribution:forTime:view',
                name: {
                  zh_HK: '查看閱讀時長分佈數據',
                  en_uk: '查看閱讀時長分佈數據',
                },
              },
              {
                key: 'dashboard:readingTimeDistribution:forTime:view&dashboard:readingTimeDistribution:forTime:export',
                name: {
                  zh_HK: '查看閱讀時長分佈數據及下載數據',
                  en_uk: '查看閱讀時長分佈數據及下載數據',
                },
              },
            ],
          },
          {
            key: 'dashboard:readingDetail',
            name: {
              zh_HK: '閱讀詳細',
              en_uk: '閱讀詳細',
            },
            children: [
              {
                key: 'dashboard:readingDetail:forAccount:view',
                name: {
                  zh_HK: '查看用戶閱讀詳細數據',
                  en_uk: '查看用戶閱讀詳細數據',
                },
              },
              {
                key: 'dashboard:readingDetail:forAccount:view&dashboard:readingDetail:forAccount:export',
                name: {
                  zh_HK: '查看用戶閱讀詳細數據及下載數據',
                  en_uk: '查看用戶閱讀詳細數據及下載數據',
                },
              },
              {
                key: 'dashboard:readingDetail:forBook:view',
                name: {
                  zh_HK: '查看書籍閱讀詳細數據',
                  en_uk: '查看書籍閱讀詳細數據',
                },
              },
              {
                key: 'dashboard:readingDetail:forBook:view&dashboard:readingDetail:forBook:export',
                name: {
                  zh_HK: '查看書籍閱讀詳細數據及下載數據',
                  en_uk: '查看書籍閱讀詳細數據及下載數據',
                },
              },
            ],
          },
          {
            key: 'dashboard:hotTopicsTOP10',
            name: { zh_HK: '最受歡迎課題 TOP 10', en_uk: '最受歡迎課題 TOP 10' },
            children: [
              {
                key: 'dashboard:hotTopicsTOP10:view',
                name: {
                  zh_HK: '查看最受歡迎課題 TOP 10',
                  en_uk: '查看最受歡迎課題 TOP 10',
                },
              },
              {
                key: 'dashboard:hotTopicsTOP10:view&dashboard:hotTopicsTOP10:export',
                name: {
                  zh_HK: '查看最受歡迎課題 TOP 10並導出數據',
                  en_uk: '查看最受歡迎課題 TOP 10並導出數據',
                },
              },
            ],
          },
          {
            key: 'dashboard:engagementData',
            name: { zh_HK: '互動數據', en_uk: '互動數據' },
            children: [
              {
                key: 'dashboard:engagementDataOverview:view',
                name: { zh_HK: '查看互動數據概覽', en_uk: '查看互動數據概覽' },
              },
              {
                key: 'dashboard:theNumberOfEngagement(Users)Distribution:view',
                name: { zh_HK: '查看互動人數分佈', en_uk: '查看互動人數分佈' },
              },
              {
                key: 'dashboard:theNumberOfEngagement(Users)Distribution:view&dashboard:theNumberOfEngagement(Users)Distribution:export',
                name: {
                  zh_HK: '查看互動人數分佈並導出數據',
                  en_uk: '查看互動人數分佈並導出數據',
                },
              },
              {
                key: 'dashboard:theNumberOfEngagement(Counts)Distribution:view',
                name: { zh_HK: '查看互動人次分佈', en_uk: '查看互動人次分佈' },
              },
              {
                key: 'dashboard:theNumberOfEngagement(Counts)Distribution:view&dashboard:theNumberOfEngagement(Counts)Distribution:export',
                name: {
                  zh_HK: '查看互動人次分佈並導出數據',
                  en_uk: '查看互動人次分佈並導出數據',
                },
              },
            ],
          },
          {
            key: 'dashboard:engagementDetails',
            name: { zh_HK: '互動詳細', en_uk: '互動詳細' },
            children: [
              {
                key: 'dashboard:engagementDetails-Users:view',
                name: { zh_HK: '查看互動詳細-用戶', en_uk: '查看互動詳細-用戶' },
              },
              {
                key: 'dashboard:engagementDetails-Users:view&dashboard:engagementDetails-Users:export',
                name: {
                  zh_HK: '查看互動詳細-用戶並導出數據',
                  en_uk: '查看互動詳細-用戶並導出數據',
                },
              },
              {
                key: 'dashboard:engagementDetails-Topics:view',
                name: { zh_HK: '查看互動詳細-課題', en_uk: '查看互動詳細-課題' },
              },
              {
                key: 'dashboard:engagementDetails-Topics:view&dashboard:engagementDetails-Topics:export',
                name: {
                  zh_HK: '查看互動詳細-課題並導出數據',
                  en_uk: '查看互動詳細-課題並導出數據',
                },
              },
            ],
          },
        ],
      },
      account: {
        key: 'account',
        name: {
          zh_HK: '用戶管理',
          en_uk: '用戶管理',
        },
        children: [
          {
            key: 'account:accountAdd',
            name: {
              zh_HK: '添加用戶',
              en_uk: '添加用戶',
            },
            children: [
              {
                key: 'account:accountAdd:single',
                name: {
                  zh_HK: '單個添加用戶',
                  en_uk: '查看閱讀時間 Top10',
                },
              },
              {
                key: 'account:accountAdd:multiple',
                name: {
                  zh_HK: '批量添加用戶',
                  en_uk: '批量添加用戶',
                },
              },
            ],
          },
          {
            key: 'account:student',
            name: {
              zh_HK: '學生用戶',
              en_uk: '學生用戶',
            },
            children: [
              {
                key: 'account:student:view',
                name: {
                  zh_HK: '查看學生用戶資訊',
                  en_uk: '查看學生用戶資訊',
                },
              },
              {
                key: 'account:student:view&account:student:update',
                name: {
                  zh_HK: '查看及編輯學生用戶資訊',
                  en_uk: '查看及編輯學生用戶資訊',
                },
              },
              {
                key: 'account:student:view&account:student:reset',
                name: {
                  zh_HK: '查看學生用戶資訊及重置密碼',
                  en_uk: '查看學生用戶資訊及重置密碼',
                },
              },
              {
                key: 'account:student:view&account:student:update&account:student:disable&account:student:delete',
                name: {
                  zh_HK: '查看學生用戶資訊及編輯/禁用/刪除學生帳戶',
                  en_uk: '查看學生用戶資訊及編輯/禁用/刪除學生帳戶',
                },
              },
            ],
          },
          {
            key: 'account:teacher',
            name: {
              zh_HK: '教職員用戶',
              en_uk: '教職員用戶',
            },
            children: [
              {
                key: 'account:teacher:view',
                name: {
                  zh_HK: '查看教職員用戶資訊',
                  en_uk: '查看教職員用戶資訊',
                },
              },
              {
                key: 'account:teacher:view&account:teacher:update',
                name: {
                  zh_HK: '查看及編輯教職員用戶資訊',
                  en_uk: '查看及編輯教職員用戶資訊',
                },
              },
              {
                key: 'account:teacher:view&account:teacher:reset',
                name: {
                  zh_HK: '查看教職員用戶資訊及重置密碼',
                  en_uk: '查看教職員用戶資訊及重置密碼',
                },
              },
              {
                key: 'account:teacher:view&account:teacher:update&account:teacher:disable&account:teacher:delete',
                name: {
                  zh_HK: '查看教職員用戶資訊及編輯/禁用/刪除教職員帳戶',
                  en_uk: '查看教職員用戶資訊及編輯/禁用/刪除教職員帳戶',
                },
              },
            ],
          },
          {
            key: 'account:gradeAndClass',
            name: {
              zh_HK: '年級與班別管理',
              en_uk: '年級與班別管理',
            },
            children: [
              {
                key: 'account:gradeAndClass:view',
                name: {
                  zh_HK: '查看年級與班別數據',
                  en_uk: '查看年級與班別數據',
                },
              },
              {
                key: 'account:gradeAndClass:view&account:gradeAndClass:add',
                name: {
                  zh_HK: '查看數據及新增年級與班別',
                  en_uk: '查看數據及新增年級與班別',
                },
              },
              {
                key: 'account:gradeAndClass:view&account:gradeAndClass:update',
                name: {
                  zh_HK: '查看數據及編輯年級與班別',
                  en_uk: '查看數據及編輯年級與班別',
                },
              },
              {
                key: 'account:gradeAndClass:view&account:gradeAndClass:delete',
                name: {
                  zh_HK: '查看數據及刪除年級與班別',
                  en_uk: '查看數據及刪除年級與班別',
                },
              },
            ],
          },
        ],
      },
      book: {
        key: 'book',
        name: {
          zh_HK: '書籍管理',
          en_uk: '書籍管理',
        },
        children: [
          {
            key: 'book:all',
            name: {
              zh_HK: '全部書籍管理',
              en_uk: '全部書籍管理',
            },
            children: [
              {
                key: 'book:all:view',
                name: {
                  zh_HK: '查看全部書籍列表',
                  en_uk: '查看全部書籍列表',
                },
              },
              {
                key: 'book:all:view&book:all:hidden',
                name: {
                  zh_HK: '查看全部書籍列表及隱藏書籍',
                  en_uk: '查看全部書籍列表及隱藏書籍',
                },
              },
            ],
          },
          {
            key: 'book:recommend',
            name: {
              zh_HK: '推薦書籍管理',
              en_uk: '推薦書籍管理',
            },
            children: [
              {
                key: 'book:recommend:view',
                name: {
                  zh_HK: '查看推薦書籍列表',
                  en_uk: '查看推薦書籍列表',
                },
              },
              {
                key: 'book:recommend:view&book:recommend:option',
                name: {
                  zh_HK: '查看推薦書籍列表及操作',
                  en_uk: '查看推薦書籍列表及操作',
                },
              },
            ],
          },
        ],
      },
      blance: {
        key: 'blance',
        name: {
          zh_HK: '時數分配管理',
          en_uk: '時數分配管理   ',
        },
        children: [
          {
            key: 'blance:data',
            name: {
              zh_HK: '時數分配數據',
              en_uk: '時數分配數據',
            },
            children: [
              {
                key: 'blance:data:totalBuy:view',
                name: {
                  zh_HK: '查看學校帳戶累積購買時數數據',
                  en_uk: '查看學校帳戶累積購買時數數據',
                },
              },
              {
                key: 'blance:data:remainder:view',
                name: {
                  zh_HK: '查看當前剩餘閱讀時數數據',
                  en_uk: '查看當前剩餘閱讀時數數據',
                },
              },
              {
                key: 'blance:data:unassigned:view',
                name: {
                  zh_HK: '查看當前未分配時數數據',
                  en_uk: '查看當前未分配時數數據',
                },
              },
            ],
          },
          {
            key: 'blance:list',
            name: {
              zh_HK: '時數分配列表',
              en_uk: '時數分配列表',
            },
            children: [
              {
                key: 'blance:list:view',
                name: {
                  zh_HK: '查看時數分配列表',
                  en_uk: '查看時數分配列表',
                },
              },
              {
                key: 'blance:list:view&blance:list:export',
                name: {
                  zh_HK: '查看時數分配列表及下載數據',
                  en_uk: '查看時數分配列表及下載數據',
                },
              },
              {
                key: 'blance:list:view&blance:list:update',
                name: {
                  zh_HK: '查看時數分配列表及分配閱讀時數',
                  en_uk: '查看時數分配列表及分配閱讀時數',
                },
              },
            ],
          },
        ],
      },
      system: {
        key: 'system',
        name: {
          zh_HK: '系統管理',
          en_uk: '系統管理',
        },
        children: [
          {
            key: 'system:admin',
            name: {
              zh_HK: '管理員帳戶管理',
              en_uk: '管理員帳戶管理',
            },
            children: [
              {
                key: 'system:admin:view',
                name: {
                  zh_HK: '查看管理員列表',
                  en_uk: '查看管理員列表',
                },
              },
              {
                key: 'system:admin:view&system:admin:add',
                name: {
                  zh_HK: '查看管理員列表及添加管理員',
                  en_uk: '查看管理員列表及添加管理員',
                },
              },
              {
                key: 'system:admin:view&system:admin:update',
                name: {
                  zh_HK: '查看管理員列表及編輯管理員',
                  en_uk: '查看管理員列表及編輯管理員',
                },
              },
              {
                key: 'system:admin:view&system:admin:reset',
                name: {
                  zh_HK: '查看管理員列表及重置管理員密碼',
                  en_uk: '查看管理員列表及重置管理員密碼',
                },
              },
              {
                key: 'system:admin:view&system:admin:update&system:admin:disable&system:admin:delete',
                name: {
                  zh_HK: '查看管理員列表及編輯/禁用/刪除管理員',
                  en_uk: '查看管理員列表及編輯/禁用/刪除管理員',
                },
              },
            ],
          },
          {
            key: 'system:role',
            name: {
              zh_HK: '角色管理',
              en_uk: '角色管理',
            },
            children: [
              {
                key: 'system:role:view',
                name: {
                  zh_HK: '查看角色列表',
                  en_uk: '查看角色列表',
                },
              },
              {
                key: 'system:role:view&system:role:add',
                name: {
                  zh_HK: '查看角色列表及創建新角色',
                  en_uk: '查看角色列表及創建新角色',
                },
              },
              {
                key: 'system:role:view&system:role:update',
                name: {
                  zh_HK: '查看角色列表及編輯角色',
                  en_uk: '查看角色列表及編輯角色',
                },
              },
              {
                key: 'system:role:view&system:role:update&system:role:delete',
                name: {
                  zh_HK: '查看角色列表及編輯/刪除角色',
                  en_uk: '查看角色列表及編輯/刪除角色',
                },
              },
            ],
          },
          {
            key: 'system:message',
            name: {
              zh_HK: '通知紀錄',
              en_uk: '通知紀錄',
            },
            children: [
              {
                key: 'system:message:view',
                name: {
                  zh_HK: '查看通知紀錄',
                  en_uk: '查看通知紀錄',
                },
              },
              {
                key: 'system:message:view&system:message:add',
                name: {
                  zh_HK: '查看通知紀錄及發布新通知',
                  en_uk: '查看通知紀錄及發布新通知',
                },
              },
            ],
          },
          {
            key: 'system:application',
            name: {
              zh_HK: '申請時數記錄',
              en_uk: '申請時數記錄',
            },
            children: [
              {
                key: 'system:application:view',
                name: {
                  zh_HK: '查看申請時數記錄',
                  en_uk: '查看申請時數記錄',
                },
              },
              {
                key: 'system:application:view&system:application:update',
                name: {
                  zh_HK: '查看申請時數記錄及批核',
                  en_uk: '查看申請時數記錄及批核',
                },
              },
            ],
          },
          {
            key: 'system:recharge',
            name: {
              zh_HK: '充值紀錄',
              en_uk: '充值紀錄',
            },
            children: [
              {
                key: 'system:recharge:view',
                name: {
                  zh_HK: '查看充值記錄',
                  en_uk: '查看充值記錄',
                },
              },
            ],
          },
          {
            key: 'system:log',
            name: {
              zh_HK: '系統日誌',
              en_uk: '系統日誌',
            },
            children: [
              {
                key: 'system:log:view',
                name: {
                  zh_HK: '查看系統日誌',
                  en_uk: '查看系統日誌',
                },
              },
            ],
          },
        ],
      },
      scienceroom: {
        key: 'scienceroom',
        name: {
          zh_HK: '科學活動室',
          en_uk: '科學活動室',
        },
        children: [
          {
            key: 'scienceroom:list',
            name: {
              zh_HK: '查看列表',
              en_uk: '查看列表',
            },
            children: [
              {
                key: 'scienceroom:list:view',
                name: {
                  zh_HK: '查看列表',
                  en_uk: '查看列表',
                },
              },
              {
                key: 'scienceroom:list:view&scienceroom:list:hideSubject',
                name: {
                  zh_HK: '查看列表及隱藏課題',
                  en_uk: '查看列表及隱藏課題',
                },
              },
              {
                key: 'scienceroom:list:view&scienceroom:list:online',
                name: {
                  zh_HK: '查看列表及上線課題',
                  en_uk: '查看列表及上線課題',
                },
              },
            ],
          },
          {
            key: 'scienceroom:detail',
            name: {
              zh_HK: '查看及課題詳情',
              en_uk: '查看及課題詳情',
            },
            children: [
              {
                key: 'scienceroom:detail:info',
                name: {
                  zh_HK: '查看課題信息',
                  en_uk: '查看課題信息',
                },
              },
              {
                key: 'scienceroom:detail:enagagement:view&scienceroom:detail:enagagement:export',
                name: {
                  zh_HK: '查看互動數據及導出數據',
                  en_uk: '查看互動數據及導出數據',
                },
              },
              {
                key: 'scienceroom:detail:lessonDocuments:view&scienceroom:detail:lessonDocuments:export',
                name: {
                  zh_HK: '查看教案文件及下載',
                  en_uk: '查看教案文件及下載',
                },
              },
            ],
          },
        ],
      },
      // notice: {
      //   key: 'notice',
      //   name: {
      //     zh_HK: '通知管理',
      //     en_uk: '通知管理',
      //   },
      //   children: [
      //     {
      //       key: 'notice:view',
      //       name: {
      //         zh_HK: '查看',
      //         en_uk: '查看',
      //       },
      //     },
      //     {
      //       key: 'notice:school-insufficient-read-time:receive',
      //       name: {
      //         zh_HK: '學校閱讀時數不足提示郵件',
      //         en_uk: '學校閱讀時數不足提示郵件',
      //       },
      //     },
      //   ],
      // },
    }
    return this.schoolRoleService.initPermissions(Object.values(data))
  }
}
