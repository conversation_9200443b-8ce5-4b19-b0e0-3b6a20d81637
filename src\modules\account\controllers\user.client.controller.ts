import { Body, Controller, Delete, Get, Patch, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger' 
import { ApiBaseR<PERSON>ult, ClientAuth, CurrentUser, NotFoundException } from '@/common'
import { EBookVersion, ETicketType } from '@/enums'
import { IAssistantContractsService, IGradeService, IReadingReflectionService,
  IReadingTimeManagerService,
  IReferenceReadService, IUserBalanceService } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system'
import {
  BindClientUserPlayerIdDto,
  ClientDeleteUserDto,
  getUserDto,
  PatchClientUserDto,
  SendEmailVerificationCodeDto,
  SendVerificationCodeResponse,
  TicketResponse,
  UserDto,
  UserResetPasswordDto,
  VerifyCodeDto,
} from '../dto'
import { GetTicketResponse } from '../dto/ticket.dto'
import {
  EmailNotMatchException,
  MissingOTPTicketException,
  PermissionDenied,
} from '../exception'
import { UserRepository } from '../repositories'
import { UserService } from '../services'
import { TicketUtil, VerificationCodeUtil } from '../utils'

@ApiTags('Account')
@ApiExtraModels(UserDto, GetTicketResponse)
@Controller('/v1/client')
export class UserClientController {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly verificationCodeUtil: VerificationCodeUtil,
    private readonly userService: UserService,
    private readonly ticketUtil: TicketUtil,
    private readonly balanceService: IUserBalanceService,
    private readonly gradeService: IGradeService,
    private readonly logService: OperationLogService,
    private readonly readingTimeManagerService: IReadingTimeManagerService,
    private readonly referenceReadService: IReferenceReadService,
    private readonly readingReflectionService: IReadingReflectionService,
    private readonly assistantContractsService: IAssistantContractsService
  ) {}

  @ClientAuth()
  @ApiBaseResult(UserDto, 200, 'Get user successful')
  @ApiOperation({ summary: 'Get user info ' })
  @Get('users/detail')
  async getUser(@CurrentUser() user: any) {
    const data = await this.userRepository.findOne({
      where: { id: user.userId },
      relations: ['school', 'userClass'],
    })
    if (!data)
    {throw new NotFoundException(`Cannot found user info by userId: ${user.userId}`)}
    let grade
    if (data.userClass?.gradeId) {
      grade = await this.gradeService.getGrade(data.userClass.gradeId)
    }

    if (data.school?.hasAssistant) {
      const assistantContract = data.school.hasAssistant
        ? await this.assistantContractsService.getLastContract(user.schoolId)
        : null
      data.school.assistantPreferredVersion =
        assistantContract?.assistant.preferredVersion
    }
    const referenceBookCount = await this.referenceReadService.getTotalBooks(user.userId)
    const referenceReadingReflectionCount =
      await this.readingReflectionService.getReadingReflectionBookCount(
        user.userId,
        EBookVersion.REFERENCE
      )

    return {
      ...getUserDto(data, grade),
      referenceBookCount,
      referenceReadingReflectionCount,
    }
  }

  // @ClientAuth()
  // @ApiBaseResult(UserDto, 200, 'Patch user')
  // @ApiOperation({ summary: 'Patch user ' })
  // @ApiParam({
  //   name: 'userId',
  //   type: String,
  // })
  // @Patch('/users/:userId')
  // async patchUser(
  //   @Param('userId', ParseIntPipe) userId: number,
  //   @Body() body: UpdateUserDto,
  // ) {
  //   const data = await this.userService.updateUser(userId, body)
  //   return getUserDto(data)
  // }

  @ClientAuth()
  @ApiBaseResult(UserDto, 201, 'Reset password')
  @ApiOperation({ summary: 'Reset password' })
  @Post('users/reset-password')
  async resetPassword(@Body() body: UserResetPasswordDto, @CurrentUser() user: any) {
    const account = await this.userRepository.findOne({
      where: { id: user.userId },
      relations: ['school'],
    })
    if (account.school.hasOTP && !body.ticket) {
      throw new MissingOTPTicketException()
    }
    if (account.school.hasOTP)
    {await this.ticketUtil.consume(ETicketType.RESET_PASSWORD, body.ticket)}
    const data = await this.userService.resetPassword(
      user.userId,
      body.password,
      account.school.hasOTP ? undefined : body.originalPassword
    )
    return getUserDto(data)
  }

  @ClientAuth()
  @ApiBaseResult(UserDto, 201, 'update user')
  @ApiOperation({ summary: 'update user' })
  @Patch('users')
  async changeLocal(@Body() body: PatchClientUserDto, @CurrentUser() user: any) {
    const data = await this.userService.updateUser(user.userId, {
      locale: body.locale,
      profileImage: body.profileImage,
    })
    return getUserDto(data)
  }

  @ClientAuth()
  @ApiBaseResult(SendVerificationCodeResponse, 201, 'Send verification code by email')
  @ApiOperation({ summary: 'Send verification code by email ' })
  @Post('send-verifications')
  async sendVerificationCodeByEmail(
    @Body() body: SendEmailVerificationCodeDto,
    @CurrentUser() user: any
  ) {
    if (user.email !== body.identify) {throw new PermissionDenied()}
    await this.verificationCodeUtil.sendVerificationCodeByEmail({
      type: body.type,
      identify: body.identify,
    })
    return { status: true }
  }

  @Post('check-verification-code')
  @ClientAuth()
  @ApiOperation({ summary: 'check verification code' })
  @ApiBaseResult(TicketResponse, 201, 'check verification code')
  async verifyCode(@Body() data: VerifyCodeDto) {
    await this.verificationCodeUtil.checkVerificationCode(
      data.type,
      data.identify,
      data.verificationCode
    )
    const ticket = await this.ticketUtil.generate(ETicketType.RESET_PASSWORD, {
      identify: data.identify,
      expiresIn: 86400,
      ticketType: ETicketType.RESET_PASSWORD,
    })
    return { ticket }
  }

  @ClientAuth()
  @ApiBaseResult(UserDto, 200)
  @ApiOperation({ summary: 'Delete an client user' })
  @Delete()
  async deleteUser(@Body() body: ClientDeleteUserDto, @CurrentUser() loginUser: any) {
    const { email } = body
    const user = await this.userRepository.findOne({
      where: { id: loginUser.userId },
      relations: ['balance', 'school'],
    })
    if (!email || email !== user.email) {
      throw new EmailNotMatchException()
    }

    const time = user.balance.totalQuota - user.balance.usedQuota
    if (time > 0)
    {await this.readingTimeManagerService.revokeTimeFromUsers(
      loginUser,
      { userIds: [user.id], time },
      true
    )}

    await this.balanceService.remove(user.balance.id, user.id)

    await this.userService.removeUser([loginUser.userId], loginUser)
    await this.logService.createLog({
      operation: `删除賬戶${email}`,
      metaData: { email },
      user: loginUser,
    })
    return getUserDto(user)
  }

  @ClientAuth()
  @ApiResponse({
    status: 200,
    description: 'bind client user player id',
  })
  @ApiOperation({ summary: 'bind client user player id' })
  @Patch('bind-player-id')
  async bindPlayerId(
    @CurrentUser('userId') userId: number,
    @Body() data: BindClientUserPlayerIdDto
  ): Promise<any> {
    const res = await this.userService.updatePlayerId(userId, data.playerId)
    return { res }
  }
}
