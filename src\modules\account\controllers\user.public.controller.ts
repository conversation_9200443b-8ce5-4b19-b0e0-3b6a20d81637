import { Body, Controller, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  AuthSchema,
  BooleanResponse,
  CurrentPlatform,
  CurrentPlatformHeader,
  ELoginMethod,
  EPlatform,
  JwtService,
  PublicAuth,
} from '@/common'
import { User } from '@/entities'
import { ESchoolStatus, ETicketType, EUserType, EVerificationCodeType } from '@/enums'
import { IAssistantContractsService } from '@/modules/shared/interfaces'
import { SchoolNotExistException } from '@/modules/schools/exception'
import {
  AppleLoginCredential,
  ForgetPasswordRequest,
  GenerateLoginTicketRequest,
  GoogleLoginCredential,
  LoginByTicket,
  LoginDto,
  LoginResponse,
  RefreshTokenRequest,
  SendEmailVerificationCodeDto,
  SendVerificationCodeResponse,
  TestPayloadDto,
  TicketResponse,
  UserDto,
} from '../dto'
import {
  ETicketCredentialType,
  GetTicketDto,
  GetTicketResponse,
  TicketEmailCredential,
} from '../dto/ticket.dto'
import { SchoolStatusException, UserStatusException } from '../exception'
import { UserRepository } from '../repositories'
import { IAccessTokenPayload, TokenService, UserService } from '../services'
import { validateInput, VerificationCodeUtil } from '../utils'
import { TicketUtil } from '../utils/ticket.util'

@ApiTags('Account')
@ApiExtraModels(
  UserDto,
  LoginResponse,
  TicketResponse,
  SendVerificationCodeResponse,
  BooleanResponse,
)
@Controller('/v1/public/accounts/users')
export class UserPublicController {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly userService: UserService,
    private readonly verificationCodeUtil: VerificationCodeUtil,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly ticketUtil: TicketUtil,
    private readonly assistantContractsService: IAssistantContractsService,
  ) {}

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 200)
  @Post('token')
  async testToken(@Body() payload: TestPayloadDto) {
    const accessToken = await this.tokenService.createAccessToken(
      AuthSchema.CLIENT,
      EPlatform.WEB,
      payload as IAccessTokenPayload,
    )

    const refreshToken = await this.tokenService.createRefreshToken(
      payload.id,
      accessToken,
      AuthSchema.CLIENT,
    )
    return { accessToken, refreshToken }
  }

  @PublicAuth()
  @ApiBaseResult(LoginDto, 201, 'Pre-login Steps')
  @ApiOperation({ summary: 'Pre-login Steps' })
  @CurrentPlatformHeader()
  @Post('email/pre-login')
  async generateLoginTicket(
    @Body() body: GenerateLoginTicketRequest,
    // @Param('schoolId', ParseIntPipe) schoolId: number,
    @CurrentPlatform() platform: EPlatform = EPlatform.MOBILE_WEB,
  ) {
    // if (!schoolId) {
    //   throw new SelectSchoolException()
    // }
    const { ticket, user } = await this.userService.generateLoginTicket(body)
    if (!ticket) {
      return this.generateToken(user, platform)
    }
    return { ticket }
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Login by ticket')
  @ApiOperation({ summary: 'Login by ticket' })
  @Post('email/login')
  async loginByTicket(@Body() body: LoginByTicket) {
    const { ticket, verificationCode } = body

    const payload = this.jwtService.verify(ticket)
    const { email, platform, schoolId } = payload as any

    await this.verificationCodeUtil.checkVerificationCode(
      EVerificationCodeType.LOGIN,
      email,
      verificationCode,
    )
    const user = await this.userRepository.findUser(email, schoolId)
    if (!user.school) {
      throw new SchoolNotExistException()
    }

    if (user.school.status === ESchoolStatus.INACTIVE) {
      throw new SchoolStatusException()
    }

    if (!user.isEnabled) {
      throw new UserStatusException()
    }

    await this.ticketUtil.consume(ETicketType.LOGIN_BY_EMAIL, ticket)

    return this.generateToken(user, platform)
  }

  @PublicAuth()
  @ApiBaseResult(LoginDto, 201, 'Google pre-login')
  @ApiOperation({ summary: 'Google pre-login' })
  @CurrentPlatformHeader()
  @Post('google/pre-login')
  async generateGoogleLoginTicket(
    @Body() body: GoogleLoginCredential,
    @CurrentPlatform() platform: EPlatform = EPlatform.MOBILE_WEB,
  ) {
    const { ticket, user } = await this.userService.generateLoginTicketByGoogle(
      platform,
      body,
    )
    if (!ticket) {
      return this.generateToken(user, platform)
    }
    return { ticket }
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Google login by ticket')
  @ApiOperation({ summary: 'Google login by ticket' })
  @Post('google/login')
  async googlLoginByTicket(@Body() body: LoginByTicket) {
    const { ticket, verificationCode } = body

    const payload = this.jwtService.verify(ticket)
    const { email, platform, schoolId } = payload as any

    await this.verificationCodeUtil.checkVerificationCode(
      EVerificationCodeType.LOGIN,
      email,
      verificationCode,
    )
    const user = await this.userRepository.findUser(email, schoolId)
    if (!user.school) {
      throw new SchoolNotExistException()
    }

    if (user.school.status === ESchoolStatus.INACTIVE) {
      throw new SchoolStatusException()
    }

    if (!user.isEnabled) {
      throw new UserStatusException()
    }

    await this.ticketUtil.consume(ETicketType.LOGIN_BY_EMAIL, ticket)

    return this.generateToken(user, platform)
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Login by apple')
  @ApiOperation({ summary: 'Login by apple' })
  @Post('apple/login')
  async loginByApple(
    @CurrentPlatform() platform: EPlatform,
    @Body() body: AppleLoginCredential,
  ) {
    const user = await this.userService.loginByApple(body)

    return this.generateToken(user, platform)
  }

  @PublicAuth()
  @ApiBaseResult(SendVerificationCodeResponse, 201, 'Send verification code by email')
  @ApiOperation({ summary: 'Send verification code by email ' })
  @Post('send-verifications')
  async sendVerificationCodeByEmail(@Body() body: SendEmailVerificationCodeDto) {
    await this.userRepository.findUser(body.identify)
    await this.verificationCodeUtil.sendVerificationCodeByEmail(body)
    return { status: true }
  }

  @PublicAuth()
  @ApiBaseResult(GetTicketResponse, 201, 'Get access ticket')
  @ApiOperation({
    summary: 'Get access ticket',
    description: `
  access ticket 适用于多步骤鉴权的场景，比如
    - 重置密码前，旧密码鉴权；
    - 忘记密码重置密码前 email + verification code 鉴权；
    - 删除账户前，三方登陆鉴权
    - Email 中链接跳转登陆等
    - 类似场景，均是先拿到ticket， 再用ticket去完成最终的权限校验
  `,
  })
  @Post('tickets')
  async getPasswordResetTicket(@Body() body: GetTicketDto): Promise<GetTicketResponse> {
    const { credential, credentialType, ticketType } = body

    const ValidationSchemas = {
      [ETicketCredentialType.EMAIL]: TicketEmailCredential,
    }

    await validateInput(credential, ValidationSchemas[credentialType])

    if (ticketType === ETicketType.FORGET_PASSWORD) {
      const { email } = credential
      const ticket = await this.userService.generateTicket({
        credential,
        credentialType,
        email,
        ticketType,
      })
      return { ticket }
    }
  }

  @PublicAuth()
  @ApiBaseResult(BooleanResponse, 201, 'Reset password')
  @ApiOperation({ summary: 'Reset password' })
  @Post('reset-password')
  async resetPassword(@Body() body: ForgetPasswordRequest) {
    const status = await this.userService.forgetPassword(body)
    return {
      status,
    }
  }

  @PublicAuth()
  @ApiBaseResult(LoginResponse, 201, 'Refresh token')
  @ApiOperation({ summary: 'Refresh token' })
  @CurrentPlatformHeader()
  @Post('token/refresh')
  async refreshToken(
    @Body() body: RefreshTokenRequest,
    @CurrentPlatform() platform: EPlatform,
  ) {
    const { refreshToken } = body

    return this.tokenService.retrieveAccessTokenByRefreshToken(
      refreshToken,
      platform,
      AuthSchema.CLIENT,
    )
  }

  private async generateToken(user: User, platform: EPlatform = EPlatform.MOBILE_WEB) {
    const assistantContract = user.school.hasAssistant
      ? await this.assistantContractsService.getLastContract(user.school.id)
      : null
    const accessToken = await this.tokenService.createAccessToken(
      AuthSchema.CLIENT,
      platform,
      {
        id: user.userId,
        loginMethod: ELoginMethod.EMAIL,
        userId: user.id,
        profileImage: user.profileImage,
        familyName: user.familyName,
        givenName: user.givenName,
        displayName: user.displayName,
        email: user.email,
        schoolId: user.school.id,
        logo: user.school.logo,
        schoolName: user.school.name,
        gradeId: user.userClass?.gradeId,
        region: user.school.region,
        classId: user.userClass?.id,
        isTeacher: user.type === EUserType.TEACHER,
        levels: user.school.studentLevelIds,
        staffLevelIds: user.school.staffLevelIds,
        isAllLevelForStudent: user.school.isAllLevelForStudent,
        isAllLevelForStaff: user.school.isAllLevelForStaff,
        version: user.school.version,
        hasScienceRoom: user.school.hasScienceRoom,
        hasAssistant: user.school.hasAssistant,
        assistantId: assistantContract?.assistant.assistantId ?? null,
      },
    )

    const refreshToken = await this.tokenService.createRefreshToken(
      user.userId,
      accessToken,
      AuthSchema.CLIENT,
    )

    await this.userRepository.updateUser(user.id, { lastLoginAt: new Date() })
    return { accessToken, refreshToken }
  }
}
