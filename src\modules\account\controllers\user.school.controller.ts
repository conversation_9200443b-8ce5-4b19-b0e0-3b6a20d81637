import {BadRequestException, Body, Controller, Delete, Get, Header, Param, ParseIntPipe, Patch, Post, Query, Res, UploadedFile, UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectDataSource } from '@nestjs/typeorm'
import { Response } from 'express'
import moment from 'moment'
import R from 'ramda'
import type { DataSource } from 'typeorm'
import {
  ApiBaseResult,
  ApiFile,
  ApiListResult,
  ApiPageResult, 
  BooleanResponse,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  getPageResponse,
  SchoolAdminAuth,
  SessionService,
} from '@/common'
import { User } from '@/entities'
import { EUserType } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { IGradeService, IReadRecordService, ISchoolService } from '@/modules/shared/interfaces'
import { LogService, OperationLogService } from '@/modules/system'
import {
  BatchUpdateUserDto,
  CreateUserDto,
  DeleteClientUsersDto,
  GetResetPasswordSampleFileRequest,
  getUserDto,
  PatchMultipleClientUsersDto,
  ResetUserPasswordDto,
  SearchUserDto,
  UpdateUserDto,
  UserDto,
} from '../dto'
import { UserRepository } from '../repositories'
import { FileTemplateService, UserService } from '../services'

@ApiTags('Account')
@ApiExtraModels(UserDto, User)
@Controller('/v1/school-admin/accounts/users')
export class UserSchoolAdminController {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly userService: UserService,
    private readonly readRecordService: IReadRecordService,
    private readonly userFileService: FileTemplateService,
    private readonly logService: OperationLogService,
    private readonly gradeService: IGradeService,
    private readonly excelService: ExcelService,
    private readonly sessionService: SessionService,
    private readonly schoolService: ISchoolService,
    private readonly log: LogService,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  @SchoolAdminAuth()
  @ApiBaseResult(UserDto, 201)
  @ApiOperation({ summary: 'Create user' })
  @Post()
  async createUser(@CurrentSchoolAdmin() admin: any, @Body() body: CreateUserDto) {
    const school = await this.schoolService.findOne({ where: { id: admin.schoolId  } })
    const user = await this.userService.createUser(school, body, admin)
    await this.logService.createLog({
      user: admin,
      operation: `添加${user.email}帳戶`,
      metaData: { userId: user.id },
    })
    return user
  }

  @SchoolAdminAuth()
  @ApiListResult(UserDto, 201)
  @ApiOperation({ summary: 'Add users in batches' })
  @Post('/batch-add-users')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async batchTemplate(
    @UploadedFile() file: Express.Multer.File,
    @CurrentSchoolAdmin() operator: any
  ): Promise<any> {
    const school = await this.schoolService.findOne({ where: { id: operator.schoolId  } })
    const { errors, users } = await this.userService.batchAddUsers(file, operator, school)
    return {
      errors: errors?.length ? errors : undefined,
      users: users?.map((item) => getUserDto(item)),
    }
  }

  @SchoolAdminAuth()
  @Get('/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=User.xlsx')
  @ApiOperation({ summary: 'export users' })
  async exportUsers(
    @Res() res: Response,
    @Query() query: SearchUserDto,
    @CurrentLocale() local: ELocaleType,
    @CurrentSchoolAdmin() user: any
  ) {
    let pageIndex = 1
    const pageSize = 100
    let total = 0
    let users = []
    do {
      const data = await this.listUsers({ ...query, pageIndex, pageSize }, user.schoolId)
      total = data.total
      pageIndex += 1
      users = users.concat(
        data.items.map((item) => ({
          type: item.type,
          name: `${item.givenName ? item.givenName : ''}`,
          serialNo: item.serialNo,
          email: item.email,
          class: item.userClass?.class || '-',
          grade: (item.userClass as any)?.grade || '-',
          lastLoginAt:
            item.type === EUserType.STUDENT && item.lastLoginAt
              ? moment
                .tz(item.lastLoginAt * 1000, 'Asia/Hong_Kong')
                .format('DD/MM/YYYY HH:mm')
              : '-',
        }))
      )
    } while ((pageIndex - 1) * pageSize < total)
    const file = await this.excelService.buildExcel({
      name: `usersByAdmin.${local}`,
      data: users,
    })
    res.send(Buffer.from(file))
    await this.log.save('下载用户信息csv', user, query)
  }

  @SchoolAdminAuth()
  @Get('/download-template-file')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Add User Example.xlsx')
  @ApiOperation({ summary: 'download model' })
  async downloadTemplateFile(@Res() res: Response, @CurrentLocale() local: ELocaleType) {
    const file = await this.userFileService.downloadTemplateFile('addUsers', local)
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @ApiPageResult(UserDto, 200)
  @ApiOperation({ summary: 'search user' })
  @Get()
  async searchUser(@Query() query: SearchUserDto, @CurrentSchoolAdmin() user: any) {
    return await this.listUsers(query, user.schoolId)
  }

  @SchoolAdminAuth()
  @ApiListResult(UserDto, 200)
  @ApiOperation({ summary: 'batch update users' })
  @Patch('batch')
  async batchUpdateUser(
    @Body() data: BatchUpdateUserDto,
    @CurrentSchoolAdmin() admin: any
  ) {
    const { ids, exceptions, isEnabled, ...query } = data
    let userIds = ids?.length ? ids : []
    if (userIds.length === 0) {
      const users = await this.userRepository.searchUsers(admin.schoolId, query)
      userIds = users.items.map((item) => item.id)
      if (exceptions?.length) {
        userIds = userIds.filter((id) => !exceptions.includes(id))
      }
    }
    const users = await this.userService.batchUpdateUser(userIds, { isEnabled }, admin)
    if (R.isNil(data.isEnabled)) {
      const operation = data.isEnabled
        ? users.length > 3
          ? `批量啟用`
          : '啟用賬戶'
        : users.length > 3
          ? `批量禁用`
          : '禁用賬戶'
      await this.logService.createLog({
        operation: `${operation}${users
          .slice(0, 3)
          .map((item) => `“${item.email}”`)
          .join(',')} ${users.length > 3 ? `等${users.length}個賬戶` : ''}`,
        metaData: { publisherIds: data.ids },
        user: admin,
      })

      if (!data.isEnabled) {
        for (const user of users) {await this.sessionService.deleteSession(user.userId)}
      }
    }
    return users.map((u) => getUserDto(u))
  }

  @SchoolAdminAuth()
  @ApiListResult(UserDto, 200)
  @ApiOperation({ summary: 'Delete an client user' })
  @Delete()
  async deleteUser(@Body() body: DeleteClientUsersDto, @CurrentSchoolAdmin() admin: any) {
    const { userIds: ids, exceptions, ...query } = body
    let userIds = ids?.length ? ids : []
    if (userIds.length === 0) {
      const users = await this.userRepository.searchUsers(admin.schoolId, query)
      userIds = users.items.map((item) => item.id)
      if (exceptions?.length) {
        userIds = userIds.filter((id) => !exceptions.includes(id))
      } 
    }
    if (!userIds || userIds.length <= 0)
    {throw new BadRequestException('將要刪除的用戶不存在')}

    const users = await this.userRepository.findUsers({ ids: userIds })

    await this.dataSource.manager.transaction(async (entityManager) => {
      // 更新学校额度
      const school = await entityManager.query(`select * from schools where id = ?`, [
        admin.schoolId,
      ])
      if (!school.isSharingTime)
      {await entityManager.query(`update school_balances set 
        distribution_quota = distribution_quota - (select ifnull(sum(total_quota),0) - ifnull(sum(used_quota),0) 
        from user_balances where user_id IN (?)) where school_id = ? ;`,
      [userIds, Number(admin.schoolId)]
      )}
      // 更新用户额度，并且打上删除标记
      await entityManager.query(
        `update user_balances set deleted_at = CURRENT_TIMESTAMP(6), total_quota = used_quota where user_id IN (?);`,
        [userIds]
      )
      // 给用户打上删除标记，并变更email 为 `email [timestamp]`
      await entityManager.query(
        `update users set deleted_at = CURRENT_TIMESTAMP(6), email = concat(email, ' ',REPLACE(unix_timestamp(current_timestamp(3)),'.','')) where id IN (?);`,
        [userIds]
      )
    })

    for (const user of users) {await this.sessionService.deleteSession(user.userId)}

    await this.logService.createLog({
      operation: `${users.length > 3 ? `批量删除` : '删除賬戶'}${users
        .slice(0, 3)
        .map((item) => `“${item.email}”`)
        .join(',')} ${users.length > 3 ? `等${users.length}個賬戶` : ''}`,
      metaData: { userIds },
      user: admin,
    })
    return users.map((item) => getUserDto(item))
  }

  @Patch(':id/reset-password-single')
  @ApiOperation({ summary: 'reset password for a user' })
  @ApiBaseResult(UserDto, 200)
  @SchoolAdminAuth()
  async resetPasswordSingle(@Param('id', ParseIntPipe) id: number) {
    const [password] = await this.userRepository.resetPassword([id])
    return { password }
  }

  @Post('reset-multiple-passwords')
  @ApiOperation({ summary: 'Reset multiple passwords' })
  @ApiBaseResult(BooleanResponse, 201)
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  @SchoolAdminAuth()
  async resetMultiplePassword(
    @UploadedFile() file: Express.Multer.File,
    @CurrentLocale() local = ELocaleType.ZH_HK,
    // @Body() body: ResetMultiplePasswordRequest,
    @Res() res: Response
  ) {
    // const { items } = body
    const items = await this.userService.parseResetMultiplePasswordFile(file)
    const users = await this.userRepository.resetMultiplePasswords(items)

    const exportedFile = await this.excelService.buildExcel({
      name: `resetMultiplePassword.${local}`,
      data: users,
    })
    res.setHeader('Content-Disposition', `attachment; filename=passwordResetLog.csv`)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    res.send(Buffer.from(exportedFile))
  }

  @Post('multiple-patch')
  @ApiOperation({ summary: 'Patch multiple users' })
  @ApiBaseResult(BooleanResponse, 201)
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  @SchoolAdminAuth()
  async patchMultipleUsers(
    @CurrentSchoolAdmin() user: any,
    @UploadedFile() file: Express.Multer.File
  ) {
    const items = await this.userService.parsePatchMultipleUsersFile(file, user.schoolId)
    await this.userRepository.patchMultipleUsers(items)
    if (this.isNewSchoolYear()) {
      //新学年 升年级更新一下对应的学生阅读记录
      await this.readRecordService.patchUsersReadRecord(user.schoolId, items)
    }
    return {
      status: true,
    }
  }

  @Post('reset-password-sample')
  @ApiOperation({
    summary: 'Download a sample file for resetting password by customed password',
  })
  @ApiBaseResult(BooleanResponse, 201)
  @SchoolAdminAuth()
  async getResetPasswordSampleFile(
    @CurrentLocale() local = ELocaleType.ZH_HK,
    @Body() body: GetResetPasswordSampleFileRequest,
    @Res() res: Response
  ) {
    const {
      isFullSelected = false,
      specifiedUsers = null,
      excludedUsers = null,
      schoolId,
      type,
      keyword,
    } = body

    let users: User[] = null

    const condition = {
      isFullSelected,
      specifiedUsers,
      excludedUsers,
      schoolId,
      type,
      keyword,
    }

    if (!schoolId) {
      users = []
    } else {
      if (type) {
        users = await this.userRepository.findUserNames(condition)
      } else {
        const students = await this.userRepository.findUserNames({
          ...condition,
          type: EUserType.STUDENT,
        })

        const teachers = await this.userRepository.findUserNames({
          ...condition,
          type: EUserType.TEACHER,
        })

        users = [].concat(students, teachers)
      }
      const gradeIds = users
        .filter((item) => !!item.userClass)
        .map((item) => item.userClass.gradeId)
      const grades = gradeIds.length ? await this.gradeService.listGrades(gradeIds) : []
      users = users.map((item) => {
        const grade = grades.find((g) => g.id === item.userClass?.gradeId)?.grade ?? ''
        return {
          ...item,
          class: item.userClass?.class ?? '',
          grade,
          password: '',
        }
      })
    }

    const file = await this.excelService.buildExcel({
      name: `passwordResetSample.${local}`,
      data: users,
    })
    res.setHeader('Content-Disposition', `attachment; filename=sample.csv`)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    res.send(Buffer.from(file))
  }

  @Post('multiple-patch-sample')
  @ApiOperation({
    summary: 'Download a sample file for patching multiple users',
  })
  @ApiBaseResult(BooleanResponse, 201)
  @SchoolAdminAuth()
  async getMultiplePatchSampleFile(
    @CurrentLocale() local = ELocaleType.ZH_HK,
    @Body() body: PatchMultipleClientUsersDto,
    @CurrentSchoolAdmin() admin: any,
    @Res() res: Response
  ) {
    const { userIds: ids, exceptions, ...query } = body
    let userIds = ids?.length ? ids : []
    if (userIds.length === 0) {
      const users = await this.userRepository.searchUsers(admin.schoolId, query)
      userIds = users.items.map((item) => item.id)
      if (exceptions?.length) {
        userIds = userIds.filter((id) => !exceptions.includes(id))
      }
    }
    if (!userIds || userIds.length <= 0) {throw new BadRequestException('用戶不存在')}

    let users = await this.userRepository.findUsers({ ids: userIds })

    users = users.filter((x) => x.isEnabled)

    const gradeIds = users
      .filter((item) => !!item.userClass)
      .map((item) => item.userClass.gradeId)
    const grades = gradeIds.length ? await this.gradeService.listGrades(gradeIds) : []

    users = users.map((item) => {
      const grade = grades.find((g) => g.id === item.userClass?.gradeId)?.grade ?? ''
      return {
        ...item,
        class: item.userClass?.class ?? '',
        grade,
      }
    })

    const file = await this.excelService.buildExcel({
      name: `patchMultipleGradesSample.${local}`,
      data: users,
    })
    res.setHeader('Content-Disposition', `attachment; filename=sample.csv`)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'reset password of search users ' })
  @SchoolAdminAuth()
  @Get('reset-password')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=users.xlsx')
  @CurrentLocaleHeader()
  async resetPassword(
    @Query() query: ResetUserPasswordDto,
    @CurrentSchoolAdmin() user: any,
    @CurrentLocale() local = ELocaleType.ZH_HK,
    @Res() res: Response
  ) {
    let ids = query.ids ?? []
    let users = []
    if (query.ids?.length) {
      users = await this.userRepository.findUsers({ ids })
    } else {
      const data = await this.userRepository.searchUsers(user.schoolId, query)
      ids = data.items.map((item) => item.id)
      if (query.exceptions?.length) {
        ids = ids.filter((id) => !query.exceptions.includes(id))
      }
      users = data.items
    }

    const passwords = await this.userRepository.resetPassword(ids)
    const gradeIds = users.map((item) => item.userClass?.gradeId).filter((item) => !!item)
    const grades = gradeIds.length ? await this.gradeService.listGrades(gradeIds) : []
    const data = users.map((item) => {
      const password = passwords.find((p) => p.id === item.id)
      const grade = grades.find((g) => g.id === item.userClass?.gradeId)
      return {
        serialNo: item.serialNo ?? '-',
        email: item.email,
        password: password.password,
        className: item.userClass?.class ?? '-',
        gradeName: grade?.grade ?? '-',
      }
    })
    const file = await this.excelService.buildExcel({
      name: `resetUserPassword.${local}`,
      data,
    })
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @ApiBaseResult(UserDto, 200)
  @ApiOperation({ summary: 'update user' })
  @Patch(':id')
  async updateUser(@Param('id', ParseIntPipe) id: number, @Body() data: UpdateUserDto) {
    const user = await this.userService.updateUser(id, data)
    return getUserDto(user)
  }

  private async listUsers(query: SearchUserDto, schoolId: number) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const data = await this.userRepository.searchUsers(schoolId, {
      ...query,
      pageIndex,
      pageSize,
    })
    const userData = getPageResponse<UserDto, User>(data as any, data.items, getUserDto)
    const grades = await this.gradeService.listGrades(
      data.items.map((item) => item.userClass?.gradeId)
    )
    userData.items = userData.items.map((item) => ({
      ...item,
      userClass: {
        ...item.userClass,
        grade: grades.find((g) => g.id === item.userClass?.gradeId)?.grade,
      },
    }))
    return userData
  }
  // 当前月份大于或等于9 月及以后），则为新学年
  private isNewSchoolYear(): boolean {
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1 // 获取当前月份（0-11）
    return currentMonth >= 9
  }
}
