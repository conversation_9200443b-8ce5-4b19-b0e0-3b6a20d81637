import { getExceptionGroup } from '@/common/decorators'
import { ExceptionGroup } from '@/common/enums'
import { BaseException } from '@/common/exceptions'

const AccountExceptionMeta = getExceptionGroup(ExceptionGroup.ACCOUNT)

@AccountExceptionMeta(1, {
  en_us: 'Facebook login failed',
  zh_HK: 'facebook 登入失敗',
  zh_cn: 'facebook 登入失败',
})
export class FacebookLoginFailedException extends BaseException {}

@AccountExceptionMeta(2, {
  en_us: 'Google login failed',
  zh_HK: 'google 登入失敗',
  zh_cn: 'google 登入失败',
})
export class GoogleLoginFailedException extends BaseException {}

@AccountExceptionMeta(3, {
  en_us: 'User has registered',
  zh_HK: '當前用戶已註冊',
  zh_cn: '当前用户已注册',
})
export class UserExistsException extends BaseException {}

@AccountExceptionMeta(4, {
  en_us: 'Verification code is incorrect',
  zh_HK: '驗證碼輸入錯誤',
  zh_cn: '验证码输入错误',
})
export class VerificationCodeNotMatched extends BaseException {}

@AccountExceptionMeta(5, {
  en_us: 'verification code sent too often',
  zh_HK: '驗證碼發送太頻繁',
  zh_cn: '验证码发送太频繁',
})
export class VerificationCodeSentTooOften extends BaseException {}

@AccountExceptionMeta(6, {
  en_us: 'Verification code is expired',
  zh_HK: '驗證碼已過期',
  zh_cn: '验证码已过期',
})
export class VerificationCodeExpired extends BaseException {}

@AccountExceptionMeta(8, {
  en_us: 'Apple login failed',
  zh_HK: 'apple 登陆失败',
  zh_cn: 'apple 登陆失败',
})
export class AppleLoginFailedException extends BaseException {}

@AccountExceptionMeta(11, {
  en_us: 'Verification code not found, please check your code type and expiration time',
  zh_HK: '未能找到該驗證碼，請檢查輸入是否正確，或驗證碼已過期',
  zh_cn: '未能找到该验证码，请检查输入是否正确，或验证码已过期',
})
export class VerificationCodeNotFound extends BaseException {}

@AccountExceptionMeta(12, {
  en_us: 'Verification code send failed',
  zh_HK: '驗證碼發送失敗',
  zh_cn: '验证码发送失败',
})
export class VerificationSendFailed extends BaseException {}

@AccountExceptionMeta(13, {
  en_us: 'This accout must binded an valid email',
  zh_HK: '帳戶需要綁定一個郵箱地址',
  zh_cn: '帐户需要绑定一个邮箱地址',
})
export class UnbindedEmailException extends BaseException {}

@AccountExceptionMeta(14, {
  en_us: 'Provider not found',
  zh_HK: '三方登錄信息未找到',
  zh_cn: '三方登录信息未找到',
})
export class ProviderNotFoundException extends BaseException {}

@AccountExceptionMeta(15, {
  en_us: 'Account has binded a google/facebook/apple provider',
  zh_HK: '當前帳戶已經綁定了google/facebook/apple帳戶',
  zh_cn: '当前账户已经绑定了google/facebook/apple账户',
})
export class AccountHasBindedProviderException extends BaseException {}

@AccountExceptionMeta(16, {
  en_us: 'Provider has been binded',
  zh_HK: '三方登錄帳戶已經被綁定',
  zh_cn: '三方登录账户已经被绑定',
})
export class ProviderHasBeenBindedException extends BaseException {}

@AccountExceptionMeta(17, {
  en_us: 'Current administrator not support google login',
  zh_HK: '當前管理員帳戶不允許Google登入',
  zh_cn: '当前管理员账户不允许Google登入',
})
export class AdministratorNotSupportGoogleLogin extends BaseException {}

@AccountExceptionMeta(18, {
  en_us: 'Forbidden patch administrator google email',
  zh_HK: '禁止編輯管理員google email',
  zh_cn: '禁止编辑管理员google email',
})
export class ForbiddenPatchGoogleEmail extends BaseException {}

@AccountExceptionMeta(19, {
  en_us: 'Need super administrator permission',
  zh_HK: '需要super administrator權限',
  zh_cn: '需要super administrator权限',
})
export class NeedSuperAdministratorPermission extends BaseException {}

@AccountExceptionMeta(20, {
  en_us: 'Need resource update permission',
  zh_HK: '需要資源操作權限',
  zh_cn: '需要资源操作权限',
})
export class NeedResourceUpdatePermission extends BaseException {}

@AccountExceptionMeta(21, {
  en_us: 'Password not matched',
  zh_HK: '密碼錯誤',
  zh_cn: '密码错误',
})
export class PasswordNotMatched extends BaseException {}

@AccountExceptionMeta(22, {
  en_us: 'Verification code type error',
  zh_HK: '驗證碼類型有誤',
  zh_cn: '验证码类型有误',
})
export class VerificationCodeTypeError extends BaseException {}

@AccountExceptionMeta(23, {
  en_us: 'This account is disabled, please contact the school administrator',
  zh_HK: '此帳戶已禁用，請聯繫學校管理員',
  zh_cn: '此账户已禁用，请联系学校管理员',
})
export class AdministratorStatusError extends BaseException {}

@AccountExceptionMeta(24, {
  en_us: 'Invalid referrer',
  zh_HK: '未找到推薦人',
  zh_cn: '未找到推荐人',
})
export class InvalidReferrer extends BaseException {}

@AccountExceptionMeta(25, {
  en_us: 'Ticket has consumed',
  zh_HK: '憑據已被銷毀',
  zh_cn: '凭据已被销毁',
})
export class TicketConsumedException extends BaseException {}

@AccountExceptionMeta(26, {
  en_us: 'Ticket identify not matched',
  zh_HK: '憑據認證信息不匹配',
  zh_cn: '凭据认证信息不匹配',
})
export class TicketIdentifyNotMatchedException extends BaseException {}

@AccountExceptionMeta(27, {
  en_us: 'Referral code code cannot be changed once filled in',
  zh_HK: '邀請碼填寫之後不支持變更',
  zh_cn: '邀请码填写之后不支持变更',
})
export class ReferralCodeCannotBeUpdated extends BaseException {}

@AccountExceptionMeta(28, {
  en_us: 'Google login type not found',
  zh_HK: 'Google登入管道方式未提供, 請參考文檔提供合法參數',
  zh_cn: 'Google登录管道方式未提供, 请参考文档提供合法参数',
})
export class GoogleLoginTypeNotFound extends BaseException {}

@AccountExceptionMeta(29, {
  en_us: 'Can not unlink current login method',
  zh_HK: '不能解綁當前登入方式',
  zh_cn: '不能解绑当前登入方式',
})
export class CanNotUnlinkCurrentLoginMethod extends BaseException {}

@AccountExceptionMeta(30, {
  en_us: 'Exists linked provider',
  zh_HK: '當前用戶已綁定此類型三方登入管道',
  zh_cn: '当前用户已绑定此类型三方登入管道',
})
export class ExistsLinkedProvider extends BaseException {}

@AccountExceptionMeta(31, {
  en_us: 'Get ticket authorized error',
  zh_HK: '授權獲取Ticket失敗',
  zh_cn: '授权获取Ticket失败',
})
export class GetTicketAuthorizedError extends BaseException {}

@AccountExceptionMeta(32, {
  en_us: 'This email has been set as the administrator of another school',
  zh_HK: '該郵箱已經被設定為其他學校的管理員',
  zh_cn: '该邮箱已经被设置为其他学校的管理员',
})
export class DuliplicatedSchoolEmail extends BaseException {}

@AccountExceptionMeta(33, {
  en_us: 'Username  cannot be changed once filled in',
  zh_HK: 'Username填寫之後不支持變更',
  zh_cn: 'Username填写之后不支持变更',
})
export class UsernameCannotBeUpdated extends BaseException {}

@AccountExceptionMeta(34, {
  en_us: "Can't find account with this email",
  zh_HK: '使用此電子郵件找不到帳戶',
  zh_cn: '使用此电子邮件找不到帐户',
})
export class PermissionDenied extends BaseException {}

@AccountExceptionMeta(35, {
  en_us: 'Challenge expired',
  zh_HK: 'Challenge已過期',
  zh_cn: 'Challenge已过期',
})
export class ChallengeExpired extends BaseException {}

@AccountExceptionMeta(36, {
  en_us: 'Passcode error',
  zh_HK: '支付密碼錯誤',
  zh_cn: '支付密码错误',
})
export class PasscodeError extends BaseException {}

@AccountExceptionMeta(37, {
  en_us: 'Passcode exists',
  zh_HK: '支付密碼存在，請通過原支付密碼，或通過忘記密碼途徑修改',
  zh_cn: '支付密码存在，请通过原支付密码，或通过忘记密码途径修改',
})
export class PasscodeExists extends BaseException {}

@AccountExceptionMeta(38, {
  en_us: 'Encrypt method not supported',
  zh_HK: '客戶為開啟此類加密方式',
  zh_cn: '客户为开启此类加密方式',
})
export class EncryptMethodNotSupported extends BaseException {}

@AccountExceptionMeta(39, {
  en_us: 'Signature error',
  zh_HK: '簽名錯誤',
  zh_cn: '签名错误',
})
export class SignatureError extends BaseException {}

@AccountExceptionMeta(40, {
  en_us: 'Username cannot be same with other users',
  zh_HK: 'username 不能和其他的用戶重複',
  zh_cn: 'username 不能和其他的用户重复',
})
export class UsernameCannotBeSameWithOtherUsers extends BaseException {}

@AccountExceptionMeta(41, {
  en_us: 'Username not found',
  zh_HK: 'username 未設定',
  zh_cn: 'username 未设置',
})
export class UsernameNotFound extends BaseException {}

@AccountExceptionMeta(42, {
  en_us: 'Refresh token expired',
  zh_HK: 'Refresh token 過期',
  zh_cn: 'Refresh token 过期',
})
export class RefreshTokenExpired extends BaseException {}

@AccountExceptionMeta(43, {
  en_us: 'Refresh access token expired',
  zh_HK: 'Refresh access token 間隔時間太長，請重新登入',
  zh_cn: 'Refresh access token 间隔时间太长，请重新登入',
})
export class RefreshAccessTokenExpired extends BaseException {}

@AccountExceptionMeta(44, {
  en_us: 'email already exist',
  zh_HK: '郵箱已存在',
  zh_cn: '邮箱已存在',
})
export class EmailAlreadyExistException extends BaseException {}

@AccountExceptionMeta(45, {
  en_us: 'Invalid email account',
  zh_HK: '無效的電郵賬戶',
  zh_cn: '无效的电邮账户',
})
export class UserNotExistException extends BaseException {}

@AccountExceptionMeta(46, {
  en_us: 'school admin user not exist',
  zh_HK: '學校管理員用戶不存在',
  zh_cn: '学校管理员用户不存在',
})
export class SchoolAdminNotExistException extends BaseException {}

@AccountExceptionMeta(47, {
  en_us: 'Can not find parent resource',
  zh_HK: '未能找到上級資源',
  zh_cn: '未能找到上级资源',
})
export class ParentResourceNotFound extends BaseException {}

@AccountExceptionMeta(48, {
  en_us: 'Resource not found',
  zh_HK: '未找到資源',
  zh_cn: '未找到资源',
})
export class ResourceNotFound extends BaseException {}

@AccountExceptionMeta(49, {
  en_us: 'Resource exists',
  zh_HK: '資源已存在',
  zh_cn: '资源已存在',
})
export class ResourceExists extends BaseException {}

@AccountExceptionMeta(50, {
  en_us: 'Can not update resource key and parentId, if children resources exists',
  zh_HK: '當存在子級資源時，該資源不能被變更',
  zh_cn: '当存在子级资源时，该资源不能被变更',
})
export class CannotUpdateResourceKeyOrParentId extends BaseException {}

@AccountExceptionMeta(51, {
  en_us:
    'Can not delete resource key and parentId, if children resources or permissions exists',
  zh_HK: '當存在子級資源和權限時，該資源不能被刪除',
  zh_cn: '当存在子级资源和权限时，该资源不能被删除',
})
export class CannotDeleteResourceKeyOrParentId extends BaseException {}

@AccountExceptionMeta(52, {
  en_us: 'Permission exists',
  zh_HK: '權限已存在',
  zh_cn: '权限已存在',
})
export class PermissionExists extends BaseException {}

@AccountExceptionMeta(53, {
  en_us: 'Permission name repeated',
  zh_HK: '權限名字重複',
  zh_cn: '权限名字重复',
})
export class PermissionNameRepeated extends BaseException {}

@AccountExceptionMeta(54, {
  en_us: 'Permission not found',
  zh_HK: '權限未找到',
  zh_cn: '权限未找到',
})
export class PermissionNotfound extends BaseException {}

@AccountExceptionMeta(55, {
  en_us: 'Role name repeated',
  zh_HK: '角色名字重複',
  zh_cn: '角色名字重复',
})
export class RoleNameRepeated extends BaseException {}

@AccountExceptionMeta(56, {
  en_us: 'Role not found',
  zh_HK: '角色未找到',
  zh_cn: '角色未找到',
})
export class RoleNotFound extends BaseException {}

@AccountExceptionMeta(57, {
  en_us: 'resource relations are toot long',
  zh_HK: '資源級聯關係過長',
  zh_cn: '资源级联关系过长',
})
export class ResourceRelationsTooLong extends BaseException {}

@AccountExceptionMeta(58, {
  en_us: 'Admin user exists',
  zh_HK: '該管理員已存在',
  zh_cn: '该管理员已存在',
})
export class AdminUserExists extends BaseException {}

@AccountExceptionMeta(59, {
  en_us: 'Parent resource cannot be themselves',
  zh_HK: '上級資源不能與當級資源重複',
  zh_cn: '上级资源不能与当级资源重复',
})
export class ParentResourceCannotBeThemselves extends BaseException {}

@AccountExceptionMeta(60, {
  en_us: 'Phone repeated error',
  zh_HK: '管理員電話不能重複',
  zh_cn: '管理员电话不能重复',
})
export class PhoneRepeatedError extends BaseException {}

@AccountExceptionMeta(61, {
  en_us: 'Role has been bound to users',
  zh_HK: '已有用戶綁定此角色，請先解綁',
  zh_cn: '已有用户绑定此角色，请先解绑',
})
export class RoleHasBeenBoundToUsers extends BaseException {}

@AccountExceptionMeta(62, {
  en_us: 'user file data error',
  zh_HK: '用戶數據有誤',
  zh_cn: '用户数据有误',
})
export class UseFileDataException extends BaseException {}

@AccountExceptionMeta(63, {
  en_us:
    'This school account has been disabled. Please contact the school administrator if any enquiry.',
  zh_HK: '此學校帳戶已失效，如有查詢請聯絡學校管理員',
  zh_cn: '此学校账户已失效，如有查询请联系学校管理员',
})
export class SchoolStatusException extends BaseException {}

@AccountExceptionMeta(64, {
  en_us: 'Please contact your school administrator',
  zh_HK: '請聯絡學校管理員',
  zh_cn: '请联系学校管理员',
})
export class UserStatusException extends BaseException {}

@AccountExceptionMeta(65, {
  en_us: 'file exception',
  zh_HK: '文件格式錯誤',
  zh_cn: '文件格式错误',
})
export class FileException extends BaseException {}

@AccountExceptionMeta(66, {
  en_us: 'select school please',
  zh_HK: '請選擇學校',
  zh_cn: '请选择学校',
})
export class SelectSchoolException extends BaseException {}

@AccountExceptionMeta(67, {
  en_us: ' Duplicated Phone, please replace with another one',
  zh_HK: 'Phone重複, 請替換為其他的',
  zh_cn: 'Phone重复, 请替换为其他的',
})
export class DuplicatedPhoneException extends BaseException {}

@AccountExceptionMeta(68, {
  en_us: ' Duplicated Phone or email, please replace with another one',
  zh_HK: 'Phone或者email重複, 請替換為其他的',
  zh_cn: 'Phone或者email重复, 请替换为其他的',
})
export class DuplicatedPhoneOrEmailException extends BaseException {}

@AccountExceptionMeta(69, {
  en_us: 'Missing OTP ticket',
  zh_HK: '缺少OTP ticket',
  zh_cn: '缺少OTP ticket',
})
export class MissingOTPTicketException extends BaseException {}

@AccountExceptionMeta(70, {
  en_us:
    'this email has more than one account in schools, please contact with school administrators',
  zh_HK: '該email在多個學校有賬戶，請聯繫學校管理員',
  zh_cn: '该email在多个学校有账户，请联系学校管理员',
})
export class TooManyUserException extends BaseException {}

@AccountExceptionMeta(71, {
  en_us: 'The email not matched',
  zh_HK: '輸入的email不匹配',
  zh_cn: '输入的email不匹配',
})
export class EmailNotMatchException extends BaseException {}

@AccountExceptionMeta(72, {
  en_us: 'Invalid email account',
  zh_HK: '無效的電郵賬戶',
  zh_cn: '无效的电邮账户',
})
export class AdminUserNotExists extends BaseException {}

@AccountExceptionMeta(73, {
  en_us: 'Duplicated school name',
  zh_HK: '重复的学校名字',
  zh_cn: '重复的学校名字',
})
export class DuplicatedSchoolName extends BaseException {}
