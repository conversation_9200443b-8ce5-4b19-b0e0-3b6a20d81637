import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { FindOneOptions, FindOptionsWhere, Like, Repository } from 'typeorm'
import { And, ELocaleType, generatePassword, generateUniqueId, Or } from '@/common'
import { Administrator } from '@/entities'
import { EAdministratorStatus } from '@/enums'
import {IAdminRepo} from '@/modules/shared/account'
import { LANG_NO, LANG_YES } from '../constant'
import { ListAdministratorsRequest, ResetPasswordDto } from '../dto'
import {
  AdminUserNotExists,
  DuplicatedPhoneOrEmailException,
  PasswordNotMatched,
} from '../exception'
import { encryptPassword, makeSalt } from '../utils'

@Injectable()
export class AdminRepository implements IAdminRepo {
  constructor(
    @InjectRepository(Administrator)
    private readonly adminRepository: Repository<Administrator>
  ) {}

  async findAdministrator(options: { userId: string }) {
    const { userId } = options
    return this.adminRepository.findOne({ where: { userId  } })
  }

  async listAdministrators(options?: ListAdministratorsRequest) {
    const { keyword, status, pageIndex, pageSize } = options || {}

    // let condition = undefined
    // let param = undefined

    // if (keyword) {
    //   const searchFields = ['googleEmail', 'familyName', 'givenName']
    //   condition = searchFields.map((key) => `${key} LIKE %:${key}`).join(' OR ')
    //   param = searchFields.reduce((obj, key) => (obj[`${key}`] = keyword), {})
    // }

    // if (status) {
    //   condition = (condition ? `${condition} AND ` : '') `status = :status`
    //   param.status = status
    // }

    const condition = []

    const keyworkCondition = []
    if (keyword) {
      keyworkCondition.push([
        { googleEmail: Like(`%${keyword}%`) },
        { familyName: Like(`%${keyword}%`) },
        { givenName: Like(`%${keyword}%`) },
      ])
      condition.push(Or(keyworkCondition))
    }

    if (status) {condition.push({ status })} 

    const builder = this.adminRepository
      .createQueryBuilder('admin')
      .leftJoinAndSelect('admin.roles', 'roles')
      .leftJoinAndSelect('roles.permissions', 'roles.permissions')
      .leftJoinAndSelect('admin.publishers', 'publishers')
    if (condition.length) {builder.where(And(condition))}
    const total = await builder.getCount()
    if (pageIndex && pageSize) {builder.take(pageSize).skip((pageIndex - 1) * pageSize)}
    const items = await builder.orderBy('admin.createdAt', 'DESC').getMany()
    return { pageIndex, total, items, pageSize }
  }

  async deleteAdministrator(
    userId: string,
    options: {
      operator: any
    }
  ) {
    const administrator = await this.adminRepository.findOne({
      where: { userId },
      relations: ['roles', 'publishers'],
    })
    // console.log(administrator)
    if (!administrator) {throw new NotFoundException('admin not found')}
    if (administrator.roles?.length) {
      await this.adminRepository.query(
        `delete from admin_user_roles where admin_user_id = ${administrator.id}`
      )
    }

    if (administrator.publishers?.length) {
      await this.adminRepository.query(
        `delete from admin_publishers where admin_id = ${administrator.id}`
      )
    }

    const result = await this.adminRepository.delete({ id: administrator.id })
    return administrator
  }

  async count() {
    return this.adminRepository.count()
  }

  async countByDay(query: { startTime: number; endTime: number }) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    const res = await this.adminRepository.query(
      `
        select
          date,
          COUNT(*) as total
        from
          (
            select
              id,
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
            from
              administrators
            where
              ${where}
          ) t
        group by
          date
      `
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  async createAdministrator(data: Partial<Administrator>) {
    const {
      email,
      familyName,
      givenName,
      displayName,
      password,
      createdBy,
      roles = null,
      publishers = null,
    } = data
    const salt = await makeSalt()

    await this.checkoutUnique(email, undefined, data.phone, data.prefixNo)

    const encrypted = await encryptPassword(
      salt,
      Buffer.from(password, 'base64').toString()
    )

    const user = new Administrator({
      userId: generateUniqueId(),
      email,
      salt,
      password: encrypted,
      familyName,
      givenName,
      displayName,
      googleEmail: email,
      status: EAdministratorStatus.ACTIVE,
      createdBy,
      publishers,
      roles,
    })

    const administrator = await this.adminRepository.save(user)

    return administrator
  }

  async updateAdministrator(userId: string, data: Partial<Administrator>) {
    const user = await this.adminRepository.findOne({
      where: { userId },
      relations: ['roles', 'roles.permissions', 'publishers'],
    })
    if (!user) {throw new NotFoundException('user not found')}

    await this.checkoutUnique(data.email, user.id, data.phone, data.prefixNo)

    Object.assign(user, data)

    return this.adminRepository.save(user)
  }

  async findOne(options?: FindOneOptions<Administrator>) {
    const res = await this.adminRepository.findOne(options)
    if (!res) {
      throw new AdminUserNotExists()
    }
    return res
  }

  async resetPasswordByOther(id: number) {
    const user = await this.adminRepository.findOne({ where: { id }, select: ['id'] })
    if (!user) {throw new BadRequestException()}

    // const orignalPassword = RandomUtil.generateId('', 8)
    const originalPassword = generatePassword({ length: 8 })

    const salt = await makeSalt()
    const password = await encryptPassword(salt, originalPassword)

    await this.adminRepository.update(
      { id },
      {
        salt,
        password,
      }
    )
    return Buffer.from(originalPassword).toString('base64')
  }

  async resetPassword(id: number, data: ResetPasswordDto) {
    const user = await this.adminRepository.findOne({
      where: { id },
      select: ['id', 'password', 'salt'],
    })
    if (!user) {throw new BadRequestException()}

    const originalEncrypted = await encryptPassword(
      user.salt,
      Buffer.from(data.originalPassword, 'base64').toString()
    )
    if (originalEncrypted !== user.password) {throw new PasswordNotMatched()}

    const salt = await makeSalt()
    const password = await encryptPassword(
      salt,
      Buffer.from(data.password, 'base64').toString()
    )

    await this.adminRepository.update(
      { id },
      {
        salt,
        password,
      }
    )
    // return true
  }

  async export(local: ELocaleType = ELocaleType.ZH_HK) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let users = []
    do {
      const data = await this.listAdministrators({
        pageIndex,
        pageSize,
      })
      pageIndex += 1
      total = data.total
      const items = data.items.map((item) => ({
        name: `${item.familyName ?? ''} ${item.givenName ?? ''}`,
        phone: item.phone ?? '',
        email: item.email ?? '',
        lastLoginAt: item.lastLoginAt
          ? moment(item.lastLoginAt).format('YYYY/MM/DD')
          : '',
        isRoot: item.isRoot
          ? local === ELocaleType.ZH_HK
            ? '是'
            : 'Y'
          : local === ELocaleType.ZH_HK
            ? '否'
            : 'N',
        publisherName: item.publishers?.map((item) => item.name?.[local]).join(','),
        disable:
          item.status === EAdministratorStatus.ACTIVE ? LANG_NO[local] : LANG_YES[local],
      }))
      users = users.concat(items)
    } while ((pageIndex - 1) * pageSize < total)
    return users
  }

  private async checkoutUnique(
    email: string,
    id?: number,
    phone?: string,
    prefixNo?: string
  ) {
    if (!email && !phone && !prefixNo) {return}
    const where: FindOptionsWhere<Administrator>[] = []
    if (phone && prefixNo) {
      where.push({ phone, prefixNo })
    }
    if (email) {
      where.push({ email })
    }
    const admins = await this.adminRepository.find({ where })
    if (admins.filter((item) => item.id !== id).length) {
      throw new DuplicatedPhoneOrEmailException()
    }
  }
}