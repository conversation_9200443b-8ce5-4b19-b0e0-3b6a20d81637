import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import R from 'ramda'
import {FindManyOptions, FindOneOptions, FindOptionsWhere, In, Not, Repository,
} from 'typeorm'
import { ELocaleType, generatePassword, generateUniqueId } from '@/common'
import { School, SchoolAdministrator, SchoolRole } from '@/entities'
import {
  EAdministratorStatus,
  EBookVersion,
  ESchoolStatus,
  ESchoolVersion,
} from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { ISchoolAdminRepo } from '@/modules/shared/interfaces'
import { LANG_NO, LANG_YES } from '../constant'
import {
  DeleteMultipleSchoolAdminsRequest,
  ListSchoolAdminDto,
  PatchMultipleSchoolUserStatusRequest,
  ResetMultipleSchoolUserPasswordsRequest,
  UpdateSchoolAdminDto,
} from '../dto'
import { DuliplicatedSchoolEmail, DuplicatedPhoneException } from '../exception'
import { encryptPassword, makeSalt } from '../utils'
import { schoolAdminValidator } from '../validators'

@Injectable()
export class SchoolAdminRepository implements ISchoolAdminRepo {
  constructor(
    @InjectRepository(SchoolAdministrator)
    private readonly adminRepository: Repository<SchoolAdministrator>,
    @InjectRepository(SchoolRole)
    private readonly schoolRole: Repository<SchoolRole>
  ) {}

  async findAndCount(options?: FindManyOptions<SchoolAdministrator>) {
    return this.adminRepository.findAndCount(options)
  }

  async count(options?: FindManyOptions<SchoolAdministrator>) {
    return this.adminRepository.count(options)
  }

  async find(options?: FindManyOptions<SchoolAdministrator>) {
    return this.adminRepository.find(options)
  }

  async findOne(options?: FindOneOptions<SchoolAdministrator>) {
    const res = await this.adminRepository.findOne(options)
    schoolAdminValidator(res).exist()
    return res
  }

  async listSchoolAdmins(schoolId?: number, options?: ListSchoolAdminDto) {
    const {
      userId,
      username,
      displayName,
      isRoot,
      version,
      pageIndex = 1,
      pageSize = PAGE_SIZE,
    } = options || {}

    const condition = {}
    if (userId) {Object.assign(condition, { userId })}
    if (username) {Object.assign(condition, { username })}
    if (displayName) {Object.assign(condition, { displayName })}
    if (!R.isNil(isRoot)) {Object.assign(condition, { isRoot })}

    const schoolCondition = []
    if (schoolId) {schoolCondition.push('school.id = :schoolId')}
    if (version) {schoolCondition.push(`school.version like '%${version}%'`)}
    const builder = this.adminRepository
      .createQueryBuilder('admin')
      .innerJoinAndSelect(
        'admin.school',
        'school',
        schoolCondition.length ? schoolCondition.join(' AND ') : undefined,
        schoolId ? { schoolId } : undefined
      )
      .where(condition)
    const total = await builder.getCount()
    builder.orderBy('admin.createdAt', 'DESC')
    if (options) {builder.take(pageSize).skip((pageIndex - 1) * pageSize)}
    const items = await builder.getMany()

    return { total, pageIndex, pageSize, items }
  }

  async createSchoolAdmin(data: Partial<SchoolAdministrator>) {
    const { email, password, school, phone, prefixNo } = data
    const duplicatedItem = await this.adminRepository.findOne({ where: { email } })
    if (duplicatedItem) {throw new DuliplicatedSchoolEmail()}
    if (phone && prefixNo) {
      const duplicatePhone = await this.adminRepository.findOne({
        where: { school, phone, prefixNo },
      })
      if (duplicatePhone) {throw new DuplicatedPhoneException()}
    }

    const salt = await makeSalt()
    const encrypted = await encryptPassword(salt, password)

    return this.adminRepository.save({
      ...data,
      userId: generateUniqueId(),
      status: EAdministratorStatus.ACTIVE,
      salt,
      password: encrypted,
    })
  }

  async updateSchoolAdmin(
    id: number,
    data: UpdateSchoolAdminDto | Partial<SchoolAdministrator>
  ) {
    let user = await this.adminRepository.findOne({ where: { id } })
    if (!user) {throw new NotFoundException()}

    const { password, roles, ...rst } = data

    if (password) {
      const salt = await makeSalt()
      const newPassword = await encryptPassword(salt, password)

      user = {
        ...user,
        salt,
        password: newPassword,
      }
    }

    if (roles) {
      const _roles = await this.schoolRole.find({
        where: roles.map((x) => ({ id: x.id })),
        select: ['id', 'title', 'roleId'],
      })
      user = {
        ...user,
        roles: _roles,
      }
    }

    user = {
      ...user,
      ...rst,
    }
    return this.adminRepository.save(user)
  }

  async bindAdmin(userIds: number[], school: School, password: string) {
    const users = await this.adminRepository.find({ where: { id: In(userIds) } })
    if (users.length !== userIds.length) {
      throw new NotFoundException('users not found')
    }
    const salt = await makeSalt()
    const encrypted = await encryptPassword(salt, password)
    await this.adminRepository.update(
      { id: In(userIds) },
      { school, salt, password: encrypted }
    )
    return users
  }

  async deleteUser(userIds: number[], schoolId: number) {
    const admins = await this.adminRepository.find({
      where: { id: In(userIds) },
      relations: ['school'],
    })
    if (admins.length !== userIds.length)
    {throw new NotFoundException('school admins can not found')}
    // if (admins.some((item) => item.school.id !== schoolId)) {
    //   throw new BadRequestException('school not match')
    // }
    return this.adminRepository.remove(admins)
    // await this.addLog(EOperationType.DELETE_ADMIN, admin, admins)
  }

  async patchMultipleSchoolUserStatus(data: PatchMultipleSchoolUserStatusRequest) {
    const { isFullSelected, excludedIds, specifiedIds, status } = data

    let where: any = {
      isRoot: false,
    }
    if (isFullSelected)
    {where = {
      ...where,
      id: Not(In(excludedIds)),
    }}
    else
    {where = {
      ...where,
      id: In(specifiedIds),
    }}

    if (status === EAdministratorStatus.ACTIVE) {
      where = {
        ...where,
        status: In([EAdministratorStatus.INACTIVE, EAdministratorStatus.PENDING]),
      }
    }

    if (status === EAdministratorStatus.INACTIVE) {
      where = {
        ...where,
        status: In([EAdministratorStatus.ACTIVE, EAdministratorStatus.PENDING]),
      }
    }

    const users = await this.adminRepository.find({
      where: where,
      select: ['id', 'status', 'email'],
    })

    await this.adminRepository.update({ id: In(users.map((x) => x.id)) }, { status })

    return this.adminRepository.find({
      where: where,
      select: ['id', 'email', 'status', 'deletedAt'],
    })
  }

  async deleteMultipleSchoolUsers(data: DeleteMultipleSchoolAdminsRequest) {
    const { isFullSelected, excludedIds, specifiedIds } = data

    let where: any = {
      isRoot: false,
    }
    if (isFullSelected)
    {where = {
      ...where,
      id: Not(In(excludedIds)),
    }}
    else
    {where = {
      ...where,
      id: In(specifiedIds),
    }}

    const users = await this.adminRepository.find({
      where: {
        ...where,
      },
      select: ['id'],
    })

    await this.adminRepository.remove(users)

    const result = await this.adminRepository.find({
      where: where,
      select: ['id', 'deletedAt', 'status', 'email'],
      withDeleted: true,
    })
    return result.map((x) => ({
      ...x,
      deletedAt: moment().tz('Asia/Hong_Kong').format(),
    }))
  }

  async resetSingleSchoolUserPasswords(id: number) {
    const user = await this.adminRepository.findOne({
      where: { id },
      select: ['id', 'email', 'givenName'],
    })
    const password = generatePassword({ length: 8 })
    const salt = await makeSalt()
    const encrypted = await encryptPassword(salt, password)

    user.salt = salt
    user.password = encrypted
    await this.adminRepository.save(user)

    return {
      id,
      password,
    }
  }

  async resetMultipleSchoolUserPasswords(data: ResetMultipleSchoolUserPasswordsRequest) {
    const { isFullSelected, excludedIds, specifiedIds } = data

    let where: any = {}
    if (isFullSelected)
    {where = {
      ...where,
      id: Not(In(excludedIds)),
    }}
    else
    {where = {
      ...where,
      id: In(specifiedIds),
    }}

    const users = await this.adminRepository.find({
      where: where,
      select: ['id', 'email', 'givenName'],
    })

    const result: any[] = []

    for (const user of users) {
      const password = generatePassword({ length: 8 })
      const salt = await makeSalt()
      const encrypted = await encryptPassword(salt, password)

      user.salt = salt
      user.password = encrypted
      await this.adminRepository.save(user)

      result.push({
        id: user.id,
        email: user.email,
        givenName: user.givenName,
        password,
      })
    }

    return result
  }

  async batchDelete(schoolId: number, exceptions: number[]) {
    const where = `where school_id = ${schoolId} ${
      exceptions?.length ? `and id not in (${exceptions.join(',')})` : ''
    }`
    const users = await this.adminRepository.query(
      `select id, email from school_administrators ${where}`
    )
    await this.adminRepository.query(`delete from school_administrators ${where}`)
    return users
  }

  async countByDay(query: { startTime: number; endTime: number }) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    const res = await this.adminRepository.query(
      `
        select
          date,
          COUNT(*) as total
        from
          (
            select
              id,
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
            from
              school_administrators
            where
              ${where}
          ) t
        group by
          date
      `
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  async export(local: ELocaleType = ELocaleType.ZH_HK) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let users = []
    do {
      const data = await this.listSchoolAdmins(undefined, {
        pageIndex,
        pageSize,
        version: EBookVersion.SUBSCRIPTION,
      })
      pageIndex += 1
      total = data.total
      const items = data.items.map((item) => ({
        name: `${item.familyName ?? ''} ${item.givenName ?? ''}`,
        phone: item.phone ?? '',
        email: item.email ?? '',
        lastLoginAt: item.lastLoginAt
          ? moment(item.lastLoginAt).format('YYYY/MM/DD')
          : '',
        isRoot: item.isRoot
          ? local === ELocaleType.ZH_HK
            ? '是'
            : 'Y'
          : local === ELocaleType.ZH_HK
            ? '否'
            : 'N',
        schoolName: item.school?.name?.[local] ?? '',
        hasSubVer: item.school?.version.includes(ESchoolVersion.SUBSCRIPTION)
          ? '訂閱版'
          : '',
        hasRefVer: item.school?.version.includes(ESchoolVersion.REFERENCE)
          ? '參考館'
          : '',
        hasSrVer: item.school?.hasScienceRoom ? '科學活動室' : '',
        hasAIVer: item.school?.hasAssistant ? '文心智友' : '',
        disable:
          item.school?.status == ESchoolStatus.ACTIVE ? LANG_NO[local] : LANG_YES[local],
      }))
      users = users.concat(items)
    } while ((pageIndex - 1) * pageSize < total)
    return users
  }
}
