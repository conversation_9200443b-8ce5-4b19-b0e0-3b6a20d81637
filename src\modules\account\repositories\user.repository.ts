/* eslint-disable prefer-const */
import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment' 
import R from 'ramda'
import {FindManyOptions, FindOneOptions, FindOptionsWhere, In, IsNull, Like, Not, Repository,
} from 'typeorm'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'
import { ELocaleType, generateUniqueId, Or, RedisService } from '@/common'
import { User, UserBalance } from '@/entities'
import { countries, EBookVersion, EOrderDirection, EUserType } from '@/enums'
import { QuerySomeCountDto } from '@/modules/books/dto/report.dto'
import { DistributionTimeToSearchUserDto } from '@/modules/schools/dto'
import { IGradeService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { convertKeyword, filterObjectValue, RandomUtil } from '@/utils'
import { getUserCacheKey } from '../../constants'
import { LANG_NO, LANG_YES } from '../constant'
import {
  PatchMultipleUsersPayload, 
  ResetPasswordPayload,
  SearchUserBalanceDto,
  SearchUserDto,
} from '../dto'
import {
  EmailAlreadyExistException,
  TooManyUserException,
  UserNotExistException,
} from '../exception'
import { encryptPassword, makeSalt } from '../utils'
import {IUserRepo} from '@/modules/shared/account'

@Injectable()
export class UserRepository implements IUserRepo{
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly gradeService: IGradeService,
    private readonly redisService: RedisService,
    private readonly logService: LogService
  ) {}

  async find(options?: FindManyOptions<User>) {
    return this.userRepository.find(options)
  }

  async update(
    criteria:
    | string 
    | string[]
    | number
    | number[]
    | Date
    | Date[]
    | FindOptionsWhere<User>,
    partialEntity: QueryDeepPartialEntity<User>
  ) {
    return this.userRepository.update(criteria, partialEntity)
  }

  async findUserNames(options: {
    isFullSelected?: boolean
    specifiedUsers?: number[]
    excludedUsers?: number[]
    schoolId: number
    type: EUserType
    keyword?: string
  }) {
    const {
      isFullSelected = false,
      specifiedUsers = null,
      excludedUsers = null,
      schoolId,
      type,
      keyword,
    } = options

    // 指定schoolId，提升索引命中率
    const condition: any[] = [
      {
        school: {
          id: schoolId,
        },
      },
    ]

    // 关键字查询
    if (keyword) {
      const onvertedKeyword = convertKeyword(keyword)
      condition.push(
        Or([
          { email: Like(`%${onvertedKeyword}%`) },
          { givenName: Like(`%${onvertedKeyword}%`) },
          { familyName: Like(`%${onvertedKeyword}%`) },
          { serialNo: Like(`%${onvertedKeyword}%`) },
        ])
      )
    }

    // 类型是student时，userClass 不能为空
    if (type === EUserType.STUDENT) {
      condition.push({
        userClass: Not(IsNull()),
      })
      condition.push({
        type: EUserType.STUDENT,
      })
    }

    if (type === EUserType.TEACHER) {
      condition.push({
        type: EUserType.TEACHER,
      })
    }

    // 全选时，排序反选数据
    if (isFullSelected) {
      if (excludedUsers)
      {condition.push({
        id: Not(In(excludedUsers)),
      })}
      // 将多个条件合并为一个对象
      const whereCondition = condition.reduce((acc, curr) => ({ ...acc, ...curr }), {})
      return await this.userRepository.find({
        where: whereCondition,
        relations: ['userClass'],
        // select: ['id', 'email', 'givenName', 'type'],
      })
    }

    // 非全选时，对指定用户数组进行校验
    if (!specifiedUsers || specifiedUsers.length <= 0)
    {throw new BadRequestException('specifiedUsers is null or is a empty array')}
    condition.push({
      id: In(specifiedUsers),
    })

    // 将多个条件合并为一个对象
    const whereCondition = condition.reduce((acc, curr) => ({ ...acc, ...curr }), {})
    return await this.userRepository.find({
      where: whereCondition,
      relations: ['userClass'],
      // select: ['id', 'email', 'givenName', 'type'],
    })
  }

  async findOne(options?: FindOneOptions<User>) {
    const user = await this.userRepository.findOne(options)
    if (!user) {
      throw new UserNotExistException()
    }
    return user
  }

  async findUserWithCache(id: number) {
    let user = await this.redisService.getByJson(getUserCacheKey(id))

    if (!user) {
      const _user = await this.userRepository.findOne({
        where: { id },
        relations: ['userClass', 'school'],
      })

      if (!_user) {throw new UserNotExistException()}
      user = {
        id: _user.id,
        school: {
          id: _user.school.id,
        },
        userClass: {
          id: _user.userClass?.id,
          gradeId: _user.userClass?.gradeId,
        },
        type: _user.type,
      }
      await this.redisService.set(getUserCacheKey(id), JSON.stringify(user))
    }

    return user
  }

  async findUser(email: string, schoolId?: number) {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .innerJoinAndSelect(
        'user.school',
        'school',
        schoolId ? 'school.id = :schoolId' : undefined,
        schoolId ? { schoolId } : undefined
      )
      .leftJoinAndSelect('user.userClass', 'userClass')
      .where('user.email = :email', { email })
      .getMany()
    if (user.length === 0) {
      throw new UserNotExistException()
    }
    if (user.length > 1) {
      throw new TooManyUserException()
    }
    return user[0]
  }

  async getUser(userId: string) {
    return this.userRepository.findOne({
      where: { userId },
      relations: ['userClass', 'userClass.school'],
    })
  }

  findUsers(options: { ids?: number[]; emails?: string[] }): Promise<User[]> {
    const where: any = {}
    if (options.ids?.length) {
      where.id = In(options.ids)
    }
    if (options.emails?.length) {
      where.email = In(options.emails)
    }
    return this.userRepository.find({
      where,
      relations: ['userClass', 'school'],
    })
  }

  findUsersWithDelete(options: { ids?: number[]; emails?: string[] }): Promise<User[]> {
    const where: any = {}
    if (options.ids?.length) {
      where.id = In(options.ids)
    }
    if (options.emails?.length) {
      where.email = In(options.emails)
    }
    return this.userRepository.find({
      where,
      withDeleted: true,
      relations: ['userClass', 'school'],
    })
  }

  async getUsers(schoolId: number, keyword?: string) {
    const builder = this.userRepository.createQueryBuilder('user')

    builder.innerJoin(
      'user.school',
      'school',
      schoolId ? 'school.id = :schoolId' : undefined,
      schoolId ? { schoolId } : undefined
    )

    if (keyword) {
      const fields = ['family_name', 'given_name']

      const where = `${fields.map((item) => `${item} LIKE :${item}`).join(' OR ')}`
      const param = fields.reduce(
        (obj, key) => Object.assign(obj, { [`${key}`]: `%${convertKeyword(keyword)}%` }),
        {}
      ) as any
      return builder.where(where, param).getMany()
    }
    return builder.getMany()
  }

  async searchUsers(schoolId?: number, options?: SearchUserDto) {
    const {
      pageIndex,
      pageSize,
      keyword,
      classId,
      type,
      grade,
      version,
      hasScienceRoom,
      orderField = 'serialNo',
      orderDirection = EOrderDirection.ASC,
    } = options || {}

    const builder = this.userRepository.createQueryBuilder('user').select('user.id')

    if (classId?.length) {
      builder.innerJoin('user.userClass', 'userClass', 'userClass.id IN (:...classId)', {
        classId,
      })
    } else {
      const schoolCondition = []
      if (schoolId) {schoolCondition.push('school.id = :schoolId')}
      if (version) {schoolCondition.push(`school.version like '%${version}%'`)}
      if (hasScienceRoom) {schoolCondition.push(`school.has_science_room=1`)}

      builder.innerJoin(
        'user.school',
        'school',
        schoolCondition.length ? schoolCondition.join(' AND ') : undefined,
        schoolId ? { schoolId } : undefined
      )
      if (grade?.length || type === EUserType.STUDENT) {
        builder.innerJoin(
          'user.userClass',
          'userClass',
          grade?.length ? 'userClass.gradeId in (:grade)' : undefined,
          grade?.length ? { grade: grade.join(',') } : undefined
        )
      } else {
        builder.leftJoin('user.userClass', 'userClass')
      }
    }
    const condition = {
      where: '',
      param: undefined,
    }

    if (type) {
      condition.where = 'user.type = :type'
      condition.param = { type }
    }

    if (keyword) {
      const fields = ['email', 'family_name', 'given_name', 'serial_no']

      condition.where =
        condition.where +
        (condition.where.length ? ' AND ' : '') +
        `(${fields.map((item) => `${item} LIKE :${item}`).join(' OR ')})`
      condition.param = fields.reduce(
        (obj, key) => Object.assign(obj, { [`${key}`]: `%${convertKeyword(keyword)}%` }),
        condition.param ?? {}
      ) as any
    }

    if (condition.where.length && condition.param)
    {builder.where(condition.where, condition.param)}

    const total = await builder.getCount()

    let [sql, parameters] = builder.getQueryAndParameters()

    const orderMap = {
      serialNo: 'serial_no',
      lastLoginAt: 'last_login_at',
    }
    sql = sql + ` order by user.${orderMap[orderField]}  ${orderDirection}`

    if (pageIndex && pageSize) {
      sql += ` limit ${pageSize} offset ${(pageIndex - 1) * pageSize}`
      // builder.skip((pageIndex - 1) * pageSize).take(pageSize)
    }
    const data = await this.userRepository.query(sql, parameters)

    const items = await this.userRepository.find({
      where: { id: In(data.map((item) => item.user_id)) },
      relations: ['userClass', 'school'],
    })

    return {
      total,
      items: data.map((item) => items.find((user) => user.id === item.user_id)),
      pageIndex,
      pageSize,
    }
  }

  async searchUserBalances(schoolId?: number, options?: SearchUserBalanceDto) {
    const {
      pageIndex,
      pageSize,
      keyword,
      classId,
      type,
      grade,
      startTime,
      endTime,
      orderDirection = EOrderDirection.ASC,
      orderField = 'totalUsedQuota',
    } = options || {}

    const builder = this.userRepository.createQueryBuilder('user')

    if (classId?.length) {
      builder.innerJoinAndSelect(
        'user.userClass',
        'userClass',
        'userClass.id IN (:...classId)',
        { classId }
      )
    } else {
      builder.innerJoin(
        'user.school',
        'school',
        schoolId ? 'school.id = :schoolId' : undefined,
        schoolId ? { schoolId } : undefined
      )
      if (grade || type === EUserType.STUDENT) {
        builder.innerJoinAndSelect(
          'user.userClass',
          'userClass',
          grade ? 'userClass.gradeId = :grade' : undefined,
          grade ? { grade } : undefined
        )
      } else {
        builder.leftJoinAndSelect('user.userClass', 'userClass')
      }
    }
    builder.leftJoinAndSelect('user.balance', 'balance')
    const condition = {
      where: '',
      param: undefined,
    }

    if (type) {
      condition.where = 'user.type = :type'
      condition.param = { type }
    }

    if (keyword) {
      const fields = ['email', 'family_name', 'given_name', 'serial_no']

      condition.where =
        condition.where +
        (condition.where.length ? ' AND ' : '') +
        `(${fields.map((item) => `${item} LIKE :${item}`).join(' OR ')})`
      condition.param = fields.reduce(
        (obj, key) => Object.assign(obj, { [`${key}`]: `%${convertKeyword(keyword)}%` }),
        condition.param ?? {}
      ) as any
    }

    if (condition.where.length && condition.param)
    {builder.where(condition.where, condition.param)}

    const total = await builder.getCount()
    if (pageIndex && pageSize) {
      builder.skip((pageIndex - 1) * pageSize).take(pageSize)
    }

    let field = `balance.${orderField}`
    if (!['totalQuota', 'totalUsedQuota', 'usedQuota'].includes(orderField))
    {field = `balance.totalUsedQuota`}

    const items = await builder.orderBy(field, orderDirection).getMany()

    return { total, items, pageIndex, pageSize }
  }

  async searchUserIds(schoolId?: number, options?: DistributionTimeToSearchUserDto) {
    const { keyword, classId, type, grade } = options || {}

    const builder = this.userRepository.createQueryBuilder('user')

    if (classId) {
      builder.innerJoin('user.userClass', 'userClass', 'userClass.id IN (:...classId)', {
        classId,
      })
    } else {
      builder.innerJoin(
        'user.school',
        'school',
        schoolId ? 'school.id = :schoolId' : undefined,
        schoolId ? { schoolId } : undefined
      )
      if (grade || type === EUserType.STUDENT) {
        const gradeIds = Array.isArray(grade) ? grade : grade ? [grade] : undefined
        builder.innerJoin(
          'user.userClass',
          'userClass',
          gradeIds?.length ? 'userClass.gradeId in (:...grade)' : undefined,
          gradeIds?.length ? { grade: gradeIds } : undefined
        )
      }
    }
    const condition = {
      where: '',
      param: undefined,
    }

    if (type) {
      condition.where = 'user.type = :type'
      condition.param = { type }
    }

    if (keyword) {
      const fields = ['email', 'family_name', 'given_name', 'serial_no']

      condition.where =
        condition.where +
        (condition.where.length ? ' AND ' : '') +
        `(${fields.map((item) => `${item} LIKE :${item}`).join(' OR ')})`
      condition.param = fields.reduce(
        (obj, key) => Object.assign(obj, { [`${key}`]: `%${convertKeyword(keyword)}%` }),
        condition.param ?? {}
      ) as any
    }

    if (condition.where.length && condition.param)
    {builder.where(condition.where, condition.param)}

    return builder.select(['user.id', 'user.playerId', 'user.userId']).getMany()

    // return items.map((item) => item.id)
  }

  async createUser(schoolId: number, data: Partial<User>) {
    const emailExist = await this.userRepository.find({
      where: { email: data.email },
      relations: ['userClass', 'userClass.school', 'school'],
    })

    if (
      emailExist.filter((item) => item.userClass?.school?.id === schoolId).length > 0 ||
      emailExist.filter((item) => item.school?.id === schoolId).length > 0
    ) {
      throw new EmailAlreadyExistException()
    }
    if (!data.userClass && data.type !== EUserType.TEACHER) {
      throw new BadRequestException()
    }

    const balance = new UserBalance({
      totalQuota: 0,
      usedQuota: 0,
      totalUsedQuota: 0,
    })

    const user = new User({
      ...filterObjectValue(data),
      userId: generateUniqueId(),
      balance,
    })

    if (data.password) {
      const salt = await makeSalt()
      const encrypted = await encryptPassword(salt, data.password)
      user.salt = salt
      user.password = encrypted
    }

    return this.userRepository.save(user)
  }

  async batchCreateUser(schoolId: number, data: Partial<User>[]) {
    const existedUsers = await this.findUsers({
      emails: data.map((item) => item.email),
    })

    if (existedUsers.filter((item) => item.school.id === schoolId).length) {
      throw new EmailAlreadyExistException()
    }

    const users = []
    for (const item of data) {
      const duplicate = users.find((u) => u.email === item.email)
      if (!duplicate) {
        const balance = new UserBalance({
          totalQuota: 0,
          usedQuota: 0,
          totalUsedQuota: 0,
        })
        const user: Partial<User> = {}
        if (item.password) {
          const salt = await makeSalt()
          const encrypted = await encryptPassword(salt, item.password)
          user.salt = salt
          user.password = encrypted
        }
        users.push(new User({ ...item, ...user, balance }))
      }
    }
    try {
      return this.userRepository.save(users)
    } catch (err) {
      throw new EmailAlreadyExistException()
    }
  }

  async updateUser(id: number, data: Partial<User>) {
    // check1
    const user = await this.userRepository.findOne({ where: { id  } })
    if (!user) {throw new UserNotExistException()}

    Object.assign(user, data)
    if (data.password) {
      const salt = await makeSalt()
      const encrypted = await encryptPassword(salt, data.password)
      user.salt = salt
      user.password = encrypted
    }
    await this.clearUserCache(id)
    return this.userRepository.save(user)
  }

  async updateUsers(ids: number[], data: Partial<User>) {
    await Promise.all(ids.map((id) => this.clearUserCache(id)))
    await this.userRepository.update({ id: In(ids) }, data)
  }

  async resetPassword(ids: number[]) {
    // // check1
    // const user = await this.userRepository.findOne({ where: { id  } })
    // if (!user) throw new UserNotExistException()

    // Object.assign(user, data)
    const result = []
    for (const id of ids) {
      const password = RandomUtil.generateId()
      const salt = await makeSalt()
      const encrypted = await encryptPassword(salt, password)

      await this.userRepository.update({ id }, { salt, password: encrypted })
      result.push({ id, password })
    }
    return result
  }

  async resetMultiplePasswords(items: ResetPasswordPayload[]) {
    let result = {}
    for (const item of items) {
      const { email, password } = item
      // const password = Buffer.from(newPassword, 'base64').toString()
      const salt = await makeSalt()
      const encrypted = await encryptPassword(salt, password)

      await this.userRepository.update({ email }, { salt, password: encrypted })
      result[email] = password
    }

    const users = await this.findUsers({
      emails: items.map((x) => x.email),
    })

    const grades = await this.gradeService.listGrades(
      users
        .filter((x: User) => x.type === EUserType.STUDENT && x?.userClass?.gradeId)
        .map((x) => x.userClass.gradeId)
    )

    return users.map((user) => ({
      type: user.type,
      name: user.givenName,
      serialNo: user.serialNo,
      email: user.email,
      class: user?.userClass?.class ?? '-',
      grade:
        user.type === EUserType.STUDENT && user?.userClass?.gradeId
          ? grades.find((g) => g.id === user?.userClass?.gradeId)?.grade
          : '',
      password: result[user.email],
    }))
  }

  async patchMultipleUsers(items: PatchMultipleUsersPayload[]) {
    if (items.length <= 0) {return}

    let sql = `
    UPDATE users 
    SET given_name = CASE id 
    `

    for (const item of items) {
      const { id, givenName } = item
      sql = `${sql}
      WHEN ${id} THEN '${givenName.replace('\'', '\'\'')}'
      `
    }
    sql = `${sql}
      END
      `

    const users = items.filter((x) => !!x.serialNo)

    if (users.length > 0) {
      sql = `${sql},
      serial_no = CASE id
      `
      for (const item of users) {
        const { id, serialNo } = item
        sql = `${sql}
          WHEN ${id} THEN '${serialNo}'
          `
      }

      sql = `${sql}
      END
      `
    }

    const students = items.filter((x) => x.type === EUserType.STUDENT)

    if (students.length > 0) {
      sql = `${sql},
      user_class_id = CASE id
      `
      for (const item of students) {
        const { id, userClassId } = item
        sql = `${sql}
          WHEN ${id} THEN ${userClassId}
          `
      }

      sql = `${sql}
      END
      `
    }

    sql = `${sql}
        WHERE id IN ( ${items.map((x) => x.id).join(',')}  )
        `

    await Promise.all(items.map((x) => this.clearUserCache(x.id)))
    console.log('sql:', sql)
    return this.userRepository.query(sql)
  }

  async removeUser(id: number, deletedBy: any) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['balance'],
    })
    if (!user) {throw new UserNotExistException()}

    await this.userRepository.update(
      { id },
      { deletedAt: new Date(), deletedBy, email: `${user.email} ${Date.now()}` }
    )

    await this.clearUserCache(id)
    return user
  }

  async count(options: { type: EUserType; schoolIds?: number[] }) {
    const [data] = await this.userRepository.query(
      `SELECT COUNT(*) as total FROM users WHERE type = '${
        options.type
      }' and school_id in (${options.schoolIds.join(',')}) and deleted_at is null`
    )
    return Number(data.total || 0)
  }

  async countByDay(query: {
    startTime: number
    endTime: number
    type?: EUserType
    version?: EBookVersion
    hasScienceRoom?: boolean
  }) {
    const schools = await this.userRepository.query(
      `select id from schools where ${
        R.isNil(query.hasScienceRoom)
          ? `version like '%${query.version}%'`
          : `has_science_room = ${query.hasScienceRoom}`
      } and deleted_at is null`
    )

    if (schools.length === 0) {return []}

    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") and school_id in (${schools.map((item) => item.id).join(',')})`

    if (query.type) {
      where = where + ` AND type = '${query.type}'`
    }

    const res = await this.userRepository.query(
      `
        select
          date,
          COUNT(*) as total
        from
          (
            select
              id,
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
            from
              users
            where
              ${where}
          ) t
        group by
          date
      `
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  async export(
    type: EUserType,
    user: any,
    options: { local: ELocaleType; filter?: QuerySomeCountDto }
  ) {
    const { local, filter } = options
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let users = []
    // const version = EBookVersion.SUBSCRIPTION
    do {
      const data = await this.searchUsers(undefined, {
        type,
        pageIndex,
        version: filter.version,
        hasScienceRoom: filter.hasScienceRoom,
        pageSize,
      })

      const grades = await this.gradeService.listGrades(
        data.items.map((item) => item.userClass?.gradeId)
      )
      pageIndex += 1
      total = data.total
      const items = data.items.map((item) => ({
        serialNo: item.serialNo ?? '',
        email: item.email ?? '',
        className: item.userClass?.class ?? '',
        gradeName: grades.find((g) => g.id === item.userClass?.gradeId)?.grade ?? '',
        familyName: item.familyName ?? '',
        givenName: item.givenName ?? '',
        lastLoginAt: item.lastLoginAt
          ? moment.tz(item.lastLoginAt, 'Asia/Hong_Kong').format('DD/MM/YYYY')
          : '-',
        disable: item.isEnabled ? LANG_NO[local] : LANG_YES[local],
        schoolName: item.school?.name?.[local],
        region: item.school?.region ? countries[item.school.region][local] : '',
      }))
      users = users.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    await this.logService.save('下载教职员用户人数', user)
    return users
  }

  async getUsersSchoolGradeClass(schoolId: number, query: any, userIds?: number[]) {
    let sql = `SELECT
                    U.id,
                    U.given_name studentName,
                    U.serial_no serialNo,
                    S.NAME schoolName,
                    S.logo,
                    UC.class,
                    G.grade
                  FROM
                  users U
                  LEFT JOIN schools S ON U.school_id = S.id 
                  LEFT JOIN user_class UC ON U.user_class_id = UC.id
                  LEFT JOIN grades G ON G.id = UC.grade_id
                  WHERE U.school_id = ${schoolId}`
    if (userIds?.length > 0) {
      sql += ` AND U.id IN (${userIds})`
    }
    if (query.grade) {
      sql += ` AND G.id = '${query.grade}'`
    }

    if (query.class) {
      sql += ` AND UC.id = '${query.class}'`
    }
    const items = await this.userRepository.query(sql)
    return items
  }

  private clearUserCache(userId: number) {
    return this.redisService.del(getUserCacheKey(userId))
  }
}
