import { Injectable } from '@nestjs/common'
import { ELocaleType, ExcelService } from '@/common'
import {IFileTemplateService} from '@/modules/shared/interfaces'

@Injectable()
export class FileTemplateService implements IFileTemplateService{
  constructor(private readonly excelService: ExcelService) {}

  async downloadTemplateFile(template: string, local: ELocaleType = ELocaleType.ZH_HK) {
    return this.excelService.buildExcel([
      {
        name: `${template}.${local}`,
        data: [],
      },
    ])
  }
}
