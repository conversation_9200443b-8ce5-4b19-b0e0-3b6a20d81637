import { UnprocessableEntityException } from '@nestjs/common'
import { pbkdf2, randomBytes } from 'crypto'
import { promisify } from 'util'

export async function makeSalt(byteSize = 16): Promise<string> {
  const buff = await promisify(randomBytes)(byteSize)
  return buff.toString('base64')
}

export async function encryptPassword(
  salt: string,
  password: string,
  defaultIterations = 10000,
  defaultKeyLength = 64,
): Promise<string> {
  if (!password || !salt)
    throw new UnprocessableEntityException('Missing password or salt')
  const buff = Buffer.from(salt, 'base64')
  const res = await promisify(pbkdf2)(
    password,
    buff,
    defaultIterations,
    defaultKeyLength,
    'sha256',
  )
  return res.toString('base64')
}
