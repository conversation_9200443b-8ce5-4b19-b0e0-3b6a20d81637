import { SchoolAdministrator } from '@/entities'
import { SchoolAdminNotExistException } from '../exception'

export class SchoolAdminValidator {
  constructor(private readonly model: SchoolAdministrator) {}

  exist(): SchoolAdminValidator {
    if (!this.model) {
      throw new SchoolAdminNotExistException()
    }
    return this
  }
}

export const schoolAdminValidator = (model: SchoolAdministrator) =>
  new SchoolAdminValidator(model)
