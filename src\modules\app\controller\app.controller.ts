import { <PERSON>, Get, Res } from '@nestjs/common'
import { ApiOperation, ApiResponse } from '@nestjs/swagger'
import { Response } from 'express'
import { DataSource } from 'typeorm'
import { RedisService } from '@/common'
import { WebsocketGateway } from '../../websocket'

@Controller('')
export class AppController { 
  constructor(
    private websocketGateway: WebsocketGateway,
    private readonly mysql: DataSource,
    private readonly redis: RedisService
  ) {}

  @Get('version')
  @ApiOperation({
    summary: 'check services version',
  })
  health(@Res() res: Response) {
    res.setHeader('x-db-status', this.mysql.isInitialized ? 'active' : 'inactive')
    res.setHeader(
      'x-redis-status',
      this.redis.instance.status === 'ready' ? 'active' : 'inactive'
    )
    res.status(200).json({
      commit: process.env.CI_SOURCE_VERSION || 'unknown',
      build: process.env.CI_CODEBUILD_NUMBER || 'unknown',
      source: process.env.CI_SOURCE_BRANCH || 'unknown',
      dbStatus: this.mysql.isInitialized ? 'active' : 'inactive',
      redisStatus: this.redis.instance.status === 'ready' ? 'active' : 'inactive',
    })
  }

  @Get('websocket/users')
  @ApiOperation({
    summary: 'server timestamp ',
  })
  @ApiResponse({
    status: 200,
  })
  listUsers() {
    return this.websocketGateway.listActiveUsers()
  }

  // @Get('websocket/send')
  // @ApiOperation({
  //   summary: 'server timestamp ',
  // })
  // @ApiResponse({
  //   status: 200,
  // })
  // @ApiQuery({
  //   name: 'userId',
  //   type: String,
  //   required: true,
  // })
  // @ApiQuery({
  //   name: 'msg',
  //   type: String,
  //   required: true,
  // })
  // sendNotifications(@Query() query: any) {
  //   const { userId, msg } = query
  //   return this.websocketGateway.sendNotification(userId, query.schema, { msg })
  // }
}
