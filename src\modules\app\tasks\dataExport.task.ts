import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import moment from 'moment-timezone'
import { ELocaleType, EmailService, ExcelService } from '@/common'
import {
  EDataExportType,
  ETaskType, 
  TASK_QUEUE_NAME,
  TaskService,
} from '@/common/components/task'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { IUserService, IAuthorService, IReadRecordService, ISchoolDashboardService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { regionPublisherSpecification } from '@/utils'

@Processor(TASK_QUEUE_NAME)
export class DataExportTask {
  constructor(
    private readonly taskService: TaskService,
    private readonly readRecordService: IReadRecordService,
    private readonly userService: IUserService,
    private readonly authorService: IAuthorService,
    private readonly excelService: ExcelService,
    private readonly mailService: EmailService,
    private readonly logService: LogService,
    private readonly schoolDashboardService: ISchoolDashboardService
  ) {}
 
  @Process(ETaskType.DATA_EXPORT)
  async dataExport(job: Job<any>) {
    console.log({ dataExportTask: job?.id ?? 0 })

    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, email, local, query, user, filter } = job.data as any

      if (type === EDataExportType.EXPORT_STUDENTS) {
        await this.userService.exportStudents({ local, email }, user)
        return
      }
      if (type === EDataExportType.EXPORT_ALL_USERS) {
        await this.userService.exportAccounts({ local, email, filter }, user)
        return
      }
      if (type === EDataExportType.EXPORT_AUTHORS) {
        await this.authorService.exportAuthors({ local, email, query, user })
        return
      }
      if (type === EDataExportType.EXPORT_PUBLISHER_READING_TIME) {
        await this.exportPublisherReadingTime({local, query, user})
        return
      }
      if (type === EDataExportType.EXPORT_SCHOOL_READING_TIME_FROM_PLATFORM_ADMIN) {
        await this.schoolDashboardService.exportReadingOfStudent({local, schoolId: query.schoolId, query, user})
        return
      }
      if (type === EDataExportType.EXPORT_SCHOOL_READING_TIME_FROM_SCHOOL_ADMIN) {
        await this.schoolDashboardService.exportReadingOfStudent({local, schoolId: user.schoolId, query, user})
      }
    })
  }

  async exportPublisherReadingTime(options: {
    query: QueryReadingTimeDto
    local: ELocaleType
    user: any
  }) {
    const { query, local, user } = options
    const data = await this.readRecordService.exportPublisherReadingTimeCSV(query)
    // 兼容数据为空的情况
    const file = await this.excelService.buildRegionExcel({
      name: `AllPublisherReading.${local}`,
      specification: regionPublisherSpecification(
        data && data.length > 0 ? data[0].regions : [],
        true,
        local
      ),
      data: data && data.length > 0 ? data : [{}],
    })
    await this.mailService.sendPrepared(user.email, 'publisherReadingTime', {
      attachments: [
        {
          content: file,
          filename: `PublisherReadingTimeExportedFile_${moment()
            .tz('Asia/Hong_Kong')
            .format('YYYYMMDDHHmmss')}.csv`,
        },
      ],
    })
    await this.logService.save('下载订阅版出版社总报表', user, query)
    return {
      status: true,
    }
  }
}
