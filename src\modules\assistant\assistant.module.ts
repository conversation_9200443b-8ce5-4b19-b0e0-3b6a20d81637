import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { TypeOrmModule } from '@nestjs/typeorm'
import {
  Assistant,
  AssistantContracts,
  Assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt,
  Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>nt, Assistant<PERSON><PERSON><PERSON>, AssistantThread<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Assistant<PERSON><PERSON><PERSON>, Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>nt,
  AssistantVector<PERSON>re<PERSON><PERSON>, AssistantVectorstoreFilesBatch,
} from '@/entities'
import { AssistantMessages, AssistantMessagesSchema } from '@/entities/assistantMessage.entity'
import {AppController} from '@/modules/app/controller'
import {
  Assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  AssistantA<PERSON><PERSON><PERSON><PERSON>sController,
  AssistantC<PERSON><PERSON>ontroller, AssistantExportAdminController, AssistantExportSchoolController,
  Assistant<PERSON><PERSON><PERSON><PERSON>min<PERSON>ontroller, AssistantS<PERSON><PERSON><PERSON>ontroller,
  AssistantTopicAdminController,
  AssistantTopicSchoolAdminController,
} from '@/modules/assistant/controllers'
import { IAssistantContractsService } from '@/modules/shared/interfaces'
import { SharedModule } from '@/modules/shared/shared.module'
import {PublicContriller} from '@/modules/system/controllers/public.controller'
import {
  AssistantContractsService,
  AssistantExportService,
  AssistantFilesService,
  AssistantSchoolTopicService,
  AssistantService,
  AssistantStatsService,
  AssistantTopicService,
  AssistantVectorstoreFilesService,
  OpenAIS3Service,
} from './services'

@Module({
  exports: [
    AssistantService,
    AssistantStatsService,
    AssistantVectorstoreFilesService,
    AssistantFilesService,
  ],

  providers: [
    // Interface implementations
    { provide: IAssistantContractsService, useClass: AssistantContractsService },

    AssistantService,
    AssistantContractsService,
    AssistantExportService,
    AssistantFilesService,
    AssistantStatsService,
    AssistantSchoolTopicService,
    AssistantTopicService,
    AssistantVectorstoreFilesService,
    OpenAIS3Service,
  ],

  imports: [
    TypeOrmModule.forFeature([
      Assistant,
      AssistantThread,
      AssistantSessionCount,
      AssistantVectorstoreFiles,
      AssistantThreadMessageRuns,
      AssistantContracts,
      AssistantSchoolTopic,
      AssistantSchoolTopicCount,
      AssistantTopic,
      AssistantTopicCount,
      AssistantVectorstoreFilesBatch,
    ]),
    MongooseModule.forFeature([
      { name: AssistantMessages.name, schema: AssistantMessagesSchema },
    ]),
  ],

  controllers: [
    PublicContriller,
    AppController,

    AssistantAdminController,
    AssistantAdminStatsController,
    AssistantFilesAdminController,
    AssistantClientController,
    AssistantTopicAdminController,
    AssistantTopicSchoolAdminController,
    AssistantSchoolController,
    AssistantExportSchoolController,
    AssistantExportAdminController,
  ],
})
export class AssistantModule {}
