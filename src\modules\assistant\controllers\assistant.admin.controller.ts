import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  CurrentLocale,
  ELocaleType,
  PageRequest,
} from '@/common'
import { AssistantContracts } from '@/entities'
import { EAssistantStatus } from '@/enums'
import {
  CreateAssistantContractDto,
  CreateAssistantDto,
  QueryAssistantDetailDto,
  QueryAssistantDto,
  UpdateAssistantDto,
} from '../dto/assistant'
import { QueryAssistantFileListDto, QueryFilesListDto } from '../dto/assistantFiles'
import { DuplicateAssistantException } from '../exception'
import {
  AssistantContractsService,
  AssistantService,
  AssistantStatsService,
  AssistantVectorstoreFilesService,
} from '../services'

@ApiTags('AI admin')
@Controller('v1/admin/assistants')
export class AssistantAdminController {
  constructor(
    private readonly assistantService: AssistantService,
    private readonly assistantVectorstoreFilesService: AssistantVectorstoreFilesService,
    private readonly assistantContractsService: AssistantContractsService,
  ) {}

  /**
   * AI套餐TAB列表
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'AI套餐TAB列表' })
  @ApiBaseResult(BooleanResponse, 200)
  @Get('tab/list')
  async getAssistantTabList() {
    return await this.assistantService.getAssistantTabList()
  }

  /**
   * 创建AI套餐
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: '创建AI套餐' })
  @ApiBaseResult(BooleanResponse, 200)
  @Post('create')
  async createAI(
    @Body() data: CreateAssistantDto,
    @CurrentAdmin() admin: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  ) {
    const assistant = await this.assistantService.getAssistantByName(data.name)
    if (assistant && assistant.id !== data.assistantNumberId)
      throw new DuplicateAssistantException()

    if (data.status == EAssistantStatus.ONLINE) {
      await this.assistantService.createAI(data, admin, local)
    } else {
      return await this.assistantService.createAIDraft(data, admin)
    }
    return { status: true }
  }

  /**
   * 更新编辑AI套餐
   * 1，草稿-->草稿
    2，草稿-->上线
    3，上线-->上线
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: '更新编辑AI套餐' })
  @ApiBaseResult(BooleanResponse, 200)
  @Patch('update')
  async updateAI(
    @Body() data: UpdateAssistantDto,
    @CurrentAdmin() admin: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  ) {
    const assistant = await this.assistantService.getAssistantByName(data.name)
    if (assistant && assistant.id !== data.assistantNumberId)
      throw new DuplicateAssistantException()

    if (data.status == EAssistantStatus.ONLINE) {
      await this.assistantService.updateAI(data, admin, local)
    } else {
      return await this.assistantService.updateAIDraft(data, admin)
    }
    return { status: true }
  }

  /**
   * 删除AI套餐
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: '删除AI套餐' })
  @ApiBaseResult(BooleanResponse, 200)
  @Delete('delete')
  async deleteAI(
    @Query() query: QueryAssistantDetailDto,
    @CurrentAdmin() admin: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  ) {
    return await this.assistantService.deleteAI(query.id, admin, local)
  }

  /**
   * AI套餐列表
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'AI套餐列表' })
  @ApiBaseResult(BooleanResponse, 200)
  @Get('list')
  async getAIList(@Query() query: QueryAssistantDto) {
    query.page = query.page || 1
    query.pageSize = query.pageSize || 10
    return await this.assistantService.getAssistantList(query)
  }

  /**
   * AI套餐详情
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'AI套餐详情' })
  @ApiBaseResult(BooleanResponse, 200)
  @Get('detail')
  async getAIDetail(@Query() query: QueryAssistantDetailDto) {
    return await this.assistantService.getAssistantDetail(query.id)
  }

  /**
   * AI套餐文件列表
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'AI套餐书籍列表' })
  @Get('file-list')
  async getAIFileList(@Query() query: QueryAssistantFileListDto) {
    return await this.assistantVectorstoreFilesService.getVectorstoreFilesList(query)
  }

  /**
   * 发布ai合约
   * @param schoolId
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: '发布AI合约' })
  @ApiBaseResult(BooleanResponse, 200)
  @Post('school/ai-contracts/:schoolId')
  async addContract(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Body() data: CreateAssistantContractDto,
    @CurrentAdmin() admin: any,
  ) {
    await this.assistantContractsService.createContracts(schoolId, data, admin)
    return { status: true }
  }

  /**
   * 获取合约列表
   * @param schoolId
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiPageResult(AssistantContracts, 200)
  @ApiOperation({ summary: '获取AI合约列表' })
  @Get('school/ai-contracts/:schoolId')
  async getScienceContracts(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: PageRequest,
  ) {
    return await this.assistantContractsService.getContracts(schoolId, query)
  }
}
