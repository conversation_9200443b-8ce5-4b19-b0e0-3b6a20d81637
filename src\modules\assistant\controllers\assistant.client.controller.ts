import { <PERSON>, Get, Param, ParseIntPipe, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiPageResult, ClientAuth, CurrentUser } from '@/common'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { EAssistantTopicStatus, EUserType } from '@/enums'
import { IGradeService } from '@/modules/shared/interfaces'
import { QueryThreadMessageDto } from '../dto/assistant'
import { QuerySchoolTopicListDto } from '../dto/assistantSchoolTopic'
import { AssistantSchoolTopicService, AssistantService } from '../services'
import { AssistantTopicService } from '../services/assistantTopic.service'

@ApiTags('AI client')
@Controller('v1/client/assistants')
export class AssistantClientController {
  constructor(
    private readonly assistantService: AssistantService,
    private readonly assistantTopicService: AssistantTopicService,
    private readonly assistantSchoolTopicService: AssistantSchoolTopicService,
    private readonly gradeService: IGradeService,
  ) {}

  /**
   * 获取用户对话id
   * @param user
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: 'openai get thread per user' })
  @Get('/thread')
  async getThread(@CurrentUser() user: any) {
    const userThreadId = await this.assistantService.getUserThreadId(
      user.userId,
      user.schoolId,
    )
    if (!userThreadId) {
      return ''
    }
    return userThreadId
  }

  /**
   * 获取话题列表
   * @param query
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: 'openai list topic' })
  @ApiPageResult(AssistantTopic, 200)
  @Get('/topics')
  async findAll(@Query() query: QuerySchoolTopicListDto, @CurrentUser() user: any) {
    query.gradeId = user.gradeId
    query.schoolId = user.schoolId
    query.classId = user.classId
    query.status = EAssistantTopicStatus.ONLINE
    query.userType = user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT

    // 先查询学校话题
    const schoolTopic = await this.assistantSchoolTopicService.findAllClient(query)
    if (schoolTopic.total > 0) {
      return schoolTopic
    }

    // 查询admin话题
    if (!user.isTeacher) {
      // 学生则返回年级话题
      const grade = await this.gradeService.getGrade(user.gradeId)
      query.grade = grade.gradeCode
      query.assistant = user.assistantId
    }
    return await this.assistantTopicService.findAllClient(query)
  }

  /**
   * 获取消息列表
   * Threads and Messages represent a conversation session between an Assistants and a user.
   * There is a limit of 100,000 Messages per Thread.
   * Once the size of the Messages exceeds the context window of the model,
   * the Thread will attempt to smartly truncate messages, before fully dropping the ones it considers the least important.
   * @param threadId
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: 'openai messages list' })
  @Get('/thread/messages')
  async messages(
    @Query() query: QueryThreadMessageDto,
    @CurrentUser() user: any,
  ): Promise<any> {
    query.limit = query.limit || 10
    query.userId = user.userId
    return await this.assistantService.getMessagesList(query)
  }

  /**
   *
   * 打开对话统计
   * @param createAssistantSessionCountDto
   * @param user
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: 'assistant session count' })
  @Post('session/count')
  async createSessionCount(@CurrentUser() user: any) {
    const userThreadId = await this.assistantService.getUserThreadId(
      user.userId,
      user.schoolId,
    )
    await this.assistantService.createAssistantSessionCount({
      assistantId: user.assistantId,
      threadId: userThreadId ?? null,
      userId: user.userId,
      schoolId: user.schoolId,
      gradeId: user.gradeId,
      userClassId: user.classId,
      userType: user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
    })

    return {}
  }

  /**
   *
   * 使用话题统计
   * @param createAssistantSessionCountDto
   * @param user
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: 'assistant topic count' })
  @Post('topic/count/:topicId')
  async createTopicCount(
    @Param('topicId', ParseIntPipe) topicId: number,
    @CurrentUser() user: any,
  ) {
    const {
      isTeacher,
      assistantId,
      userId,
      schoolId = null,
      classId = null,
      gradeId = null,
    } = user
    const userThreadId = await this.assistantService.getUserThreadId(userId, schoolId)
    const findSchoolTopicExist =
      await this.assistantSchoolTopicService.findSchoolTopicExist(schoolId)
    if (findSchoolTopicExist > 0) {
      await this.assistantSchoolTopicService.createAssistantTopicCount({
        assistantId: assistantId,
        userType: isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
        threadId: userThreadId ?? null,
        userId,
        schoolId,
        gradeId,
        topicId,
        userClassId: classId,
      })
    } else {
      await this.assistantTopicService.createAssistantTopicCount({
        userType: isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
        threadId: userThreadId ?? null,
        assistantId: assistantId,
        userId,
        schoolId,
        gradeId,
        topicId,
        userClassId: classId,
      })
    }
    return {}
  }
}
