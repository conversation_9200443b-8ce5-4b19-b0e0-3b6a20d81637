import { Controller, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiPageResult, CurrentUser, SchoolAdminAuth } from '@/common'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { EAssistantTopicStatus, EUserType } from '@/enums'
import { IUserRepo, IGradeService } from '@/modules/shared/interfaces'
import {
  QueryAssistantStatsDto,
  QueryAssistantTimeDto,
  QuerySchoolAssistantUserThreadDto,
  QueryThreadDetailDto,
  QueryThreadMessageDto,
} from '../dto/assistant'
import { QueryAssistantFileListDto } from '../dto/assistantFiles'
import { QueryTopicListDto } from '../dto/assistantTopic'
import {
  AssistantService,
  AssistantStatsService,
  AssistantTopicService,
  AssistantVectorstoreFilesService,
} from '../services'

@ApiTags('AI school')
@Controller('v1/school/assistants')
export class AssistantSchoolController {
  constructor(
    private readonly assistantStatsService: AssistantStatsService,
    private readonly assistantService: AssistantService,
    private readonly assistantTopicService: AssistantTopicService,
    private readonly userRepository: IUserRepo,
    private readonly gradeService: IGradeService,
    private readonly assistantVectorstoreFilesService: AssistantVectorstoreFilesService,
  ) {}

  /**
   * AI套餐文件列表
   * @param query
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'AI套餐书籍列表' })
  @Get('file-list')
  async getAIFileList(
    @Query() query: QueryAssistantFileListDto,
    @CurrentUser() user: any,
  ) {
    query.assistantId = user.assistantId
    if (!query.assistantId) {
      return {}
    }
    return await this.assistantVectorstoreFilesService.getVectorstoreFilesList(query)
  }

  /**
   * 获取统计数据
   * @returns
   */
  @ApiOperation({ summary: 'assistant some stats' })
  @Get('/stats')
  @SchoolAdminAuth()
  async getAssistantStats(
    @Query() query: QueryAssistantStatsDto,
    @CurrentUser() user: any,
  ) {
    const [totalStudents, totalUniqueConversations] = await Promise.all([
      this.assistantStatsService.countStudents(query, user.schoolId),
      this.assistantStatsService.countUniqueConversations({
        schoolId: user.schoolId,
        userType: EUserType.STUDENT,
      }),
    ])
    return {
      totalStudents: totalStudents.count,
      totalUniqueConversations: totalUniqueConversations.count,
    }
  }

  /**
   * 用户对话数量 对话人次统计
   * @param query
   * @returns
   */
  @ApiOperation({ summary: 'count user session by date' })
  @SchoolAdminAuth()
  @Get('user-session-count')
  async assistantUserSessionCount(
    @Query() query: QueryAssistantTimeDto,
    @CurrentUser() user: any,
  ) {
    return await this.assistantStatsService.assistantUserSessionCount({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: user.schoolId,
    })
  }

  @Get('users-by-grade')
  @ApiOperation({ summary: '使用人数分布' })
  @SchoolAdminAuth()
  async usersByGrade(@Query() query: QueryAssistantTimeDto, @CurrentUser() user: any) {
    return this.assistantStatsService.usersGroupByGrade(query, user.schoolId)
  }

  @Get('times-by-grade')
  @ApiOperation({ summary: '使用人次分布' })
  @SchoolAdminAuth()
  async timesByGrade(@Query() query: QueryAssistantTimeDto, @CurrentUser() user: any) {
    return this.assistantStatsService.timesGroupByGrade(query, user.schoolId)
  }

  @Get('user-thread-detail')
  @ApiOperation({ summary: '使用详细 - 用户' })
  @SchoolAdminAuth()
  async userThreadDetail(@Query() query: QueryThreadDetailDto, @CurrentUser() user: any) {
    query.schoolId = user.schoolId
    return this.assistantService.getAssistantUserDetail(query)
  }

  @Get('user-thread-list')
  @ApiOperation({ summary: '对话使用详细 - 列表' })
  @SchoolAdminAuth()
  async detailByAssistantThreadList(
    @Query() query: QuerySchoolAssistantUserThreadDto,
    @CurrentUser() user: any,
  ) {
    return this.assistantService.getAssistantUserThread(query, user.schoolId)
  }

  /**
   * 获取消息列表
   * @param threadId
   * @returns
   */
  @ApiOperation({ summary: 'user openai messages list' })
  @Get('user-thread-messages')
  @SchoolAdminAuth()
  async messages(@Query() query: QueryThreadMessageDto): Promise<any> {
    query.limit = query.limit || 10
    return await this.assistantService.getMessagesList(query)
  }

  /**
   * 获取话题列表
   * @param query
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'openai list topic' })
  @ApiPageResult(AssistantTopic, 200)
  @Get('/topics/:userId')
  async findAll(
    @Query() query: QueryTopicListDto,
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    const user = await this.userRepository.findUsers({ ids: [userId] })
    query.status = EAssistantTopicStatus.ONLINE
    if (user[0].type == EUserType.TEACHER) {
      return this.assistantTopicService.findAll(query)
    }
    const grade = await this.gradeService.getGrade(user[0].userClass.gradeId)
    query.grade = grade.gradeCode
    return await this.assistantTopicService.findAll(query)
  }
}
