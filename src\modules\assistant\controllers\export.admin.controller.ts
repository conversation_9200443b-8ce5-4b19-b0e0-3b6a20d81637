import { <PERSON>, Get, Header, <PERSON>m, Query, Res } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import {
  AdminAuth,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
} from '@/common'
import { AdminQueryReadingTimeDto, QueryReadingTimeDto } from '@/modules/books/dto'
import { LogService } from '@/modules/system'
import { ExportGroupBySchoolDto, QueryThreadMessageDto } from '../dto/assistant'
import { QueryTopicListDto } from '../dto/assistantTopic'
import { AssistantExportService } from '../services/assistantExport.service'

@ApiTags('AI admin export')
@Controller('v1/admin/assistants/export/')
export class AssistantExportAdminController {
  constructor(private readonly assistantExportService: AssistantExportService) {}

  @AdminAuth()
  @Get('grade-pv/:schoolId')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=assistant-grade-pv.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant pv csv' })
  async gradePVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Param('schoolId') schoolId: number,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: AdminQueryReadingTimeDto,
  ) {
    this.assistantExportService.exportGradePV(user, schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('grade-uv/:schoolId')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=assistant-grade-uv.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant UV csv' })
  async gradeUVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Param('schoolId') schoolId: number,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: AdminQueryReadingTimeDto,
  ) {
    return this.assistantExportService.exportGradeUV(user, schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('detail-by-thread/:schoolId')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=user-message-detail.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant 对话列表页导出' })
  async exportDetailByThread(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Param('schoolId') schoolId: number,
    @Query() query: QueryThreadMessageDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    return this.assistantExportService.exportUserThread(user, schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('group-by-school')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Number-of-engagement(school).xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '文心智友 使用數量(學校) top' })
  async uvpvGroupBySchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportGroupBySchoolDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    this.assistantExportService.exportUVPVBySchool(user, query, local, res)
  }

  @AdminAuth()
  @Get('topic')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=topics.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '话题列表导出' })
  async topicExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryTopicListDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    this.assistantExportService.exportTopics(user, query, local, res)
  }

  @AdminAuth()
  @Get('files')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=files.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '文件列表导出' })
  async filesExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryTopicListDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    this.assistantExportService.exportFiles(user, query, local, res)
  }
}
