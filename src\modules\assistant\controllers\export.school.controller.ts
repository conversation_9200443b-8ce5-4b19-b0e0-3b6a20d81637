import { <PERSON>, Get, Header, Query, Res } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import * as ExcelJS from 'exceljs'
import { Response } from 'express'
import {
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  SchoolAdminAuth,
} from '@/common'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { LogService } from '@/modules/system'
import { QueryThreadMessageDto } from '../dto/assistant'
import { AssistantService } from '../services'
import { AssistantExportService } from '../services/assistantExport.service'

@ApiTags('AI school export')
@Controller('v1/school/assistants/export/')
export class AssistantExportSchoolController {
  constructor(
    private readonly assistantService: AssistantService,
    private readonly assistantExportService: AssistantExportService,
    private readonly logService: LogService,
  ) {}

  @SchoolAdminAuth()
  @Get('grade-pv')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=assistant-grade-pv.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant pv csv' })
  async gradePVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto,
  ) {
    this.assistantExportService.exportGradePV(user, user.schoolId, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('grade-uv')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=assistant-grade-uv.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant UV csv' })
  async gradeUVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto,
  ) {
    return this.assistantExportService.exportGradeUV(
      user,
      user.schoolId,
      query,
      local,
      res,
    )
  }

  @SchoolAdminAuth()
  @Get('detail-by-thread')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=user-message-detail.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant 对话列表页导出' })
  async exportDetailByThread(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryThreadMessageDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    return this.assistantExportService.exportUserThread(
      user,
      user.schoolId,
      query,
      local,
      res,
    )
  }

  @SchoolAdminAuth()
  @Get('user-message-detail')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment filename=user-message-detail.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'assistant 用户对话消息列表导出' })
  async exportUserMessageDetail(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryThreadMessageDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
  ) {
    const assistantUserDetail = await this.assistantService.getAssistantUserDetail({
      userId: query.userId,
      startTime: query.startTime,
      endTime: query.endTime,
    })
    const messageList = await this.assistantService.getMessagesList(query)

    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('個別用戶-使用及對話詳細')
    const startDate = new Date(query.startTime * 1000).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
    const endDate = new Date(query.endTime * 1000).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
    // 设置 Excel 文件格式
    worksheet.getColumn(1).width = 30
    worksheet.getColumn(2).width = 20
    worksheet.getCell('A1').value = '用戶類型'
    worksheet.getCell('B1').value = '學生'
    worksheet.getCell('A2').value = '用戶姓名'
    worksheet.getCell('B2').value = assistantUserDetail.userName
    worksheet.getCell('A3').value = '用戶班別'
    worksheet.getCell('B3').value = assistantUserDetail.class
    worksheet.getCell('A4').value = '所屬年級'
    worksheet.getCell('B4').value = assistantUserDetail.grade
    worksheet.getCell('A5').value = '學號'
    worksheet.getCell('B5').value = assistantUserDetail.serialNo
    worksheet.getCell('A6').value = '對話詳細時間段'
    worksheet.getCell('B6').value = `${startDate} - ${endDate}`
    worksheet.getCell('A7').value = '對話詳細時間段內的對話次數'
    worksheet.getCell('B7').value = assistantUserDetail.userThreadCount
    worksheet.getCell('A8').value = '對話詳細時間段內的對話數量'
    worksheet.getCell('B8').value = assistantUserDetail.userMessageNumber
    // 添加日期、时间、用户信息的表头
    worksheet.addRow({})
    worksheet.addRow(['日期', '時間', '用戶類別/AI', '用戶姓名', '內容'])
    worksheet.getRow(10).font = { bold: true }

    // 填充对话详细信息
    messageList.data.forEach((message) => {
      const createdAt = new Date(message.created_at)
      const date = createdAt.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })
      const time = createdAt.toLocaleTimeString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        hour12: false, // 24小时制
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
      const role = message.role === 'user' ? '學生' : 'AI'
      const userName = message.role === 'user' ? assistantUserDetail.userName : ''
      worksheet.addRow([date, time, role, userName, message.content[0].text.value])
    })

    // 导出 Excel 文件为 Buffer 并返回
    const buffer = await workbook.xlsx.writeBuffer()
    res.end(buffer)
    await this.logService.save('下载-文心智友-使用詳情', user)
  }
}
