import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger'
import { now } from 'lodash'
import path from 'path'
import { Like } from 'typeorm'
import { AdminAuth, ApiFile, CurrentAdmin, CurrentLocale, ELocaleType } from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { EOpeaiFileStatus } from '@/enums'
import { IBookRepo } from '@/modules/shared/interfaces'
import { S3_OPENAI_DIR } from '@/modules/constants'
import { OpenAIService } from '@/modules/websocket/services/openai.service'
import {
  DeleteAssistantFilesDtoRequest,
  QueryAssistantFileDto,
  QueryFilesListDto,
  SaveAssistantFileDto,
} from '../dto/assistantFiles'
import {
  OnlySupportPdfException,
  SubmitFileException,
  UploadFileException,
} from '../exception'
import { AssistantFilesService, OpenAIS3Service } from '../services'

@ApiTags('AI admin')
@Controller('v1/admin/assistants/files')
export class AssistantFilesAdminController {
  constructor(
    private readonly bookRepositories: IBookRepo,
    private readonly assistantFilesService: AssistantFilesService,
    private readonly openAIService: OpenAIService,
    private readonly openAIS3Service: OpenAIS3Service,
    private readonly taskService: TaskService,
  ) {}

  /**
   * 获取文件容量
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'assistants list files' })
  @Get('/useSize')
  async getUsedCapacity() {
    return await this.assistantFilesService.getUsedCapacity()
  }

  /**
   * 获取文件列表
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'assistants list files' })
  @Post('/list')
  async filesList(@Body() body: QueryFilesListDto): Promise<any> {
    return await this.assistantFilesService.getFileList(body)
  }

  /**
   * 获取文件信息
   */
  @ApiOperation({ summary: 'assistants file info ' })
  @AdminAuth()
  @Get('info')
  async getUploadFile(@Query() query: QueryAssistantFileDto) {
    const book = await this.bookRepositories.findBooks({ isbn: [query.isbn] })
    if (book.length == 0) {
      return {}
    }
    return {
      bookId: book[0].id,
      isbn: book[0].isbn,
      bookName: book[0].name,
      authorName: book[0].authors[0].name,
    }
  }

  /**
   * 上传文件 openai storage  此时是预提交
   * 1,url ==> openai   返回 openai_file_id
   * 2,记录相关db信息
   * 3,重新上传则需要删除对应的 openai文件 删除和更新数据库
   * @param file
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'assistants upload file' })
  @Post('/upload')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @CurrentAdmin() admin: any,
  ): Promise<any> {
    // 验证文件格式
    if (path.extname(file.originalname).toLowerCase() !== '.pdf') {
      throw new OnlySupportPdfException()
    }

    const fileName = file.originalname.split('.')[0]
    const S3fileName = '_' + now() + '_' + file.originalname

    // 上传文件
    const url = await this.openAIS3Service.upload({
      fileName: S3fileName,
      path: S3_OPENAI_DIR,
      file: file.buffer,
    })

    if (!url) {
      throw new UploadFileException()
    }

    // 检查文件是否存在并获取书籍信息
    const [existFile, book] = await Promise.all([
      this.assistantFilesService.existFile(fileName),
      this.bookRepositories.findOne({
        where: { isbn: Like(`%${fileName}%`) },
      }),
    ])
    // 构建基础更新参数
    const baseParams = {
      fileBytes: file.size,
      fileName: book?.name || {
        en_uk: fileName,
        zh_HK: fileName,
        zh_cn: fileName,
      },
      bookId: book?.id ?? null,
      version: book?.version ?? null,
      isbn: book?.isbn ?? fileName, //兼容非系统书籍情况
    }

    // 处理文件更新或创建
    if (existFile) {
      // 1, 已删除的文件重新上传
      // 2, 已存在的文件更新
      const updateParam = existFile.deletedAt
        ? {
            ...baseParams,
            createdBy: admin,
            deletedAt: null,
            deletedBy: null,
          }
        : {
            ...baseParams,

            updatedBy: admin,
          }

      await this.assistantFilesService.updateFile(existFile.id, updateParam)
      return { id: existFile.id, isbn: existFile.isbn, url, fileName: existFile.fileName }
    } else {
      // 3,创建新文件记录
      const newFile = await this.assistantFilesService.saveFile({
        ...baseParams,
        openaiFileId: '',
        createdBy: admin,
      })
      return { id: newFile.id, isbn: newFile.isbn, url, fileName: newFile.fileName }
    }
  }

  /**
   * 保存file至openai
   * @param query
   * @param local
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'save openai files' })
  @Post('/save')
  async saveFileToOpenAI(
    @Body() body: SaveAssistantFileDto[],
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() admin: any,
  ): Promise<any> {
    try {
      const files = []
      const updateResults = await Promise.all(
        body.map(async (file) => {
          const oldFileName = file.url.split('/').pop() //_1729162957849_9789887478218.pdf
          const newFileName = file.url.split('_').pop() // 9789887478218.pdf
          const newUrl = await this.openAIS3Service.copy({
            oldFileName: oldFileName,
            newFileName: newFileName,
            path: S3_OPENAI_DIR,
          })
          const openaiRes = await this.openAIService.uploadFile(newUrl)
          const existFIle = await this.assistantFilesService.existFileById(file.id)
          if (existFIle.status == EOpeaiFileStatus.COMPLETED && existFIle.openaiFileId) {
            files.push({
              openaiFileId: existFIle.openaiFileId,
              updateOpenaiFileId: openaiRes.id,
            })
          }
          return this.assistantFilesService.updateFile(file.id, {
            openaiFileId: openaiRes.id,
            awsUrl: newUrl,
            status: openaiRes.id ? EOpeaiFileStatus.COMPLETED : EOpeaiFileStatus.FAILED,
            updatedBy: admin,
          })
        }),
      )
      await this.taskService.deliver(
        ETaskType.UPDATE_FILE_ASSISTANT,
        {
          query: files,
          user: admin,
          type: ETaskType.UPDATE_FILE_ASSISTANT,
        },
        {},
      )

      return {
        status: true,
        data: updateResults,
      }
    } catch (error) {
      throw new SubmitFileException()
    }
  }

  /**
   * 删除文件
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: ' batch delete files' })
  @Delete('/delete')
  async deleteTopic(
    @CurrentAdmin() admin: any,
    @Body() body: DeleteAssistantFilesDtoRequest,
  ) {
    return await this.assistantFilesService.deleteFilesBatch(body, admin)
  }
}
