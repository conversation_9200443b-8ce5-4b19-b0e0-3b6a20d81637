import { Body, Controller, Get, Param, ParseIntPipe, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  PageRequest,
} from '@/common'
import { AssistantContracts } from '@/entities'
import { EUserType } from '@/enums'
import { S3_OPENAI_DIR } from '@/modules/constants'
import {
  AdminAssistantUse,
  CreateAssistantContractDto,
  QueryAssistantStatsDto,
  QueryAssistantTimeDto,
  QuerySchoolAssistantUserThreadDto,
} from '../dto/assistant'
import {
  AssistantContractsService,
  AssistantService,
  AssistantStatsService,
} from '../services'

@ApiTags('AI admin')
@Controller('v1/admin/assistants/stats')
export class AssistantAdminStatsController {
  constructor(
    private readonly assistantService: AssistantService,
    private readonly assistantStatsService: AssistantStatsService,
    private readonly assistantContractsService: AssistantContractsService,
  ) {}

  /**
   * 获取统计数据
   * @returns
   */
  @ApiOperation({ summary: 'assistant some stats' })
  @Get('')
  @AdminAuth()
  async getAssistantStats(@Query() query: QueryAssistantStatsDto) {
    const [
      totalUsers,
      totalTeachers,
      totalStudents,
      totalBooks,
      totalMessages,
      totalStudentMessages,
      totalUniqueConversations,
      totalStudentUniqueConversations,
    ] = await Promise.all([
      this.assistantStatsService.countUserIds(query),
      this.assistantStatsService.countTeachers(query),
      this.assistantStatsService.countStudents(query),
      this.assistantStatsService.countBooks(query),
      this.assistantStatsService.countMessages(query),
      this.assistantStatsService.countStudentMessages({
        assistantId: query?.assistantId ?? null,
      }),
      this.assistantStatsService.countUniqueConversations({
        assistantId: query?.assistantId ?? null,
      }),
      this.assistantStatsService.countUniqueConversations({
        assistantId: query?.assistantId ?? null,
        userType: EUserType.STUDENT,
      }),
    ])

    return {
      totalUsers: totalUsers.count ?? 0,
      totalTeachers: totalTeachers.count ?? 0,
      totalStudents: totalStudents.count ?? 0,
      totalBooks: totalBooks.count ?? 0,
      totalMessages: totalMessages.count ?? 0,
      totalStudentMessages: totalStudentMessages.count ?? 0,
      totalUniqueConversations: totalUniqueConversations.count ?? 0,
      totalStudentUniqueConversations: totalStudentUniqueConversations.count ?? 0,
    }
  }

  /**
   * 新增使用用户统计
   * @param query
   * @returns
   */
  @ApiOperation({ summary: 'count new user use by date' })
  @AdminAuth()
  @Get('new-user-count')
  async assistantNewUserCount(@Query() query: QueryAssistantTimeDto) {
    return this.assistantStatsService.assistantNewUserCount(query)
  }

  /**
   * 用户消息数量统计
   * @param query
   * @returns
   */
  @ApiOperation({ summary: 'count user message by date' })
  @AdminAuth()
  @Get('user-message-count')
  async assistantUserMessageCount(@Query() query: QueryAssistantTimeDto) {
    return this.assistantStatsService.assistantUserMessageCount(query)
  }

  /**
   * 用户对话数量 对话人次统计
   * @param query
   * @returns
   */
  @ApiOperation({ summary: 'count user session by date' })
  @AdminAuth()
  @Get('user-session-count')
  async assistantUserSessionCount(@Query() query: QueryAssistantTimeDto) {
    return await this.assistantStatsService.assistantUserSessionCount({
      startTime: query.startTime,
      endTime: query.endTime,
      assistantId: query?.assistantId ?? null,
    })
  }

  /**
   * 发布ai合约
   * @param schoolId
   * @param data
   * @param admin
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: '发布AI合约' })
  @ApiBaseResult(BooleanResponse, 200)
  @Post('school/ai-contracts/:schoolId')
  async addContract(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Body() data: CreateAssistantContractDto,
    @CurrentAdmin() admin: any,
  ) {
    await this.assistantContractsService.createContracts(schoolId, data, admin)
    return { status: true }
  }

  /**
   * 获取合约列表
   * @param schoolId
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiPageResult(AssistantContracts, 200)
  @ApiOperation({ summary: '获取AI合约列表' })
  @Get('school/ai-contracts/:schoolId')
  async getScienceContracts(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: PageRequest,
  ) {
    return this.assistantContractsService.getContracts(schoolId, query)
  }

  /**
   * 获取统计数据
   * @returns
   */
  @ApiOperation({ summary: 'admin school assistant some stats' })
  @Get('school/:schoolId')
  @AdminAuth()
  async getAssistantStatsBySchool(
    query: QueryAssistantStatsDto,
    @Param('schoolId', ParseIntPipe) schoolId: number,
  ) {
    const [totalStudents, totalUniqueConversations] = await Promise.all([
      this.assistantStatsService.countStudents(query, schoolId),
      this.assistantStatsService.countUniqueConversations({
        schoolId: schoolId,
        userType: EUserType.STUDENT,
      }),
    ])
    return {
      totalStudents: totalStudents.count,
      totalUniqueConversations: totalUniqueConversations.count,
    }
  }

  /**
   * 用户对话数量 对话人次统计
   * @param query
   * @returns
   */
  @ApiOperation({ summary: 'admin school count user session by date' })
  @AdminAuth()
  @Get('school/user-session-count/:schoolId')
  async assistantUserSessionCountBySchool(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryAssistantTimeDto,
  ) {
    return await this.assistantStatsService.assistantUserSessionCount({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: schoolId,
    })
  }

  @Get('school/times-by-grade/:schoolId')
  @ApiOperation({ summary: 'admin school 使用人次分布' })
  @AdminAuth()
  async timesByGradeBySchool(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryAssistantTimeDto,
  ) {
    return this.assistantStatsService.timesGroupByGrade(query, schoolId)
  }

  @Get('school/users-by-grade/:schoolId')
  @ApiOperation({ summary: 'admin school 使用人数分布' })
  @AdminAuth()
  async usersByGradeBySchool(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryAssistantTimeDto,
  ) {
    return this.assistantStatsService.usersGroupByGrade(query, schoolId)
  }

  @Get('school/user-thread-list/:schoolId')
  @ApiOperation({ summary: 'admin school 对话使用详细 - 列表' })
  @AdminAuth()
  async detailByAssistantThreadList(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QuerySchoolAssistantUserThreadDto,
  ) {
    return this.assistantService.getAssistantUserThread(query, schoolId)
  }

  @AdminAuth()
  @Get('times-group-by-school')
  @ApiOperation({ summary: '使用人次 - 學校' })
  async timesGroupBySchool(@Query() query: AdminAssistantUse) {
    return this.assistantService.timesGroupBySchool(query)
  }

  @AdminAuth()
  @Get('users-group-by-school')
  @ApiOperation({ summary: '使用人數 - 學校' })
  async usersGroupBySchool(@Query() query: AdminAssistantUse) {
    return this.assistantService.usersGroupBySchool(query)
  }
}
