import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
} from '@/common'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { GRADES_LOCALE } from '@/modules/schools/constants'
import { OperationLogService } from '@/modules/system/services'
import {
  CreateAssistantTopicDto,
  DeleteAssistantTopicDtoRequest,
  QueryTopicListDto,
  UpdateAssistantTopicDto,
  UpdateAssistantTopicStatusDtoRequest,
} from '../dto/assistantTopic'
import { AssistantTopicService } from '../services/assistantTopic.service'

@ApiTags('AI admin')
@Controller('v1/admin/assistants/topic')
export class AssistantTopicAdminController {
  constructor(
    private readonly assistantTopicService: AssistantTopicService,
    private readonly logService: OperationLogService,
  ) {}

  /**
   * 创建话题
   * @param createAssistantTopicDto
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'openai create topic' })
  @Post()
  async create(
    @Body() createAssistantTopicDto: CreateAssistantTopicDto,
    @CurrentAdmin() admin: any,
  ): Promise<AssistantTopic> {
    createAssistantTopicDto.created_by = admin
    return this.assistantTopicService.create(createAssistantTopicDto)
  }

  /**
   * 更新话题
   * @param id
   * @param updateAssistantTopicDto
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'openai update topic' })
  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateAssistantTopicDto: UpdateAssistantTopicDto,
  ): Promise<AssistantTopic> {
    return this.assistantTopicService.update(id, updateAssistantTopicDto)
  }

  /**
   * 上下架话题
   * @param id
   * @param updateAssistantTopicDto
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: 'openai update topic' })
  @Post('/status')
  async updateStatus(
    @Body() updateAssistantTopicDto: UpdateAssistantTopicStatusDtoRequest,
  ): Promise<AssistantTopic[]> {
    return this.assistantTopicService.batchUpdateStatus(updateAssistantTopicDto)
  }

  /**
   * 获取话题列表
   * @param query
   * @returns
   */
  @AdminAuth()
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'openai list topic' })
  @Get()
  async findAll(
    @Query() query: QueryTopicListDto,
    @CurrentLocale() local,
  ): Promise<{
    items: AssistantTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    const { items, total, pageIndex, pageSize } =
      await this.assistantTopicService.findAll(query)
    return {
      total: total,
      pageIndex: pageIndex,
      pageSize: pageSize,
      items: items.map((v) => {
        return {
          ...v,
          gradeName: v.grades.includes('all')
            ? local == ELocaleType.ZH_CN
              ? ['全部年级']
              : local == ELocaleType.EN_UK
              ? ['All years']
              : ['全部年级']
            : v.grades.map((grade) => GRADES_LOCALE[local][grade]),
        }
      }),
    }
  }

  /**
   * 删除话题
   * @param query
   * @returns
   */
  @AdminAuth()
  @ApiOperation({ summary: ' batch delete topics' })
  @Delete('delete')
  async deleteTopic(
    @CurrentAdmin() admin: any,
    @Body() deleteAssistantTopicDto: DeleteAssistantTopicDtoRequest,
  ) {
    const topics = await this.assistantTopicService.softDeleteBatch(
      deleteAssistantTopicDto,
    )

    await this.logService.createLog({
      operation: `${topics.length > 3 ? `批量删除` : '删除话题'}${topics
        .slice(0, 3)
        .map((item) => `“${item.id}”`)
        .join(',')} ${topics.length > 3 ? `等${topics.length}個话题` : ''}`,
      user: admin,
    })

    return {
      items: topics,
    }
  }
}
