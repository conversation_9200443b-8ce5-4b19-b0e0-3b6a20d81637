import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  SchoolAdminAuth,
} from '@/common'
import { AssistantSchoolTopic } from '@/entities/assistantSchoolTopic.entity'
import { IGradeService, IUserClassService } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system/services'
import {
  CreateAssistantSchoolTopicDto,
  DeleteAssistantSchoolTopicDtoRequest,
  QuerySchoolTopicListDto,
  UpdateAssistantSchoolTopicDto,
  UpdateAssistantSchoolTopicStatusDtoRequest,
} from '../dto/assistantSchoolTopic'
import { SchoolAssistantTopicMustHasGradesException } from '../exception'
import { AssistantSchoolTopicService } from '../services'

@ApiTags('AI school')
@Controller('v1/school/assistants/topic')
export class AssistantTopicSchoolAdminController {
  constructor(
    private readonly assistantSchoolTopicService: AssistantSchoolTopicService,
    private readonly logService: OperationLogService,
    private readonly gradeService: IGradeService,
    private readonly classService: IUserClassService,
  ) {}

  /**
   * 创建话题
   * @param createAssistantSchoolTopicDto
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'school create topic' })
  @Post()
  async create(
    @Body() createAssistantSchoolTopicDto: CreateAssistantSchoolTopicDto,
    @CurrentSchoolAdmin() admin: any,
  ): Promise<AssistantSchoolTopic> {
    if (
      createAssistantSchoolTopicDto.userType === 'student' &&
      !createAssistantSchoolTopicDto.gradeIds
    ) {
      throw new SchoolAssistantTopicMustHasGradesException()
    }
    createAssistantSchoolTopicDto.createdBy = admin
    createAssistantSchoolTopicDto.schoolId = admin.schoolId
    return this.assistantSchoolTopicService.create(createAssistantSchoolTopicDto)
  }

  /**
   * 更新话题
   * @param id
   * @param updateAssistantSchoolTopicDto
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'school update topic' })
  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateAssistantSchoolTopicDto: UpdateAssistantSchoolTopicDto,
  ): Promise<AssistantSchoolTopic> {
    return this.assistantSchoolTopicService.update(id, updateAssistantSchoolTopicDto)
  }

  /**
   * 上下架话题
   * @param id
   * @param updateAssistantSchoolTopicDto
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'school update topic' })
  @Post('/status')
  async updateStatus(
    @Body()
    updateAssistantSchoolTopicStatusDtoRequest: UpdateAssistantSchoolTopicStatusDtoRequest,
  ): Promise<AssistantSchoolTopic[]> {
    return this.assistantSchoolTopicService.batchUpdateStatus(
      updateAssistantSchoolTopicStatusDtoRequest,
    )
  }

  /**
   * 获取话题列表
   * @param query
   * @returns
   */
  @SchoolAdminAuth()
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'school list topic' })
  @Get()
  async findAll(
    @Query() query: QuerySchoolTopicListDto,
    @CurrentSchoolAdmin() admin: any,
  ): Promise<{
    items: AssistantSchoolTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    query.schoolId = admin.schoolId
    const { items, total, pageIndex, pageSize } =
      await this.assistantSchoolTopicService.findAll(query)

    const classes = await this.classService.listAllClass(query.schoolId)
    const grades = await this.gradeService.listAllGrade(query.schoolId)
    return {
      total: total,
      pageIndex: pageIndex,
      pageSize: pageSize,
      items: items.map((v) => {
        return {
          ...v,
          userTypeName:
            v.userType === 'all'
              ? '全部用户'
              : v.userType === 'student'
              ? '学生'
              : '教职员',
          gradeName: v.gradeIds?.includes(0)
            ? ['全部年级']
            : !v.gradeIds || v.gradeIds.length === 0
            ? ['']
            : v.gradeIds
                .map((gradeId) => {
                  const gradeInfo = grades.find((g) => g.id === gradeId)
                  return gradeInfo ? gradeInfo.grade : ''
                })
                .filter(Boolean),
          className: v.classIds?.includes(0)
            ? ['全部班级']
            : !v.classIds || v.classIds.length === 0
            ? ['']
            : v.classIds
                .map((classId) => {
                  const classInfo = classes.find((c) => c.id === classId)
                  return classInfo ? classInfo.class : ''
                })
                .filter(Boolean),
        }
      }),
    }
  }

  /**
   * 删除话题
   * @param query
   * @returns
   */
  @SchoolAdminAuth()
  @ApiOperation({ summary: ' school delete topics' })
  @Delete('delete')
  async deleteTopic(
    @CurrentSchoolAdmin() admin: any,
    @Body() deleteAssistantSchoolTopicDtoRequest: DeleteAssistantSchoolTopicDtoRequest,
  ) {
    const topics = await this.assistantSchoolTopicService.softDeleteBatch(
      deleteAssistantSchoolTopicDtoRequest,
    )

    // await this.logService.createLog({
    //   operation: `${topics.length > 3 ? `批量删除` : '删除话题'}${topics
    //     .slice(0, 3)
    //     .map((item) => `“${item.id}”`)
    //     .join(',')} ${topics.length > 3 ? `等${topics.length}個话题` : ''}`,
    //   user: admin,
    // })

    return {
      items: topics,
    }
  }
}
