import {
  ApiProperty,
  ApiPropertyOptional,
  IntersectionType,
  PartialType,
  PickType,
} from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { PageRequest } from '@/common'
import { AssistantContracts } from '@/entities'
import { EAssistantStatus, EOrderDirection } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { PageListDto } from './base'

export class CreateAssistantDto {
  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  assistantNumberId?: number

  @ApiProperty()
  name?: MultiLanguage

  @ApiPropertyOptional({
    description: '套餐状态 创建则提交该字段 状态为： ONLINE',
    example: EAssistantStatus.ONLINE,
    enum: EAssistantStatus,
  })
  @IsOptional()
  @IsEnum(EAssistantStatus)
  status?: EAssistantStatus

  @ApiProperty({
    description: '优先跳转',
    example: 'SUBSCRIPTION | REFERENCE',
  })
  @IsOptional()
  preferredVersion?: string

  @ApiProperty()
  @IsOptional()
  openaiFileIds?: string[]

  fileStatus?: string
}

export class UpdateAssistantDto {
  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  assistantNumberId: number

  @ApiProperty()
  name?: MultiLanguage

  @ApiPropertyOptional({
    description: '套餐状态 创建则提交该字段 状态为： ONLINE',
    example: EAssistantStatus.ONLINE,
    enum: EAssistantStatus,
  })
  @IsOptional()
  @IsEnum(EAssistantStatus)
  status?: EAssistantStatus

  @ApiProperty({
    description: '优先跳转',
    example: 'SUBSCRIPTION | REFERENCE',
  })
  @IsOptional()
  preferredVersion?: string

  @ApiProperty()
  @IsOptional()
  openaiFileIds?: string[]
}

export class QueryAssistantDetailDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  id: number
}

export class QueryAssistantDto {
  @ApiProperty()
  @IsOptional()
  page: number

  @ApiProperty()
  @IsOptional()
  pageSize: number

  @ApiProperty()
  @IsOptional()
  status?: EAssistantStatus

  @ApiProperty()
  @IsOptional()
  version?: string

  @ApiProperty()
  @IsOptional()
  keyword?: string

  @ApiProperty()
  @IsOptional()
  orderField?: string | 'createdAt' | 'updatedAt'

  @ApiProperty()
  @IsOptional()
  orderDirection?: 'ASC' | 'DESC'
}

export class QueryAssistantStatsDto {
  @ApiProperty()
  @IsOptional()
  assistantId?: string
}

export class QueryAssistantTimeDto {
  @ApiProperty()
  @Type(() => Number)
  startTime?: number

  @ApiProperty()
  @Type(() => Number)
  endTime?: number

  @ApiProperty()
  @IsOptional()
  assistantId?: string
}

export class AdminAssistantUse extends IntersectionType(
  PageListDto,
  QueryAssistantTimeDto,
) {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  sortBy?: 'times' | 'users'

  assistantId?: string
}

export class CreateThreadMessageDto {
  @ApiProperty()
  content: string
}

export class CancelThreadMessageDto {
  @ApiProperty()
  runId: string
}

export class ThreadMessageDto {
  @ApiProperty()
  threadId?: string

  @ApiProperty()
  runId?: string

  @ApiProperty()
  content?: string

  @ApiProperty()
  after?: string

  @ApiProperty()
  limit?: number
}

export class ExportGroupBySchoolDto extends QueryAssistantTimeDto {
  @ApiPropertyOptional({
    description: 'times | users',
  })
  @IsOptional()
  @Type(() => String)
  sortBy?: 'times' | 'users'
}

export class QueryThreadMessageDto extends QueryAssistantTimeDto {
  @ApiPropertyOptional()
  @IsOptional()
  schoolId?: number

  @ApiProperty()
  userId?: number

  @ApiProperty()
  after?: string

  @ApiProperty()
  @Type(() => Number)
  limit?: number

  @ApiProperty()
  order?: any
}

export class CreateAssistantContractDto extends PartialType(
  PickType(AssistantContracts, ['contractNo']),
) {
  @ApiProperty()
  @IsString()
  assistantId: string

  @ApiProperty()
  @IsBoolean()
  hasAssistant: boolean
}

export class QueryThreadDetailDto extends QueryAssistantTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  userId: number

  @ApiProperty()
  @Type(() => Number)
  schoolId?: number
}

export class QuerySchoolAssistantUserThreadDto extends IntersectionType(
  QueryAssistantTimeDto,
  PageRequest,
) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  gradeId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  classId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional({
    description: 'className | gradeName | users | times',
  })
  @IsOptional()
  sortBy?: 'className' | 'gradeName' | 'users' | 'times'
  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}
