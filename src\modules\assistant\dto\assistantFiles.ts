import {
  ApiProperty,
  ApiPropertyOptional,
  IntersectionType,
  OmitType,
} from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsString } from 'class-validator'
import { EOrderDirection } from '@/enums'
import { PageListDto, SelectDtoRequest } from './base'

export class QueryFilesListDto extends IntersectionType(PageListDto, SelectDtoRequest) {
  @ApiPropertyOptional({
    description: '语言',
    example: 'en | zh_en | zh',
  })
  language?: string

  @ApiPropertyOptional({
    description: 'isbn|书籍名称|作者名',
    example: '9789629967109',
  })
  keyword?: string

  @ApiPropertyOptional({
    description: '是否需要分页数据  boolean',
  })
  isNeedPage?: boolean

  @ApiPropertyOptional()
  authorIds: number[]

  @ApiPropertyOptional()
  publisherIds: number[]

  @ApiProperty({
    description: '使用版本',
  })
  useAassistant?: string

  @ApiPropertyOptional({
    description: '排序',
    example: 'ASC|DESC',
  })
  orderDirection?: 'ASC' | 'DESC'
}

export class QueryAssistantFileListDto extends QueryFilesListDto {
  @ApiProperty({
    description: '套餐id',
  })
  assistantNumberId?: number

  assistantId?: string
}

export class QueryAssistantFileDto {
  @ApiProperty()
  @IsString()
  @Type(() => String)
  isbn: string
}

export class SaveAssistantFileDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  id: number

  @ApiProperty()
  @IsString()
  @Type(() => String)
  url: string
}

export class DeleteAssistantFileTaskDto {
  @IsString()
  @Type(() => String)
  isbn: string

  @IsString()
  @Type(() => String)
  openaiFileId?: string

  @IsString()
  @Type(() => String)
  vectorStoreId?: string

  @IsString()
  @Type(() => String)
  assistantId?: string
}

export class UpdateAssistantFileTaskDto {
  @IsString()
  @Type(() => String)
  openaiFileId?: string

  @IsString()
  @Type(() => String)
  updateOpenaiFileId?: string
}

export class UpdateFileDto {
  updatedBy?: any
  assistantId?: string
  bookId?: number
  isbn?: string
  awsUrl?: string
  openaiFileId?: string
  vectorStoreId?: string
  fileBytes?: number
  status?: string
  fileName?: any
  version?: any
}

export class SaveFileDto {
  createdBy?: any
  assistantId?: string
  bookId?: number
  isbn?: string
  awsUrl?: string
  openaiFileId?: string
  vectorStoreId?: string
  fileBytes?: number
  status?: string
  fileName?: any
  version?: any
}

export class DeleteAssistantFilesDtoRequest extends IntersectionType(
  OmitType(QueryFilesListDto, ['total', 'pageIndex', 'pageSize'] as const),
  SelectDtoRequest,
) {}
