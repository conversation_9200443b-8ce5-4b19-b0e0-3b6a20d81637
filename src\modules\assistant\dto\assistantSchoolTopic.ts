import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { IsArray, IsJSON, IsNumber, IsOptional, IsString } from 'class-validator'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { MultiLanguage } from '@/interfaces'
import { PageListDto, SelectDtoRequest } from './base'

export class CreateAssistantSchoolTopicDto {
  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  answer?: string

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  name: MultiLanguage

  @ApiProperty({ type: Array })
  @ApiPropertyOptional({
    description: '年级id 全部则为[0]',
    example: '[0]',
  })
  @IsOptional()
  @IsArray()
  gradeIds?: number[]

  @ApiProperty({ type: Array })
  @ApiPropertyOptional({
    description: '班级id 全部则为[0]',
    example: '[0]',
  })
  @IsOptional()
  @IsArray()
  classIds?: number[]

  @ApiProperty({ type: String })
  @ApiPropertyOptional({
    description: '用户类别',
    example: 'all | student | teacher',
  })
  @IsOptional()
  @IsString()
  userType: string

  @IsOptional()
  @IsNumber()
  schoolId?: any

  @IsOptional()
  @IsJSON()
  createdBy?: any

  @IsOptional()
  @IsJSON()
  updatedBy?: any
}

export class UpdateAssistantSchoolTopicDto {
  @ApiPropertyOptional({ type: String })
  @IsOptional()
  name: MultiLanguage

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  answer?: string

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  status?: string

  @ApiProperty({ type: Array })
  @ApiPropertyOptional({
    description: '年级id 全部则为[0]',
    example: '[0]',
  })
  @IsOptional()
  @IsArray()
  gradeIds?: number[]

  @ApiProperty({ type: Array })
  @ApiPropertyOptional({
    description: '班级id 全部则为[0]',
    example: '[0]',
  })
  @IsOptional()
  @IsArray()
  classIds?: number[]

  @ApiProperty({ type: String })
  @ApiPropertyOptional({
    description: '用户类别',
    example: 'all | student | teacher',
  })
  @IsOptional()
  @IsString()
  userType: string

  @IsOptional()
  @IsJSON()
  createdBy?: any

  @IsOptional()
  @IsJSON()
  updatedBy?: any

  @IsOptional()
  @IsNumber()
  schoolId?: any
}

export class QuerySchoolTopicListDto extends PageListDto {
  @ApiPropertyOptional({
    description: 'grade_id',
    example: '',
  })
  schoolId?: number

  @ApiPropertyOptional({
    description: 'grade_id',
    example: '',
  })
  gradeId?: number

  @ApiPropertyOptional({
    description: 'class_id',
    example: '',
  })
  classId?: number

  @ApiPropertyOptional({
    description: '如果是全部 则为all',
    example: 'all | student | teacher',
  })
  userType?: string

  @ApiPropertyOptional({
    description: '话题名称',
    example: '你好',
  })
  name?: string

  @ApiPropertyOptional({
    description: '话题状态',
    example: 'online',
  })
  status?: string

  @ApiPropertyOptional({
    description: 'grade code',
    example: 'KINDERGARTEN',
  })
  grade?: string

  assistant?: string
}

export class SchoolQueryTopicListDto extends PageListDto {
  @ApiPropertyOptional({
    description: '用户id',
    example: '213',
  })
  userId: string
}

export class TopicPageListResultDto extends PageListDto {
  items: AssistantTopic[]
}

export class UpdateAssistantSchoolTopicStatusDtoRequest extends IntersectionType(
  QuerySchoolTopicListDto,
  SelectDtoRequest,
) {
  @ApiProperty()
  updateStatus: string
}

export class DeleteAssistantSchoolTopicDtoRequest extends IntersectionType(
  QuerySchoolTopicListDto,
  SelectDtoRequest,
) {}
