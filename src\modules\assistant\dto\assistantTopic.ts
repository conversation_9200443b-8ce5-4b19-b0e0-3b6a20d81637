import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { IsArray, IsJSON, IsOptional, IsString } from 'class-validator'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { PageListDto, SelectDtoRequest } from './base'

export class CreateAssistantTopicDto {
  @ApiPropertyOptional({ type: String })
  nameLocale?: string

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  answer?: string

  @ApiProperty({ type: Array })
  @IsOptional()
  @IsArray()
  grades: string[]

  @ApiProperty({ type: Array })
  @IsOptional()
  @IsArray()
  assistants: string[]

  @IsOptional()
  @IsJSON()
  created_by?: any

  @IsOptional()
  @IsJSON()
  updated_by?: any
}
export class CreateAssistantSessionCountDto {
  @ApiPropertyOptional()
  @IsOptional()
  assistantId: string

  @ApiPropertyOptional()
  @IsOptional()
  threadId: string

  @ApiPropertyOptional()
  @IsOptional()
  userId: number

  @ApiPropertyOptional()
  @IsOptional()
  userType: string

  @ApiPropertyOptional()
  @IsOptional()
  userClassId?: number

  @ApiPropertyOptional()
  @IsOptional()
  gradeId?: number

  @ApiPropertyOptional()
  @IsOptional()
  schoolId?: number

  @ApiPropertyOptional()
  @IsOptional()
  topicId?: number
}

export class CreateAssistantTopicCountDto {
  @ApiPropertyOptional()
  @IsOptional()
  assistantId: string

  @ApiPropertyOptional()
  @IsOptional()
  threadId: string

  @ApiPropertyOptional()
  @IsOptional()
  userId: number

  @ApiPropertyOptional()
  @IsOptional()
  userType: string

  @ApiPropertyOptional()
  @IsOptional()
  userClassId?: number

  @ApiPropertyOptional()
  @IsOptional()
  gradeId?: number

  @ApiPropertyOptional()
  @IsOptional()
  schoolId?: number

  @ApiPropertyOptional()
  @IsOptional()
  topicId?: number
}
export class UpdateAssistantTopicDto {
  @ApiPropertyOptional({ type: Boolean })
  @IsOptional()
  isAll?: boolean

  @ApiPropertyOptional({ type: Array })
  @IsOptional()
  @IsArray()
  ids?: number[]

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  nameLocale?: string

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  answer?: string

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  status?: string

  @ApiPropertyOptional({ type: Array })
  @IsOptional()
  @IsArray()
  grades?: string[]

  @ApiPropertyOptional({ type: Array })
  @IsOptional()
  @IsArray()
  assistants?: string[]

  @IsOptional()
  @IsJSON()
  updated_by?: any
}

export class QueryTopicListDto extends PageListDto {
  @ApiPropertyOptional({
    description: 'grade code',
    example: 'KINDERGARTEN',
  })
  grade?: string

  @ApiPropertyOptional({
    description: 'assistant id  如果是全部 则为all',
    example: 'asst_Dwk9hBsH9S82L70YSc6jJb4C',
  })
  assistant?: string

  @ApiPropertyOptional({
    description: '话题名称',
    example: '你好',
  })
  name?: string

  @ApiPropertyOptional({
    description: '话题状态',
    example: 'online',
  })
  status?: string

  @ApiPropertyOptional({
    description: '话题排序字段',
    example: "'ASC' | 'DESC'",
  })
  orderDirection?: 'ASC' | 'DESC'

  @ApiPropertyOptional({
    description: '是否为单套餐显示下的话题列表',
  })
  isOne?: boolean
}

export class SchoolQueryTopicListDto extends PageListDto {
  @ApiPropertyOptional({
    description: '用户id',
    example: '213',
  })
  userId: string
}

export class TopicPageListResultDto extends PageListDto {
  items: AssistantTopic[]
}

export class UpdateAssistantTopicStatusDtoRequest extends IntersectionType(
  QueryTopicListDto,
  SelectDtoRequest,
) {
  @ApiProperty()
  updateStatus: string
}

export class DeleteAssistantTopicDtoRequest extends IntersectionType(
  QueryTopicListDto,
  SelectDtoRequest,
) {}
