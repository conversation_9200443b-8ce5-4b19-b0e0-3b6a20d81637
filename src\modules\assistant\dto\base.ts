import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsBoolean, IsNumber, IsOptional } from 'class-validator'

export class PageListDto {
  @ApiPropertyOptional()
  total?: number

  @ApiPropertyOptional()
  pageIndex?: number

  @ApiPropertyOptional()
  pageSize?: number
}

export class SelectDtoRequest {
  @ApiPropertyOptional({
    description: '是否全选',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isFullSelected?: boolean

  @ApiPropertyOptional({
    description: '反选数组, isFullSelected = true时有效',
    type: () => [Number],
    example: [1, 2],
  })
  @IsOptional()
  @Type(() => Number)
  @Transform((params) => [].concat(params.value))
  @IsNumber({ allowNaN: false }, { each: true })
  excludedIds?: number[]

  @ApiPropertyOptional({
    description: '指定数组, isFullSelected = false时有效',
    type: () => [Number],
    example: [1, 2],
  })
  @IsOptional()
  @Type(() => Number)
  @Transform((params) => [].concat(params.value))
  @IsNumber({ allowNaN: false }, { each: true })
  specifiedIds?: number[]
}
