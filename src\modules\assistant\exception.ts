import { getExceptionGroup } from '@/common/decorators'
import { ExceptionGroup } from '@/common/enums'
import { BaseException } from '@/common/exceptions'

const AssistantExceptionMeta = getExceptionGroup(ExceptionGroup.ASSISTANT)

@AssistantExceptionMeta(1, {
  en_us: 'upload file failed',
  zh_HK: '上傳文件失敗',
  zh_cn: '上傳文件失敗',
})
export class UploadFileException extends BaseException {}

@AssistantExceptionMeta(2, {
  en_us: 'submit file failed',
  zh_HK: '保存文件失敗',
  zh_cn: '保存文件失敗',
})
export class SubmitFileException extends BaseException {}

@AssistantExceptionMeta(3, {
  en_us: 'Only pdf is supported',
  zh_HK: '僅支持pdf類型',
  zh_cn: '仅支持pdf类型',
})
export class OnlySupportPdfException extends BaseException {}
@AssistantExceptionMeta(10, {
  en_us: 'duplicate assistant name',
  zh_HK: '重複的助手名稱',
  zh_cn: '重复的助手名称',
})
export class DuplicateAssistantException extends BaseException {}

@AssistantExceptionMeta(11, {
  en_us: 'assistant not exist',
  zh_cn: 'AI套餐不存在',
  zh_HK: 'AI套餐不存在',
})
export class AssistantNotExistException extends BaseException {}

@AssistantExceptionMeta(12, {
  en_us: 'assistant not exist',
  zh_cn: '正有學校使用中，不能刪除',
  zh_HK: '正有學校使用中，不能刪除',
})
export class AssistantInUseBySchoolException extends BaseException {}

@AssistantExceptionMeta(13, {
  en_us: 'student must choose grade',
  zh_cn: '學生必須選擇年級',
  zh_HK: '学生必须选择年級',
})
export class SchoolAssistantTopicMustHasGradesException extends BaseException {}

@AssistantExceptionMeta(15, {
  en_us: 'User not exist thread',
  zh_cn: '用户不已存在对话',
  zh_HK: '用戶不已存在對話',
})
export class UserThreadNotExistException extends BaseException {}

@AssistantExceptionMeta(16, {
  en_us: 'User not exist run id',
  zh_cn: '用户不已存在运行记录',
  zh_HK: '用戶不已存在运行记录',
})
export class UserRunIdNotExistException extends BaseException {}

@AssistantExceptionMeta(17, {
  en_us: 'Something went wrong,please try again',
  zh_cn: '出了点问题，请重试',
  zh_HK: '出了點問題，請重試',
})
export class AssistantErrorException extends BaseException {}

@AssistantExceptionMeta(18, {
  en_us: "I'm swamped with conversations right now. Please wait a moment.",
  zh_cn: '太多人跟我对话了，请等一下',
  zh_HK: '太多人跟我對話了，請等一下',
})
export class AssistantRateLimitException extends BaseException {}
