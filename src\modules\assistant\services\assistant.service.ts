import { Inject, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { InjectRepository } from '@nestjs/typeorm'
import { Model } from 'mongoose'
import { In, Repository } from 'typeorm'
import { RedisService } from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import {
  AssistantCont<PERSON>ts,
  AssistantThread,
  AssistantThreadMessageRuns,
} from '@/entities'
import { Assistant } from '@/entities/assistant.entity'
import { AssistantMessages } from '@/entities/assistantMessage.entity'
import { AssistantSessionCount } from '@/entities/assistantSessionCount.entity'
import { AssistantVectorstoreFiles } from '@/entities/assistantVectorFiles.entity'
import { EAssistantStatus, EOpeaiVectorStoreFileStatus, EUserType } from '@/enums'
import { getUserAssistantThreadKey, PAGE_SIZE } from '@/modules/constants'
import { IGradeService, IUserRepo } from '@/modules/shared/interfaces'
import { allNameCondition, nameLikeCondition } from '@/utils/nameCondition.util'
import { MultiLanguage } from '@/interfaces'
import {
  AdminAssistantUse,
  CreateAssistantDto,
  ExportGroupBySchoolDto,
  QuerySchoolAssistantUserThreadDto,
  QueryThreadDetailDto,
  QueryThreadMessageDto,
} from '../dto/assistant'
import { CreateAssistantSessionCountDto } from '../dto/assistantTopic'
import { AssistantInUseBySchoolException, AssistantNotExistException } from '../exception'
import { AssistantStatsService } from './assistantStats.service'

@Injectable()
export class AssistantService {
  constructor(
    @InjectModel(AssistantMessages.name)
    private assistantMessagesModel: Model<AssistantMessages>,
    @InjectRepository(Assistant)
    private readonly assistantRepository: Repository<Assistant>,
    @InjectRepository(AssistantThread)
    private readonly assistantThreadRepository: Repository<AssistantThread>,
    @InjectRepository(AssistantSessionCount)
    private readonly assistantSessionCountRepository: Repository<AssistantSessionCount>,
    @InjectRepository(AssistantThreadMessageRuns)
    private readonly assistantThreadMessageRunsRepository: Repository<AssistantThreadMessageRuns>,
    @InjectRepository(AssistantContracts)
    private readonly assistantContractsRepository: Repository<AssistantContracts>,
    private readonly redisService: RedisService,
    private readonly assistantStatsService: AssistantStatsService,
    private readonly gradeService: IGradeService,
    @Inject(IUserRepo) private readonly userRepository: IUserRepo,
    private readonly taskService: TaskService
  ) {}

  /**
   * 【数据表记录】
   *  关系型 mysql
   *
   *  一，assistant ===> id,assistant_id,vector_store_id,name,status,preferredVersion
   *  二，assistant_files ===> id,isbn,aws_url,openai_file_id,file_bytes,book_id,file_name
   *  三，assistant_vectorstore_files ==> id,assistant_id,vector_store_id,openai_file_id,status
   *
   *  四，assistant_thread ===> id,assistant_id,thread_id,user_id,user_type,class,grade,school_id
   *  五，assistant_thread_message_runs ===> id,assistant_id,thread_id,from_msg_id,to_run_id,user_id,user_type,class,grade,school_id
   *  六，assistant_admin_error_response ===> id,assistant,file_id,ibsn,error_msg
   *  七，assistant_client_error_response ===> id,assistant,thread_id,run_id,msg_id,error_msg
   *
   *  非关系型 mongodb redis
   *  缓存 redis   1，assistant 基础信息  2，缓存file_id book_name version
   *  缓存 mongodb 1，messages 对话数据
   */

  /**
   * 草稿状态只记录
   * @param data
   * @param admin
   */
  async createAIDraft(data: CreateAssistantDto, admin: any) {
    const queryRunner = this.assistantRepository.manager.connection.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      // 保存 assistant
      const assistant = await queryRunner.manager.save(Assistant, {
        id: data.assistantNumberId ?? null,
        name: data.name,
        status: data.status ?? EAssistantStatus.DRAFT,
        preferredVersion: data.preferredVersion,
        createdBy: admin,
      })

      // 批量保存 vectorstore files
      const vectorStoreFileRecords = data.openaiFileIds?.map((fileId) => ({
        assistantNumberId: data.assistantNumberId ? data.assistantNumberId : assistant.id,
        status: data.fileStatus ?? EOpeaiVectorStoreFileStatus.PENDING,
        openaiFileId: fileId,
      }))
      await queryRunner.manager.save(AssistantVectorstoreFiles, vectorStoreFileRecords)

      // 提交事务
      await queryRunner.commitTransaction()

      return assistant
    } catch (error) {
      // 发生错误时回滚事务
      await queryRunner.rollbackTransaction()
      throw error
    } finally {
      // 释放数据库连接
      await queryRunner.release()
    }
  }

  /**
   * 创建AI套餐
   * @param data
   * @param admin
   */
  async createAI(data: CreateAssistantDto, admin: any, local: any) {
    // 1. 先保存数据 状态改为进行中
    data.status = EAssistantStatus.IN_PROGRESS
    data.fileStatus = EOpeaiVectorStoreFileStatus.IN_PROGRESS
    const assistant = await this.createAIDraft(data, admin)
 
    // 2. 异步创建AI助手
    data.assistantNumberId = assistant.id
    await this.taskService.deliver(
      ETaskType.CREATE_AI_ASSISTANT,
      {
        data,
        admin,
        local,
        type: ETaskType.CREATE_AI_ASSISTANT,
      },
      {}
    )
    return { status: true }
  }

  /**
   * 更新AI套餐草稿
   * @param data
   * @param admin
   */
  async updateAIDraft(data: CreateAssistantDto, admin: any) {
    const queryRunner = this.assistantRepository.manager.connection.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      // 更新 assistant
      const assistant = await queryRunner.manager.save(Assistant, {
        id: data.assistantNumberId,
        name: data.name,
        status: data.status ?? EAssistantStatus.DRAFT,
        preferredVersion: data.preferredVersion,
        updatedBy: admin,
      })
      const newFileIds = data.openaiFileIds ?? []
      // 获取现有的文件列表
      const existingFiles = await queryRunner.manager.find(AssistantVectorstoreFiles, {
        where: {
          assistantNumberId: data.assistantNumberId,
        },
      })

      const existingFilesMap = new Map(
        existingFiles.map((file) => [file.openaiFileId, file])
      )
      const fileIToDeleteData = existingFiles
        .filter((file) => !newFileIds.includes(file.openaiFileId))
        .map((file) => ({ id: file.id, openaiFileId: file.openaiFileId }))

      const fileIdsToUpsert = newFileIds
      const filesToUpsertData = fileIdsToUpsert.map((fileId) => {
        const existingFile = existingFilesMap.get(fileId)
        // 过滤已完成状态的文件
        const statusToSet =
          existingFile && existingFile.status === EOpeaiVectorStoreFileStatus.COMPLETED
            ? EOpeaiVectorStoreFileStatus.COMPLETED
            : data.fileStatus ?? EOpeaiVectorStoreFileStatus.PENDING

        return {
          id: existingFile ? existingFile.id : undefined,
          assistantNumberId: data.assistantNumberId,
          openaiFileId: fileId,
          status: statusToSet,
        }
      })

      // 删除不再关联的文件
      if (fileIToDeleteData.length > 0) {
        await queryRunner.manager.softDelete(
          AssistantVectorstoreFiles,
          fileIToDeleteData.map((f) => f.id)
        )
      }

      //  新增或更新文件
      if (filesToUpsertData.length > 0) {
        await queryRunner.manager.upsert(AssistantVectorstoreFiles, filesToUpsertData, [
          'assistantNumberId',
          'openaiFileId',
        ])
      }

      // 提交事务
      await queryRunner.commitTransaction()

      return {
        assistant,
        fileIToDeleteData,
        filesToUpsertData,
      }
    } catch (error) {
      // 发生错误时回滚事务
      await queryRunner.rollbackTransaction()
      throw error
    } finally {
      // 释放数据库连接
      await queryRunner.release()
    }
  }

  /**
   * 更新AI套餐
   * @param data
   * @param admin
   */
  async updateAI(data: CreateAssistantDto, admin: any, local: any) {
    const assistant = await this.getAssistantDetail(data.assistantNumberId)
    if (assistant.status === EAssistantStatus.DRAFT) {
      data.status = EAssistantStatus.IN_PROGRESS
    }
    data.fileStatus = EOpeaiVectorStoreFileStatus.IN_PROGRESS
    const { fileIToDeleteData, filesToUpsertData } = await this.updateAIDraft(data, admin)

    await this.taskService.deliver(
      ETaskType.UPDATE_AI_ASSISTANT,
      {
        data: {
          ...data,
          fileIToDeleteData,
          filesToUpsertData,
        },
        admin,
        local,
        type: ETaskType.UPDATE_AI_ASSISTANT,
      },
      {}
    )
    return { status: true }
  }

  /**
   * 删除AI套餐
   * @param data
   * @param admin
   */
  async deleteAI(id: number, admin: any, local: any) {
    //获取assistant
    const assistant = await this.getAssistantDetail(id)

    // 学校使用套餐则不能删除
    const existSchoolInUse = await this.assistantContractsRepository
      .createQueryBuilder('contract')
      .leftJoin('contract.assistant', 'assistant')
      .where('assistant.assistantId = :assistantId', {
        assistantId: assistant.assistantId,
      })
      .getOne()

    if (existSchoolInUse) {
      throw new AssistantInUseBySchoolException()
    }

    await this.assistantRepository.softDelete(id)
    if (assistant.status === EAssistantStatus.ONLINE) {
      await this.taskService.deliver(
        ETaskType.DELETE_AI_ASSISTANT,
        {
          data: assistant,
          admin,
          local,
          type: ETaskType.DELETE_AI_ASSISTANT,
        },
        {}
      )
    }

    return { status: true }
  }

  async getAssistantByName(name: MultiLanguage) {
    return await this.assistantRepository
      .createQueryBuilder('assistant')
      .where(allNameCondition(name))
      .getOne()
  }

  /**
   * 获取套餐Tab列表
   * @returns
   */
  async getAssistantTabList(status?: EAssistantStatus) {
    return await this.assistantRepository.find({
      where: {
        status: status ?? EAssistantStatus.ONLINE,
      },
      select: ['id', 'assistantId', 'name'],
    })
  }

  /**
   * 获取套餐列表
   * @param options
   * @returns
   */
  async getAssistantList(options: {
    page: number
    pageSize: number
    status?: EAssistantStatus
    version?: string
    keyword?: string
    orderField?: string | 'createdAt' | 'updatedAt'
    orderDirection?: 'ASC' | 'DESC'
  }) {
    const builder = this.assistantRepository
      .createQueryBuilder('assistant')
      .select([
        'assistant.id',
        'assistant.name',
        'assistant.status',
        'assistant.preferredVersion',
        'assistant.assistantId',
        'assistant.vectorStoreId',
        'assistant.createdAt',
        'assistant.createdBy',
        'assistant.updatedBy',
        'assistant.updatedAt',
      ])

    if (options.status) {
      builder.andWhere('assistant.status = :status', { status: options.status })
    }
    if (options.version) {
      builder.andWhere('assistant.preferredVersion = :version', {
        version: options.version,
      })
    }
    if (options.keyword) {
      const condition = nameLikeCondition(options.keyword, 'assistant')
      builder.andWhere(condition.where, condition.parameter)
    }

    builder.orderBy(
      options.orderField ? 'assistant.' + options.orderField : 'assistant.createdAt',
      options.orderDirection
    )
    const skip = (options.page - 1) * options.pageSize
    builder.skip(skip).take(options.pageSize)
    const [items, total] = await builder.getManyAndCount()

    return {
      items,
      total,
      pageIndex: options.page,
      pageSize: options.pageSize,
    }
  }

  /**
   * 获取套餐详情
   * @param id
   * @returns
   */
  async getAssistantDetail(id: number) {
    const assistant = await this.assistantRepository.findOne({ where: { id  } })
    if (!assistant) {throw new AssistantNotExistException()}
    return assistant
  }

  async getSchoolAssistant(schoolId: number) {
    const assistantContract = await this.assistantContractsRepository
      .createQueryBuilder('contract')
      .leftJoin('contract.assistant', 'assistant')
      .where('contract.schoolId = :schoolId', { schoolId })
      .getOne()
    if (!assistantContract) {
      throw new AssistantNotExistException()
    }
    return assistantContract.assistant
  }

  async existRunId(runId: string) {
    return await this.assistantThreadMessageRunsRepository.findOne({ where: { runId  } })
  }

  /**
   * 根据用户id获取线程对话
   * @param userId
   * @returns
   */
  async getUserThreadId(userId: number, schoolId: number) {
    // get redis
    const userThreadId = await this.redisService.hget(
      getUserAssistantThreadKey(schoolId),
      userId.toString()
    )
    if (userThreadId) {
      return userThreadId
    }
    // 不存在查询db
    const userThread = await this.assistantThreadRepository.findOne({ where: { userId  } })
    if (!userThread) {
      return ''
    }
    // 设置 Redis 数据
    await this.redisService.hset(
      getUserAssistantThreadKey(schoolId),
      userId.toString(),
      userThread.threadId.toString()
    )
    return userThread.threadId
  }

  /**
   * 根据 userId 获取对应的 messages，并通过 msgId 分页
   * @param query
   * @returns
   */
  async getMessagesList(query: QueryThreadMessageDto) {
    try {
      let queryConditions: Record<string, any> = { userId: query.userId }
      query.order = query.order ?? { created_at: -1 }
      if (query.after) {
        const lastMessage = await this.assistantMessagesModel.findOne({
          where: { msgId: query.after},
        })
        if (lastMessage) {
          queryConditions = {
            ...queryConditions,
            created_at: { $lt: lastMessage.created_at },
          }
        }
      }
      if (query.startTime && query.endTime) {
        queryConditions.created_at = {
          ...queryConditions.created_at,
          $gte: new Date(query.startTime * 1000), // 大于等于 startTime
          $lte: new Date(query.endTime * 1000), // 小于等于 endTime
        }
      }
      // 查询 MongoDB 获取数据
      const response = await this.assistantMessagesModel
        .find(queryConditions)
        .sort(query.order) // 按时间降序排序
        .limit(query.limit + 1) // 查询多一条数据用于判断是否有下一页
        .exec()

      // 处理数据
      const processedData = await Promise.all(
        response.slice(0, query.limit).map(async (message) => {
          message = message.toObject()
          return {
            id: message.msgId,
            content: message.content,
            created_at: message.created_at,
            metadata: {},
            role: message.role,
            run_id: message.runId,
            thread_id: message.threadId,
            object: message.object,
          }
        })
      )

      // 判断是否有下一页
      const hasNextPage = response.length > query.limit

      // 如果有下一页，生成 `nextPageInfo`
      const nextPageInfo = hasNextPage
        ? { params: { after: response[query.limit - 1].msgId } }
        : null

      // 返回处理后的数据
      return {
        data: processedData,
        hasNextPage,
        nextPageInfo,
      }
    } catch (error) {
      // 错误处理
      console.error(error)
    }
  }

  /**
   * 保存对话消息到mongodb
   * @param conversationData
   * @returns
   */
  async saveMessage(data: Partial<AssistantMessages>): Promise<AssistantMessages> {
    const createdConversation = new this.assistantMessagesModel(data)
    return await createdConversation.save()
  }

  /**
   * 更新对话消息到mongodb
   * @param updateData
   * @returns
   */
  async updateMessage(
    runId: string,
    updateData: Partial<AssistantMessages>
  ): Promise<AssistantMessages | null> {
    return this.assistantMessagesModel.findOneAndUpdate({ runId: runId }, updateData, {
      new: true,
    })
  }

  /**
   * 每打开一次对话记录一次
   * @param createAssistantTopicDto
   * @returns
   */
  async createAssistantSessionCount(data: CreateAssistantSessionCountDto) {
    await this.assistantSessionCountRepository.save({
      assistantId: data.assistantId,
      threadId: data.threadId,
      user: { id: data.userId },
      school: { id: data.schoolId },
      userClass: data.userClassId ? { id: data.userClassId } : null,
      gradeId: data.gradeId ? data.gradeId : null,
      userType: data.userType,
    })
  }

  /**
   * 获取学生对话详情
   * @param quey
   * @returns
   */
  async getAssistantUserDetail(query: QueryThreadDetailDto) {
    const userMessageNumber = await this.assistantStatsService.countStudentMessages(query)
    const userThreadCount = await this.assistantStatsService.countUniqueConversations(
      query
    )
    const user = await this.userRepository.findUsers({ ids: [query.userId] })
    const grade = await this.gradeService.getGrade(user[0].userClass.gradeId)
    return {
      userName: user[0].givenName,
      serialNo: user[0].serialNo,
      class: user[0].userClass.class,
      grade: grade.grade,
      userMessageNumber: userMessageNumber.count,
      userThreadCount: userThreadCount.count,
      userType: user[0].type,
      startTime: query.startTime,
      endTime: query.endTime,
    }
  }

  /**
   * 获取消息列表
   * @param userId
   * @returns
   */
  async getUserMessageList(query: {
    userId: number
    startTime: number
    endTime: number
  }) {
    return await this.assistantMessagesModel.find({
      where: { userId: query.userId  },
    }).lean().exec()
  }

  /**
   * 获取用户对话列表 by学校
   * @param query
   * @param schoolId
   * @returns
   */
  async getAssistantUserThread(
    query: QuerySchoolAssistantUserThreadDto,
    schoolId?: number
  ) {
    const {
      pageIndex = 1,
      pageSize = 10,
      keyword,
      sortBy = 'messageNumber',
      orderDirection = 'desc',
      gradeId,
      classId,
      startTime,
      endTime,
    } = query

    // 根据关键字进行用户筛选
    let keywordCondition = ''
    if (keyword) {
      keywordCondition = `
        (
          users.given_name LIKE '%${keyword}%' OR
          users.serial_no LIKE '%${keyword}%' OR
          users.email LIKE '%${keyword}%'
        )
      `
    }

    const startTimeISO = startTime ? new Date(startTime * 1000).toISOString() : null
    const endTimeISO = endTime ? new Date(endTime * 1000).toISOString() : null

    const conditionStr = [
      `assistant_thread_message_runs.school_id = ${schoolId}`,
      `assistant_thread_message_runs.user_type = 'student'`,
      gradeId ? `assistant_thread_message_runs.grade_id = ${gradeId}` : '',
      classId ? `assistant_thread_message_runs.user_class_id = ${classId}` : '',
      keywordCondition,
      startTimeISO ? `assistant_thread_message_runs.created_at >= '${startTimeISO}'` : '',
      endTimeISO ? `assistant_thread_message_runs.created_at <= '${endTimeISO}'` : '',
    ]
      .filter((v) => v)
      .join(' AND ')

    // 查询消息数量和线程数量
    const groupByQuery = `
      SELECT
          users.id AS userId,
          users.given_name AS userName,
          users.serial_no AS studentSerialNo,
          users.email AS userEmail,
          assistant_thread_message_runs.user_type AS userType,
          assistant_thread_message_runs.grade_id AS gradeId,
          assistant_thread_message_runs.user_class_id AS classId,
          user_class.class AS className,
          grades.grade AS gradeName,
          COUNT(assistant_thread_message_runs.user_id) AS messageNumber,
          sessionCounts.sessionCount AS threadCount
      FROM
          assistant_thread_message_runs
      LEFT JOIN users ON assistant_thread_message_runs.user_id = users.id
      LEFT JOIN user_class ON users.user_class_id = user_class.id
      LEFT JOIN grades ON user_class.grade_id = grades.id
      LEFT JOIN (
          SELECT user_id, COUNT(*) AS sessionCount
          FROM assistant_session_count
          WHERE created_at BETWEEN '${startTimeISO}' AND '${endTimeISO}'
          GROUP BY user_id
      ) AS sessionCounts ON users.id = sessionCounts.user_id
      WHERE ${conditionStr}
      GROUP BY
        users.id,
        users.given_name,
        users.serial_no,
        users.email,
        assistant_thread_message_runs.user_type,
        assistant_thread_message_runs.grade_id,
        assistant_thread_message_runs.user_class_id,
        user_class.class,
        grades.grade,
        sessionCounts.sessionCount
      ORDER BY ${sortBy} ${orderDirection}
      LIMIT ${pageSize} OFFSET ${(pageIndex - 1) * pageSize}
    `
    const totalQuery = `
      SELECT COUNT(DISTINCT assistant_thread_message_runs.user_id) AS total
      FROM assistant_thread_message_runs
      LEFT JOIN users ON assistant_thread_message_runs.user_id = users.id
      LEFT JOIN user_class ON users.user_class_id = user_class.id
      LEFT JOIN grades ON user_class.grade_id = grades.id
      WHERE ${conditionStr}
    `

    const [totalResult] = await this.assistantThreadMessageRunsRepository.query(
      totalQuery
    )
    const total = totalResult.total

    if (total === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.assistantThreadMessageRunsRepository.query(groupByQuery)

    return {
      items,
      total,
      pageIndex,
      pageSize,
    }
  }

  async timesGroupBySchool(query: AdminAssistantUse) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const [count] = await this.assistantSessionCountRepository.query(`
      select
        count(*) as total
      from
        (
          select
            count(*) as times,
            school_id as schoolId
          from
            assistant_session_count
          where
            unix_timestamp(assistant_session_count.created_at) > ${query.startTime}
            and unix_timestamp(assistant_session_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }
    const items = await this.assistantSessionCountRepository.query(`
      select
        times,
        schools.id,
        schools.name
      from
        schools
        inner join (
          select
            count(*) as times,
            school_id as schoolId
          from
            assistant_session_count
          where
            unix_timestamp(assistant_session_count.created_at) > ${query.startTime}
            and unix_timestamp(assistant_session_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as t on t.schoolId = schools.id
      order by
        times desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async usersGroupBySchool(query: AdminAssistantUse) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const [count] = await this.assistantThreadMessageRunsRepository.query(`
      select
        count(*) as total
      from
        (
          select
            count(distinct(user_id)) as users,
            school_id as schoolId
          from
            assistant_thread_message_runs
          where
            unix_timestamp(assistant_thread_message_runs.created_at) > ${query.startTime}
            and unix_timestamp(assistant_thread_message_runs.created_at) < ${
  query.endTime
}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as t
    `)

    if (Number(count.total) === 0) {
      return { total: 0, items: [], pageIndex, pageSize }
    }

    const items = await this.assistantThreadMessageRunsRepository.query(`
      select
        users,
        schools.id,
        schools.name
      from
        schools
        inner join (
          select
            count(distinct(user_id)) as users,
            school_id as schoolId
          from
            assistant_thread_message_runs
          where
            unix_timestamp(assistant_thread_message_runs.created_at) > ${query.startTime}
            and unix_timestamp(assistant_thread_message_runs.created_at) < ${
  query.endTime
}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as t on t.schoolId = schools.id
      order by
        users desc
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize}
    `)

    return { total: Number(count.total), items, pageIndex, pageSize }
  }

  async uvpvGroupBySchool(query: ExportGroupBySchoolDto) {
    const result = await this.assistantThreadMessageRunsRepository.query(`
      select
        schools.id as schoolId,
        schools.name as schoolName,
        schools.region as region,
        ifnull(userData.users, 0) as users,  
        ifnull(sessionData.times, 0) as times
      from
        schools
        inner join (
          select
            count(DISTINCT user_id) as users,
            school_id as schoolId
          from
            assistant_thread_message_runs
          where
            unix_timestamp(assistant_thread_message_runs.created_at) > ${query.startTime}
            and unix_timestamp(assistant_thread_message_runs.created_at) < ${
  query.endTime
}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as userData on userData.schoolId = schools.id
        inner join (
          select
            count(*) as times,
            school_id as schoolId
          from
            assistant_session_count
          where
            unix_timestamp(assistant_session_count.created_at) > ${query.startTime}
            and unix_timestamp(assistant_session_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
            ${query.assistantId ? `and assistant_id = '${query.assistantId}'` : ''}
          group by
            school_id
        ) as sessionData on sessionData.schoolId = schools.id
      order by
        ${query.sortBy == 'users' ? 'users' : 'times'} desc
    `)

    return { items: result }
  }
}
