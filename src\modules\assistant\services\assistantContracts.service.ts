import { Injectable } from '@nestjs/common'
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { DataSource, Repository } from 'typeorm'
import { PageRequest } from '@/common'
import { AssistantContracts, School } from '@/entities'
import { PAGE_SIZE } from '@/modules/constants'
import { IAssistantContractsService} from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { CreateAssistantContractDto } from '../dto/assistant'

@Injectable()
export class AssistantContractsService implements IAssistantContractsService{
  constructor(
    @InjectRepository(School)
    private readonly schoolRepository: Repository<School>,
    @InjectRepository(AssistantContracts)
    private readonly AssistantContractsRepository: Repository<AssistantContracts>,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly logService: LogService
  ) {}

  async createContracts(
    schoolId: number,
    data: CreateAssistantContractDto,
    admin: any
  ) {
    return this.dataSource.transaction(async (manager) => {
      // 开启并且有合约编码
      if (data.hasAssistant && data.contractNo) {
        // 查找是否已存在相同合约号的合约
        let contract = await manager.findOne(AssistantContracts, {
          where: { contractNo: data.contractNo, school: { id: schoolId } },
        })
        // 合约不存在则创建
        if (!contract) {
          contract = await manager.save(AssistantContracts, {
            contractNo: data.contractNo,
            assistant: { assistantId: data.assistantId },
            school: { id: schoolId },
            createdBy: R.pick(
              ['id', 'name', 'email', 'userId', 'profileImage', 'familyName', 'givenName'],
              admin
            ),
          })
        } else {
          // 获取最新的合约信息
          const latestContract = await manager
            .createQueryBuilder(AssistantContracts, 'assistant_contracts')
            .leftJoinAndSelect('assistant_contracts.assistant', 'assistant')
            .where('assistant_contracts.school_id = :schoolId', { schoolId })
            .orderBy('assistant_contracts.created_at', 'DESC')
            .getOne()
          // 如果存在最新合约，并且合约号相同，则更新合约信息
          if (latestContract && latestContract.contractNo === data.contractNo) {
            await manager.update(
              AssistantContracts,
              { id: latestContract.id },
              {
                assistant: { assistantId: data.assistantId },
                updatedBy: R.pick(
                  [
                    'id',
                    'name',
                    'email',
                    'userId',
                    'profileImage',
                    'familyName',
                    'givenName',
                  ],
                  admin
                ),
              }
            )
          }
        }
      }

      // 更新学校的AI状态
      await manager.update(School, { id: schoolId }, { hasAssistant: data.hasAssistant })
    })
  }

  async getContracts(schoolId: number, query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const [items, total] = await this.AssistantContractsRepository.findAndCount({
      where: { school: { id: schoolId } },
      select: ['id', 'contractNo', 'createdAt', 'createdBy'],
      relations: ['assistant'],
      order: { id: 'DESC' },
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
    })
    const filteredItems = items.map((item) => ({
      ...item,
      assistant: item.assistant ? { name: item.assistant.name } : null,
    }))
    return { items: filteredItems, total, pageIndex, pageSize }
  }

  async getLastContract(schoolId: number) {
    return this.AssistantContractsRepository.findOne({
      where: { school: { id: schoolId } },
      relations: ['assistant'],
      order: { id: 'DESC' },
    })
  }

  async hasContract(schoolId: number) {
    const contract = await this.AssistantContractsRepository.findOne({
      where: {
        school: { id: schoolId },
      },
    })
    return !!contract
  }
}
