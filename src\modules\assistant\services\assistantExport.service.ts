import { Injectable } from '@nestjs/common'
import AdmZip from 'adm-zip'
import * as ExcelJS from 'exceljs'
import moment from 'moment'
import R from 'ramda'
import { countries, EUserType } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { GRADES_LOCALE } from '@/modules/schools/constants'
import { ExcelService } from '../../../common'
import { QueryReadingTimeDto } from '../../books/dto'
import { LogService } from '../../system'
import { AssistantService } from './assistant.service'
import { AssistantFilesService } from './assistantFiles.service'
import { AssistantStatsService } from './assistantStats.service'
import { AssistantTopicService } from './assistantTopic.service'

@Injectable()
export class AssistantExportService {
  constructor(
    private readonly assistantService: AssistantService,
    private readonly assistantFilesService: AssistantFilesService,
    private readonly assistantStatsService: AssistantStatsService,
    private readonly assistantTopicService: AssistantTopicService,
    private readonly excelService: ExcelService,
    private readonly logService: LogService
  ) {}

  /**
   *
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportGradeUV(user, schoolId, query, local, res) {
    const data = await this.assistantStatsService.queryUVByGrade(query, schoolId)
    const teachData = await this.assistantStatsService.queryUVByTeach(query, schoolId)

    const teachUV = Number(teachData[0]?.times || 0)
    const { flatList, merges } = this.calculateGradeStats(data, query, teachUV)

    const file = await this.excelService.buildExcel({
      name: `assistantUV.${local}`,
      data: flatList,
      merges,
    })

    await this.logService.save('下载-文心智友-使用人數分佈', user)
    res.send(Buffer.from(file))
  }

  /**
   *
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportGradePV(user, schoolId, query, local, res) {
    const data = await this.assistantStatsService.queryPVByGrade(query, schoolId)
    const teachData = await this.assistantStatsService.queryPVByTeach(query, schoolId)
    const teachPV = Number(teachData[0]?.times || 0)
    const { flatList, merges } = this.calculateGradeStats(data, query, teachPV)
    const file = await this.excelService.buildExcel({
      name: `assistantPV.${local}`,
      data: flatList,
      merges,
    })

    await this.logService.save('下载-文心智友-使用人次分佈', user)
    res.send(Buffer.from(file))
  }

  /**
   *
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportUserThread(user, schoolId, query, local, res) {
    let total = 0
    let buffer
    query.pageIndex = 1
    query.pageSize = 100
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('使用及對話詳細')
    worksheet.addRow([
      '對話日期',
      '時間',
      '用戶班別',
      '所屬年級',
      '學號',
      '所選時段對話詳細時間段內的對話次數',
      '對話詳細時間段內的對話數量',
      '用戶類別/AI',
      '用戶姓名',
      '內容',
    ])
    do {
      const data = await this.assistantService.getAssistantUserThread(query, schoolId)
      for (const assistantUserDetail of data.items) {
        query.userId = assistantUserDetail.userId
        query.order = { created_at: 1 }
        const messageList = await this.assistantService.getMessagesList(query)

        messageList.data.forEach((message) => {
          const createdAt = new Date(message.created_at)
          const date = createdAt.toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })
          const time = createdAt.toLocaleTimeString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            hour12: false, // 24小时制
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })
          const role = message.role === 'user' ? '學生' : 'AI'
          // const userName = message.role === 'user' ? assistantUserDetail.userName : ''
          worksheet.addRow([
            date,
            time,
            assistantUserDetail.className,
            assistantUserDetail.gradeName,
            assistantUserDetail.studentSerialNo,
            assistantUserDetail.threadCount,
            assistantUserDetail.messageNumber,
            role,
            assistantUserDetail.userName,
            message.content[0].text.value,
          ])
        })

        // 导出 Excel 文件为 Buffer 并返回
        buffer = await workbook.xlsx.writeBuffer()
      }

      query.pageIndex += 1
      total = data.total
    } while ((query.pageIndex - 1) * query.pageSize < total)

    await this.logService.save('下载-文心智友-使用詳情', user)
    res.send(buffer)
  }

  /**
   * 用户 使用及对话详细
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportUserMessageDetail(user: any, schoolId: number, query, local, res) {
    const dateRange = this.getDateRange(query)
    let list = []
    const messageList = await this.assistantService.getMessagesList(query)
    const data = messageList.data.map((v) => {
      return {
        ...v,
        ...dateRange,
      }
    })
    list = list.concat(data)
    const file = await this.excelService.buildExcel(
      {
        name: `assistantUserMessageDetail.${local}`,
        data: list,
      },
      schoolId == undefined ? '使用及对话详细' : undefined
    )

    await this.logService.save('下载-文心智友-使用及对话详细', user)
    res.send(Buffer.from(file))
  }

  async exportUVPVBySchool(user, query, local, res) {
    const dateRange = this.getDateRange(query)
    const list = await this.assistantService.uvpvGroupBySchool(query)
    const file = await this.excelService.buildExcel({
      name: `assistantGroupBySchool.${local}`,
      data: list.items.map((v) => {
        return {
          ...v,
          ...dateRange,
          schoolName: v.schoolName?.[local] || '',
          region: countries[v.region]?.[local] || '',
        }
      }),
    })
    await this.logService.save('admin 文心智友 使用數量/次数(學校) ', user)
    res.send(Buffer.from(file))
  }

  /**
   * 话题列表导出
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportTopics(user: any, query, local, res) {
    const dateRange = this.getDateRange(query)
    query.pageIndex = 1
    query.pageSize = 100
    let total = 0
    let list = []
    do {
      const topics = await this.assistantTopicService.findAll(query, true)
      const data = topics.items.map((v) => {
        return {
          ...v,
          topicsZk: v.name.zh_HK,
          topicsCn: v.name.zh_cn,
          topicsEn: v.name.en_uk,
          createDate: moment(new Date(v.createdAt))
            .tz('Asia/Hong_Kong')
            .format('YYYY/MM/DD HH:mm:ss'),
          createAdmin: v.createdBy.email,
          delete: v.deletedAt ? 'Y' : 'N',
          status: v.status == 'online' ? '已上架' : '已下架',
          gradeName: v.grades.map((grade) => GRADES_LOCALE[local][grade]),
          ...dateRange,
        }
      })
      list = list.concat(data)
      query.pageIndex += 1
      total = topics.total
    } while ((query.pageIndex - 1) * query.pageSize < total)
    const file = await this.excelService.buildExcel(
      {
        name: `assistantTopics.${local}`,
        data: list,
      },
      '话题列表导出'
    )

    await this.logService.save('下载-文心智友-话题列表', user)
    res.send(Buffer.from(file))
  }

  /**
   * 书籍文件列表导出
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportFiles(user: any, query, local, res) {
    const dateRange = this.getDateRange(query)
    query.pageIndex = 1
    query.pageSize = 100
    let total = 0
    let list = []
    do {
      const files = await this.assistantFilesService.getFileList(query)
      const data = files.items.map((v) => {
        return {
          ...v,
          name: v.name[local],
          author: v.authors.map((author) => author.name[local]).join(','),
          publisher: v.publisher[local],
          status: v.status == 'completed' ? '成功' : '失敗',
          ...dateRange,
        }
      })
      list = list.concat(data)
      query.pageIndex += 1
      total = files.total
    } while ((query.pageIndex - 1) * query.pageSize < total)
    const file = await this.excelService.buildExcel(
      {
        name: `assistantFiles.${local}`,
        data: list,
      },
      '文件列表导出'
    )

    await this.logService.save('下载-文心智友-文件列表', user)
    res.send(Buffer.from(file))
  }

  calculateGradeStats(data, query: QueryReadingTimeDto, teachUV?: number) {
    const dateRange = this.getDateRange(query)
    const groupedClasses = R.groupBy((c: any) => c.gradeId)(data)
    const groupedClassesList = Object.entries(groupedClasses)
    const flatList = []
    const sum = (arr) => arr.reduce((p, c) => p + Number(c.times), 0)
    const studentTotalCount = groupedClassesList.reduce((pre, v) => pre + sum(v[1]), 0)

    const totalCount = studentTotalCount + teachUV

    groupedClassesList.forEach((v) => {
      const gradeCount = sum(v[1])

      v[1].forEach((vv) => {
        const classCount = Number(vv.times)
        flatList.push({
          ...vv,
          ...dateRange,
          type: '學生',
          gradeCount,
          gradeCountRatio: (totalCount ? (gradeCount / totalCount) * 100 : 0).toFixed(2),
          classCount,
          classCountRatio: (totalCount ? (classCount / totalCount) * 100 : 0).toFixed(2),
        })
      })
    })
    flatList.push({
      ...dateRange,
      type: '教職員',
      gradeName: '-',
      gradeCount: teachUV,
      gradeCountRatio: (totalCount ? (teachUV / totalCount) * 100 : 0).toFixed(2),
    })

    let row = 2
    const mergeCountList = groupedClassesList.map((v) => v[1].length)
    const merges = mergeCountList
      .map((length) => {
        const merge = [
          {
            start: { row: row, column: 4 },
            end: { row: row + length - 1, column: 4 },
          },
          {
            start: { row: row, column: 5 },
            end: { row: row + length - 1, column: 5 },
          },
          {
            start: { row: row, column: 6 },
            end: { row: row + length - 1, column: 6 },
          },
        ]
        row += length
        return merge
      })
      .flat()
    // startTime
    merges.push({
      start: { row: 2, column: 1 },
      end: { row: flatList.length + 1, column: 1 },
    })
    // endTime
    merges.push({
      start: { row: 2, column: 2 },
      end: { row: flatList.length + 1, column: 2 },
    })
    // user type
    merges.push({
      start: { row: 2, column: 3 },
      end: { row: flatList.length, column: 3 },
    })

    return { flatList, merges }
  }

  getDateRange(query: QueryReadingTimeDto) {
    const startTime = query.startTime
      ? moment.tz(query.startTime * 1e3, 'Asia/Hong_Kong').format('YYYY/MM/DD')
      : '-'
    const endTime = query.endTime
      ? moment.tz(query.endTime * 1e3, 'Asia/Hong_Kong').format('YYYY/MM/DD')
      : '-'
    return {
      startTime,
      endTime,
    }
  }
}
