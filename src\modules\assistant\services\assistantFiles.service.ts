import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Repository } from 'typeorm'
import { RedisService } from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { AssistantFiles, AssistantVectorstoreFiles, Book } from '@/entities'
import { EOpeaiFileStatus, EOrderDirection, EUserType } from '@/enums'
import { ASSISTANTS_BOOKS_KEY } from '@/modules/constants'
import {
  DeleteAssistantFilesDtoRequest,
  QueryFilesListDto,
  SaveFileDto,
  UpdateFileDto,
} from '../dto/assistantFiles'

@Injectable()
export class AssistantFilesService {
  constructor(
    @InjectRepository(AssistantFiles)
    private readonly assistantFilesRepository: Repository<AssistantFiles>,
    @InjectRepository(AssistantVectorstoreFiles)
    private assistantVectorstoreFilesRepository: Repository<AssistantVectorstoreFiles>,
    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,
    private readonly redisService: RedisService,
    private readonly taskService: TaskService
  ) {}

  /**
   * 【数据表记录】
   *  关系型 mysql
   *  一，assistant ===> id,assistant_id,vector_store_id
   *  二，assistant_files ===> id,isbn,aws_url,openai_file_id,vector_store_id,status,file_bytes,book_id
   *  三，assistant_thread ===> id,assistant_id,thread_id,user_id,user_type,class,grade,school_id
   *  四，assistant_thread_message_runs ===> id,assistant_id,thread_id,from_msg_id,to_run_id,user_id,user_type,class,grade,school_id
   *  五，assistant_admin_error_response ===> id,assistant,file_id,ibsn,error_msg
   *  六，assistant_client_error_response ===> id,assistant,thread_id,run_id,msg_id,error_msg
   *  非关系型 mongodb redis
   *  缓存 redis   1，assistant 基础信息  2，缓存file_id book_name
   *  缓存 mongodb 1，messages 对话数据
   */

  /**
   * 获取存储容量
   * @param page
   * @param pageSize
   * @returns
   */
  async getUsedCapacity() {
    const result = await this.assistantFilesRepository.query(
      `SELECT SUM(file_bytes) as totalBytes FROM assistant_files where deleted_at is null and status = '${EOpeaiFileStatus.COMPLETED}' `
    )
    const totalBytes = result[0].totalBytes || 0
    return {
      totalBytes,
    }
  }

  /**
   * 获取书籍文件列表
   * @param query
   * @returns
   */
  async getFileList(body: QueryFilesListDto) {
    const {
      isNeedPage,
      isFullSelected,
      excludedIds,
      specifiedIds,
      pageIndex = 1,
      keyword = '',
      language = '',
      authorIds,
      publisherIds,
      useAassistant = 'all',
      orderDirection,
    } = body
    let pageSize = body.pageSize || 10
    if (!isNeedPage) {
      pageSize = 1000
    }

    // 构建查询生成器
    const queryBuilder = this.assistantFilesRepository
      .createQueryBuilder('assistantFiles')
      .leftJoinAndSelect('assistantFiles.book', 'book')
      .leftJoinAndSelect('book.authors', 'authors')
      .leftJoinAndSelect('book.publisher', 'publisher')

    queryBuilder.andWhere('assistantFiles.deletedAt IS NULL')

    // 处理关键字搜索和语言筛选
    if (keyword) {
      queryBuilder.andWhere(
        '(assistantFiles.isbn LIKE :isbn OR assistantFiles.fileName LIKE :bname OR authors.name LIKE :aname OR publisher.name LIKE :pname)',
        {
          isbn: `%${keyword}%`,
          bname: `%${keyword}%`,
          aname: `%${keyword}%`,
          pname: `%${keyword}%`,
        }
      )
    }

    if (language) {
      queryBuilder.andWhere('book.language = :language', { language })
    }

    if (authorIds && authorIds.length > 0) {
      queryBuilder.andWhere('authors.id IN (:...authorIds)', { authorIds })
    }

    if (publisherIds && publisherIds.length > 0) {
      queryBuilder.andWhere('publisher.id IN (:...publisherIds)', { publisherIds })
    }

    // 处理全选和反选逻辑
    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.andWhere('assistantFiles.id NOT IN (:...excludedIds)', {
          excludedIds,
        })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.andWhere('assistantFiles.id IN (:...specifiedIds)', {
          specifiedIds,
        })
      }
    }

    if (orderDirection) {
      queryBuilder.orderBy('book.publishedAt', orderDirection)
    }

    // 处理 useAassistant
    if (useAassistant !== 'all') {
      const assistantFileIds = await this.assistantVectorstoreFilesRepository
        .createQueryBuilder('vf')
        .select('vf.openaiFileId')
        .leftJoin('assistant', 'a', 'a.id = vf.assistantNumberId')
        .where('vf.deletedAt IS NULL AND a.assistantId = :assistantId', {
          assistantId: useAassistant,
        })
        .getRawMany()
      const openaiFileIds = assistantFileIds.map((file) => file.vf_openai_file_id)
      if (openaiFileIds.length > 0) {
        queryBuilder.andWhere('assistantFiles.openaiFileId IN (:...openaiFileIds)', {
          openaiFileIds,
        })
      } else {
        queryBuilder.andWhere('1 = 0')
      }
    } 

    const assistants = await this.assistantVectorstoreFilesRepository
      .createQueryBuilder('vf')
      .select(['vf.openaiFileId', 'vf.assistantNumberId', 'a.assistantId', 'a.name'])
      .leftJoin('assistant', 'a', 'a.id = vf.assistantNumberId')
      .where('vf.deletedAt IS NULL')
      .getRawMany()

    // 分页
    queryBuilder.skip((pageIndex - 1) * pageSize).take(pageSize)

    const [fileRecords, total] = await queryBuilder.getManyAndCount()

    const filteredBooks = fileRecords.map((file) => {
      const relatedAssistants = assistants
        .filter((record) => record.vf_openai_file_id === file.openaiFileId)
        .map((record) => ({
          assistantId: record.a_assistant_id,
          name: record.a_name,
        }))
        .filter((assistant) => assistant.assistantId && assistant.name)
      return {
        id: file.id,
        name: file.fileName,
        status: file.status,
        isbn: file.isbn,
        awsUrl: file.awsUrl,
        bookId: file.book?.id || null,
        language: file.book?.language || null,
        publishedAt: file.book?.publishedAt || null,
        coverUrl: file.book?.coverUrl || null,
        publisher: file.book?.publisher?.name || '',
        openfileId: file.openaiFileId,
        assistants: relatedAssistants,
        authors:
          file.book?.authors?.map((author) => ({
            name: author.name,
            description: author.description || '',
          })) || [],
        createdAt: file.createdAt,
      }
    })

    return {
      items: filteredBooks,
      total,
      pageIndex,
      pageSize,
    }
  }

  async getFileNameByIsbn(isbn: string) {
    const file = await this.bookRepository.findOne({ where: { isbn  } })
    if (file) {
      return file.name
    }
    return null
  }

  async existFile(keyword: string) {
    return await this.assistantFilesRepository
      .createQueryBuilder('assistantFiles')
      .where('assistantFiles.isbn LIKE :isbn', { isbn: `%${keyword}%` })
      .orWhere('assistantFiles.fileName LIKE :fileName', { fileName: `%${keyword}%` })
      .getOne()
  }

  async existFileById(id: number) {
    return await this.assistantFilesRepository.findOne({
      where: { id },
      relations: ['book'],
    })
  }

  /**
   * 保存文件记录
   * @param saveFileDto
   * @returns
   */
  async saveFile(saveFileDto: SaveFileDto) {
    try {
      return await this.assistantFilesRepository.save(saveFileDto)
    } catch (error) {
      console.error('Error saving data:', error)
    }
  }

  /**
   * 更新文件记录
   * @param id
   * @param updateFileDto
   * @param bookName
   * @returns
   */
  async updateFile(id: number, updateFileDto: UpdateFileDto) {
    console.log('openai updateFileDto', updateFileDto)
    try {
      // 1,更新数据库
      await this.assistantFilesRepository.update({ id }, updateFileDto)
      const updatedFile = await this.assistantFilesRepository.findOne({ where: { id,
      } })

      // 2，redis 存放一个hash集合 根据file_id获取对应的书籍名字
      if (updateFileDto.status === EOpeaiFileStatus.COMPLETED) {
        await this.updateFilesInfoCache(
          updatedFile.openaiFileId,
          updatedFile.fileName,
          updatedFile.bookId,
          updatedFile.version
        )
      }
      return updatedFile
    } catch (error) {
      throw new Error(`Error saving data: ${error}`)
    }
  }

  async updateFilesInfoCache(
    openaiFileId: string,
    fileName: any,
    bookId: number,
    version: string
  ) {
    await this.redisService.hset(
      ASSISTANTS_BOOKS_KEY,
      openaiFileId,
      JSON.stringify({ fileName, bookId, version })
    )
  }

  async getFilesInfoCache(openaiFileId: string) {
    const fileInfoString = await this.redisService.hget(
      ASSISTANTS_BOOKS_KEY,
      openaiFileId
    )
    return fileInfoString ? JSON.parse(fileInfoString) : null
  }

  /**
   * openai对应文件删除需要控制容量
   * aws可以不删除会覆盖
   * 数据表做软删除
   * @param data
   * @param admin
   */
  async deleteFilesBatch(data: DeleteAssistantFilesDtoRequest, admin: any) {
    const { isFullSelected, excludedIds, specifiedIds, keyword, language } = data
    // 构建查询生成器
    const queryBuilder = this.assistantFilesRepository
      .createQueryBuilder('assistantFiles')
      .leftJoinAndSelect('assistantFiles.book', 'book')
      .leftJoinAndSelect('book.authors', 'authors')
      .leftJoinAndSelect('book.publisher', 'publisher')

    queryBuilder.where('assistantFiles.deletedAt IS NULL')

    // 处理关键字搜索和语言筛选
    if (keyword) {
      queryBuilder.andWhere(
        '(assistantFiles.isbn LIKE :isbn OR book.name LIKE :bname OR authors.name LIKE :aname OR assistantFiles.fileName LIKE :bname)',
        { isbn: `%${keyword}%`, bname: `%${keyword}%`, aname: `%${keyword}%` }
      )
    }

    if (language) {
      queryBuilder.andWhere(
        '(book.language = :language OR assistantFiles.bookId IS NULL)',
        { language }
      )
    }

    // 处理全选和反选逻辑
    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.andWhere('assistantFiles.id NOT IN (:...excludedIds)', {
          excludedIds,
        })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.andWhere('assistantFiles.id IN (:...specifiedIds)', {
          specifiedIds,
        })
      }
    }

    // 获取需要删除的文件数据
    const files = await queryBuilder
      .select(['assistantFiles.id', 'assistantFiles.openaiFileId'])
      .getMany()

    if (files.length == 0) {
      return {
        status: true,
      }
    }
    const fileIds = files.map((file) => file.id)
    await this.assistantFilesRepository
      .createQueryBuilder()
      .update()
      .set({
        deletedBy: admin,
        deletedAt: moment().tz('Asia/Hong_Kong').format(),
      })
      .where('id IN (:...fileIds)', { fileIds })
      .execute()

    // 创建删除任务
    await this.taskService.deliver(
      ETaskType.DELETE_FILE_ASSISTANT,
      {
        query: files,
        user: admin,
        type: ETaskType.DELETE_FILE_ASSISTANT,
      },
      {}
    )

    return {
      status: true,
    }
  }
}
