import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Brackets, Repository } from 'typeorm'
import { AssistantSchoolTopic } from '@/entities/assistantSchoolTopic.entity'
import { AssistantSchoolTopicCount } from '@/entities/assistantSchoolTopicCount.entity'
import { EAssistantTopicStatus, EUserType } from '@/enums'
import {
  CreateAssistantSchoolTopicDto,
  DeleteAssistantSchoolTopicDtoRequest,
  QuerySchoolTopicListDto,
  UpdateAssistantSchoolTopicDto,
  UpdateAssistantSchoolTopicStatusDtoRequest,
} from '../dto/assistantSchoolTopic'
import { CreateAssistantTopicCountDto } from '../dto/assistantTopic'
import { AssistantService } from './assistant.service'
@Injectable()
export class AssistantSchoolTopicService {
  constructor(
    private readonly assistantService: AssistantService,
    @InjectRepository(AssistantSchoolTopic)
    private readonly assistantSchoolTopicRepository: Repository<AssistantSchoolTopic>,
    @InjectRepository(AssistantSchoolTopicCount)
    private readonly assistantScchoolTopicCountRepository: Repository<AssistantSchoolTopicCount>
  ) {}

  async create(
    createAssistantSchoolTopicDto: CreateAssistantSchoolTopicDto
  ): Promise<AssistantSchoolTopic> {
    const assistantSchoolTopic = this.assistantSchoolTopicRepository.create(
      createAssistantSchoolTopicDto 
    )
    return await this.assistantSchoolTopicRepository.save(assistantSchoolTopic)
  }

  async update(
    id: number,
    updateAssistantSchoolTopicDto: UpdateAssistantSchoolTopicDto
  ): Promise<AssistantSchoolTopic> {
    await this.assistantSchoolTopicRepository.update(id, updateAssistantSchoolTopicDto)
    return this.assistantSchoolTopicRepository.findOne({ where: { id: id } })
  }

  async batchUpdateStatus(
    updateAssistantSchoolTopicStatusDtoRequest: UpdateAssistantSchoolTopicStatusDtoRequest
  ): Promise<AssistantSchoolTopic[]> {
    const {
      isFullSelected,
      excludedIds,
      specifiedIds,
      status,
      classId,
      gradeId,
      name,
      updateStatus,
      userType,
    } = updateAssistantSchoolTopicStatusDtoRequest

    const queryBuilder = this.assistantSchoolTopicRepository.createQueryBuilder(
      'assistant_school_topic'
    )

    // 处理全选的情况
    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.where('assistant_school_topic.id NOT IN (:...excludedIds)', {
          excludedIds,
        })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.where('assistant_school_topic.id IN (:...specifiedIds)', {
          specifiedIds,
        })
      } else {
        throw new Error('No specified IDs provided for update.')
      }
    }

    // 其他可选条件
    if (status) {
      queryBuilder.andWhere('assistant_school_topic.status = :status', { status })
    }
    if (gradeId) {
      queryBuilder.andWhere(
        '(JSON_EXTRACT(assistant_school_topic.gradeIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.gradeIds, CAST(:gradeIds AS JSON)))',
        {
          gradeIds: [gradeId],
        }
      )
    }
    if (classId) {
      queryBuilder.andWhere(
        '(JSON_EXTRACT(assistant_school_topic.classIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.classIds, CAST(:classIds AS JSON)))',
        {
          classIds: [classId],
        }
      )
    }
    if (name) {
      queryBuilder.andWhere('assistant_school_topic.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    if (userType) {
      queryBuilder.andWhere('assistant_school_topic.userType =:userType', {
        userType,
      })
    }

    // 执行更新
    await queryBuilder.update().set({ status: updateStatus }).execute()

    // 查询并返回更新后的记录
    const updatedTopics = await queryBuilder.getMany()

    return updatedTopics.map((x) => ({
      ...x,
      deletedAt: new Date(moment().tz('Asia/Hong_Kong').format()),
    }))
  }

  async findAllClient(
    query: QuerySchoolTopicListDto,
    queryExport?: boolean
  ): Promise<{
    items: AssistantSchoolTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    const {
      userType,
      status,
      schoolId,
      gradeId,
      classId,
      name,
      pageIndex = 1,
      pageSize = 10,
    } = query

    const queryBuilder = this.assistantSchoolTopicRepository.createQueryBuilder(
      'assistant_school_topic'
    )

    if (queryExport) {
      queryBuilder.withDeleted()
    }
    queryBuilder.where('assistant_school_topic.schoolId = :schoolId', { schoolId })
    if (status) {
      queryBuilder.andWhere('assistant_school_topic.status = :status', { status })
    }

    if (userType == EUserType.STUDENT) {
      queryBuilder.andWhere(
        '(JSON_EXTRACT(assistant_school_topic.gradeIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.gradeIds, CAST(:gradeIds AS JSON)))',
        { gradeIds: [gradeId] }
      )
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where(
            `(JSON_EXTRACT(assistant_school_topic.gradeIds, "$[0]") != 0 OR JSON_LENGTH(assistant_school_topic.gradeIds) <= 1)
            AND
            (JSON_EXTRACT(assistant_school_topic.classIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.classIds, CAST(:classIds AS JSON)))
            `
          )
            .orWhere('JSON_EXTRACT(assistant_school_topic.gradeIds, "$[0]") = 0')
            .orWhere(
              '(JSON_OVERLAPS(assistant_school_topic.gradeIds, CAST(:gradeIds AS JSON)) AND JSON_LENGTH(assistant_school_topic.classIds) <= 0)'
            )
        }),
        { gradeIds: [gradeId], classIds: [classId] } //
      )
    }

    if (userType) {
      queryBuilder.andWhere(
        '(assistant_school_topic.userType = :userType OR assistant_school_topic.userType = "all")',
        {
          userType,
        }
      )
    }
    if (name) {
      queryBuilder.andWhere('assistant_school_topic.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    const total = await queryBuilder.getCount()
    const rawItems =
      (await queryBuilder
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize)
        .getMany()) || []
    const itemsWithUsageCount = await Promise.all(
      rawItems.map(async (item) => {
        const usageCountResult = await this.assistantScchoolTopicCountRepository
          .createQueryBuilder('topicCount')
          .select([
            'COUNT(topicCount.topicId) AS usage_count',
            'COUNT(DISTINCT topicCount.user_id) AS user_count',
            'COUNT(DISTINCT topicCount.school_id) AS school_count',
          ])
          .where('topicCount.topicId = :topicId', { topicId: item.id })
          .getRawOne()
        return {
          ...item,
          usageCount: Number(usageCountResult?.usage_count) || 0,
          userCount: Number(usageCountResult?.user_count) || 0,
          schoolCount: Number(usageCountResult?.school_count) || 0,
        }
      })
    )
    return { items: itemsWithUsageCount, total, pageIndex, pageSize }
  }

  async findAll(
    query: QuerySchoolTopicListDto,
    queryExport?: boolean
  ): Promise<{
    items: AssistantSchoolTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    const {
      userType,
      status,
      schoolId,
      gradeId,
      classId,
      name,
      pageIndex = 1,
      pageSize = 10,
    } = query

    const queryBuilder = this.assistantSchoolTopicRepository.createQueryBuilder(
      'assistant_school_topic'
    )

    if (queryExport) {
      queryBuilder.withDeleted()
    }
    queryBuilder.where('assistant_school_topic.schoolId = :schoolId', { schoolId })
    if (status) {
      queryBuilder.andWhere('assistant_school_topic.status = :status', { status })
    }

    if (gradeId) {
      queryBuilder.andWhere(
        '(JSON_OVERLAPS(assistant_school_topic.gradeIds, CAST(:gradeIds AS JSON)))',
        {
          gradeIds: [gradeId],
        }
      )
    }
    if (classId) {
      queryBuilder.andWhere(
        '(JSON_OVERLAPS(assistant_school_topic.classIds, CAST(:classIds AS JSON)))',
        {
          classIds: [classId],
        }
      )
    }
    if (userType) {
      queryBuilder.andWhere('assistant_school_topic.userType = :userType', {
        userType,
      })
    }
    if (name) {
      queryBuilder.andWhere('assistant_school_topic.name LIKE :name', {
        name: `%${name}%`,
      })
    }
    const total = await queryBuilder.getCount()
    const rawItems =
      (await queryBuilder
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize)
        .getMany()) || []
    const itemsWithUsageCount = await Promise.all(
      rawItems.map(async (item) => {
        const usageCountResult = await this.assistantScchoolTopicCountRepository
          .createQueryBuilder('topicCount')
          .select([
            'COUNT(topicCount.topicId) AS usage_count',
            'COUNT(DISTINCT topicCount.user_id) AS user_count',
            'COUNT(DISTINCT topicCount.school_id) AS school_count',
          ])
          .where('topicCount.topicId = :topicId', { topicId: item.id })
          .getRawOne()
        return {
          ...item,
          usageCount: Number(usageCountResult?.usage_count) || 0,
          userCount: Number(usageCountResult?.user_count) || 0,
          schoolCount: Number(usageCountResult?.school_count) || 0,
        }
      })
    )
    return { items: itemsWithUsageCount, total, pageIndex, pageSize }
  }

  /**
   * 批量删除
   * @param data
   * @returns
   */
  async softDeleteBatch(data: DeleteAssistantSchoolTopicDtoRequest) {
    const {
      isFullSelected,
      excludedIds,
      specifiedIds,
      status,
      gradeId,
      classId,
      name,
      userType,
    } = data

    const queryBuilder = this.assistantSchoolTopicRepository.createQueryBuilder(
      'assistant_school_topic'
    )

    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.where('assistant_school_topic.id NOT IN (:...excludedIds)', {
          excludedIds,
        })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.where('assistant_school_topic.id IN (:...specifiedIds)', {
          specifiedIds,
        })
      } else {
        throw new Error('No specified IDs provided for deletion.')
      }
    }

    if (status) {
      queryBuilder.andWhere('assistant_school_topic.status = :status', { status })
    }
    if (gradeId) {
      queryBuilder.andWhere(
        '(JSON_EXTRACT(assistant_school_topic.gradeIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.gradeIds, CAST(:gradeIds AS JSON)))',
        {
          gradeIds: [gradeId],
        }
      )
    }
    if (classId) {
      queryBuilder.andWhere(
        '(JSON_EXTRACT(assistant_school_topic.classIds, "$[0]") = 0 OR JSON_OVERLAPS(assistant_school_topic.classIds, CAST(:classIds AS JSON)))',
        {
          classIds: [classId],
        }
      )
    }
    if (name) {
      queryBuilder.andWhere('assistant_school_topic.name LIKE :name', {
        name: `%${name}%`,
      })
    }

    if (userType) {
      queryBuilder.andWhere('assistant_school_topic.userType = :userType', {
        userType,
      })
    }

    const topics = await queryBuilder.select(['assistant_school_topic.id']).getMany()

    await this.assistantSchoolTopicRepository.softRemove(topics)

    const result = await this.assistantSchoolTopicRepository.find({
      select: ['id', 'deletedAt', 'status', 'name', 'answer'],
      withDeleted: false,
    })

    return result.map((x) => ({
      ...x,
      deletedAt: new Date(moment().tz('Asia/Hong_Kong').format()),
    }))
  }

  async findSchoolTopicExist(schoolId: number) {
    return this.assistantSchoolTopicRepository
      .createQueryBuilder('topic')
      .where('topic.status = :status', { status: EAssistantTopicStatus.ONLINE })
      .andWhere('topic.schoolId = :schoolId', { schoolId })
      .getCount()
  }

  /**
   * 每使用一次对话记录一次
   * @param createAssistantTopicDto
   * @returns
   */
  async createAssistantTopicCount(data: CreateAssistantTopicCountDto) {
    await this.assistantScchoolTopicCountRepository.save({
      assistantId: data.assistantId,
      topicId: data.topicId,
      threadId: data.threadId,
      user: { id: data.userId },
      school: { id: data.schoolId },
      userClass: data.userClassId ? { id: data.userClassId } : null,
      gradeId: data.gradeId ? data.gradeId : null,
      userType: data.userType,
    })
  }
}
