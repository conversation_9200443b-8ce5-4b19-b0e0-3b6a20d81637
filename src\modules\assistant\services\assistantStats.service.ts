import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Repository } from 'typeorm'
import {
  AssistantF<PERSON>,
  AssistantThread,
  AssistantThreadMessageRuns,
  AssistantVectorstoreFiles,
} from '@/entities'
import { AssistantSessionCount } from '@/entities/assistantSessionCount.entity'
import { EOpeaiFileStatus, EUserType } from '@/enums'
import { getDays } from '@/utils'
import { QueryAssistantStatsDto, QueryAssistantTimeDto } from '../dto/assistant'

@Injectable()
export class AssistantStatsService {
  constructor(
    @InjectRepository(AssistantThread)
    private assistantThreadRepository: Repository<AssistantThread>,
    @InjectRepository(AssistantFiles)
    private assistantFilesRepository: Repository<AssistantFiles>,
    @InjectRepository(AssistantVectorstoreFiles)
    private assistantVectorstoreFilesRepository: Repository<AssistantVectorstoreFiles>,
    @InjectRepository(AssistantThreadMessageRuns)
    private assistantThreadMessageRunsRepository: Repository<AssistantThreadMessageRuns>,
    @InjectRepository(AssistantSessionCount)
    private assistantSessionCountRepository: Repository<AssistantSessionCount>,
  ) {}

  /**
   * 统计使用用户数量（根据 user_id）
   * @returns
   */
  async countUserIds(query: QueryAssistantStatsDto) {
    const queryBuilder = this.assistantThreadRepository
      .createQueryBuilder('assistantThread')
      .select('COUNT(DISTINCT assistantThread.userId)', 'count')

    if (query?.assistantId) {
      queryBuilder.where('assistantThread.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }
    return await queryBuilder.getRawOne()
  }

  /**
   * 统计教师使用的用户数量（根据 user_type = 'teacher'）
   * @returns
   */
  async countTeachers(query: QueryAssistantStatsDto) {
    const queryBuilder = this.assistantThreadRepository
      .createQueryBuilder('assistantThread')
      .select('COUNT(DISTINCT assistantThread.userId)', 'count')
      .where('assistantThread.userType = :userType', { userType: 'teacher' })

    if (query?.assistantId) {
      queryBuilder.andWhere('assistantThread.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }

    return await queryBuilder.getRawOne()
  }

  /**
   * 统计学生使用的用户数量（根据 user_type = 'student'）
   * @returns
   */
  async countStudents(query: QueryAssistantStatsDto, schoolId?: number) {
    const queryBuilder = this.assistantThreadRepository
      .createQueryBuilder('assistantThread')
      .select('COUNT(DISTINCT assistantThread.userId)', 'count')
      .where('assistantThread.userType = :userType', { userType: 'student' })

    if (schoolId) {
      queryBuilder.andWhere('assistantThread.schoolId = :schoolId', { schoolId })
    }

    if (query?.assistantId) {
      queryBuilder.andWhere('assistantThread.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }

    return await queryBuilder.getRawOne()
  }

  /**
   * 统计书籍数量
   * @returns
   */
  async countBooks(query: QueryAssistantStatsDto) {
    const queryBuilder = this.assistantVectorstoreFilesRepository
      .createQueryBuilder('assistantVectorstoreFiles')
      .innerJoin(
        'assistantVectorstoreFiles.assistantFile',
        'assistantFile',
        'assistantFile.deletedAt IS NULL',
      )
      .select('COUNT(DISTINCT assistantVectorstoreFiles.openaiFileId)', 'count')
      .where('assistantVectorstoreFiles.status = :status', {
        status: EOpeaiFileStatus.COMPLETED,
      })

    if (query?.assistantId) {
      queryBuilder.andWhere('assistantVectorstoreFiles.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }

    return await queryBuilder.getRawOne()
  }
  // ... existing code ...

  /**
   * 统计对话数量
   * @returns
   */
  async countMessages(query: QueryAssistantStatsDto) {
    const queryBuilder = this.assistantThreadMessageRunsRepository
      .createQueryBuilder('assistantThreadMessageRuns')
      .select(`COUNT(assistantThreadMessageRuns.userId) AS count`)

    if (query?.assistantId) {
      queryBuilder.where('assistantThreadMessageRuns.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }

    return await queryBuilder.getRawOne()
  }

  /**
   * 统计学生对话数量
   * @returns
   */
  async countStudentMessages(
    query: {
      userId?: number
      schoolId?: number
      userType?: EUserType
      startTime?: number
      endTime?: number
      assistantId?: string
    } = {},
  ) {
    const queryBuilder = this.assistantThreadMessageRunsRepository
      .createQueryBuilder('assistantThreadMessageRuns')
      .where('assistantThreadMessageRuns.userType = :userType', { userType: 'student' })
      .select(`Count(assistantThreadMessageRuns.userId) AS count`)
    if (query.userId) {
      queryBuilder.andWhere('assistantThreadMessageRuns.userId = :userId', {
        userId: query.userId,
      })
    }
    if (query.schoolId) {
      queryBuilder.andWhere('assistantThreadMessageRuns.schoolId = :schoolId', {
        schoolId: query.schoolId,
      })
    }
    if (query?.assistantId) {
      queryBuilder.andWhere('assistantThreadMessageRuns.assistantId = :assistantId', {
        assistantId: query.assistantId,
      })
    }
    if (query.startTime && query.endTime) {
      queryBuilder.andWhere(
        'assistantThreadMessageRuns.createdAt BETWEEN :startTime AND :endTime',
        {
          startTime: new Date(query.startTime * 1000).toISOString(),
          endTime: new Date(query.endTime * 1000).toISOString(),
        },
      )
    }
    return queryBuilder.getRawOne()
  }
  /**
   * 统计对话人次（根据 user_id 统计对话人次）
   * @returns
   */
  async countUniqueConversations(
    query: {
      userId?: number
      schoolId?: number
      userType?: EUserType
      startTime?: number
      endTime?: number
      assistantId?: string
    } = {},
  ) {
    const queryBuilder = this.assistantSessionCountRepository
      .createQueryBuilder('assistantSessionCount')
      .select('COUNT(assistantSessionCount.user)', 'count')
    if (query.userId) {
      queryBuilder.andWhere('assistantSessionCount.user = :userId', {
        userId: query.userId,
      })
    }
    if (query.schoolId) {
      queryBuilder.andWhere('assistantSessionCount.school_id = :schoolId', {
        schoolId: query.schoolId,
      })
    }
    if (query.userType) {
      queryBuilder.andWhere('assistantSessionCount.user_type = :userType', {
        userType: query.userType,
      })
    }
    if (query?.assistantId) {
      queryBuilder.andWhere('assistantSessionCount.assistant_id = :assistantId', {
        assistantId: query.assistantId,
      })
    }
    if (query.startTime && query.endTime) {
      queryBuilder.andWhere(
        'assistantSessionCount.created_at BETWEEN :startTime AND :endTime',
        {
          startTime: new Date(query.startTime * 1000).toISOString(),
          endTime: new Date(query.endTime * 1000).toISOString(),
        },
      )
    }
    return queryBuilder.getRawOne()
  }

  /**
   * 新增用户
   * @param startTime
   * @param endTime
   * @returns
   */
  async assistantNewUserCount(query: QueryAssistantTimeDto) {
    const studentData = await this.assistantNewUserCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      assistantId: query.assistantId,
      type: EUserType.STUDENT,
    })

    const teacherData = await this.assistantNewUserCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      assistantId: query.assistantId,
      type: EUserType.TEACHER,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      studentCount:
        studentData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
      teacherCount:
        teacherData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
    }))
  }

  /**
   * 用户消息数量统计
   * @param startTime
   * @param endTime
   * @returns
   */
  async assistantUserMessageCount(query: QueryAssistantTimeDto) {
    const studentData = await this.assistantUserMessageCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      assistantId: query.assistantId,
      type: EUserType.STUDENT,
    })

    const teacherData = await this.assistantUserMessageCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      assistantId: query.assistantId,
      type: EUserType.TEACHER,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      studentCount:
        studentData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
      teacherCount:
        teacherData.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
    }))
  }

  /**
   * 对话人数 对话人次统计
   * @param startTime
   * @param endTime
   * @returns
   */
  async assistantUserSessionCount(query: {
    startTime: number
    endTime: number
    schoolId?: number
    assistantId?: string
  }) {
    //对话人数
    const studentUserCount = await this.assistantUseUserCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: query.schoolId,
      type: EUserType.STUDENT,
      assistantId: query.assistantId,
    })

    const studentSessionCount = await this.assistantUserSessionCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: query.schoolId,
      type: EUserType.STUDENT,
      assistantId: query.assistantId,
    })

    //对话人次
    const teacherUserCount = await this.assistantUseUserCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: query.schoolId,
      type: EUserType.TEACHER,
      assistantId: query.assistantId,
    })
    const teacherSessionCount = await this.assistantUserSessionCountByDay({
      startTime: query.startTime,
      endTime: query.endTime,
      schoolId: query.schoolId,
      type: EUserType.TEACHER,
      assistantId: query.assistantId,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      studentUserCount:
        studentUserCount.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
      studentSessionCount:
        studentSessionCount.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
      teacherUserCount:
        teacherUserCount.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
      teacherSessionCount:
        teacherSessionCount.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date,
        )?.total ?? 0,
    }))
  }

  /**
   * 新增用户 根据天数
   * @param query
   * @returns
   */
  async assistantNewUserCountByDay(query: {
    startTime: number
    endTime: number
    type?: EUserType
    assistantId?: string
  }) {
    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    if (query.type) {
      where = where + ` AND user_type = '${query.type}'`
    }
    if (query?.assistantId) {
      where = where + ` AND assistant_id = '${query.assistantId}'`
    }

    const res = await this.assistantThreadRepository.query(
      `
        select
          date,
          COUNT(DISTINCT user_id) as total
        from
          (
            select
              user_id,
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
            from
              assistant_thread 
            where
              ${where}
              AND NOT EXISTS (
                SELECT 1
                FROM assistant_thread t2
                WHERE t2.user_id = assistant_thread.user_id
                AND t2.created_at < assistant_thread.created_at
              )
          ) t
        group by
          date
      `,
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  /**
   * 对话人数 根据天数
   * @param query
   * @returns
   */
  async assistantUseUserCountByDay(query: {
    startTime: number
    endTime: number
    schoolId?: number
    type?: EUserType
    assistantId?: string
  }) {
    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    if (query.type) {
      where = where + ` AND user_type = '${query.type}'`
    }
    if (query.schoolId) {
      where = where + ` AND school_id = '${query.schoolId}'`
    }
    if (query?.assistantId) {
      where = where + ` AND assistant_id = '${query.assistantId}'`
    }
    const res = await this.assistantThreadMessageRunsRepository.query(
      `
        select
          date,
          COUNT(DISTINCT user_id) as total
        from
          (
            select
              user_id,
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
            from
              assistant_thread_message_runs
            where
              ${where}
          ) t
        group by
          date
      `,
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  /**
   * 对话人次 根据天数
   * @param query
   * @returns
   */
  async assistantUserSessionCountByDay(query: {
    startTime: number
    endTime: number
    schoolId?: number
    type?: EUserType
    assistantId?: string
  }) {
    // 构建时间范围筛选条件
    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}")`
    if (query.type) {
      where = where + ` AND user_type = '${query.type}'`
    }
    if (query.schoolId) {
      where = where + ` AND school_id = '${query.schoolId}'`
    }
    if (query?.assistantId) {
      where = where + ` AND assistant_id = '${query.assistantId}'`
    }
    // 查询按天统计的唯一用户数
    const res = await this.assistantSessionCountRepository.query(
      `
        SELECT
          DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) AS date,
          COUNT(user_id) AS total
        FROM
          assistant_session_count
        WHERE
          ${where}
        GROUP BY
          date
      `,
    )

    // 返回结果，转换 total 为数字类型
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  /**
   * 用户消息数量统计 根据天数
   * @param query
   * @returns
   */
  async assistantUserMessageCountByDay(query: {
    startTime: number
    endTime: number
    schoolId?: number
    type?: EUserType
    assistantId?: string
  }) {
    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    if (query.type) {
      where = where + ` AND user_type = '${query.type}'`
    }
    if (query.schoolId) {
      where = where + ` AND school_id = '${query.schoolId}'`
    }
    if (query?.assistantId) {
      where = where + ` AND assistant_id = '${query.assistantId}'`
    }
    const res = await this.assistantThreadMessageRunsRepository.query(
      `
      select
        date,
        Count(user_id) AS total
      from
        (
          select
            user_id,
            DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date
          from
            assistant_thread_message_runs
          where
            ${where}
        ) t
      group by
        date
      `,
    )
    return res.map((item) => ({ ...item, total: Number(item.total) }))
  }

  async usersGroupByGrade(query: QueryAssistantTimeDto, schoolId: number) {
    const data = await this.queryUVByGrade(query, schoolId)
    const teachData = await this.queryUVByTeach(query, schoolId)
    const teachUV = Number(teachData[0]?.times || 0)
    return [...new Set(data.map((item) => item.gradeId))]
      .map((gradeId) => {
        const classes = data
          .filter((item) => item.gradeId === gradeId)
          .map((item) => ({
            id: item.classId,
            name: item.className,
            users: Number(item.times || 0),
          }))
        return {
          id: gradeId,
          name: data.find((item) => item.gradeId === gradeId).gradeName,
          classes,
          users: classes.reduce((acc, cur) => acc + Number(cur.users || 0), 0),
        }
      })
      .concat({
        id: -999,
        name: '教职员',
        classes: [],
        users: teachUV,
      })
  }

  async queryUVByGrade(query: QueryAssistantTimeDto, schoolId: number) {
    return await this.assistantThreadMessageRunsRepository.query(`
      select
        grades.id as gradeId,
        user_class.id as classId,
        grades.grade as gradeName,
        user_class.class as className,
        ifnull(t.times, 0) as times
      from
        user_class
        left join grades on grades.id = user_class.grade_id
        left join(
          select
            user_class.grade_id as gradeId,
            user_class.id as classId,
            count(distinct(assistant_thread_message_runs.user_id)) as times
          from
            assistant_thread_message_runs
            inner join users on assistant_thread_message_runs.user_id = users.id
            inner join user_class on users.user_class_id = user_class.id
          where
            assistant_thread_message_runs.school_id = ${schoolId}
            and unix_timestamp(assistant_thread_message_runs.created_at) > ${query.startTime}
            and unix_timestamp(assistant_thread_message_runs.created_at) < ${query.endTime}
            and assistant_thread_message_runs.user_type = '${EUserType.STUDENT}'
          group by
            user_class.id,
            user_class.grade_id
        ) as t on user_class.id = t.classId
      where grades.school_id = ${schoolId} and user_class.deleted_at is null
      order by
        grades.sequence asc,
        user_class.sequence asc;
    `)
  }
  async queryUVByTeach(query: QueryAssistantTimeDto, schoolId: number) {
    return await this.assistantThreadMessageRunsRepository.query(`
        select
            count(distinct(assistant_thread_message_runs.user_id)) as times
          from
            assistant_thread_message_runs
            inner join users u on assistant_thread_message_runs.user_id = u.id
          where
            assistant_thread_message_runs.school_id = ${schoolId}
            and unix_timestamp(assistant_thread_message_runs.created_at) > ${query.startTime}
            and unix_timestamp(assistant_thread_message_runs.created_at) < ${query.endTime}
						and u.type= 'teacher'
    `)
  }
  async timesGroupByGrade(query: QueryAssistantTimeDto, schoolId: number) {
    const data = await this.queryPVByGrade(query, schoolId)
    const teachData = await this.queryPVByTeach(query, schoolId)
    const teachPV = Number(teachData[0]?.times || 0)
    return [...new Set(data.map((item) => item.gradeId))]
      .map((gradeId) => {
        const classes = data
          .filter((item) => item.gradeId === gradeId)
          .map((item) => ({
            id: item.classId,
            name: item.className,
            times: Number(item.times || 0),
          }))
        return {
          id: gradeId,
          name: data.find((item) => item.gradeId === gradeId).gradeName,
          classes,
          times: classes.reduce((acc, cur) => acc + Number(cur.times || 0), 0),
        }
      })
      .concat({
        id: -999,
        name: '教职员',
        classes: [],
        times: teachPV,
      })
  }

  async queryPVByGrade(query: QueryAssistantTimeDto, schoolId: number) {
    return await this.assistantSessionCountRepository.query(`
      select
        grades.id as gradeId,
        user_class.id as classId,
        grades.grade as gradeName,
        user_class.class as className,
        ifnull(t.times, 0) as times
      from
        user_class
        left join grades on grades.id = user_class.grade_id
        left join(
          select
            user_class.grade_id as gradeId,
            user_class.id as classId,
            count(*) as times
          from
            assistant_session_count
            inner join users on assistant_session_count.user_id = users.id
            inner join user_class on users.user_class_id = user_class.id
          where
            assistant_session_count.school_id = ${schoolId}
            and unix_timestamp(assistant_session_count.created_at) > ${query.startTime}
            and unix_timestamp(assistant_session_count.created_at) < ${query.endTime}
            and user_type = '${EUserType.STUDENT}'
          group by
            user_class.id,
            user_class.grade_id
        ) as t on user_class.id = t.classId
      where grades.school_id = ${schoolId} and user_class.deleted_at is null
      order by
        grades.sequence asc,
        user_class.sequence asc;
    `)
  }
  async queryPVByTeach(query: QueryAssistantTimeDto, schoolId: number) {
    return await this.assistantSessionCountRepository.query(`
        select
            count(*) as times
          from
            assistant_session_count
            inner join users u on assistant_session_count.user_id = u.id
          where
            assistant_session_count.school_id = ${schoolId}
            and unix_timestamp(assistant_session_count.created_at) > ${query.startTime}
            and unix_timestamp(assistant_session_count.created_at) < ${query.endTime}
						and u.type= 'teacher'
    `)
  }
}
