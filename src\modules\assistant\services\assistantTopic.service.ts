import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Repository } from 'typeorm'
import { AssistantTopic } from '@/entities/assistantTopic.entity'
import { AssistantTopicCount } from '@/entities/assistantTopicCount.entity'
import {
  CreateAssistantTopicCountDto,
  CreateAssistantTopicDto,
  DeleteAssistantTopicDtoRequest,
  QueryTopicListDto,
  UpdateAssistantTopicDto,
  UpdateAssistantTopicStatusDtoRequest,
} from '../dto/assistantTopic'
import { AssistantService } from './assistant.service'

@Injectable()
export class AssistantTopicService {
  constructor(
    private readonly assistantService: AssistantService,
    @InjectRepository(AssistantTopic)
    private readonly assistantTopicRepository: Repository<AssistantTopic>,
    @InjectRepository(AssistantTopicCount)
    private readonly assistantTopicCountRepository: Repository<AssistantTopicCount>
  ) {}

  async create(
    createAssistantTopicDto: CreateAssistantTopicDto
  ): Promise<AssistantTopic> {
    const assistantTopic = this.assistantTopicRepository.create(createAssistantTopicDto)
    return await this.assistantTopicRepository.save(assistantTopic)
  }
 
  async update(
    id: number,
    updateAssistantTopicDto: UpdateAssistantTopicDto
  ): Promise<AssistantTopic> {
    delete updateAssistantTopicDto.ids
    await this.assistantTopicRepository.update(id, updateAssistantTopicDto)
    return this.assistantTopicRepository.findOne({ where: { id: id } })
  }

  async batchUpdateStatus(
    updateAssistantTopicDto: UpdateAssistantTopicStatusDtoRequest
  ): Promise<AssistantTopic[]> {
    const {
      isFullSelected,
      excludedIds,
      specifiedIds,
      status,
      grade,
      name,
      updateStatus,
      assistant,
    } = updateAssistantTopicDto

    const queryBuilder =
      this.assistantTopicRepository.createQueryBuilder('assistant_topic')

    // 处理全选的情况
    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.where('assistant_topic.id NOT IN (:...excludedIds)', { excludedIds })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.where('assistant_topic.id IN (:...specifiedIds)', { specifiedIds })
      } else {
        throw new Error('No specified IDs provided for update.')
      }
    }

    // 其他可选条件
    if (status) {
      queryBuilder.andWhere('assistant_topic.status = :status', { status })
    }
    if (grade) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.grades, :grade)', {
        grade: JSON.stringify([grade]),
      })
    }
    if (name) {
      queryBuilder.andWhere('assistant_topic.name LIKE :name', { name: `%${name}%` })
    }
    if (assistant) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.assistants, :assistant)', {
        assistant: JSON.stringify([assistant]),
      })
    }

    // 执行更新
    await queryBuilder.update().set({ status: updateStatus }).execute()

    // 查询并返回更新后的记录
    const updatedTopics = await queryBuilder.getMany()

    return updatedTopics.map((x) => ({
      ...x,
      deletedAt: new Date(moment().tz('Asia/Hong_Kong').format()),
    }))
  }

  async findAllClient(
    query: QueryTopicListDto,
    queryExport?: boolean
  ): Promise<{
    items: AssistantTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    const {
      assistant,
      status,
      grade,
      name,
      orderDirection = 'DESC',
      pageIndex = 1,
      pageSize = 10,
    } = query

    const queryBuilder =
      this.assistantTopicRepository.createQueryBuilder('assistant_topic')

    if (queryExport) {
      queryBuilder.withDeleted()
    }

    queryBuilder
      .leftJoin(
        'assistant_topic_count',
        'topicCount',
        'topicCount.topic_id = assistant_topic.id'
      )
      .select([
        'assistant_topic.id AS id',
        'assistant_topic.name AS name',
        'assistant_topic.status AS status',
        'assistant_topic.grades AS grades',
        'assistant_topic.assistants AS assistants',
        'assistant_topic.created_at AS created_at',
        'assistant_topic.created_by AS created_by',
        'assistant_topic.updated_at AS updatedAt',
        'assistant_topic.deleted_at AS deletedAt',
        'COUNT(topicCount.topic_id) AS usageCount',
        'COUNT(DISTINCT topicCount.user_id) AS userCount',
        'COUNT(DISTINCT topicCount.school_id) AS schoolCount',
      ])
      .groupBy('assistant_topic.id')

    if (status) {
      queryBuilder.andWhere('assistant_topic.status = :status', { status })
    }

    if (grade) {
      queryBuilder.andWhere(
        'JSON_CONTAINS(assistant_topic.grades, :grade)  OR JSON_EXTRACT(assistant_topic.grades, :gradeAll) ',
        {
          grade: JSON.stringify([grade]),
          gradeAll: JSON.stringify(['all']),
        }
      )
    }
    if (assistant) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.assistants, :assistant)', {
        assistant: JSON.stringify([assistant]),
      })
    }
    if (name) {
      queryBuilder.andWhere('assistant_topic.name LIKE :name', { name: `%${name}%` })
    }

    queryBuilder.orderBy('usageCount', orderDirection)

    const total = await queryBuilder.getCount()
    const rawItems =
      (await queryBuilder
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize)
        .getRawMany()) || []
    const assistants = await this.assistantService.getAssistantTabList()
    const itemsWithUsageCount = await Promise.all(
      rawItems.map(async (item) => {
        const assistantNames =
          item.assistants[0] === 'all'
            ? [
                {
                  zh_HK: '全部功能',
                  en_uk: 'All Features',
                  zh_cn: '全部功能',
                },
              ]
            : assistants
              .filter((assistant) => item.assistants.includes(assistant.assistantId))
              .map((assistant) => assistant.name)
        return {
          ...item,
          assistantNames,
          usageCount: Number(item?.usageCount) || 0,
          userCount: Number(item?.usageCount) || 0,
          schoolCount: Number(item?.usageCount) || 0,
        }
      })
    )
    return { items: itemsWithUsageCount, total, pageIndex, pageSize }
  }

  async findAll(
    query: QueryTopicListDto,
    queryExport?: boolean
  ): Promise<{
    items: AssistantTopic[]
    total: number
    pageIndex: number
    pageSize: number
  }> {
    const {
      assistant,
      status,
      grade,
      name,
      orderDirection,
      pageIndex = 1,
      pageSize = 10,
      isOne = false,
    } = query

    const queryBuilder =
      this.assistantTopicRepository.createQueryBuilder('assistant_topic')

    if (queryExport) {
      queryBuilder.withDeleted()
    }

    queryBuilder
      .leftJoin(
        'assistant_topic_count',
        'topicCount',
        'topicCount.topic_id = assistant_topic.id'
      )
      .select([
        'assistant_topic.id AS id',
        'assistant_topic.name AS name',
        'assistant_topic.status AS status',
        'assistant_topic.grades AS grades',
        'assistant_topic.assistants AS assistants',
        'assistant_topic.created_at AS created_at',
        'assistant_topic.created_by AS created_by',
        'assistant_topic.updated_at AS updatedAt',
        'assistant_topic.deleted_at AS deletedAt',
        'COUNT(topicCount.topic_id) AS usageCount',
        'COUNT(DISTINCT topicCount.user_id) AS userCount',
        'COUNT(DISTINCT topicCount.school_id) AS schoolCount',
      ])
      .groupBy('assistant_topic.id')

    if (status) {
      queryBuilder.andWhere('assistant_topic.status = :status', { status })
    }

    if (grade) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.grades, :grade)', {
        grade: JSON.stringify([grade]),
      })
    }
    if (assistant) {
      const assistantCondition = isOne
        ? '(JSON_EXTRACT(assistant_topic.assistants, "$[0]") = "all" OR JSON_CONTAINS(assistant_topic.assistants, :assistant))'
        : 'JSON_CONTAINS(assistant_topic.assistants, :assistant)'

      queryBuilder.andWhere(assistantCondition, {
        assistant: JSON.stringify([assistant]),
      })
    }
    if (name) {
      queryBuilder.andWhere('assistant_topic.name LIKE :name', { name: `%${name}%` })
    }

    if (orderDirection) {
      queryBuilder.orderBy('usageCount', orderDirection)
    }

    const total = await queryBuilder.getCount()
    queryBuilder.limit(pageSize).offset((pageIndex - 1) * pageSize)
    const rawItems = (await queryBuilder.getRawMany()) || []

    const assistants = await this.assistantService.getAssistantTabList()
    const itemsWithUsageCount = await Promise.all(
      rawItems.map(async (item) => {
        const assistantNames =
          item.assistants[0] === 'all'
            ? [
                {
                  zh_HK: '全部功能',
                  en_uk: 'All Features',
                  zh_cn: '全部功能',
                },
              ]
            : assistants
              .filter((assistant) => item.assistants.includes(assistant.assistantId))
              .map((assistant) => assistant.name)
        return {
          ...item,
          assistantNames,
          usageCount: Number(item?.usageCount) || 0,
          userCount: Number(item?.usageCount) || 0,
          schoolCount: Number(item?.usageCount) || 0,
        }
      })
    )
    return { items: itemsWithUsageCount, total, pageIndex, pageSize }
  }

  /**
   * 批量删除
   * @param data
   * @returns
   */
  async softDeleteBatch(data: DeleteAssistantTopicDtoRequest) {
    const { isFullSelected, excludedIds, specifiedIds, status, grade, name, assistant } =
      data

    const queryBuilder =
      this.assistantTopicRepository.createQueryBuilder('assistant_topic')

    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.where('assistant_topic.id NOT IN (:...excludedIds)', { excludedIds })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.where('assistant_topic.id IN (:...specifiedIds)', { specifiedIds })
      } else {
        throw new Error('No specified IDs provided for deletion.')
      }
    }

    if (status) {
      queryBuilder.andWhere('assistant_topic.status = :status', { status })
    }
    if (grade) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.grades, :grade)', {
        grade: JSON.stringify([grade]),
      })
    }
    if (name) {
      queryBuilder.andWhere('assistant_topic.name LIKE :name', { name: `%${name}%` })
    }

    if (assistant) {
      queryBuilder.andWhere('JSON_CONTAINS(assistant_topic.assistants, :assistant)', {
        assistant: JSON.stringify([assistant]),
      })
    }

    const topics = await queryBuilder.select(['assistant_topic.id']).getMany()

    await this.assistantTopicRepository.softRemove(topics)

    const result = await this.assistantTopicRepository.find({
      select: ['id', 'deletedAt', 'status', 'name', 'answer'],
      withDeleted: false,
    })

    return result.map((x) => ({
      ...x,
      deletedAt: moment().tz('Asia/Hong_Kong').format(),
    }))
  }

  /**
   * 每使用一次对话记录一次
   * @param createAssistantTopicDto
   * @returns
   */
  async createAssistantTopicCount(data: CreateAssistantTopicCountDto) {
    await this.assistantTopicCountRepository.save({
      assistantId: data.assistantId,
      topicId: data.topicId,
      threadId: data.threadId,
      user: { id: data.userId },
      school: { id: data.schoolId },
      userClass: data.userClassId ? { id: data.userClassId } : null,
      gradeId: data.gradeId ? data.gradeId : null,
      userType: data.userType,
    })
  }
}
