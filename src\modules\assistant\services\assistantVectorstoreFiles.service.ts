import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { AssistantVectorstoreFiles } from '@/entities/assistantVectorFiles.entity'
import { EOpeaiVectorStoreFileStatus } from '@/enums'
import { QueryAssistantFileListDto, QueryFilesListDto } from '../dto/assistantFiles'

@Injectable()
export class AssistantVectorstoreFilesService {
  constructor(
    @InjectRepository(AssistantVectorstoreFiles)
    private readonly assistantVectorstoreFilesRepository: Repository<AssistantVectorstoreFiles>,
  ) {}

  async update(
    where: { id?: number; openaiFileId?: string; vectorStoreId?: string },
    update: {
      openaiFileId?: string
      status?: string
      assistantId?: string
      vectorStoreId?: string
    },
  ) {
    return await this.assistantVectorstoreFilesRepository.update(where, update)
  }
  async find(openaiFileId: string) {
    return await this.assistantVectorstoreFilesRepository.find({
      where: {
        openaiFileId,
      },
      select: ['id', 'openaiFileId', 'vectorStoreId'],
    })
  }

  async delete(id: number) {
    return await this.assistantVectorstoreFilesRepository.softDelete(id)
  }

  /**
   * 更新存储桶为新的文件id
   * @param openaiFileId
   * @param upOpenaiFileId
   * @returns
   */
  async updateByOpenaiFileId(openaiFileId: string, upOpenaiFileId: string) {
    return await this.assistantVectorstoreFilesRepository.update(
      {
        openaiFileId,
      },
      {
        openaiFileId: upOpenaiFileId,
        status: EOpeaiVectorStoreFileStatus.IN_PROGRESS,
      },
    )
  }

  async getVectorstoreFilesList(body: QueryAssistantFileListDto) {
    const {
      isFullSelected,
      excludedIds,
      specifiedIds,
      assistantNumberId,
      assistantId,
      keyword = '',
      language = '',
      pageIndex = 1,
      pageSize = 2000,
    } = body

    // 构建查询生成器
    const queryBuilder = this.assistantVectorstoreFilesRepository
      .createQueryBuilder('vectorFiles')
      .innerJoinAndSelect('vectorFiles.assistantFile', 'assistantFile')
      .leftJoinAndSelect('assistantFile.book', 'book')
      .leftJoinAndSelect('book.authors', 'authors')
      .leftJoinAndSelect('book.publisher', 'publisher')

    queryBuilder.andWhere('vectorFiles.deletedAt IS NULL')

    if (assistantId) {
      queryBuilder.andWhere('vectorFiles.assistantId = :assistantId', {
        assistantId,
      })
    }

    // 添加assistantNumberId条件
    if (assistantNumberId) {
      queryBuilder.andWhere('vectorFiles.assistantNumberId = :assistantNumberId', {
        assistantNumberId,
      })
    }

    // 处理关键字搜索和语言筛选
    if (keyword) {
      queryBuilder.andWhere(
        '(assistantFile.isbn LIKE :isbn OR assistantFile.fileName LIKE :bname OR authors.name LIKE :aname OR publisher.name LIKE :pname)',
        {
          isbn: `%${keyword}%`,
          bname: `%${keyword}%`,
          aname: `%${keyword}%`,
          pname: `%${keyword}%`,
        },
      )
    }

    if (language) {
      queryBuilder.andWhere('book.language = :language', { language })
    }

    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.andWhere('vectorFiles.id NOT IN (:...excludedIds)', {
          excludedIds,
        })
      }
    } else {
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.andWhere('vectorFiles.id IN (:...specifiedIds)', {
          specifiedIds,
        })
      }
    }

    const skip = (pageIndex - 1) * pageSize
    queryBuilder.skip(skip).take(pageSize)

    const [fileRecords, total] = await queryBuilder.getManyAndCount()

    const filteredBooks = fileRecords.map((vectorFile) => ({
      id: vectorFile.id,
      name: vectorFile.assistantFile?.fileName,
      status: vectorFile.status,
      isbn: vectorFile.assistantFile?.isbn,
      awsUrl: vectorFile.assistantFile?.awsUrl,
      bookId: vectorFile.assistantFile?.book?.id || null,
      language: vectorFile.assistantFile?.book?.language || null,
      publishedAt: vectorFile.assistantFile?.book?.publishedAt || null,
      coverUrl: vectorFile.assistantFile?.book?.coverUrl || null,
      publisher: vectorFile.assistantFile?.book?.publisher?.name || '',
      openfileId: vectorFile.openaiFileId,
      authors:
        vectorFile.assistantFile?.book?.authors?.map((author) => ({
          name: author.name,
          description: author.description || '',
        })) || [],
      createdAt: vectorFile.createdAt,
    }))

    return {
      items: filteredBooks,
      total,
      pageIndex,
      pageSize,
    }
  }
}
