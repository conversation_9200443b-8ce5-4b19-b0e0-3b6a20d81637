import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import AWS from 'aws-sdk'
import mime from 'mime'
import { IS3Config } from '@/common/interfaces/s3.interface'
import { fetch, remove, upload } from '@/utils'

@Injectable()
export class OpenAIS3Service {
  private connection: AWS.S3
  private config: IS3Config

  constructor(private readonly configService: ConfigService) {
    this.config = this.configService.get('common.openaiS3')
    this.connection = new AWS.S3(this.config)
  }

  async upload(options: {
    fileName: string
    path: string
    file: Buffer
    contentType?: string
  }) {
    // console.log({
    //   contentType: options.contentType ?? mime.getType(options.fileName),
    //   path: options.path,
    // })
    const key = await upload(this.connection, {
      bucket: this.config.bucketName,
      fileName: options.fileName,
      fileBuffer: options.file,
      path: options.path,
      contentType: options.contentType ?? (mime.getType(options.fileName) || 'application/octet-stream'),
    })
    return this.getUrl(key)
  }

  async fetch(url: string) {
    const fileFullName = url.substring(
      url.indexOf(this.config.bucketName) + this.config.bucketName.length + 1
    )

    return fetch(this.connection, { fileFullName, bucket: this.config.bucketName })
  }

  async remove(url: string) {
    const fileFullName = url.substring(
      url.indexOf(this.config.bucketName) + this.config.bucketName.length + 1
    )
    return remove(this.connection, { bucket: this.config.bucketName, fileFullName })
  }
  async copy(options: { oldFileName: string; newFileName: string; path: string }) {
    const oldKey = `${options.path}/${options.oldFileName}`
    const newKey = `${options.path}/${options.newFileName}`
    await this.connection
      .copyObject({
        Bucket: this.config.bucketName,
        CopySource: `${this.config.bucketName}/${oldKey}`,
        Key: newKey,
      })
      .promise()

    return this.getUrl(newKey)
  }
  getUrl(key: string): string {
    return `https://${this.config.cdn}/${key}`
    // return `https://s3.${this.config.region}.amazonaws.com/${this.config.bucketName}/${key}`
  }
}
