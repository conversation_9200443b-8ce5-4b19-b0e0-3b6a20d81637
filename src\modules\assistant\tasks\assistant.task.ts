import { Process, Processor } from '@nestjs/bull'
import { InjectRepository } from '@nestjs/typeorm'
import { Job } from 'bull'
import R from 'ramda'
import { IsNull, Not, Repository } from 'typeorm'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import {
  Assistant,
  AssistantVectorstoreFiles,
  AssistantVectorstoreFilesBatch,
  OperationLog,
} from '@/entities'
import { EAssistantStatus, EOpeaiVectorStoreFileStatus } from '@/enums'
import { LogService, OperationLogService } from '@/modules/system'
import { OpenAIService } from '@/modules/websocket/services/openai.service'

@Processor(TASK_QUEUE_NAME)
export class AssistantTask {
  constructor(
    @InjectRepository(Assistant)
    private readonly assistantRepository: Repository<Assistant>,
    @InjectRepository(AssistantVectorstoreFiles)
    private readonly assistantVectorstoreFilesRepository: Repository<AssistantVectorstoreFiles>,
    @InjectRepository(AssistantVectorstoreFilesBatch)
    private readonly assistantVectorstoreFilesBatchRepository: Repository<AssistantVectorstoreFilesBatch>,
    private readonly openAIService: OpenAIService,
    private readonly taskService: TaskService,
    private readonly opLogService: OperationLogService,
  ) {}

  @Process(ETaskType.CREATE_AI_ASSISTANT)
  async createAIJob(job: Job<any>) {
    console.log({ createAssistantTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, data, admin } = job.data as any
      if (type === ETaskType.CREATE_AI_ASSISTANT) {
        await this.createAI({ data, admin })
      }
    })
  }

  @Process(ETaskType.DELETE_AI_ASSISTANT)
  async deleteAIJob(job: Job<any>) {
    console.log({ deleteAssistantTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, data, admin } = job.data as any
      if (type === ETaskType.DELETE_AI_ASSISTANT) {
        await this.deleteAI({ data, admin })
      }
    })
  }

  @Process(ETaskType.UPDATE_AI_ASSISTANT)
  async updateAIJob(job: Job<any>) {
    console.log({ updateAssistantTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, data, admin } = job.data as any
      if (type === ETaskType.UPDATE_AI_ASSISTANT) {
        return await this.updateAI({ data, admin })
      }
    })
  }

  async updateAI(payload: any) {
    const { data, admin } = payload
    console.log('updateAssistantTask data', data)
    console.log('updateAssistantTask admin', admin)

    // const queryRunner = this.assistantRepository.manager.connection.createQueryRunner()
    // await queryRunner.connect()
    // await queryRunner.startTransaction()

    try {
      let assistantId
      let vectorStoreId
      let batchId
      // 1. 获取现有的assistant信息
      const assistant = await this.assistantRepository.findOne({
        where: { id: data.assistantNumberId },
      })
      assistantId = assistant.assistantId
      vectorStoreId = assistant.vectorStoreId
      if (assistant.status !== EAssistantStatus.ONLINE && !assistant.assistantId) {
        const openaiResources = await this.createOpenAIResources(data.name.zh_HK, [])
        assistantId = openaiResources.assistant.id
        vectorStoreId = openaiResources.vectorStore.id
      }

      // 2. 更新 Assistant 名称
      await this.openAIService.updateAssistant(assistantId, data.name.zh_HK)

      // 3. 获取需要处理的文件
      const fileIToDeleteData = data.fileIToDeleteData
      const filesToUpsertData = data.filesToUpsertData

      // 4. 删除标记为 softdelete 的文件
      if (fileIToDeleteData.length > 0) {
        for (const fileIToDelete of fileIToDeleteData) {
          if (vectorStoreId) {
            await this.openAIService.deleteVectorStoreFile({
              openaiFileId: fileIToDelete.openaiFileId,
              vectorStoreId,
            })
          }
        }
      }

      // 5. 添加标记为 pending 的新文件
      if (filesToUpsertData.length > 0) {
        const openaiFileIdsToAdd = filesToUpsertData.map(
          (filesToUpsertData) => filesToUpsertData.openaiFileId,
        )
        const openaiVectorStoreFilesBatch =
          await this.openAIService.createVectorStoreFileBatch({
            vectorStoreId,
            openaiFileIds: openaiFileIdsToAdd,
          })
        batchId = openaiVectorStoreFilesBatch.id
        await this.assistantVectorstoreFilesBatchRepository.save({
          assistantId: assistantId,
          vectorStoreId: vectorStoreId,
          batchId: batchId,
          status: openaiVectorStoreFilesBatch.status,
        })

        // 更新 AssistantVectorstoreFiles 记录
        await this.assistantVectorstoreFilesRepository
          .createQueryBuilder()
          .update(AssistantVectorstoreFiles)
          .set({
            assistantId: assistantId,
            vectorStoreId: vectorStoreId,
          })
          .where(
            'openaiFileId IN (:...fileIds) AND assistantNumberId = :assistantNumberId',
            {
              id: filesToUpsertData.id,
              fileIds: data.openaiFileIds,
              assistantNumberId: data.assistantNumberId,
            },
          )
          .execute()
      }

      // 7. 更新 Assistant 状态
      await this.assistantRepository.save({
        id: data.assistantNumberId,
        assistantId: assistantId,
        vectorStoreId: vectorStoreId,
        status: EAssistantStatus.ONLINE,
        updatedBy: admin,
      })

      // 8. 记录日志
      await this.opLogService.createLog({
        operation: `更新AI套餐"${data.name.zh_HK}"`,
        user: admin,
      })
      // 缓存assistant
      await this.openAIService.cacheAssistant(assistantId)

      // 10. 触发文件检查任务
      if (filesToUpsertData.length > 0) {
        await this.taskService.deliver(
          ETaskType.CHECK_FILE_ASSISTANT,
          {
            query: {
              batchId: batchId,
              vectorStoreId: vectorStoreId,
              openaiFileIds: filesToUpsertData.map((file) => file.openaiFileId),
            },
            local: payload.local,
            user: admin,
            type: ETaskType.CHECK_FILE_ASSISTANT,
          },
          { delay: 3000 },
        )
      }
    } catch (error) {
      console.error(`Failed to update AI assistant: ${error.message}`)
      throw error
    }
  }

  async createAI(payload: any) {
    const { data, admin } = payload
    console.log('createAssistantTask  data', data)
    console.log('createAssistantTask  admin', admin)

    const queryRunner = this.assistantRepository.manager.connection.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()
    let openaiVectorStore = null
    let openaiAssistant = null
    let openaiVectorStoreFilesBatch = null
    try {
      const openaiResources = await this.createOpenAIResources(
        data.name.zh_HK,
        data.openaiFileIds,
      )
      openaiVectorStore = openaiResources.vectorStore
      openaiAssistant = openaiResources.assistant
      openaiVectorStoreFilesBatch = openaiResources.vectorStoreFilesBatch
      // 4. 更新 Assistant 记录
      await queryRunner.manager.save(Assistant, {
        id: data.assistantNumberId,
        vectorStoreId: openaiVectorStore.id,
        assistantId: openaiAssistant.id,
        status: EAssistantStatus.ONLINE,
      })

      // 5. 保存 AssistantVectorstoreFiles 记录
      await queryRunner.manager
        .createQueryBuilder()
        .update(AssistantVectorstoreFiles)
        .set({
          vectorStoreId: openaiVectorStore.id,
          status: EOpeaiVectorStoreFileStatus.IN_PROGRESS,
          assistantId: openaiAssistant.id,
        })
        .where(
          'openaiFileId IN (:...fileIds) AND assistantNumberId = :assistantNumberId',
          {
            fileIds: data.openaiFileIds,
            assistantNumberId: data.assistantNumberId,
          },
        )
        .execute()

      // 6. 记录vectorStoreFiles
      await queryRunner.manager.save(AssistantVectorstoreFilesBatch, {
        assistantId: openaiAssistant.id,
        vectorStoreId: openaiVectorStore.id,
        batchId: openaiVectorStoreFilesBatch.id,
      })

      // 7. 记录日志
      await queryRunner.manager.save(OperationLog, {
        name:
          admin?.familyName || admin?.givenName
            ? `${R.isNil(admin?.givenName) ? '' : admin?.givenName} ${
                R.isNil(admin?.familyName) ? '' : admin?.familyName
              }`
            : null,
        email: admin?.email ?? null,
        userId: admin?.userId,
        type: admin?.roles?.length ? admin.roles : '',
        operation: `创建AI套餐"${data.name.zh_HK}"`,
        metaData: {
          assistantId: openaiAssistant.id,
          vectorStoreId: openaiVectorStore.id,
        },
        createdBy: R.pick(['userId', 'roleName'], admin || {}),
      })

      // 提交事务
      await queryRunner.commitTransaction()

      // 缓存assistant
      await this.openAIService.cacheAssistant(openaiAssistant.id)

      // 8. 触发文件检查任务
      await this.taskService.deliver(
        ETaskType.CHECK_FILE_ASSISTANT,
        {
          query: {
            batchId: openaiVectorStoreFilesBatch.id,
            vectorStoreId: openaiVectorStore.id,
            openaiFileIds: data.openaiFileIds,
          },
          local: payload.local,
          user: admin,
          type: ETaskType.CHECK_FILE_ASSISTANT,
        },
        {},
      )
    } catch (error) {
      // 发生错误时回滚事务
      await queryRunner.rollbackTransaction()

      try {
        if (openaiVectorStoreFilesBatch) {
          await this.openAIService.deleteVectorStoreFileBatch(
            openaiVectorStore.id,
            openaiVectorStoreFilesBatch.id,
          )
        }

        if (openaiAssistant) {
          await this.openAIService.deleteAssistant(openaiAssistant.id)
        }

        if (openaiVectorStore) {
          await this.openAIService.deleteVectorStore(openaiVectorStore.id)
        }
      } catch (cleanupError) {
        console.error('Error cleaning up OpenAI resources:', cleanupError)
      }

      console.error(`Failed to create AI assistant: ${error.message}`)
    } finally {
      // 释放数据库连接
      await queryRunner.release()
    }
  }

  async deleteAI(payload: any) {
    const { data, admin } = payload
    console.log('DeleteAssistantTask', data, admin)
    const queryRunner = this.assistantRepository.manager.connection.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    try {
      // 1. 删除 Assistant
      await this.openAIService.deleteAssistant(data.assistantId)

      // 2. 删除 vectorStore
      await this.openAIService.deleteVectorStore(data.vectorStoreId)

      // 记录操作日志
      await queryRunner.manager.save(OperationLog, {
        name:
          admin?.familyName || admin?.givenName
            ? `${R.isNil(admin?.givenName) ? '' : admin?.givenName} ${
                R.isNil(admin?.familyName) ? '' : admin?.familyName
              }`
            : null,
        email: admin?.email ?? null,
        userId: admin?.userId,
        type: admin?.roles?.length ? admin.roles : '',
        operation: `删除AI套餐"${data.name.zh_HK}"`,
        metaData: {
          assistantNumberId: data.assistantNumberId,
        },
        createdBy: R.pick(['userId', 'roleName'], admin || {}),
      })

      // 提交事务
      await queryRunner.commitTransaction()
    } catch (error) {
      // 发生错误时回滚事务
      await queryRunner.rollbackTransaction()
      console.error(`Failed to create AI assistant: ${error.message}`)
    } finally {
      // 释放数据库连接
      await queryRunner.release()
    }
  }

  private async createOpenAIResources(name: string, openaiFileIds: string[]) {
    // 1. 创建 vectorStore
    const openaiVectorStore = await this.openAIService.createVectorStore(name)
    if (!openaiVectorStore) {
      throw new Error('Vector store creation failed')
    }

    // 2. 创建 Assistant
    const openaiAssistant = await this.openAIService.createAssistant(
      name,
      openaiVectorStore.id,
    )
    if (!openaiAssistant) {
      throw new Error('Assistant creation failed')
    }

    // 3. 创建 AssistantVectorStoreFiles
    let openaiVectorStoreFilesBatch = null
    if (openaiFileIds.length > 0) {
      openaiVectorStoreFilesBatch = await this.openAIService.createVectorStoreFileBatch({
        vectorStoreId: openaiVectorStore.id,
        openaiFileIds,
      })
      if (!openaiVectorStoreFilesBatch) {
        throw new Error('Vector store files creation failed')
      }
    }

    return {
      vectorStore: openaiVectorStore,
      assistant: openaiAssistant,
      vectorStoreFilesBatch: openaiVectorStoreFilesBatch,
    }
  }
}
