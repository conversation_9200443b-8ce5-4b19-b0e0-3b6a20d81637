import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { EOpeaiVectorStoreFileStatus } from '@/enums'
import { LogService, OperationLogService } from '@/modules/system'
import { OpenAIService } from '@/modules/websocket/services/openai.service'
import {
  DeleteAssistantFileTaskDto,
  SaveAssistantFileDto,
  UpdateAssistantFileTaskDto,
} from '../dto/assistantFiles'
import { AssistantVectorstoreFilesService } from '../services'

@Processor(TASK_QUEUE_NAME)
export class AssistantFileTask {
  constructor(
    private readonly taskService: TaskService,
    private readonly logService: LogService,
    private readonly openAIService: OpenAIService, 
    private readonly assistantVectorstoreFilesService: AssistantVectorstoreFilesService,
    private readonly opLogService: OperationLogService
  ) {}

  @Process(ETaskType.DELETE_FILE_ASSISTANT)
  async fileDelete(job: Job<any>) {
    console.log({ assistantFileDeleteTask: job?.id ?? 0 })

    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, query, user } = job.data as any
      if (type === ETaskType.DELETE_FILE_ASSISTANT) {
        await this.deleteFileTask({ query, user })
      }
    })
  }

  @Process(ETaskType.UPDATE_FILE_ASSISTANT)
  async fileUpdate(job: Job<any>) {
    console.log({ assistantFileUpdateTask: job?.id ?? 0 })

    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, query, user } = job.data as any
      if (type === ETaskType.UPDATE_FILE_ASSISTANT) {
        await this.updateFileTask({ query, user })
      }
    })
  }

  @Process(ETaskType.CHECK_FILE_ASSISTANT)
  async fileCheck(job: Job<any>) {
    console.log({ assistantFileCheckTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { type, query } = job.data as any

      if (type === ETaskType.CHECK_FILE_ASSISTANT) {
        await this.checkVectorFileTask({
          vectorStoreId: query.vectorStoreId,
          batchId: query.batchId,
        })
      }
    })
  }

  /**
   * 书籍删除任务
   * @param options
   */
  private async deleteFileTask(options: {
    query: DeleteAssistantFileTaskDto[]
    user: any
  }) {
    const { query, user } = options
    console.log('deleteFileTask query', query)
    for (const file of query) {
      const { openaiFileId } = file
      // 删除操作
      if (openaiFileId) {
        await this.openAIService.deleteFile(openaiFileId)
        const needDeleteVectorFiles = await this.assistantVectorstoreFilesService.find(
          openaiFileId
        )
        for (const needDeleteVectorFile of needDeleteVectorFiles) {
          await this.openAIService.deleteVectorStoreFile({
            openaiFileId,
            vectorStoreId: needDeleteVectorFile.vectorStoreId,
          })
          await this.assistantVectorstoreFilesService.delete(needDeleteVectorFile.id)
        }
      }
    }
    // 记录管理员操作日志
    await this.opLogService.createLog({
      operation: `${query.length > 3 ? `批量删除` : '删除书籍文件'}${query
        .slice(0, 3)
        .map((item) => `“${item.openaiFileId}”`)
        .join(',')} ${query.length > 3 ? `等${query.length}个书籍文件` : ''}`,
      user: user,
    })

    // 记录删除任务的日志
    await this.logService.save('删除书籍任务', user, query)
  }

  private async updateFileTask(options: {
    query: UpdateAssistantFileTaskDto[]
    user: any
  }) {
    const { query, user } = options
    console.log('updateFileTask query', query)
    for (const file of query) {
      const { openaiFileId, updateOpenaiFileId } = file
      if (openaiFileId) {
        await this.openAIService.deleteFile(openaiFileId)
        const needDeleteVectorFiles = await this.assistantVectorstoreFilesService.find(
          openaiFileId
        )
        for (const needDeleteVectorFile of needDeleteVectorFiles) {
          if (needDeleteVectorFile.vectorStoreId) {
            // 删除套餐旧的文件
            await this.openAIService.deleteVectorStoreFile({
              openaiFileId,
              vectorStoreId: needDeleteVectorFile.vectorStoreId,
            })

            // 重新上传文件
            await this.openAIService.createVectorStoreFile({
              vectorStoreId: needDeleteVectorFile.vectorStoreId,
              openaiFileId: updateOpenaiFileId,
            })
          }
          // 将所有文件状态更新为进行中
          await this.assistantVectorstoreFilesService.update(
            {
              openaiFileId: needDeleteVectorFile.openaiFileId,
            },
            {
              openaiFileId: updateOpenaiFileId,
              status: EOpeaiVectorStoreFileStatus.IN_PROGRESS,
            }
          )
        }
      }
    }
    // 记录管理员操作日志
    await this.opLogService.createLog({
      operation: `${query.length > 3 ? `批量更新` : '更新书籍文件'}${query
        .slice(0, 3)
        .map((item) => `“${item.updateOpenaiFileId}”`)
        .join(',')} ${query.length > 3 ? `等${query.length}个书籍文件` : ''}`,
      user: user,
    })

    // 记录更新任务的日志
    await this.logService.save('更新书籍任务', user, query)
  }

  /**
   * 检查openai文件状态
   * @param vectorStoreId 向量存储ID
   * @param batchId 批次ID
   */
  private async checkVectorFileTask(options: { vectorStoreId: string; batchId: string }) {
    const { vectorStoreId, batchId } = options

    try {
      // 获取文件列表及状态
      const vectorStoresFileList = await this.openAIService.getVectorStoresFileList(
        vectorStoreId,
        batchId
      )

      // 批量更新文件状态
      const updatePromises = vectorStoresFileList.data.map(async (file) => {
        await this.assistantVectorstoreFilesService.update(
          {
            openaiFileId: file.id,
            vectorStoreId: vectorStoreId,
          },
          { status: file.status }
        )
      })

      // 等待所有更新完成
      await Promise.all(updatePromises)

      console.log(`已更新 checkVectorFileTask ${vectorStoresFileList.total} 个文件的状态`)
    } catch (error) {
      console.error('更新向量存储文件状态失败:', error)
      throw error
    }
  }
}
