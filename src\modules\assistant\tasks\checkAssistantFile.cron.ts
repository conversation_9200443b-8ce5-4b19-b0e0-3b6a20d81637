import { Injectable } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { InjectRepository } from '@nestjs/typeorm'
import { IsNull, Repository } from 'typeorm'
import { AssistantFiles, AssistantVectorstoreFiles } from '@/entities'
import { EOpeaiFileStatus, EOpeaiVectorStoreFileStatus } from '@/enums'
import { OpenAIService } from '@/modules/websocket/services/openai.service'
import {
  AssistantFilesService,
  AssistantService,
  AssistantVectorstoreFilesService,
} from '../services'
import { AssistantFileTask } from './assistantFile.task'

@Injectable()
export class CheckAssistantFilesCron {
  constructor(
    private readonly openAIService: OpenAIService,
    private readonly assistantVectorstoreFilesService: AssistantVectorstoreFilesService,

    @InjectRepository(AssistantFiles)
    private readonly assistantFilesRepository: Repository<AssistantFiles>,
    @InjectRepository(AssistantVectorstoreFiles)
    private readonly assistantVectorstoreFilesRepository: Repository<AssistantVectorstoreFiles>,
    private readonly assistantService: AssistantService,
    private readonly assistantFilesService: AssistantFilesService,
  ) {}

  /**
 * 二，上传任务进行中显示有几种情况：
  1，系统本地队列堵塞或异常导致没执行和执行失败的情况（做重试处理）
  2，openai上传过程中堵塞或者异常情况，导致任务没检查完。
  [a]一个延迟任务在检查状态（正常情况下是可以整个流程处理完）
  [b]一个定时任务在检查数据库已有的file-id状态（第一层补漏）。
  [c]现在还需要加一个检查openai里面已经上传完的，但是数据库和缓存没记录完整的，这个任务用来做补缺数据。（第二层补漏）
 * 
 */

  /**
   * 检查vertorstore文件上传状态
   * @returns
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async checkVectorStoreFile() {
    if (process.env.APP_ENV === 'local') return
    console.log('Cron job assistant checkVectorStoresFiles')
    const inPrgoessFiles = await this.assistantVectorstoreFilesRepository
      .createQueryBuilder('AssistantVectorstoreFiles')
      .where('AssistantVectorstoreFiles.status = :status', {
        status: EOpeaiVectorStoreFileStatus.IN_PROGRESS,
      })
      .andWhere('AssistantVectorstoreFiles.openaiFileId IS NOT NULL')
      .andWhere('AssistantVectorstoreFiles.assistantId IS NOT NULL')
      .andWhere('AssistantVectorstoreFiles.vectorStoreId IS NOT NULL')
      .andWhere('AssistantVectorstoreFiles.deletedAt IS NULL')
      .getMany()
    console.log('Check file:', {
      inPrgoessFiles,
    })
    if (inPrgoessFiles.length == 0) {
      return
    }
    for (const inPrgoessFile of inPrgoessFiles) {
      const res = await this.openAIService.getVectorStoresFile(
        inPrgoessFile.vectorStoreId,
        inPrgoessFile.openaiFileId,
      )
      const id = inPrgoessFile.id
      const status = res?.status ?? EOpeaiVectorStoreFileStatus.FAILED
      console.log('Check file:', {
        openaiFileId: inPrgoessFile.openaiFileId,
        status,
      })
      await this.assistantVectorstoreFilesService.update(
        {
          id,
        },
        { status },
      )
    }
  }

  /**
   * 检查vertorstore文件上传状态
   * @returns
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleVectorStoreFile() {
    if (process.env.APP_ENV === 'local') return
    console.log('Cron job assistant handleVectorStoreFile')
    const inPrgoessFiles = await this.assistantVectorstoreFilesRepository
      .createQueryBuilder('AssistantVectorstoreFiles')
      .where('AssistantVectorstoreFiles.status = :status', {
        status: EOpeaiVectorStoreFileStatus.IN_PROGRESS,
      })
      .andWhere('AssistantVectorstoreFiles.vectorStoreId IS NOT NULL')
      .andWhere('AssistantVectorstoreFiles.deletedAt IS NULL')
      .getMany()
    if (inPrgoessFiles.length == 0) {
      return
    }
    for (const inPrgoessFile of inPrgoessFiles) {
      if (
        inPrgoessFile.vectorStoreId == null ||
        (inPrgoessFile.assistantId == null && inPrgoessFile.assistantNumberId !== null)
      ) {
        //获取
        const assistant = await this.assistantService.getAssistantDetail(
          inPrgoessFile.assistantNumberId,
        )
        await this.assistantVectorstoreFilesService.update(
          {
            id: inPrgoessFile.id,
          },
          {
            assistantId: assistant.assistantId,
            vectorStoreId: assistant.vectorStoreId,
          },
        )
      }
    }
  }

  /**
   * 删除处理未最终提交保存至openai stroage的文件
   * @returns
   */
  @Cron(CronExpression.EVERY_HOUR)
  async clearUnCompletedFiles() {
    if (process.env.APP_ENV === 'local') return
    console.log('Cron job assistant clearUnCompletedFiles')
    const penddingFiles = await this.assistantFilesRepository
      .createQueryBuilder('AssistantFiles')
      .where('AssistantFiles.status != :status', { status: EOpeaiFileStatus.COMPLETED })
      .andWhere('AssistantFiles.openaiFileId != :empty', { empty: '' })
      .getMany()
    if (penddingFiles.length == 0) {
      return
    }
    for (const penddingFile of penddingFiles) {
      console.log('clearUnCompletedFiles file:', penddingFile.isbn)
      await this.openAIService.deleteFile(penddingFile.openaiFileId)
      await this.assistantFilesService.updateFile(penddingFile.id, {
        openaiFileId: null,
        status: EOpeaiFileStatus.UNSUBMIT,
      })
    }
  }

  /**
   * 缓存同步assistant文件名
   */
  @Cron(CronExpression.EVERY_HOUR)
  async checkOpenaiCacheFileName() {
    if (process.env.APP_ENV === 'local') return
    console.log('Cron job assistant checkOpenaiCacheFileName')
    const completedFiles = await this.assistantFilesRepository
      .createQueryBuilder('AssistantFiles')
      .leftJoinAndSelect('AssistantFiles.book', 'book')
      .where('AssistantFiles.status = :status', { status: EOpeaiFileStatus.COMPLETED })
      .andWhere('AssistantFiles.openaiFileId IS NOT NULL')
      .andWhere('AssistantFiles.openaiFileId != :empty', { empty: '' })
      .andWhere('AssistantFiles.deletedAt IS NULL')
      .getMany()
    if (completedFiles.length == 0) {
      return
    }
    for (const completedFile of completedFiles) {
      let fileName = completedFile.fileName
      if (completedFile.fileName == null) {
        fileName = await this.assistantFilesService.getFileNameByIsbn(completedFile.isbn)
        await this.assistantFilesRepository.update(
          { id: completedFile.id },
          {
            fileName,
          },
        )
      }
      await this.assistantFilesService.updateFilesInfoCache(
        completedFile.openaiFileId,
        fileName,
        completedFile.bookId,
        completedFile.book.version,
      )
    }
  }
}
