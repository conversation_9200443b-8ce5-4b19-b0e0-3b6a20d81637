import {Body, Controller, Delete, Get, Header, Param, ParseIntPipe, Patch, Post, Query, Res, UploadedFile, UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectDataSource } from '@nestjs/typeorm'
import { Response } from 'express'
import type { DataSource } from 'typeorm'
import {
  AdminAuth,
  ApiBaseResult,
  ApiFile,
  ApiListResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  ExcelService,
  getPageResponse,
  PageResponse,
} from '@/common'
import { EDataExportType, ETaskType, TaskService } from '@/common/components/task'
import { Author } from '@/entities'
import { EBookVersion } from '@/enums'
import { OperationLogService } from '@/modules/system'
import {
  AuthorDto,
  AuthorReferenceStatisticDto,
  CreateAuthorDto,
  DeleteAuthorDto,
  getAuthorDto,
  QueryAuthorDto,
} from '../dto'
import { AuthorService, ReferenceBookService } from '../services'
import {IFileTemplateService} from '@/modules/shared/account'
import { BookRepository, ReferenceReadService } from '../services/index1'

@ApiTags('Author')
@ApiExtraModels(AuthorDto, BooleanResponse)
@Controller('v1/admin/authors')
export class AuthorAdminController {
  constructor(
    private readonly authorService: AuthorService,
    private readonly logService: OperationLogService,
    private readonly bookRepository: BookRepository,
    private readonly fileService: IFileTemplateService,
    private readonly taskService: TaskService,
    private readonly excelService: ExcelService,
    private readonly referenceBookService: ReferenceBookService,
    private readonly referenceReadService: ReferenceReadService,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  @ApiOperation({ summary: 'create a author' })
  @ApiBaseResult(AuthorDto, 200)
  @AdminAuth()
  @Post()
  async createAuthor(
    @Body() data: CreateAuthorDto,
    @CurrentAdmin() user: any
  ): Promise<AuthorDto> {
    const author = await this.authorService.createAuthor(data, user)
    await this.logService.createLog({
      user,
      operation: `添加“${author.name.zh_HK}”作者`,
      metaData: { authorId: author.id },
    })
    return getAuthorDto(author)
  }

  @AdminAuth()
  @ApiListResult(AuthorDto, 201)
  @ApiOperation({ summary: 'Add authors in batches' })
  @Post('/batch-create')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async batchTemplate(
    @UploadedFile() file: Express.Multer.File,
    @CurrentAdmin() operator: any
  ): Promise<any> {
    const { errors, authors } = await this.authorService.batchAddAuthors(file, operator)
    return {
      errors: errors?.length ? errors : undefined,
      authors: authors?.map((item) => getAuthorDto(item)),
    }
  }

  @AdminAuth()
  @Get('/download-template-file')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Add Authors Example.xlsx')
  @ApiOperation({ summary: 'download model' })
  async downloadTemplateFile(@Res() res: Response, @CurrentLocale() local: ELocaleType) {
    const file = await this.fileService.downloadTemplateFile('addAuthors', local)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export')
  @ApiBaseResult(BooleanResponse, 200, 'Export succeeded')
  @CurrentLocaleHeader()
  async exportAuthor(
    @Query() query: QueryAuthorDto,
    @CurrentLocale() local = ELocaleType.ZH_HK,
    @CurrentAdmin() user: any
  ) {
    await this.taskService.deliver(
      ETaskType.DATA_EXPORT,
      {
        local,
        email: user.email,
        type: EDataExportType.EXPORT_AUTHORS,
        query,
        user,
      },
      {}
    )
    return {
      status: true,
    }
  }

  @ApiOperation({ summary: 'update a author' })
  @ApiBaseResult(AuthorDto, 200)
  @AdminAuth()
  @Patch(':authorId')
  async updateAuthor(
    @Param('authorId', ParseIntPipe) id: number,
    @Body() data: CreateAuthorDto
  ): Promise<AuthorDto> {
    const author = await this.authorService.patchAuthor(id, data)
    return getAuthorDto(author)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'get statistic for author in reference' })
  @ApiPageResult(AuthorReferenceStatisticDto, 200)
  @Get(':authorId/reference/statistic')
  async statisticInReference(@Param('authorId', ParseIntPipe) id: number) {
    const builder = await this.bookRepository.searchBookConditionV2({
      authorId: [id],
      version: EBookVersion.REFERENCE,
    })
    const books = await builder.getMany()
    if (books.length <= 0)
    {return { booksCount: 0, copiesCount: 0, usersCount: 0, timesCount: 0 }}

    const referenceData = await this.referenceBookService.getCount(
      books.map((item) => item.id)
    )
    const readData = await this.referenceReadService.getCounts(
      books.map((item) => item.id)
    )
    return { ...referenceData, ...readData }
  }

  @ApiOperation({ summary: 'get a author' })
  @ApiBaseResult(AuthorDto, 200)
  @AdminAuth()
  @Get(':authorId')
  async getAuthor(@Param('authorId', ParseIntPipe) authorId: number): Promise<AuthorDto> {
    const author = await this.authorService.getAuthor(authorId)
    return getAuthorDto(author)
  }

  @ApiOperation({ summary: 'list  authors' })
  @ApiPageResult(AuthorDto, 200)
  @AdminAuth()
  @Get()
  async listAuthor(
    @Query() query: QueryAuthorDto
  ): Promise<PageResponse<AuthorDto, Author>> {
    const data = await this.authorService.listAuthor(query)
    const items = (await Promise.all(
      data.items.map(async (item) => {
        const count = await this.bookRepository.countBooks(
          { authorId: item.id },
          { version: EBookVersion.SUBSCRIPTION }
        )
        const referenceCount = await this.bookRepository.countBooks(
          { authorId: item.id },
          { version: EBookVersion.REFERENCE }
        )
        return { ...getAuthorDto(item), count, referenceCount }
      })
    )) as unknown as AuthorDto[]
    return getPageResponse({ ...data, items })
  }

  @ApiOperation({ summary: 'delete authors' })
  @ApiPageResult(AuthorDto, 200)
  @AdminAuth()
  @Delete()
  async DeleteAuthor(@Body() query: DeleteAuthorDto, @CurrentAdmin() user: any) {
    let authorIds = query.ids?.length ? query.ids : []
    if (authorIds.length <= 0) {
      const authors = await this.authorService.searchAuthor(query.name)
      authorIds = authors.map((item) => item.id)
      if (query.exceptions?.length) {
        authorIds = authorIds.filter((id) => !query.exceptions.includes(id))
      }
    }
    if (authorIds.length <= 0) {return []}
    const authors = await this.authorService.getAuthors(authorIds)
    const books = await this.bookRepository.searchBooks({ authorId: authorIds })
    await this.dataSource.transaction(async (manager) => {
      if (books.length) {
        await this.bookRepository.deleteBook(
          books.map((item) => item.id),
          user,
          manager
        )
      }

      const data = authors.map((item) => ({
        ...item,
        name: {
          en_uk: item.name.en_uk,
          zh_HK: `${item.name.zh_HK}__delete${Date.now()}`,
          zh_cn: item.name.zh_cn,
        },
        deletedAt: new Date(),
      }))

      await manager.save(Author, data)
    })

    await this.logService.createLog({
      operation: `${authors.length > 3 ? `批量删除` : '删除作者'}${authors
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${authors.length > 3 ? `等${authors.length}位作者` : ''}`,
      metaData: { authorIds, query },
      user,
    })

    return { status: true }
  }
}
