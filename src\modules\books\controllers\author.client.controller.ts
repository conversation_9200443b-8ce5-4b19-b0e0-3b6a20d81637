import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiPageResult,
  ClientAuth,
  CurrentUser,
  getPageResponse,
  PageResponse,
} from '@/common'
import { Author } from '@/entities'
import { AuthorDto, getAuthorDto, SearchAuthorDto } from '../dto'
import { AuthorService } from '../services'
import { BookRepository } from '../services/index1'

@ApiTags('Author')
@ApiExtraModels(AuthorDto)
@Controller('v1/client/authors')
export class AuthorClientController {
  constructor(
    private readonly authorService: AuthorService,
    private readonly bookRepository: BookRepository,
  ) {}

  @ApiOperation({ summary: 'get a author' })
  @ApiBaseResult(AuthorDto, 200)
  @ClientAuth()
  @Get(':authorId')
  async getAuthor(@Param('authorId', ParseIntPipe) authorId: number): Promise<AuthorDto> {
    const author = await this.authorService.getAuthor(authorId)
    return getAuthorDto(author)
  }

  @ApiOperation({ summary: 'list  authors' })
  @ApiPageResult(AuthorDto, 200)
  @ClientAuth()
  @Get()
  async listAuthor(
    @Query() query: SearchAuthorDto,
    @CurrentUser() user: any,
  ): Promise<PageResponse<AuthorDto, Author>> {
    const data = await this.authorService.listClientAuthor(
      query,
      user.isTeacher ? undefined : user.schoolId,
    )
    const items = (await Promise.all(
      data.items.map(async (item) => {
        const count = await this.bookRepository.countBooks({ authorId: item.id })
        return { id: item.id, name: item.name, count }
      }),
    )) as unknown as AuthorDto[]
    return getPageResponse({ ...data, items })
  }
}
