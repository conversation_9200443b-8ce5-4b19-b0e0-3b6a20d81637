import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiListResult, SchoolAdminAuth } from '@/common'
import { AuthorDto, getAuthorDto, SearchAuthorDto } from '../dto'
import { AuthorService } from '../services'

@ApiTags('Author')
@ApiExtraModels(AuthorDto)
@Controller('v1/school-admin/authors')
export class AuthorSchoolController {
  constructor(private readonly authorService: AuthorService) {}

  @ApiOperation({ summary: 'search  authors' })
  @ApiListResult(AuthorDto, 200)
  @SchoolAdminAuth()
  @Get()
  async searchAuthors(@Query() query: SearchAuthorDto): Promise<any> {
    const data = await this.authorService.listAuthor(query)

    return { ...data, items: data.items.map((item) => getAuthorDto(item)) }
  }
}
