import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Header,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Express, Response } from 'express'
import R from 'ramda'
import {
  AdminAuth,
  ApiFile,
  ApiListResult,
  ApiPageResult,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  PublicAuth,
} from '@/common'
import { EBookVersion } from '@/enums'
import {
  BatchBookFilesDto,
  BookDto,
  BookZipDto,
  CreateBookDto,
  ExportAdminBookDto,
  QuerySchoolBookDto,
} from '../dto'
import { BookFileService } from '../services/index3'
import { ParseBookTask } from '../tasks'

@ApiTags('Books')
@Controller('/v1/admin/books-files')
@ApiExtraModels(BookDto)
export class BooksFileAdminController {
  constructor(
    private readonly bookFileService: BookFileService,
    private readonly parseTask: ParseBookTask,
  ) {}

  @AdminAuth()
  @Get('/download-template-file')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Book Bulk Upload Example.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'download upload book template file' })
  async downloadTemplateFile(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
  ) {
    const file = await this.bookFileService.downloadTemplateFile(local)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/export-search-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportSearchBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: ExportAdminBookDto,
    @CurrentAdmin() admin: any,
  ) {
    let { publisherId } = query
    if (admin.publisherIds?.length) {
      publisherId = publisherId?.length
        ? R.intersection(publisherId, admin.publisherIds)
        : admin.publisherIds
    }
    const file = await this.bookFileService.exportBooksWithReading(
      admin,
      local,
      { ...query, publisherId },
      query,
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/export-all-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportAllBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QuerySchoolBookDto,
    @CurrentAdmin() admin: any,
  ) {
    let { publisherId } = query
    if (admin.publisherIds?.length) {
      publisherId = publisherId?.length
        ? R.intersection(publisherId, admin.publisherIds)
        : admin.publisherIds
    }
    const file = await this.bookFileService.exportBooks(
      local,
      { ...query, publisherId },
      'allBookForAdmin',
      admin,
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/export-reference-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reference books' })
  async exportReferenceBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QuerySchoolBookDto,
    @CurrentAdmin() admin: any,
  ) {
    let { publisherId } = query
    if (admin.publisherIds?.length) {
      publisherId = publisherId?.length
        ? R.intersection(publisherId, admin.publisherIds)
        : admin.publisherIds
    }
    const file = await this.bookFileService.exportBooks(
      local,
      { ...query, publisherId, version: EBookVersion.REFERENCE },
      'platformReferenceBook',
      admin,
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/export-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() admin: any,
  ) {
    const file = await this.bookFileService.exportBooks(
      local,
      {
        version: EBookVersion.SUBSCRIPTION,
      },
      'bookInformationForAdminV2',
      admin,
    )
    res.send(Buffer.from(file))
  }

  // @AdminAuth()
  @ApiListResult(CreateBookDto, 200)
  @ApiOperation({ summary: 'upload book information' })
  @Post('/upload-book-information')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async batchTemplate(
    @UploadedFile() file: Express.Multer.File,
    @CurrentAdmin() operator: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.ZH_HK,
  ): Promise<any> {
    const { errors, books } = await this.bookFileService.uploadBookTemplateFile(
      file,
      operator,
      local,
    )
    return { errors: errors?.length ? errors : undefined, books }
  }

  @AdminAuth()
  @ApiPageResult(BookZipDto, 200, 'batch upload books success')
  @ApiOperation({ summary: 'batch upload books' })
  @Post('/batch-book-files')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async BatchUploadBooks(@UploadedFile() file: Express.Multer.File, @Body() body?: any) {
    let books: BatchBookFilesDto[]
    try {
      books = JSON.parse(body.books)
    } catch (err) {
      throw new BadRequestException('invalid books parameter')
    }
    return this.bookFileService.uploadBookZip(file, books)
  }

  @PublicAuth()
  @Get('task/:id')
  async task(@Param('id', ParseIntPipe) bookId: number) {
    return this.parseTask.test(bookId)
  }
}
