import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {Body, Controller, Delete, Get, Inject, Param, ParseIntPipe, Patch, Post, Query, UploadedFile, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import moment from 'moment'
import R from 'ramda'
import {
  AdminAuth,
  ApiBaseResult,
  ApiFile,
  ApiListResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  CurrentPlatform,
  CurrentUser,
  EPlatform,
  getPageResponse,
  IdsDto,
  PageRequest,
  PageResponse,
  RedisService,
} from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { Book, BookOperateApplication } from '@/entities'
import { countries, EBookVersion, OnlineOfflineStatus } from '@/enums'
import { IContractService } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system'
import { getName } from '@/utils/book.utitl'
import {
  BatchCreateBook,
  BatchUpdateBookDto,
  BatchUpdateBookVersionDto,
  BookDto,
  BookOperateApplicationResponse,
  CreateBookDto,
  DeleteSearchBookDto,
  getBookDto,
  QueryAdminReadingInDetailDto,
  QuerySchoolBookDto,
  QuerySimpleBookDto,
  ReadingTimeDto,
  UpdateBookDto,
  UpdateBookStatusDto,
  UploadSignalBookDto,
} from '../dto'
import {
  BookshelfService,
  InitLeaderBoardService,
  ReadingPosService,
  ReferenceBookService,
} from '../services'
import { BookRepository, BookLevelService } from '../services'
import { ReadRecordService } from '../services/index1'
import { BookService } from '../services/index3'

@ApiTags('Books')
@ApiExtraModels(
  BookDto,
  UploadSignalBookDto,
  ReadingTimeDto,
  BookOperateApplication,
  BookOperateApplicationResponse
)
@Controller('v1/admin/books')
export class BookAdminController {
  constructor(
    private readonly bookService: BookService,
    private readonly bookRepo: BookRepository,
    private readonly readRecordService: ReadRecordService,
    private readonly logService: OperationLogService,
    private readonly taskService: TaskService,
    private readonly initLeaderBoardService: InitLeaderBoardService,
    private readonly bookshelfService: BookshelfService,
    private readonly readingPosService: ReadingPosService,
    private readonly bookLevelService: BookLevelService,
    private readonly redisService: RedisService,
    private readonly referenceService: ReferenceBookService,
    private readonly contractService: IContractService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @AdminAuth()
  @Get('country-code')
  getCountiesCode() {
    return countries
  }

  @ApiOperation({ summary: 'create a book' })
  @AdminAuth()
  @ApiBaseResult(BookDto, 200)
  @Post('single')
  async createBook(
    @Body() data: CreateBookDto,
    @CurrentAdmin() user: any
  ): Promise<BookDto> {
    const book = await this.bookService.saveBook(data)
    await this.logService.createLog({
      user,
      operation: `添加書籍《${book.name.zh_HK}》`,
      metaData: { bookId: book.id },
    })
    return getBookDto(book)
  }

  @ApiOperation({ summary: 'batch create book' })
  @AdminAuth()
  @ApiListResult(BookDto, 200)
  @Post('batch')
  async createBatchBook(
    @Body() data: BatchCreateBook,
    @CurrentAdmin() user: any
  ): Promise<BookDto[]> {
    const books = await Promise.all(
      data.books
        .filter((item) => !item.isDuplicated)
        .filter((item) => !R.isNil(item.url))
        .map((item) =>
          this.bookService.saveBook({ ...item, status: OnlineOfflineStatus.OFFLINE })
        )
    )

    await this.logService.createLog({
      user,
      operation: `批量添加${books.length > 3 ? '' : '書籍'}${books
        .slice(0, 3)
        .map((item) => item.name.zh_HK)
        .join(',')}${books.length > 3 ? `等${books.length}本書籍` : ''}`,
      metaData: { bookId: books.map((item) => item.id) },
    })

    await Promise.all(
      data.books
        .filter((item) => item.isDuplicated)
        .map((book) =>
          this.bookService.updateBook(
            book.iD,
            R.omit(['iD', 'status'], book) as unknown as UpdateBookDto,
            user
          )
        )
    )

    return books.map((item) => getBookDto(item))
  }

  @ApiOperation({ summary: 'batch update book' })
  @AdminAuth()
  @ApiListResult(BookDto, 200)
  @Patch('batch')
  async batchUpdateBook(@Body() data: BatchUpdateBookDto): Promise<BookDto[]> {
    const books = await Promise.all(
      data.books.map((item) =>
        this.bookRepo.updateBook(
          item.id,
          R.pick(['coverUrl', 'hyperlink', 'isScienceRoom'], item)
        )
      )
    )
    return books.map((item) => getBookDto(item))
  }

  @ApiOperation({ summary: 'batch update book version' })
  @AdminAuth()
  @ApiListResult(BooleanResponse, 200)
  @Patch('version')
  async updateBookVersion(
    @Body() data: BatchUpdateBookVersionDto,
    @CurrentAdmin() user: any
  ) {
    const { version, ids, exceptions, ...query } = data
    let bookIds = ids?.length ? ids : []
    if (bookIds.length <= 0) {
      let { publisherId } = query
      if (user.publisherIds?.length) {
        publisherId = publisherId?.length
          ? R.intersection(user.publisherIds, publisherId)
          : user.publisherIds
      }
      bookIds = await this.bookService.searchBookIds({ ...query, publisherId })
      if (exceptions?.length) {
        bookIds = bookIds.filter((item) => !exceptions.includes(item))
      }
    }

    if (bookIds.length <= 0) {return []}

    await this.bookRepo.updateBooks(bookIds, {
      version,
    })

    const books = await this.bookRepo.listBooks(
      { ids: bookIds.slice(0, 2) },
      { fields: ['id', 'name'] }
    )
    await this.logService.createLog({
      user,
      operation: `把${books.map((item) => `《${getName(item.name)}》`).join(' ')}${
        bookIds.length > 2 ? `等${bookIds.length}本書籍` : ''
      }設置為參考館/訂閱版書籍`,
      metaData: { bookIds, version },
    })

    return { status: true }
  }

  @ApiOperation({ summary: 'update a book' })
  @AdminAuth()
  @ApiBaseResult(BookDto, 200)
  @Patch(':id')
  async updateBook(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateBookDto,
    @CurrentAdmin() admin: any
  ): Promise<BookDto> {
    const book = await this.bookService.updateBook(id, R.omit(['status'], data), admin)

    const key = `cacheKey.findOneBook.${id}`
    await this.cacheManager.del(key)
    return getBookDto(book)
  }

  @ApiOperation({ summary: 'update book status' })
  @AdminAuth()
  @ApiListResult(BookDto, 200)
  @Patch()
  async updateBookStatus(
    @Body() data: UpdateBookStatusDto,
    @CurrentAdmin() user: any
  ): Promise<BookDto[]> {
    const { status, ids, exceptions, ...query } = data
    let bookIds = ids?.length ? ids : []
    if (bookIds.length <= 0) {
      let { publisherId } = query
      if (user.publisherIds?.length) {
        publisherId = publisherId?.length
          ? R.intersection(user.publisherIds, publisherId)
          : user.publisherIds
      }
      bookIds = await this.bookService.searchBookIds({ ...query, publisherId })
      if (exceptions?.length) {
        bookIds = bookIds.filter((item) => !exceptions.includes(item))
      }
    }

    if (bookIds.length <= 0) {return []}

    const time: any = {}
    if (status === OnlineOfflineStatus.OFFLINE) {
      time.offlineAt = new Date()
    } else {
      time.onlineAt = new Date()
      time.offlineAt = null
    }

    const books = await this.bookRepo.updateBooks(bookIds, {
      ...time,
      status,
    })

    const operation =
      status === OnlineOfflineStatus.OFFLINE
        ? books.length > 3
          ? `批量下架`
          : '下架書籍'
        : books.length > 3
          ? `批量上架`
          : '上架書籍'

    await this.logService.createLog({
      operation: `${operation}${books
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${books.length > 3 ? `等${books.length}本書籍` : ''}`,
      metaData: { bookIds, data },
      user,
    })

    let userName = `${user.givenName ?? ''} ${user.familyName ?? ''}`
    if (userName.length > 1) {userName = user.email}
    await this.taskService.deliver(
      ETaskType.UPDATE_BOOK_STATUS,
      {
        userName,
        bookNames: books.map((item) => `《${item.name.zh_HK}》`).join(','),
        operation: data.status === OnlineOfflineStatus.OFFLINE ? '下架' : '上架',
        date: moment
          .tz(new Date(), 'Asia/Hong_Kong')
          .format('YYYY年MM月DD日 HH时mm分ss秒'),
      },
      { delay: 1000 }
    )

    for (const id of bookIds) {
      if (status === OnlineOfflineStatus.OFFLINE)
      {await this.initLeaderBoardService.removeBookFromReadingRanking(id)}
      else if (status === OnlineOfflineStatus.ONLINE)
      {await this.initLeaderBoardService.initBookReadingTime(id)}
    }

    // 删除缓存
    await this.redisService.mdel(`*cacheKey.CurrentCacheKey*`)

    return books.map((item) => getBookDto({ ...item, status }))
  }

  @ApiOperation({ summary: 'get reading time' })
  @ApiBaseResult(ReadingTimeDto, 200)
  @AdminAuth()
  @Get('/reading-time')
  async readingTime(@Query() query: QueryAdminReadingInDetailDto) {
    return this.readRecordService.adminStatistics(query)
  }

  @ApiOperation({ summary: 'book level' })
  @AdminAuth()
  @Get('level')
  level(@Query() query: PageRequest) {
    return this.bookLevelService.list(query)
  }

  @ApiOperation({ summary: 'list school reference books' })
  @ApiPageResult(BookDto, 200)
  @AdminAuth()
  @Get('reference/:schoolId')
  async listReferenceBooks(
    @Query() query: QuerySchoolBookDto,
    @Param('schoolId') schoolId: number
  ) {
    const data = await this.bookRepo.listReferenceBooks(schoolId, query, {
      relations: ['authors', 'referenceBooks'],
      fields: ['id', 'name', 'isbn', 'coverUrl'],
    })

    const count = await this.contractService.count(schoolId)

    return {
      ...getPageResponse<BookDto, Book>(
        data as unknown as Partial<PageResponse<BookDto, Book>>,
        data.items.map((item) => ({
          ...R.pick(['id', 'isbn', 'authors', 'name', 'coverUrl'], item),
          copiesCount: item.referenceBooks?.[0].copiesCount || 0,
        })) as any,
        getBookDto
      ),
      ...count,
    }
  }

  @ApiOperation({ summary: 'get a book' })
  @AdminAuth()
  @ApiBaseResult(BookDto, 200)
  @Get(':id')
  async getBook(@Param('id', ParseIntPipe) id: number) {
    const book = await this.bookRepo.getBook({ id })
    return getBookDto(book)
  }

  @ApiOperation({ summary: 'list simple book' })
  @AdminAuth()
  @ApiListResult(BookDto, 200)
  @Post('simple')
  async listSimpleBook(@Body() query: QuerySimpleBookDto): Promise<BookDto[]> {
    const books = await this.bookRepo.searchBooks(
      { isbn: query.isbns },
      undefined,
      { fields: ['id', 'name', 'isbn'] }
    )
    return query.isbns.map((item) => {
      const book = books.find((b) => b.isbn === item)
      if (book) {return getBookDto(book)}
      return { isbn: item } as BookDto
    })
  }

  @ApiOperation({ summary: 'list book' })
  @AdminAuth()
  @ApiPageResult(BookDto, 200)
  @Get()
  async listBook(
    @Query() query: QuerySchoolBookDto,
    @CurrentAdmin() admin: any
  ): Promise<PageResponse<BookDto, Book>> {
    let { publisherId } = query
    const { version = EBookVersion.SUBSCRIPTION } = query
    if (query.isScienceRoom) {
      query.version = undefined
    }
    if (admin.publisherIds?.length) {
      publisherId = publisherId?.length
        ? R.intersection(publisherId, admin.publisherIds)
        : admin.publisherIds
    }
    const data = await this.bookService.searchBooksByPage({ ...query, publisherId })
    let statistic = []
    if (data.items.length && version === EBookVersion.SUBSCRIPTION) {
      statistic = await this.readRecordService.readingTimeCount(
        data.items.map((item) => item.id)
      )
    }

    let references = []
    if (data.items.length && version === EBookVersion.REFERENCE) {
      references = await this.referenceService.getCountByBook(
        data.items.map((item) => item.id)
      )
    }

    const result = getPageResponse<BookDto, Book>(
      data as unknown as Partial<PageResponse<BookDto, Book>>,
      data.items,
      getBookDto
    )
    result.items = result.items.map((item) => ({
      ...item,
      readingTime: statistic.find((s) => s.bookId === item.id)?.totalReadingTime ?? 0,
      copiesCount: references.find((s) => s.id === item.id)?.copiesCount ?? 0,
    }))
    return result
  }

  @ApiOperation({ summary: 'delete found books ' })
  @AdminAuth()
  @Delete('search')
  async deleteSearchBooks(@Body() data: DeleteSearchBookDto, @CurrentAdmin() user: any) {
    let { publisherId } = data
    if (user.publisherIds?.length) {
      publisherId = publisherId?.length
        ? R.intersection(publisherId, user.publisherIds)
        : user.publisherIds
    }
    let ids = await this.bookService.searchBookIds({ ...data, publisherId })
    if (data.exceptions?.length) {
      ids = ids.filter((id) => !data.exceptions.includes(id))
    }
    if (ids.length <= 0) {return []}

    await this.bookRepo.deleteBook(ids, user)
  }

  @ApiOperation({ summary: 'delete a book' })
  @AdminAuth()
  @Delete()
  async delete(@Body() data: IdsDto, @CurrentAdmin() user: any) {
    await this.bookRepo.deleteBook(data.ids, user)
  }

  @AdminAuth()
  @ApiBaseResult(UploadSignalBookDto, 201, 'upload book successful')
  @ApiOperation({ summary: 'upload a book' })
  @Post('/upload-book')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadBook(
    @UploadedFile() file: Express.Multer.File
  ): Promise<UploadSignalBookDto> {
    return this.bookService.uploadSignalBook(file)
  }

  @ApiOperation({ summary: 'get a book for preview' })
  @ApiBaseResult(BookDto, 200)
  @AdminAuth()
  @Get(':id')
  async getAdminBook(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
    @CurrentPlatform() platform: EPlatform
  ) {
    const book = await this.bookRepo.getBook({ id })
    return getBookDto(book)
  }
}
