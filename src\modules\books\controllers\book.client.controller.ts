import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {Controller, Get, Inject, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import moment from 'moment'
import R from 'ramda'
import {
  ApiBaseResult,
  ApiPageResult,
  ClientAuth,
  convertKey,
  CurrentCacheKey,
  CurrentPlatform,
  CurrentUser,
  EPlatform,
  PageRequest,
  publicEncrypt,
} from '@/common'
import { BookOrderType, EBookOrderType, EBookVersion, OnlineOfflineStatus } from '@/enums'
import { getBookIsHidden } from '@/modules/books/utils/bookhidden.util'
import { ISchoolService, IReadingReflectionService } from '@/modules/shared/interfaces'
import { QueryReadBookDto } from '@/modules/schools/dto/readingReflection.dto'
import { getDays } from '@/utils'
import { prefetchFromCache } from '@/utils/cache.util'
import {
  AesKeyDto,
  BookDto,
  getBookDto,
  QueryBookVersionDto,
  QueryClientBookDto,
  SearchClientBookDto,
  SearchWebClientBookDto,
} from '../dto'
import { IKeywordSearchBook } from '../interfaces'
import { BookshelfService, ReadingPosService,  } from '../services'
import {
  BookRepository,
  ReadRecordService,
  ReferenceReadService,
} from '../services/index1'
import { BookService } from '../services/index3'

@ApiTags('Books')
@Controller('v1/client/books')
export class BookClientController {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly bookService: BookService,
    private readonly bookshelfService: BookshelfService,
    private readonly readingPosService: ReadingPosService,
    private readonly schoolService: ISchoolService,
    private readonly readRecordService: ReadRecordService,
    private readonly referenceReadService: ReferenceReadService,
    private readonly readingReflectionService: IReadingReflectionService,

    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @ApiOperation({
    summary: 'get pdf aes key',
    description: 'the aes key is hex encoding',
  })
  @ApiBaseResult(AesKeyDto, 200)
  @ClientAuth()
  @Get('aes-key')
  async getAesKey() {
    return {
      key: publicEncrypt(convertKey(process.env.RSA_PUB_KEY), process.env.AES_KEY),
      iv: process.env.AES_IV,
    }
  }

  // @UseInterceptors(HttpCacheInterceptor)
  @ApiOperation({ summary: 'search books' })
  @ApiPageResult(BookDto, 200)
  @ClientAuth()
  @Get('search')
  async searchBooks(
    @Query() query: SearchClientBookDto,
    @CurrentUser() user: any,
    @CurrentCacheKey({
      auth: ['isTeacher', 'schoolId', 'isAllLevelForStaff', 'staffLevelIds'],
    })
      cacheKey: string
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const data = await this.bookService.searchRandomBooksByKeyword(
      version === EBookVersion.SUBSCRIPTION
        ? {
            ...R.omit(['categoryId', 'categoryLabelId', 'educationLabelId'], query),
            educationLabelId: query.educationLabelId
              ? [query.educationLabelId]
              : undefined,
            categoryLabelId: query.categoryLabelId ? [query.categoryLabelId] : undefined,
            categoryIds: query.categoryId ? [query.categoryId] : undefined,
            status: OnlineOfflineStatus.ONLINE,
            schoolId: user.isTeacher ? undefined : user.schoolId,
            isHidden: user.isTeacher ? undefined : false,
            level: this.bookService.getLevels(query.level, user),
            version,
          }
        : {
            ...R.omit(['categoryId', 'categoryLabelId', 'educationLabelId'], query),
            educationLabelId: query.educationLabelId
              ? [query.educationLabelId]
              : undefined,
            categoryLabelId: query.categoryLabelId ? [query.categoryLabelId] : undefined,
            categoryIds: query.categoryId ? [query.categoryId] : undefined,
            // status: OnlineOfflineStatus.ONLINE,
            referenceSchoolId: user.schoolId,
            version,
          },
      { userId: user.userId, withDeleted: version === EBookVersion.REFERENCE }
    )

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId } })
    )

    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      version
    )

    return {
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      total: data.total,
      items: data.items.map((item) => ({
        ...getBookDto(item),
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
    }
  }

  @ApiOperation({ summary: 'search books keyword' })
  @ApiPageResult(BookDto, 200)
  @ClientAuth()
  @Get('search-keyword')
  async searchBooksKeyword(
    @Query() query: SearchClientBookDto,
    @CurrentUser() user: any,
    @CurrentCacheKey({
      auth: ['isTeacher', 'schoolId', 'isAllLevelForStaff', 'staffLevelIds'],
    })
      cacheKey: string
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const data = await this.bookService.searchKeyword(
      version === EBookVersion.SUBSCRIPTION
        ? {
            ...R.omit(['categoryId', 'categoryLabelId', 'educationLabelId'], query),
            educationLabelId: query.educationLabelId
              ? [query.educationLabelId]
              : undefined,
            categoryLabelId: query.categoryLabelId ? [query.categoryLabelId] : undefined,
            categoryIds: query.categoryId ? [query.categoryId] : undefined,
            status: OnlineOfflineStatus.ONLINE,
            schoolId: user.isTeacher ? undefined : user.schoolId,
            isHidden: user.isTeacher ? undefined : false,
            level: this.bookService.getLevels(query.level, user),
            version,
          }
        : {
            ...R.omit(['categoryId', 'categoryLabelId', 'educationLabelId'], query),
            educationLabelId: query.educationLabelId
              ? [query.educationLabelId]
              : undefined,
            categoryLabelId: query.categoryLabelId ? [query.categoryLabelId] : undefined,
            categoryIds: query.categoryId ? [query.categoryId] : undefined,
            // status: OnlineOfflineStatus.ONLINE,
            referenceSchoolId: user.schoolId,
            version,
          },
      { userId: user.userId, withDeleted: version === EBookVersion.REFERENCE }
    )

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )

    return {
      items: data.map((item) => ({
        ...item,
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
    }
  }

  @ApiOperation({ summary: 'search books for web' })
  @ApiPageResult(BookDto, 200)
  @ClientAuth()
  @Get('web')
  async searchWebBooks(
    @Query() query: SearchWebClientBookDto,
    @CurrentUser() user: any,
    @CurrentCacheKey({
      auth: ['isTeacher', 'schoolId', 'isAllLevelForStaff', 'staffLevelIds'],
    })
      cacheKey: string
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const data = await this.bookService.searchRandomBooksByKeyword(
      version === EBookVersion.SUBSCRIPTION
        ? {
            ...R.omit(['categoryId'], query),
            categoryIds: query.categoryId,
            status: OnlineOfflineStatus.ONLINE,
            schoolId: user.isTeacher ? undefined : user.schoolId,
            isHidden: user.isTeacher ? undefined : false,
            level: this.bookService.getLevels(undefined, user),
            version,
          }
        : {
            ...R.omit(['categoryId'], query),
            categoryIds: query.categoryId,
            // status: OnlineOfflineStatus.ONLINE,
            referenceSchoolId: user.schoolId,
            version,
          },
      { userId: user.userId, withDeleted: true }
    )

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )

    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      version
    )

    return {
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      total: data.total,
      items: data.items.map((item) => ({
        ...getBookDto(item),
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
    }
  }

  @ApiOperation({ summary: 'get newest books' })
  @ApiPageResult(BookDto, 200)
  @ClientAuth()
  @Get('newest')
  async newestBooks(
    @Query() query: PageRequest,
    @CurrentUser() user: any,
    @CurrentCacheKey({
      auth: ['isTeacher', 'schoolId', 'isAllLevelForStaff', 'staffLevelIds'],
    })
      cacheKey: string
  ) {
    const data = await prefetchFromCache(this.cacheManager, cacheKey, () =>
      this.bookService.getNewestBooks(
        user.isTeacher ? undefined : user.schoolId,
        query,
        this.bookService.getLevels(undefined, user)
      )
    )

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )

    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      EBookVersion.SUBSCRIPTION
    )

    return {
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      total: data.total,
      items: data.items.map((item) => ({
        ...getBookDto(item),
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
    }
  }

  @ApiOperation({ summary: 'list books by author or publisher' })
  @ApiPageResult(BookDto, 200)
  @ClientAuth()
  @Get('list')
  async listBooks(
    @Query() query: QueryClientBookDto,
    @CurrentUser() user: any,
    @CurrentCacheKey({
      auth: ['isTeacher', 'schoolId', 'isAllLevelForStaff', 'staffLevelIds'],
    })
      cacheKey: string
  ) {
    const data = await prefetchFromCache(this.cacheManager, cacheKey, async () => {
      const filter: IKeywordSearchBook = {
        status: OnlineOfflineStatus.ONLINE,
        version: EBookVersion.SUBSCRIPTION,
        orderBy:
          query.order === BookOrderType.HOT
            ? EBookOrderType.TIMES_OF_SCHOOL_READING
            : EBookOrderType.NEWEST,
        pageIndex: query.pageIndex,
        pageSize: query.pageSize,
      }

      if (!user.isTeacher) {
        filter.schoolId = user.schoolId
        filter.isHidden = false
      }
      if (query.authorId) {
        filter.authorId = [query.authorId]
      }
      if (query.publisherId) {
        filter.publisherId = [query.publisherId]
      }

      const levels = this.bookService.getLevels(undefined, user)
      if (levels?.length) {
        filter.level = levels
      }

      return this.bookRepositories.searchBooksByPage(filter)
    })

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )

    const shelf = await this.bookshelfService.findBookShelf(
      data.items.map((item) => item.id),
      user.userId,
      EBookVersion.SUBSCRIPTION
    )

    return {
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      total: data.total,
      items: data.items.map((item) => ({
        ...getBookDto(item),
        isOnShelf: shelf.includes(item.id),
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
    }
  }

  /**
   *
   * @param user
   * @param query
   * @returns
   */
  @ClientAuth()
  @ApiOperation({ summary: ' Get user record book list ' })
  @Get('records')
  async listRecordBooks(@CurrentUser() user: any, @Query() query: QueryReadBookDto) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    if (version === EBookVersion.SUBSCRIPTION) {
      return await this.readRecordService.listReadBooksPage(
        user.userId,
        user.schoolId,
        query
      )
    } else {
      return await this.referenceReadService.listReadBooksPage(
        user.userId,
        user.schoolId,
        query
      )
    }
  }

  @ClientAuth()
  @ApiOperation({ summary: 'Get user reading time chart last 7 days ' })
  @Get('reading-chart')
  async readingTimeChart(@CurrentUser() user: any, @Query() query: QueryBookVersionDto) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const endTime = Math.floor(Date.now() / 1000)
    const startTime = endTime - 6 * 24 * 60 * 60

    if (version === EBookVersion.SUBSCRIPTION) {
      const data = await this.readRecordService.userReadingTime(user.userId, {
        startTime: startTime,
        endTime: endTime,
      })
      return getDays(startTime, endTime).map((date) => ({
        date,
        totalReadingTime:
          data.find(
            (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
          )?.totalReadingTime ?? 0,
      }))
    } else {
      const data = await this.referenceReadService.getCountsByUser(user.userId, {
        startTime: startTime,
        endTime: endTime,
      })
      return getDays(startTime, endTime).map((date) => ({
        date,
        timesCount:
          data.find(
            (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
          )?.timesCount ?? 0,
      }))
    }
  }

  @ApiOperation({ summary: 'get a book' })
  @ApiBaseResult(BookDto, 200)
  @ClientAuth()
  @Get(':id')
  async getBook(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: QueryBookVersionDto,
    @CurrentUser() user: any,
    @CurrentPlatform() platform: EPlatform
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = query

    const [book, shelf, school, [posData], readingReflectionCount] = await Promise.all([
      // 从缓存或数据库获取书籍信息
      prefetchFromCache(this.cacheManager, `cacheKey.findOneBook.${id}`, () =>
        this.bookRepositories.getBook(
          { id },
          { withDeleted: version === EBookVersion.REFERENCE }
        )
      ),

      // 查找书架
      this.bookshelfService.findBookShelf([id], user.userId, version),

      // 从缓存获取学校信息
      prefetchFromCache(
        this.cacheManager,
        `cacheKey.fondOneSchool.${user.schoolId}`,
        () => this.schoolService.findOne({ where: { id: user.schoolId  } })
      ),

      // 获取阅读位置
      this.readingPosService.getReadingPos([id], user.userId),

      // 获取读书想法计数
      this.readingReflectionService.getReadingReflectionCount(user.userId, id, version),
    ])

    // 获取 book DTO 数据
    const data = getBookDto(book)
    const level = this.bookService.getLevels(undefined, user)
    const isHidden = user.isTeacher ? undefined : false
    data.isShown = true
    if (
      user.schoolId &&
      !R.isNil(isHidden) &&
      book.hiddeSchoolIds?.includes(user.schoolId)
    ) {
      data.isShown = false
    }
    if (version && !book.version.split(',').includes(version)) {
      data.isShown = false
    }
    if (
      version === EBookVersion.SUBSCRIPTION &&
      level &&
      !level.some((level) => book.level.includes(level))
    ) {
      data.isShown = false
    }
    if (book.status === OnlineOfflineStatus.OFFLINE) {
      data.isShown = false
    }
    if (
      version === EBookVersion.REFERENCE &&
      user.schoolId &&
      !book.referenceBooks.some((item) => item.school.id === user.schoolId)
    ) {
      data.isShown = false
    }

    // 组装返回的 response 数据
    const response = {
      ...data,
      isOnShelf: shelf.includes(id),
      pos: posData?.pos,
      readingReflectionCount: readingReflectionCount ?? 0,
      isHidden: getBookIsHidden(book, school, user.isTeacher),
    }

    // 根据平台不同，返回不同的结构
    if (platform !== EPlatform.ANDROID) {
      return response
    } else {
      return {
        ...response,
        category:
          response.category && response.category.length > 0 ? response.category[0] : null,
      }
    }
  }
}
