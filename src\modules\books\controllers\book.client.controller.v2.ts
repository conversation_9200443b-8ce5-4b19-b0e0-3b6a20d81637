import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {Controller, Get, Inject, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import { Api<PERSON><PERSON><PERSON><PERSON>ult, ClientAuth, CurrentUser } from '@/common'
import { EBookVersion } from '@/enums'
import { ISchoolService } from '@/modules/shared/interfaces'
import { prefetchFromCache } from '@/utils/cache.util'
import { BookDto, getBookDto, QueryBookVersionDto } from '../dto'
import { BookshelfService, ReadingPosService, BookRepository } from '../services'
import {  } from '../services/book.repository'
import { getBookIsHidden } from '../utils/bookhidden.util'

@ApiTags('Books')
@Controller('v2/client/books')
export class BookClientV2Controller {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly bookshelfService: BookshelfService,
    private readonly readingPosService: ReadingPosService,
    private readonly schoolService: ISchoolService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @ApiOperation({ summary: 'get a book, v2, prepared for android' })
  @ApiBaseResult(BookDto, 200)
  @ClientAuth()
  @Get(':id')
  async getBook(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: QueryBookVersionDto,
    @CurrentUser() user: any
  ) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const book = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneBook.${id}`,
      () =>
        this.bookRepositories.getBook(
          { id },
          { withDeleted: version === EBookVersion.REFERENCE }
        )
    )

    // const book = await this.bookRepositories.getBook({ id })
    const shelf = await this.bookshelfService.findBookShelf([id], user.userId, version)

    const school = await prefetchFromCache(
      this.cacheManager,
      `cacheKey.findOneSchool.${user.schoolId}`,
      () => this.schoolService.findOne({ where: { id: user.schoolId  } })
    )

    // const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })

    const data = getBookDto(book)
    const [posData] = await this.readingPosService.getReadingPos([book.id], user.userId)
    return {
      ...data,
      isHidden: getBookIsHidden(book, school, user.isTeacher),
      isOnShelf: shelf.includes(id),
      pos: posData?.pos,
    }
  }
}
