import { Body, Controller, Get, Header, <PERSON>, Query, Res } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import R from 'ramda'
import {
  ApiPageResult,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  getPageResponse,
  PageRequest,
  PageResponse,
  SchoolAdminAuth,
} from '@/common'
import { Book } from '@/entities'
import { BookUserType, EBookVersion, OnlineOfflineStatus } from '@/enums'
import { LogService, OperationLogService } from '@/modules/system'
import { BookDto, getBookDto, HideBookDtoV2, QuerySchoolBookDto } from '../dto'
import { InitLeaderBoardService, LeaderBoardService } from '../services'
import { BookLevelService } from '../services/bookLevel.service'
import { BookService } from '../services/index3'
import { ReferenceBookService } from '../services/referenceBook.service'
import { booksToExcel } from '../utils/bookExcel.util'

@ApiTags('Books')
@Controller('v1/school-admin/books')
export class BookSchoolAdminController {
  constructor(
    private readonly bookService: BookService,
    private readonly initLeadBoardService: InitLeaderBoardService,
    private readonly leadBoardService: LeaderBoardService,
    private readonly bookLevelService: BookLevelService,
    private readonly excelService: ExcelService,
    private readonly logService: OperationLogService,
    private readonly referenceBookService: ReferenceBookService,
    private readonly sysLogService: LogService,
  ) {}

  @ApiOperation({ summary: 'book level' })
  @SchoolAdminAuth()
  @Get('level')
  async level(@Query() query: PageRequest, @CurrentSchoolAdmin() user: any) {
    if (!user.isAllLevelForStaff && user.staffLevelIds?.length) {
      const items = await this.bookLevelService.getByIds(user.staffLevelIds)
      return { pageIndex: 1, pageSize: items.length, total: items.length, items }
    }

    return this.bookLevelService.list(query)
  }

  @ApiOperation({ summary: 'list books' })
  @ApiPageResult(BookDto, 200)
  @Get()
  @SchoolAdminAuth()
  async listBooks(@Query() query: QuerySchoolBookDto, @CurrentSchoolAdmin() admin: any) {
    const { version = EBookVersion.SUBSCRIPTION } = query
    const data = await this.searchBooksForSchools({ ...query, version }, admin)
    const items = data.items.map((item) => {
      const userType = [BookUserType.TEACHER]

      if (
        admin.isAllLevelForStudent ||
        R.isNil(admin.studentLevelIds) ||
        admin.studentLevelIds.length === 0 ||
        R.intersection(item.level ?? [], admin.studentLevelIds).length
      )
        userType.push(BookUserType.STUDENT)
      return { ...item, userType }
    })
    return getPageResponse<BookDto, Book>(
      data as unknown as Partial<PageResponse<BookDto, Book>>,
      items,
      getBookDto,
      admin.schoolId,
    )
  }

  @SchoolAdminAuth()
  @Get('/export-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportBooks(
    @Query() query: QuerySchoolBookDto,
    @CurrentSchoolAdmin() admin: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
  ) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let books = []

    do {
      const data = await this.searchBooksForSchools(
        {
          ...query,
          version: EBookVersion.SUBSCRIPTION,
          pageIndex,
          pageSize,
        },
        admin,
      )
      pageIndex += 1
      total = data.total
      const items = data.items.map((item) => {
        return booksToExcel(item, { local, schoolId: admin.schoolId })
      })
      books = books.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    const file = this.excelService.buildExcel(
      [
        {
          name: `bookInformationV2.${local}`,
          data: books,
        },
      ],
      local === ELocaleType.EN_UK ? 'Books' : '書籍',
    )

    await this.sysLogService.save('下载书籍列表', admin, query)
    res.send(Buffer.from(file))
  }

  @SchoolAdminAuth()
  @Get('/export-reference-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename=Books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportReferenceBooks(
    @Query() query: QuerySchoolBookDto,
    @CurrentSchoolAdmin() admin: any,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
  ) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let books = []

    const version = EBookVersion.REFERENCE
    do {
      const data = await this.searchBooksForSchools(
        {
          ...query,
          version,
          pageIndex,
          pageSize,
        },
        admin,
      )
      pageIndex += 1
      total = data.total
      const items = data.items.map((item) => {
        return booksToExcel(item, { local, schoolId: admin.schoolId })
      })
      books = books.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    const file = this.excelService.buildExcel(
      [
        {
          name: `schoolReferenceBook.${local}`,
          data: books,
        },
      ],
      local === ELocaleType.EN_UK ? 'Books' : '書籍',
    )

    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'hide books' })
  @ApiPageResult(BookDto, 200)
  @Patch('hide')
  @SchoolAdminAuth()
  async hideBooks(@Body() query: HideBookDtoV2, @CurrentSchoolAdmin() admin: any) {
    let ids = []
    if (query.bookIds) ids = query.bookIds
    else {
      const data = await this.searchBooksForSchools(
        { ...query, pageIndex: 1, pageSize: 0 },
        admin,
      )
      ids = data?.allBookIds || []
    }
    if (ids.length === 0) {
      ids = await this.bookService.searchBookIds({
        ...query,
        status: OnlineOfflineStatus.ONLINE,
      })
      // ids = books.items.map((item) => item.id)
      if (query.exceptions?.length) {
        ids = ids.filter((id) => !query.exceptions.includes(id))
      }
    }
    if (ids.length) await this.bookService.updateBookVisible(ids, admin.schoolId, true)
    for (const id of ids)
      await this.leadBoardService.removeFromReadingRankingForStudent(id, admin.schoolId)

    if (ids.length) {
      const bookNames = await this.bookService.findBookTitles(ids)

      let operation = null

      if (ids.length === 1) {
        operation = `隱藏書籍${bookNames}`
      } else {
        operation = `批量隱藏${bookNames}等${ids.length}本書籍`
      }

      await this.logService.createLog({
        user: admin,
        operation,
        metaData: { ids },
      })
    }
  }

  @ApiOperation({ summary: 'unhide books' })
  @ApiPageResult(BookDto, 200)
  @Patch('unhide')
  @SchoolAdminAuth()
  async unhideBooks(@Body() query: HideBookDtoV2, @CurrentSchoolAdmin() admin: any) {
    let ids = []
    if (query.bookIds) ids = query.bookIds
    else {
      const data = await this.searchBooksForSchools(
        { ...query, pageIndex: 1, pageSize: 0 },
        admin,
      )
      ids = data?.allBookIds || []
    }
    if (ids.length === 0) {
      ids = await this.bookService.searchBookIds({
        ...query,
        status: OnlineOfflineStatus.ONLINE,
      })
      // ids = books.items.map((item) => item.id)
      if (query.exceptions?.length) {
        ids = ids.filter((id) => !query.exceptions.includes(id))
      }
    }
    if (ids.length) await this.bookService.updateBookVisible(ids, admin.schoolId, false)
    for (const id of ids)
      await this.initLeadBoardService.initBookReadingTime(id, admin.schoolId)

    if (ids.length) {
      const bookNames = await this.bookService.findBookTitles(ids)

      let operation = null

      if (ids.length === 1) {
        operation = `恢復書籍${bookNames}`
      } else {
        operation = `批量恢復${bookNames}等${ids.length}本書籍`
      }

      await this.logService.createLog({
        user: admin,
        operation,
        metaData: { ids },
      })
    }
  }

  private async searchBooksForSchools(query: QuerySchoolBookDto, admin: any) {
    if (query.version === EBookVersion.REFERENCE) {
      const data = await this.bookService.searchBooksByPage(
        {
          ...query,
          // status: OnlineOfflineStatus.ONLINE,
          referenceSchoolId: admin.schoolId,
        },
        { withDeleted: true },
      )
      if (!data.items.length) return data

      const ids = data.items.map((item) => item.id)
      const books = await this.referenceBookService.getCopiesCountForBook(
        ids,
        admin.schoolId,
      )

      return {
        ...data,
        items: data.items.map((item) => ({
          ...item,
          copiesCount: books.find((book) => book.id === item.id)?.copiesCount || 0,
        })),
      }
    }

    if (query.userType === BookUserType.STUDENT) {
      const { studentLevelIds, isAllLevelForStudent } = admin

      if (query.level?.length && !isAllLevelForStudent && studentLevelIds?.length)
        query.level = R.intersection(query.level, studentLevelIds)

      if (!query.level?.length && !isAllLevelForStudent && studentLevelIds?.length)
        query.level = studentLevelIds
    } else if (query.userType === BookUserType.TEACHER) {
      const { staffLevelIds, isAllLevelForStaff } = admin

      if (query.level?.length && !isAllLevelForStaff && staffLevelIds?.length)
        query.level = R.intersection(query.level, staffLevelIds)

      if (!query.level?.length && !isAllLevelForStaff && staffLevelIds?.length)
        query.level = staffLevelIds
    } else {
      query.level = query.level?.length
        ? query.level
        : admin.isAllLevelForStaff
        ? undefined
        : admin.staffLevelIds
    }

    return this.bookService.searchBooksByPage({
      ...query,
      schoolId: R.isNil(query.isHidden) ? undefined : admin.schoolId,
      status: OnlineOfflineStatus.ONLINE,
    })
  }
}
