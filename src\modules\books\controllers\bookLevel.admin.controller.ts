import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  getPageResponse,
  PageRequest,
} from '@/common'
import { EBookVersion } from '@/enums'
import { BookLevelDeleteException } from '@/modules/exception'
import { BookLevelDto, CreateBookLevelDto, getBookLevelDto } from '../dto/bookLevel.dto'
import { BookLevelService } from '../services'
import { BookRepository } from '../services/index1'

@Controller('v1/admin/bool-levels')
@ApiExtraModels(BookLevelDto)
export class BookLevelAdminController {
  constructor(
    private readonly bookLevelService: BookLevelService,
    private readonly bookRepository: BookRepository,
  ) {}

  @AdminAuth()
  @ApiBaseResult(BookLevelDto, 201)
  @ApiOperation({ summary: 'create book level' })
  @Post()
  async createBookLevel(@Body() data: CreateBookLevelDto) {
    const level = await this.bookLevelService.create(data)
    return getBookLevelDto(level)
  }

  @AdminAuth()
  @ApiBaseResult(BookLevelDto, 200)
  @ApiOperation({ summary: 'update book level' })
  @Patch(':id')
  async updateBookLevel(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateBookLevelDto,
  ) {
    const level = await this.bookLevelService.update(id, data)
    return getBookLevelDto(level)
  }

  @AdminAuth()
  @ApiBaseResult(BookLevelDto, 200)
  @ApiOperation({ summary: 'delete book level' })
  @Delete(':id')
  async deleteBookLevel(@Param('id', ParseIntPipe) id: number) {
    const count = await this.bookRepository.countBooks({ level: id })
    if (count > 0) {
      throw new BookLevelDeleteException()
    }
    const level = await this.bookLevelService.delete(id)
    return getBookLevelDto(level)
  }

  @AdminAuth()
  @ApiBaseResult(BookLevelDto, 200)
  @ApiOperation({ summary: 'list book level' })
  @Get(':id')
  async getBookLevel(@Param('id', ParseIntPipe) id: number) {
    const level = await this.bookLevelService.get(id)
    return getBookLevelDto(level)
  }

  @AdminAuth()
  @ApiPageResult(BookLevelDto, 200)
  @ApiOperation({ summary: 'list book level' })
  @Get()
  async listBookLevel(@Query() query: PageRequest) {
    const level = await this.bookLevelService.list(query)
    const items = await Promise.all(
      level.items.map(async (item) => {
        const count = await this.bookRepository.countBooks(
          { level: item.id },
          { version: EBookVersion.SUBSCRIPTION },
        )
        const referenceCount = await this.bookRepository.countBooks(
          { level: item.id },
          { version: EBookVersion.REFERENCE },
        )
        return { ...getBookLevelDto(item), count, referenceCount }
      }),
    )
    return getPageResponse({ ...level, items })
  }
}
