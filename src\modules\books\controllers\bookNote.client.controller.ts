import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiListResult,
  BooleanResponse,
  ClientAuth,
  CurrentUser,
} from '@/common'
import {
  BookNoteDto,
  CreateBookNoteDto,
  getBookNoteDto,
  QueryBookNoteDto,
  UpdateBookNoteDto,
} from '../dto'
import { BookNoteService } from '../services/index1'

@ApiTags('Books')
@ApiExtraModels(BookNoteDto)
@Controller('v1/client/book-notes')
export class BookNoteClientController {
  constructor(private readonly bookNoteService: BookNoteService) {}

  @ApiOperation({ summary: 'create a book note' })
  @ApiBaseResult(BookNoteDto, 200)
  @ClientAuth()
  @Post()
  async createBooknote(
    @Body() data: CreateBookNoteDto,
    @CurrentUser() user: any,
  ): Promise<BookNoteDto> {
    const note = await this.bookNoteService.createBooknote(data, user.userId)
    return getBookNoteDto(note)
  }

  @ApiOperation({ summary: 'update a book note' })
  @ApiBaseResult(BookNoteDto, 200)
  @ClientAuth()
  @Patch(':id')
  async updateBooknote(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateBookNoteDto,
    @CurrentUser() user: any,
  ): Promise<BookNoteDto> {
    const note = await this.bookNoteService.updateBooknote(id, data)
    return getBookNoteDto(note)
  }

  @ApiOperation({ summary: 'list book notes' })
  @ApiListResult(BookNoteDto, 200)
  @ClientAuth()
  @Get()
  async listBooknote(
    @Query() query: QueryBookNoteDto,
    @CurrentUser() user: any,
  ): Promise<BookNoteDto[]> {
    const notes = await this.bookNoteService.listBooknote(query.bookId, user.userId)
    return notes.map((item) => getBookNoteDto(item))
  }

  @ApiOperation({ summary: 'delete a book note' })
  @ApiBaseResult(BooleanResponse, 200)
  @ClientAuth()
  @Delete(':id')
  async deleteBooknote(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<BookNoteDto> {
    const note = await this.bookNoteService.deleteBooknote(id)
    return getBookNoteDto(note)
  }
}
