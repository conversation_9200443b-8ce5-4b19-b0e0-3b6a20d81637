import { Body, Controller, Delete, Get, Post, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {ApiBaseResult, ApiPageResult, ClientAuth, CurrentUser, getPageResponse, PageResponse,
} from '@/common'
import { Book } from '@/entities'
import { EBookVersion, OnlineOfflineStatus } from '@/enums'
import { getBookIsHidden } from '@/modules/books/utils/bookhidden.util'
import { ISchoolService } from '@/modules/shared/interfaces'
import {
  BookDto,
  BookshelfDto,
  EditBookToBookshelDto,
  getBookDto,
  getBookshelfDto,
  RemoveBookFromBookshelfDto,
  SearchBookFromBookshelfDto,
} from '../dto'
import { BookshelfService, ReadingPosService, BookRepository } from '../services'
import { BookService } from '../services/index3'

@ApiTags('Bookshelf')
@ApiExtraModels(BookshelfDto)
@Controller('v1/client/bookshelf')
export class BookshelfClientController {
  constructor(
    private readonly bookshelfService: BookshelfService,
    private readonly bookService: BookService,
    private readonly bookRepositories: BookRepository,
    private readonly readingPosService: ReadingPosService,
    private readonly schoolService: ISchoolService
  ) {}

  @ClientAuth()
  @ApiOperation({ summary: 'add a book to bookshelf' })
  @ApiBaseResult(BookshelfDto, 200)
  @Post()
  async addBook(
    @CurrentUser('userId') userId: number,
    @Body() data: EditBookToBookshelDto
  ): Promise<BookshelfDto> {
    await this.bookRepositories.getBook(
      { id: data.bookId },
      { withDeleted: data.version === EBookVersion.REFERENCE }
    )
    const bookshelf = await this.bookshelfService.addBook(userId, data)
    return getBookshelfDto(bookshelf)
  }

  @ClientAuth()
  @ApiOperation({ summary: 'get books from bookshelf' })
  @ApiPageResult(BookDto, 200)
  @Get('books')
  async getBookshelf(
    @CurrentUser() user: any,
    @Query() query: SearchBookFromBookshelfDto
  ) {
    // const user = { userId: 538, isTeacher: false, schoolId: 33 }
    const { version = EBookVersion.SUBSCRIPTION } = query
    const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
    let bookIds
    if (query.keyword) {
      const books = await this.bookService.searchBooksByPage(
        {
          keyword: query.keyword,
          isRealTimeSearch: query.isRealTimeSearch,
          status:
            version === EBookVersion.SUBSCRIPTION
              ? OnlineOfflineStatus.ONLINE
              : undefined,
          schoolId: user.schoolId,
          version,
          referenceSchoolId:
            version === EBookVersion.REFERENCE ? user.schoolId : undefined,
        },
        { withDeleted: version === EBookVersion.REFERENCE }
      )
      bookIds = books.items?.map((item) => item.id)
    }
    const data = await this.bookshelfService.getBookshelf(
      user.userId,
      user.isTeacher,
      user.schoolId,
      {
        ...query,
        bookIds,
        version,
      }
    )
    const items: Book[] =
      data.items.length === 0
        ? []
        : await this.bookRepositories.findBooks({
          ids: data.items.map((item) => item.bookId),
          withDeleted: version === EBookVersion.REFERENCE,
        })
    const posData = await this.readingPosService.getReadingPos(
      items.map((item) => item.id),
      user.userId
    )

    return getPageResponse<BookDto, Book>(
      data as unknown as PageResponse<BookDto, Book>,
      items.map((item) => ({
        ...item,
        pos: posData.find((pos) => pos.bookId === item.id)?.pos,
        isHidden: getBookIsHidden(item, school, user.isTeacher),
      })),
      getBookDto
    )
  }

  @ClientAuth()
  @ApiOperation({ summary: 'remove a book from bookshelf' })
  @Delete()
  async removeBook(
    @Body() data: RemoveBookFromBookshelfDto,
    @CurrentUser('userId') userId: number
  ): Promise<void> {
    const { version = EBookVersion.SUBSCRIPTION } = data
    return this.bookshelfService.removeBook(userId, { ...data, version })
  }
}
