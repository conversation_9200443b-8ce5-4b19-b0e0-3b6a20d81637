import {Body, Controller, Delete, Get, Param, ParseIntPipe, Patch, Post, Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectDataSource } from '@nestjs/typeorm'
import type { DataSource } from 'typeorm'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  BooleanResponse,
  getPageResponse,
  PageResponse,
} from '@/common'
import { Category } from '@/entities'
import { EBookVersion } from '@/enums'
import {
  CategoryDto,
  CreateCategoryDto,
  getCategoryDto,
  ListAdminCategoryDto,
} from '../dto'
import { CategoryService } from '../services'
import { BookRepository } from '../services/index1'

@ApiTags('Category')
@ApiExtraModels(CategoryDto)
@Controller('v1/admin/categories')
export class CategoryAdminController {
  constructor(
    private readonly categoryService: CategoryService,
    private readonly bookRepository: BookRepository,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  @ApiOperation({ summary: 'create a category' })
  @ApiBaseResult(CategoryDto, 200)
  @AdminAuth()
  @Post()
  async createCateory(@Body() data: CreateCategoryDto): Promise<CategoryDto> {
    const category = await this.categoryService.createCategory(data)
    return getCategoryDto(category)
  }

  @ApiOperation({ summary: 'update a category' })
  @ApiBaseResult(CategoryDto, 200)
  @AdminAuth()
  @Patch(':id')
  async updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateCategoryDto
  ): Promise<CategoryDto> {
    const category = await this.categoryService.updateCategory(id, data)
    return getCategoryDto(category)
  }

  @ApiOperation({ summary: 'list all category' })
  @ApiListResult(CategoryDto, 200)
  @AdminAuth()
  @Get('all')
  async listAllCategory(): Promise<CategoryDto[]> {
    return this.categoryService.getAllCategory()
  }

  @ApiOperation({ summary: 'get a category' })
  @ApiBaseResult(CategoryDto, 200)
  @AdminAuth()
  @Get(':id')
  async getCategory(@Param('id', ParseIntPipe) id: number): Promise<CategoryDto> {
    const category = await this.categoryService.getCategory({ id })
    return getCategoryDto(category)
  }

  @ApiOperation({ summary: 'list  category' })
  @ApiPageResult(CategoryDto, 200)
  @AdminAuth()
  @Get()
  async listCategory(
    @Query() query: ListAdminCategoryDto
  ): Promise<PageResponse<CategoryDto, Category>> {
    const data = await this.categoryService.listAdminCategory(query)
    const items = await Promise.all(
      data.items.map(async (item) => {
        const parent = await this.bookRepository.countBooks(
          { categoryId: [item.id] },
          { version: EBookVersion.SUBSCRIPTION }
        )
        const referenceCount = await this.bookRepository.countBooks(
          { categoryId: [item.id] },
          { version: EBookVersion.REFERENCE }
        )
        // const children = await Promise.all(
        //   item.children.map(async (c) => {
        //     const count = await this.bookRepository.countBooks({ categoryId: [c.id] })
        //     return { ...getCategoryDto(c as Category), count }
        //   }),
        // )

        return {
          ...item,
          // children,
          // count: children.length
          //   ? children.reduce((pre, child) => pre + child.count, 0) + parent
          //   : parent,
          count: parent,
          referenceCount,
        }
      }) as unknown as CategoryDto[]
    )
    return getPageResponse({ ...data, items })
  }

  @ApiOperation({ summary: 'delete  category' })
  @ApiPageResult(BooleanResponse, 200)
  @AdminAuth()
  @Delete(':id')
  async deleteCategory(@Param('id', ParseIntPipe) id: number) {
    await this.dataSource.transaction(async (manager) => {
      // const data = await this.categoryService.getCategoryWithBook({ id })
      // const bookIds = data.books.map((item) => item.id)
      // if (data.children?.length) {
      //   bookIds = bookIds.concat(
      //     R.flatten(data.children.map((item) => item.books?.map((item) => item.id))),
      //   )
      // }

      // const validBookIds = bookIds.filter((item) => !!item)
      // if (validBookIds.length)
      //   await this.bookRepository.updateBooks(validBookIds, { category: null }, manager)
      return this.categoryService.deleteCategory(id, manager)
    })
  }
}
