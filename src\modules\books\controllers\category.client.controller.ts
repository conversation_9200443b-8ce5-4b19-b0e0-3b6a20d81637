import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  ClientAuth,
  getPageResponse,
  PageResponse,
} from '@/common'
import { HttpCacheInterceptor } from '@/common/interceptors'
import { Category } from '@/entities'
import {
  CategoryDto,
  getCategoryDto,
  ListAdminCategoryDto,
  ListAllCategoryDto,
} from '../dto'
import { CategoryService } from '../services'

@ApiTags('Category')
@ApiExtraModels(CategoryDto)
@Controller('v1/client/categories')
export class CategoryClientController {
  constructor(private readonly categoryService: CategoryService) {}

  @UseInterceptors(HttpCacheInterceptor)
  @ApiOperation({ summary: 'list all category' })
  @ApiListResult(CategoryDto, 200)
  @ClientAuth()
  @Get('all')
  async listAllCategory(@Query() query: ListAllCategoryDto): Promise<CategoryDto[]> {
    return this.categoryService.getAllCategory(query)
  }

  @UseInterceptors(HttpCacheInterceptor)
  @ApiOperation({ summary: 'get a category' })
  @ApiBaseResult(CategoryDto, 200)
  @ClientAuth()
  @Get(':id')
  async getCategory(@Param('id', ParseIntPipe) id: number): Promise<CategoryDto> {
    const category = await this.categoryService.getCategory({ id })
    return getCategoryDto(category)
  }

  @UseInterceptors(HttpCacheInterceptor)
  @ApiOperation({ summary: 'list  category' })
  @ApiPageResult(CategoryDto, 200)
  @ClientAuth()
  @Get()
  async listCategory(
    @Query() query: ListAdminCategoryDto,
  ): Promise<PageResponse<CategoryDto, Category>> {
    const data = await this.categoryService.listCategory(query)
    return getPageResponse(data, data.items, getCategoryDto)
  }
}
