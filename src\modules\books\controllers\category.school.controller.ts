import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiBaseResult, ApiPageResult, getPageResponse, SchoolAdminAuth } from '@/common'
import { CategoryDto, getCategoryDto, ListAdminCategoryDto } from '../dto'
import { CategoryService } from '../services'

@ApiTags('Category')
@ApiExtraModels(CategoryDto)
@Controller('v1/school-admin/categories')
export class CategorySchoolController {
  constructor(private readonly categoryService: CategoryService) {}

  @ApiOperation({ summary: 'get a category' })
  @ApiBaseResult(CategoryDto, 200)
  @SchoolAdminAuth()
  @Get(':id')
  async getCategory(@Param('id', ParseIntPipe) id: number): Promise<CategoryDto> {
    const category = await this.categoryService.getCategory({ id })
    return getCategoryDto(category)
  }

  @ApiOperation({ summary: 'list all category' })
  @ApiPageResult(CategoryDto, 200)
  @SchoolAdminAuth()
  @Get()
  async listAllCategory(@Query() query: ListAdminCategoryDto): Promise<any> {
    const data = await this.categoryService.listAdminCategory(query)

    return getPageResponse(data)
  }
}
