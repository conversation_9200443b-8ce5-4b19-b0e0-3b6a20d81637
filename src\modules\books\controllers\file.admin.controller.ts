import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger'
import path from 'path'
import { ApiBaseResult, ApiFile, Auth, AuthSchema, generateUniqueId } from '@/common'
import { S3_IMAGE_DIR } from '@/modules/constants'
import {
  OnlySupportMP3OrMP4Exception,
  OnlySupportPngOrJpgException,
} from '@/modules/exception'
import { FileDto } from '../dto'
import { BookS3Service } from '../services'
import { compressImage } from '../utils'

@ApiTags('Files')
@Controller('v1/admin/files')
export class FileAdminController {
  constructor(private readonly s3Service: BookS3Service) {}

  @Auth({ schema: [AuthSchema.ADMIN, AuthSchema.SCHOOL_ADMIN] })
  @ApiOperation({ summary: 'upload file' })
  @ApiBaseResult(FileDto, 200)
  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async upload(@UploadedFile() file: Express.Multer.File): Promise<any> {
    const extname = path.extname(file.originalname).toLowerCase()
    // if (!['.mp3', '.mp4'].includes(extname)) throw new OnlySupportMP3OrMP4Exception()

    const url = await this.s3Service.upload({
      fileName: generateUniqueId() + extname,
      path: S3_IMAGE_DIR,
      file: file.buffer,
    })
    return { url }
  }

  @Auth({ schema: [AuthSchema.ADMIN, AuthSchema.SCHOOL_ADMIN] })
  @ApiOperation({ summary: 'upload images' })
  @ApiBaseResult(FileDto, 200)
  @Post('/images')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Express.Multer.File): Promise<any> {
    const extname = path.extname(file.originalname).toLowerCase()
    console.log({ extname })
    if (!['.png', '.jpg', '.jpeg', '.gif', ''].includes(extname))
      throw new OnlySupportPngOrJpgException()

    const buffer =
      file.size > 1024 * 1024 * 2 ? await compressImage(file.buffer) : file.buffer

    const url = await this.s3Service.upload({
      fileName: generateUniqueId() + extname,
      path: S3_IMAGE_DIR,
      file: buffer,
    })
    return { url }
  }

  @Auth({ schema: [AuthSchema.ADMIN, AuthSchema.SCHOOL_ADMIN] })
  @ApiOperation({ summary: 'upload video' })
  @ApiBaseResult(FileDto, 200)
  @Post('/videos')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadVideo(@UploadedFile() file: Express.Multer.File): Promise<any> {
    const extname = path.extname(file.originalname).toLowerCase()
    if (!['.mp3', '.mp4'].includes(extname)) throw new OnlySupportMP3OrMP4Exception()

    const url = await this.s3Service.upload({
      fileName: generateUniqueId() + extname,
      path: S3_IMAGE_DIR,
      file: file.buffer,
    })
    return { url }
  }
}
