import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger'
import path from 'path'
import { ApiBaseResult, ApiFile, Auth, AuthSchema, generateUniqueId } from '@/common'
import { S3_IMAGE_DIR } from '@/modules/constants'
import { FileDto } from '../dto'
import { BookS3Service } from '../services'

@ApiTags('Files')
@Controller('v1/files')
export class FileController {
  constructor(private readonly s3Service: BookS3Service) {}
  @Auth({ schema: [AuthSchema.ADMIN, AuthSchema.CLIENT, AuthSchema.SCHOOL_ADMIN] })
  @ApiOperation({ summary: 'upload images' })
  @ApiBaseResult(FileDto, 200)
  @Post('/images')
  @ApiConsumes('multipart/form-data')
  @ApiFile('file')
  @UseInterceptors(FileInterceptor('file'))
  async batchTemplate(@UploadedFile() file: Express.Multer.File): Promise<any> {
    const url = await this.s3Service.upload({
      fileName: generateUniqueId() + path.extname(file.originalname),
      path: S3_IMAGE_DIR,
      file: file.buffer,
    })
    return { url }
  }
}
