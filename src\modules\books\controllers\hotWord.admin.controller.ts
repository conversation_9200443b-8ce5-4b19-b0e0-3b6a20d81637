import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { AdminAuth, ApiPageResult, getPageResponse, PageRequest } from '@/common'
import { getSearchWordDto, SearchWordDto } from '../dto'
import { HotSearchWordService } from '../services'

@ApiTags('Hot-words')
@Controller('v1/admin/hot-word')
export class HotWordAdminController {
  constructor(private readonly hotWordService: HotSearchWordService) {}

  @ApiOperation({ summary: 'list hot search words' })
  @ApiPageResult(SearchWordDto, 200)
  @Get()
  @AdminAuth()
  async listWords(@Query() query: PageRequest) {
    const data = await this.hotWordService.listWord(query)
    return getPageResponse(data, data.items, getSearchWordDto)
  }
}
