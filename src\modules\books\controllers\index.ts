export * from './author.admin.controller'
export * from './category.admin.controller'
export * from './publisher.admin.controller'
export * from './label.admin.controller'
export * from './batchBookFile.admin.controller'
export * from './book.admin.controller'
export * from './bookshelf.client.controller'
export * from './book.client.controller'
export * from './file.admin.controller'
export * from './category.client.controller'
export * from './book.school.controller'
export * from './category.school.controller'
export * from './author.school.controller'
export * from './publisher.school.controller'
export * from './label.school.controller'
export * from './label.client.controller'
export * from './recommendWord.client.controller'
export * from './report.admin.controller'
export * from './recommendWord.admin.controller'
export * from './hotWord.admin.controller'
export * from './publisher.client.controller'
export * from './author.client.controller'
export * from './bookNote.client.controller'
export * from './hotWord.client.controller'
export * from './file.controller'
export * from './book.client.controller.v2'
export * from './operateApplication.admin.controller'
export * from './bookLevel.admin.controller'
export * from './util.controller'
export * from './textToSpeech.client.controller'
