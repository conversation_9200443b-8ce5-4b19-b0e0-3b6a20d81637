import {Body, Controller, Delete, Get, Param, ParseIntPipe, Patch, Post, Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectDataSource } from '@nestjs/typeorm'
import type { DataSource } from 'typeorm'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  getPageResponse,
  PageResponse,
} from '@/common'
import { Label } from '@/entities'
import { EBookVersion } from '@/enums'
import { CreateLabelDto, FindLabelDto, getLabelDto, LabelDto, ListLabelDto } from '../dto'
import { LabelService } from '../services'
import { BookRepository } from '../services/index1'

@ApiTags('Lables')
@ApiExtraModels(LabelDto)
@Controller('v1/admin/lables')
export class LabelAdminController {
  constructor(
    private readonly labelService: LabelService,
    private readonly bookRepository: BookRepository,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  @AdminAuth()
  @ApiOperation({ summary: 'create a label' })
  @ApiBaseResult(LabelDto, 200)
  @Post()
  async createLabel(@Body() data: CreateLabelDto): Promise<LabelDto> {
    const label = await this.labelService.createLabel(data)
    return getLabelDto(label)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'update a label' })
  @ApiBaseResult(LabelDto, 200)
  @Patch(':id')
  async updateLabel(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateLabelDto
  ): Promise<LabelDto> {
    const label = await this.labelService.updateLabel(id, data)
    return getLabelDto(label)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'list all label' })
  @ApiListResult(LabelDto, 200)
  @Get('/all')
  async getAllLabel(@Query() data: FindLabelDto): Promise<LabelDto[]> {
    const labels = await this.labelService.listAllLabel(data.type, data.keyword)
    return labels.map((item) => getLabelDto(item))
  }

  @AdminAuth()
  @ApiOperation({ summary: 'get a label' })
  @ApiBaseResult(LabelDto, 200)
  @Get(':id')
  async getLabel(@Param('id', ParseIntPipe) id: number): Promise<LabelDto> {
    const label = await this.labelService.getLabel(id)
    return getLabelDto(label)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'list label' })
  @ApiPageResult(LabelDto, 200)
  @Get()
  async listLabel(@Query() query: ListLabelDto): Promise<PageResponse<LabelDto, Label>> {
    const data = await this.labelService.listLabel(query)
    const items = await Promise.all(
      data.items.map(async (label) => {
        const count = await this.bookRepository.countBooks(
          { labelId: label.id },
          { version: EBookVersion.SUBSCRIPTION }
        )
        const referenceCount = await this.bookRepository.countBooks(
          { labelId: label.id },
          { version: EBookVersion.REFERENCE }
        )
        return { ...getLabelDto(label), count, referenceCount }
      }) as unknown as LabelDto[] 
    )
    return getPageResponse({ ...data, items })
  }

  @AdminAuth()
  @ApiOperation({ summary: 'delete label' })
  @ApiPageResult(LabelDto, 200)
  @Delete(':id')
  async deleteLabel(@Param('id', ParseIntPipe) id: number) {
    await this.dataSource.manager.transaction(async (manager) => {
      await manager.query(`delete from books_labels_labels where labels_id = ${id}`)
      await this.labelService.deleteLabel(id, manager)
    })
  }
}
