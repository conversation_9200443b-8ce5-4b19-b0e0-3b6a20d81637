import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiListResult,
  ApiPageResult,
  ClientAuth,
  getPageResponse,
  PageResponse,
} from '@/common'
import { Label } from '@/entities'
import { FindLabelDto, getLabelDto, LabelDto, ListLabelDto } from '../dto'
import { LabelService } from '../services'

@ApiTags('Lables')
@ApiExtraModels(LabelDto)
@Controller('v1/client/lables')
export class LabelClientController {
  constructor(private readonly labelService: LabelService) {}

  @ClientAuth()
  @ApiOperation({ summary: 'list all label' })
  @ApiListResult(LabelDto, 200)
  @Get('/all')
  async getLabel(@Query() data: FindLabelDto): Promise<LabelDto[]> {
    const labels = await this.labelService.listAllLabel(data.type, data.keyword)
    return labels.map((item) => getLabelDto(item))
  }

  @ClientAuth()
  @ApiOperation({ summary: 'list label' })
  @ApiPageResult(LabelDto, 200)
  @Get()
  async listLabel(@Query() query: ListLabelDto): Promise<PageResponse<LabelDto, Label>> {
    const data = await this.labelService.listLabel(query)
    return getPageResponse(data, data.items, getLabelDto)
  }
}
