import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiListResult,
  ApiPageResult,
  getPageResponse,
  PageResponse,
  SchoolAdminAuth,
} from '@/common'
import { Label } from '@/entities'
import { FindLabelDto, getLabelDto, LabelDto, ListLabelDto } from '../dto'
import { LabelService } from '../services'

@ApiTags('Lables')
@ApiExtraModels(LabelDto)
@Controller('v1/school-admin/lables')
export class LabelSchoolController {
  constructor(private readonly labelService: LabelService) {}

  @SchoolAdminAuth()
  @ApiOperation({ summary: 'list all label' })
  @ApiListResult(LabelDto, 200)
  @Get('/all')
  async getLabel(@Query() data: FindLabelDto): Promise<LabelDto[]> {
    const labels = await this.labelService.listAllLabel(data.type, data.keyword)
    return labels.map((item) => getLabelDto(item))
  }

  @AdminAuth()
  @ApiOperation({ summary: 'list label' })
  @ApiPageResult(LabelDto, 200)
  @Get()
  async listLabel(@Query() query: ListLabelDto): Promise<PageResponse<LabelDto, Label>> {
    const data = await this.labelService.listLabel(query)
    return getPageResponse(data, data.items, getLabelDto)
  }
}
