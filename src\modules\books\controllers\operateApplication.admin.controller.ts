import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import moment from 'moment-timezone'
import R from 'ramda'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  CurrentAdmin,
  isDev,
  isProduction,
  isStaging,
} from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { Book, BookOperateApplication } from '@/entities'
import { ApplicationStatus, BookOperationType, OnlineOfflineStatus } from '@/enums'
import { OperationLogService } from '@/modules/system'
import {
  BookDto,
  BookOperateApplicationResponse,
  CreateBookOperateApplicationRequest,
  ListBookOperateApplicationRequest,
  PatchBookOperateApplicationRequest,
  ReadingTimeDto,
  UploadSignalBookDto,
} from '../dto'
import { InitLeaderBoardService, PublisherService } from '../services'
import { BookRepository } from '../services/book.repository'
import { BookService } from '../services/index3'

@ApiTags('Books')
@ApiExtraModels(
  BookDto,
  UploadSignalBookDto,
  ReadingTimeDto,
  BookOperateApplication,
  BookOperateApplicationResponse,
)
@Controller('v1/admin')
export class OperateApplicationAdminController {
  constructor(
    private readonly bookService: BookService,
    private readonly bookRepositories: BookRepository,
    private readonly taskService: TaskService,
    private readonly publisherService: PublisherService,
    private readonly logService: OperationLogService,
    private readonly initLeaderBoardService: InitLeaderBoardService,
  ) {}

  @ApiOperation({ summary: 'Create book operate application' })
  @AdminAuth()
  @ApiBaseResult(BookOperateApplicationResponse, 201)
  @Post('operate-applications')
  async createBookOperateApplications(
    @Body() body: CreateBookOperateApplicationRequest,
    @CurrentAdmin() admin: any,
  ): Promise<any> {
    const { bookOperationType, isFullSelected, excludedBookIds, specifiedBookIds } = body

    if (admin.publisherIds.length <= 0) throw new ForbiddenException()

    let data: Book[] = null

    // 全选模式下，根据查询条件查询所有结果，再根据excludedBookIds排除反选
    if (isFullSelected) {
      let { publisherId } = body
      if (admin.publisherIds?.length) {
        publisherId = publisherId?.length
          ? R.intersection(publisherId, admin.publisherIds)
          : admin.publisherIds
      }
      const searchedBooks = await this.bookService.searchBooksByPage({
        ...body,
        publisherId,
        pageIndex: 1,
        pageSize: 100000,
      })

      data = searchedBooks.items.filter(
        (x) => !(excludedBookIds || []).some((y) => y === x.id),
      )
    }
    // 非全选模式下，只根据指定数据查询
    else {
      data =
        (await this.bookRepositories.findBooks({
          ids: specifiedBookIds,
        })) || []
    }

    if (bookOperationType === BookOperationType.BATCH_UNPUBLISH) {
      data = data.filter((x) => x.status !== OnlineOfflineStatus.OFFLINE)
    }

    data = data.map((x) => ({
      id: x.id,
      name: x.name,
      coverUrl: x.coverUrl,
      status: x.status,
      authors: (x.authors || []).map((y) => ({
        id: y.id,
        name: y.name,
      })),
    })) as Book[]

    // 取前5本书构造邮件内容
    const books = data.slice(0, 5)

    const publisher = await this.publisherService.getPublisher({
      id: admin.publisherIds[0],
    })

    await this.taskService.deliver(
      ETaskType.OPERATE_APPLICATION,
      {
        operationName:
          bookOperationType === BookOperationType.BATCH_DELETE ? '申請刪除' : '申請下架',
        publisherName: publisher.name.zh_HK,
        description: `${books.map((book) => '《' + book.name.zh_HK + '》').join('，')}${
          data.length > 5 ? '等' + data.length + '本書籍' : ''
        }。`,
        url: isProduction()
          ? 'https://admin.sjrc.club'
          : `https://admin-panel${
              isStaging() ? '-stg' : isDev() ? '-dev' : '-uat'
            }.trusive.hk`,
      },
      { delay: 1000 },
    )

    return this.bookService.createBookOperateApplication(
      {
        publisher,
        books: data,
        type: bookOperationType,
        status: ApplicationStatus.PENDING,
        metadata: {
          snapshot: data,
        },
      },
      { operator: admin },
    )
  }

  @ApiOperation({ summary: 'List book operate application' })
  @AdminAuth()
  @ApiPageResult(BookOperateApplicationResponse, 200)
  @Get('operate-applications')
  async listBookOperateApplications(
    @Query() query: ListBookOperateApplicationRequest,
  ): Promise<any> {
    return this.bookService.listBookOperateApplication(query)
  }

  @ApiOperation({ summary: 'Get book operate application' })
  @AdminAuth()
  @ApiBaseResult(BookOperateApplicationResponse, 200)
  @Get('operate-applications/:id')
  async getBookOperateApplications(@Param('id') id: string): Promise<any> {
    return this.bookService.getBookOperateApplication(Number(id))
  }

  @ApiOperation({ summary: 'Patch book operate application' })
  @AdminAuth()
  @ApiBaseResult(BookOperateApplicationResponse, 200)
  @Patch('operate-applications/:id')
  async patchBookOperateApplications(
    @Body() body: PatchBookOperateApplicationRequest,
    @Param('id') id: string,
    @CurrentAdmin() admin: any,
  ): Promise<any> {
    const entity = await this.bookService.patchBookOperateApplication(Number(id), body, {
      operator: admin,
    })

    const { books } = entity

    const ids = books.map((x) => x.id)

    if (entity.status === ApplicationStatus.APPROVED) {
      // 批量删除
      if (entity.type === BookOperationType.BATCH_DELETE) {
        await this.bookRepositories.deleteBook(ids, admin)
      }
      if (entity.type === BookOperationType.BATCH_UNPUBLISH) {
        await this.bookRepositories.updateBooks(
          books.map((x) => x.id),
          { status: OnlineOfflineStatus.OFFLINE, offlineAt: new Date() },
        )
        const operation = books.length > 3 ? `批量下架` : '下架書籍'

        await this.logService.createLog({
          operation: `${operation}${books
            .slice(0, 3)
            .map((item) => `“${item.name.zh_HK}”`)
            .join(',')} ${books.length > 3 ? `等${books.length}本書籍` : ''}`,
          metaData: { bookIds: ids, body },
          user: admin,
        })

        let userName = `${admin.givenName ?? ''} ${admin.familyName ?? ''}`
        if (userName.length > 1) userName = admin.email
        await this.taskService.deliver(
          ETaskType.UPDATE_BOOK_STATUS,
          {
            userName,
            bookNames: books.map((item) => `《${item.name.zh_HK}》`).join(','),
            operation: '下架',
            date: moment
              .tz(new Date(), 'Asia/Hong_Kong')
              .format('YYYY年MM月DD日 HH时mm分ss秒'),
          },
          { delay: 1000 },
        )

        for (const id of ids) {
          await this.initLeaderBoardService.removeBookFromReadingRanking(id)
        }
      }
    }

    return {
      ...entity,
      books: (entity.books || []).map((y) => ({
        id: y.id,
        name: y.name,
        coverUrl: y.coverUrl,
        authors: (y.authors || []).map((z) => ({
          id: z.id,
          name: z.name,
        })),
      })),
    }
  }
}
