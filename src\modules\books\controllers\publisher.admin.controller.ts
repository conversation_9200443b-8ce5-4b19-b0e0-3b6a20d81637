import {Body, Controller, Delete, Get, Header, Param, ParseIntPipe, Patch, Post, Query, Res,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import moment from 'moment'
import R from 'ramda'
import {AdminAuth, ApiBaseResult, ApiListResult, ApiPageResult, BooleanResponse, CurrentAdmin, CurrentLocale, CurrentLocaleHeader, ELocaleType, ExcelService, getPageResponse, PageResponse,
} from '@/common'
import { EDataExportType, ETaskType, TaskService } from '@/common/components/task'
import { Publisher } from '@/entities'
import { EBookVersion, EStatus } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { LogService, OperationLogService } from '@/modules/system'
import { convertTime, regionExcelData, regionPublisherSpecification } from '@/utils'
import { BOOK_DELETED_FLAG, getISBN } from '@/utils/book.utitl'
import {CreatePublisherDto, DeletePublisherDto, ExportPublisherReadingDto, getPublisherDto, PublisherDto, PublisherReferenceStatisticDto,
  QueryPublisherDto, UpdatePublisherDto, UpdatePublisherStatusDto,
} from '../dto'
import { PublisherService, ReferenceBookService } from '../services'
import { BookRepository, ReadRecordService } from '../services/index1'
import { getAuthorNameZh } from '../utils/bookExcel.util'

@ApiTags('Publishers')
@ApiExtraModels(PublisherDto)
@Controller('v1/admin/publishers')
export class PublisherAdminController {
  constructor(
    private readonly publisherService: PublisherService,
    private readonly logService: OperationLogService,
    private readonly bookRepo: BookRepository,
    private readonly readRecordService: ReadRecordService,
    private readonly excelService: ExcelService,
    private readonly taskService: TaskService,
    private readonly referenceBookService: ReferenceBookService,
    private readonly sysLogService: LogService
  ) {}

  @AdminAuth()
  @ApiOperation({ summary: 'create a publiser' })
  @ApiBaseResult(PublisherDto, 200)
  @Post()
  async createPublisher(
    @Body() data: CreatePublisherDto,
    @CurrentAdmin() user: any
  ): Promise<PublisherDto> {
    const publisher = await this.publisherService.createPublisher(data)
    await this.logService.createLog({
      operation: `添加出版社“${publisher.name.zh_HK}”`,
      metaData: { publiserId: publisher.id },
      user,
    })
    return getPublisherDto(publisher)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'update a publiser' })
  @ApiBaseResult(PublisherDto, 200)
  @Patch(':id')
  async updatePublisher(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdatePublisherDto
  ): Promise<PublisherDto> {
    const publisher = await this.publisherService.updatePublisher(id, data)
    return getPublisherDto(publisher)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'update a publiser status' })
  @ApiListResult(PublisherDto, 200)
  @Patch()
  async updatePublisherStatus(
    @Body() data: UpdatePublisherStatusDto,
    @CurrentAdmin() user: any
  ): Promise<PublisherDto[]> {
    let publisherIds = data.ids?.length ? data.ids : []
    if (publisherIds.length <= 0) {
      if (user.publisherIds?.length) {publisherIds = user.publisherIds}
      else {publisherIds = await this.publisherService.listPublisherIds(data.name)}
      if (data.exceptions?.length)
      {publisherIds = publisherIds.filter((id) => !data.exceptions?.includes(id))}
    }

    if (publisherIds.length <= 0) {return []}

    const publishers = await this.publisherService.updatePublisherStatus(
      publisherIds,
      data.status
    )
    const res = await publishers.map((item) =>
      getPublisherDto({ ...item, status: data.status })
    )
    const operation =
      data.status === EStatus.OFFLINE
        ? publishers.length > 3
          ? `批量失效`
          : '失效出版社'
        : publishers.length > 3
          ? `批量恢復`
          : '恢復出版社'
    await this.logService.createLog({
      operation: `${operation}${publishers
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${publishers.length > 3 ? `等${publishers.length}个出版社` : ''}`,
      metaData: { publisherIds, data },
      user,
    })

    return res
  }

  @AdminAuth()
  @Get('/export-publishers')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Publishers.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export books' })
  async exportPublishers(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() user: any
  ) {
    const file = await this.publisherService.export(local, user)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/export/books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Publishers.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export publisher books' })
  async exportPublisherBooks(
    @Query() query: ExportPublisherReadingDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() user: any
  ) {
    const publisher = await this.publisherService.getPublisher({ id: query.publisherId })

    let pageIndex = 1
    const pageSize = 200
    let books = []
    let total = 0
    do {
      const data = await this.bookRepo.searchBooksByPage(
        {
          publisherId: [query.publisherId],
          pageIndex,
          pageSize,
        },
        { withDeleted: true },
        {
          relations: ['authors'],
          fields: ['isbn', 'name', 'publishedAt', 'id'],
        }
      )
      books = books.concat(data.items)
      total = data.total
      pageIndex += 1
    } while ((pageIndex - 1) * pageSize < total)

    const ids = books.map((item) => item.id)
    let excelData: Array<Record<string, any>>
    let region = []
    if (ids.length > 0) {
      const reading = await this.readRecordService.getPublisherReadingTimeCSV(query, ids)
      excelData = books
        .map((book) => {
          const timing = reading.filter((read) => read.bookId === book.id)
          const readingTime =
            timing.reduce((pre, curr) => pre + Number(curr.readingTime), 0) || 0
          const userCount = timing.reduce((pre, curr) => pre + Number(curr.users), 0) || 0

          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            name: publisher?.name?.zh_HK,
            publisherGroupName: publisher?.publisherGroupName?.zh_HK ?? '',
            isDelete: book.isbn.includes(BOOK_DELETED_FLAG) ? '已删除' : '',
            isbn: getISBN(book.isbn),
            bookName: book.name.zh_HK,
            author: book.authors?.map((item) => getAuthorNameZh(item.name)).join(','),
            publisherAt: moment.tz(book.publishedAt, 'Asia/Hong_Kong').format('YYYY'),
            userCount,
            readingTime: convertTime(readingTime),
            time: readingTime,
            ...regionExcelData(
              timing.map((item) => ({
                region: item.region,
                totalReadingTime: Number(item.readingTime),
                totalUser: Number(item.users),
              }))
            ),
          }
        })
        .sort((a, b) => b.time - a.time)

      region = [...new Set(R.flatten(reading.map((item) => item.region)))]
    } else {
      excelData = [
        {
          startTime: '',
          endTime: '',
          name: '',
          publisherGroupName: '',
          isDelete: '',
          isbn: '',
          bookName: '',
          author: '',
          publisherAt: '',
          userCount: 0,
          readingTime: '',
          time: 0,
        },
      ]
    }
    const file = await this.excelService.buildRegionExcel({
      name: `publisherReading.${local}`,
      specification: regionPublisherSpecification(region, false, local),
      data: excelData,
    })

    await this.sysLogService.save('下载订阅版出版社详情报告', user, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @ApiBaseResult(BooleanResponse, 200)
  @Get('/export/reading')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export publisher reading' })
  async exportPublisherReading(
    @Query() query: ExportPublisherReadingDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any
  ) {
    await this.taskService.deliver(
      ETaskType.DATA_EXPORT,
      {
        query,
        local,
        user,
        type: EDataExportType.EXPORT_PUBLISHER_READING_TIME,
      },
      {}
    )
    return {
      status: true,
    }
  }

  // @AdminAuth()
  // @ApiBaseResult(BooleanResponse, 200)
  // @Header(
  //   'Content-Type',
  //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // )
  // @Header('Content-Disposition', 'attachment; filename=Publishers.xlsx')
  // @Get('/export/reading')
  // @CurrentLocaleHeader()
  // @ApiOperation({ summary: 'export publisher reading' })
  // async exportPublisherReading(
  //   @Query() query: ExportPublisherReadingDto,
  //   @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
  //   @CurrentAdmin() user: any,
  //   @Res() res: Response,
  // ) {
  //   // const file = await this.dataExportTask.exportPublisherReadingTime({
  //   //   local,
  //   //   query,
  //   //   user,
  //   // })
  //   // console.log(file)
  //   // res.send(Buffer.from(file))
  // }

  @AdminAuth()
  @ApiOperation({ summary: 'list publisher' })
  @ApiPageResult(PublisherReferenceStatisticDto, 200)
  @Get(':publisherId/reference/statistic')
  async statisticInReference(@Param('publisherId', ParseIntPipe) id: number) {
    const builder = await this.bookRepo.searchBookConditionV2({
      publisherId: [id],
      version: EBookVersion.REFERENCE,
    })
    const books = await builder.getMany()
    if (books.length <= 0) {return { booksCount: 0, copiesCount: 0 }}

    return this.referenceBookService.getCount(books.map((item) => item.id))
  }

  @AdminAuth()
  @ApiOperation({ summary: 'get a publiser' })
  @ApiBaseResult(PublisherDto, 200)
  @Get(':publisherId')
  async getPubliser(
    @Param('publisherId', ParseIntPipe) id: number
  ): Promise<PublisherDto> {
    const publisher = await this.publisherService.getPublisher({ id })
    return getPublisherDto(publisher)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'list publiser' })
  @ApiPageResult(PublisherDto, 200)
  @Get()
  async listPublisher(
    @Query() query: QueryPublisherDto,
    @CurrentAdmin() admin: any
  ): Promise<PageResponse<PublisherDto, Publisher>> {
    if (admin.publisherIds?.length) {
      const { pageIndex = 1, pageSize = PAGE_SIZE } = query
      const start = (pageIndex - 1) * pageSize
      let items = []
      const total = admin.publisherIds.length
      if (start < total) {
        const ids = admin.publisherIds.slice(start, start + pageSize + 1)
        items = await this.publisherService.findPublishers(ids)
      }

      return getPageResponse({ total, pageIndex, pageSize }, items, getPublisherDto)
    }
    const data = await this.publisherService.listPublisher(query)
    return getPageResponse(data, data.items, getPublisherDto)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'delete publisers' })
  @ApiPageResult(PublisherDto, 200)
  @Delete()
  async deletePublisher(@Body() query: DeletePublisherDto, @CurrentAdmin() user: any) {
    let publisherIds = query.ids?.length ? query.ids : []
    if (publisherIds.length <= 0) {
      if (user.publisherIds?.length) {publisherIds = user.publisherIds}
      else {publisherIds = await this.publisherService.listPublisherIds(query.name)}
      if (query.exceptions?.length)
      {publisherIds = publisherIds.filter((id) => !query.exceptions?.includes(id))}
    }
    if (publisherIds.length <= 0) {return []}

    const publishers = await this.publisherService.findPublishers(publisherIds, [
      'admins',
    ])

    await this.publisherService.softDeleted(publishers)

    await this.bookRepo.deleteBooksOnPublisherDeleted(publisherIds)

    await this.logService.createLog({
      operation: `${publishers.length > 3 ? `批量删除` : '删除出版社'}${publishers
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${publishers.length > 3 ? `等${publishers.length}个出版社` : ''}`,
      metaData: { publisherIds, query },
      user,
    })
  }
}
