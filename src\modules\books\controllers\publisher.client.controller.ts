import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiPageResult,
  ClientAuth,
  getPageResponse,
  PageResponse,
} from '@/common'
import { HttpCacheInterceptor } from '@/common/interceptors'
import { Publisher } from '@/entities'
import { EStatus } from '@/enums'
import { getPublisherDto, PublisherDto, QueryPublisherDto } from '../dto'
import { PublisherService } from '../services'

@ApiTags('Publishers')
@ApiExtraModels(PublisherDto)
@Controller('v1/client/publishers')
export class PublisherClientController {
  constructor(private readonly publisherService: PublisherService) {}

  @UseInterceptors(HttpCacheInterceptor)
  @ClientAuth()
  @ApiOperation({ summary: 'get a publiser' })
  @ApiBaseResult(PublisherDto, 200)
  @Get(':publisherId')
  async getPubliser(
    @Param('publisherId', ParseIntPipe) id: number,
  ): Promise<PublisherDto> {
    const publisher = await this.publisherService.getPublisher({ id })
    return getPublisherDto(publisher)
  }

  @UseInterceptors(HttpCacheInterceptor)
  @ClientAuth()
  @ApiOperation({ summary: 'list publiser' })
  @ApiPageResult(PublisherDto, 200)
  @Get()
  async listPublisher(
    @Query() query: QueryPublisherDto,
  ): Promise<PageResponse<PublisherDto, Publisher>> {
    const data = await this.publisherService.listPublisher({
      ...query,
      status: EStatus.ONLINE,
    })
    return getPageResponse(data, data.items, getPublisherDto)
  }
}
