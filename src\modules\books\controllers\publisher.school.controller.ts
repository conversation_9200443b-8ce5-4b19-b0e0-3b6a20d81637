import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiListResult, SchoolAdminAuth } from '@/common'
import { EStatus } from '@/enums'
import { getPublisherDto, PublisherDto, QueryPublisherDto } from '../dto'
import { PublisherService } from '../services'

@ApiTags('Publishers')
@ApiExtraModels(PublisherDto)
@Controller('v1/school-admin/publishers')
export class PublisherSchoolController {
  constructor(private readonly publisherService: PublisherService) {}

  @SchoolAdminAuth()
  @ApiOperation({ summary: 'list publiser' })
  @ApiListResult(PublisherDto, 200)
  @Get()
  async searchPublisher(@Query() query: QueryPublisherDto): Promise<any> {
    const data = await this.publisherService.listPublisher({
      ...query,
      status: EStatus.ONLINE,
    })
    return { ...data, items: data.items.map((item) => getPublisherDto(item)) }
  }
}
