import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  DeleteDto,
  getPageResponse,
  PageRequest,
} from '@/common'
import { OnlineOfflineStatus } from '@/enums'
import { OperationLogService } from '@/modules/system'
import {
  CreateRecommendWord,
  getRecommendWordDto,
  RecommendWordDto,
  UpdateRecommendWord,
} from '../dto'
import { RecommendWordService } from '../services'

@ApiTags('Recommended-words')
@Controller('v1/admin/recommend-word')
export class RecommendWordAdminController {
  constructor(
    private readonly recommendWordService: RecommendWordService,
    private readonly logService: OperationLogService,
  ) {}

  @ApiOperation({ summary: 'add recommend word' })
  @ApiBaseResult(BooleanResponse, 200)
  @Post()
  @AdminAuth()
  async createWord(@Body() data: CreateRecommendWord, @CurrentAdmin() user: any) {
    const workd = await this.recommendWordService.createWord(data.keyword)
    await this.logService.createLog({
      operation: `新增搜索詞“${data.keyword}”`,
      metaData: { wordId: workd.id },
      user,
    })
    return { status: true }
  }

  @ApiOperation({ summary: 'online or offline recommend word' })
  @ApiBaseResult(BooleanResponse, 200)
  @Patch(':id')
  @AdminAuth()
  async updateWord(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateRecommendWord,
    @CurrentAdmin() user: any,
  ) {
    const word = await this.recommendWordService.modifyWord(id, data.status)
    const operation = data.status === OnlineOfflineStatus.OFFLINE ? `下架` : '上架'
    await this.logService.createLog({
      operation: `${operation}搜索詞$"${word.keyword}"`,
      metaData: { wordId: word.id },
      user,
    })
    return { status: true }
  }

  @ApiOperation({ summary: 'list hot search words' })
  @ApiPageResult(RecommendWordDto, 200)
  @Get()
  @AdminAuth()
  async listWords(@Query() query: PageRequest) {
    const data = await this.recommendWordService.listWord(query)
    return getPageResponse(data, data.items, getRecommendWordDto)
  }

  @ApiOperation({ summary: 'delete recommend word' })
  @ApiBaseResult(BooleanResponse, 200)
  @Delete()
  @AdminAuth()
  async deleteWord(@Body() data: DeleteDto, @CurrentAdmin() user: any) {
    let ids = data.ids?.length ? data.ids : []
    if (ids.length <= 0) {
      ids = await this.recommendWordService.listAllWordIds()
      if (data.exceptions?.length) {
        ids = ids.filter((id) => !data.exceptions.includes(id))
      }
    }
    const words = await this.recommendWordService.getWords(ids)
    await this.recommendWordService.deleteWord(ids)
    await this.logService.createLog({
      operation: `${words.length > 3 ? `批量删除` : '删除搜索詞'}${words
        .slice(0, 3)
        .map((item) => `“${item.keyword}”`)
        .join(',')} ${words.length > 3 ? `等${words.length}個搜索詞` : ''}`,
      metaData: { wordIds: ids },
      user,
    })
    return { status: true }
  }
}
