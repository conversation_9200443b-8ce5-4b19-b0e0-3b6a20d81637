import { Controller, Get, Query, UseInterceptors } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiListResult, ClientAuth } from '@/common'
import { HttpCacheInterceptor } from '@/common/interceptors'
import { getSearchWordDto, QuerySearchWordDto, SearchWordDto } from '../dto'
import { RecommendWordService } from '../services'

@ApiTags('Recommended-words')
@ApiExtraModels(SearchWordDto)
@Controller('v1/client/search-word')
export class RecommendWordClientController {
  constructor(private readonly searchWordService: RecommendWordService) {}

  @UseInterceptors(HttpCacheInterceptor)
  @Get()
  @ClientAuth()
  @ApiOperation({ summary: 'list recommend words' })
  @ApiListResult(SearchWordDto, 200)
  async getTopSearchWord(@Query() query: QuerySearchWordDto) {
    const schools = await this.searchWordService.listTopNWord(query.top)
    return schools.map((item) => getSearchWordDto(item))
  }
}
