import { <PERSON>, Get, Header, Query, Res } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import * as ExcelJS from 'exceljs'
import { Response } from 'express'
import R from 'ramda'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  ExcelService,
  getPageResponse, 
} from '@/common'
import { EOrderDirection } from '@/enums'
import { ISchoolService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import {
  authorExcelData,
  convertTime,
  publisherExcelData,
  regionExcelData,
  regionLeftTimeSpecification,
  regionSpecification,
  schoolExcelData,
} from '@/utils'
import { ReadRecordService } from '../../books/services/index1'
import {
  ExportTopSchoolLeftReadingTimeDto,
  QueryReportCountDto,
  QuerySomeCountDto,
  QueryStudentReadingTimeDto,
  QueryTopReadingTimeDto,
  QueryTopSchoolLeftReadingTimeDto,
  QueryUserDistributionDto,
  TopReadingTimeDto,
  UserCountDto,
} from '../dto'
import { PublisherService } from '../services'
import { ReportService } from '../services/index3'

@Controller('v1/admin/report')
export class ReportAdminController {
  constructor(
    private readonly readRecordService: ReadRecordService,
    private readonly reportService: ReportService,
    private readonly recordService: ReadRecordService,
    private readonly schoolService: ISchoolService,
    private readonly publisherService: PublisherService,
    private readonly excelService: ExcelService,
    private readonly logService: LogService
  ) {}

  @ApiOperation({ summary: 'count user, book, publisher,school' })
  @ApiBaseResult(UserCountDto, 200)
  @AdminAuth()
  @Get('some-count')
  async someCount(@Query() query: QuerySomeCountDto) {
    return this.reportService.count(query)
  }

  @ApiOperation({ summary: 'count admin school user by date' })
  @ApiBaseResult(UserCountDto, 200)
  @AdminAuth()
  @Get('school-user-count')
  async schoolUserCount(@Query() query: QueryUserDistributionDto) {
    return this.reportService.schoolUserCount(query.startTime, query.endTime, {
      version: query.version,
      hasScienceRoom: query.hasScienceRoom,
    })
  }

  @ApiOperation({ summary: 'count admin user by date' })
  @ApiBaseResult(UserCountDto, 200)
  @AdminAuth()
  @Get('admin-user-count')
  async adminUserCount(@Query() query: QueryReportCountDto) {
    return this.reportService.adminCount(query.startTime, query.endTime)
  }

  @ApiOperation({ summary: 'top school reading time' })
  @ApiPageResult(TopReadingTimeDto, 200)
  @AdminAuth()
  @Get('top-school-reading-time')
  async topSchoolReadingTime(@Query() query: QueryTopReadingTimeDto) {
    const data = await this.recordService.top50School(query)
    const schools = await this.schoolService.findSchools(
      data.items.map((item) => item.schoolId)
    )
    const items = data.items.map((item) => ({
      ...item,
      name: schools.find((s) => s.id === item.schoolId)?.name,
    }))
    return getPageResponse({ ...data, items })
  }

  @ApiOperation({ summary: 'top publisher reading time' })
  @ApiPageResult(TopReadingTimeDto, 200)
  @AdminAuth()
  @Get('top-publisher-reading-time')
  async topPublisherReadingTime(@Query() query: QueryTopReadingTimeDto) {
    const data = await this.recordService.top50Publisher(query)
    const publishers = await this.publisherService.findPublishers(
      data.items.map((item) => item.publisherId)
    )
    const items = data.items.map((item) => ({
      ...item,
      name: publishers.find((s) => s.id === item.publisherId)?.name,
    }))
    return getPageResponse({ ...data, items })
  }

  @ApiOperation({ summary: 'top 50 author reading time' })
  @ApiPageResult(TopReadingTimeDto, 200)
  @AdminAuth()
  @Get('top-50-author-reading-time')
  async top50AuthorReadingTime(@Query() query: QueryTopReadingTimeDto) {
    return this.recordService.top50Author(query)
  }

  @ApiOperation({ summary: 'top school left reading time' })
  @ApiPageResult(TopReadingTimeDto, 200)
  @AdminAuth()
  @Get('top-left-school-reading-time')
  async topSchoolLeftReadingTime(@Query() query: QueryTopSchoolLeftReadingTimeDto) {
    const data = await this.recordService.top50SchoolLeft(query)
    const schools = await this.schoolService.findSchools(
      data.items.map((item) => item.schoolId)
    )
    const items = data.items.map((item) => ({
      ...item,
      name: schools.find((s) => s.id === item.schoolId)?.name,
    }))
    return getPageResponse({ ...data, items })
  }

  @AdminAuth()
  @Get('export/school-with-most-reading-time')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=school with most reading time.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export school with most reading time' })
  async exportBooks(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryReportCountDto,
    @Res() res: Response,
    @CurrentAdmin() admin: any
  ) {
    const pageSize = 9999999
    let pageIndex = 1
    let total = 0
    let readingData = []
    let regions = []
    do {
      const data = await this.recordService.top50School({ ...query, pageIndex, pageSize })
      const schools = await this.schoolService.findSchools(
        data.items.map((item) => item.schoolId)
      )
      regions = [...new Set([...regions, ...schools.map((item) => item.region ?? 'HK')])]
      const items = data.items.map((item) => {
        const school = schools.find((s) => s.id === item.schoolId)
        const region = school.region ?? 'HK'
        return {
          ...schoolExcelData(school, local),
          [region]: convertTime(item.totalReadingTime ?? 0),
          readingTime: convertTime(item.totalReadingTime ?? 0),
        }
      })
      pageIndex += 1
      total = data.total
      readingData = readingData.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    const file = await this.excelService.buildRegionExcel({
      name: `schoolReadingTime.${local}`,
      specification: regionSpecification(regions, local),
      data: readingData,
    })

    await this.logService.save('閱讀時數最長學校', admin, query)
    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'export publishers with least readers' })
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=export publishers with most reading time.xlsx'
  )
  @CurrentLocaleHeader()
  @AdminAuth()
  @Get('export/publisher-with-least-reading-time')
  async exportTopPublisherReadingTime(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryReportCountDto,
    @Res() res: Response,
    @CurrentAdmin() admin: any
  ) {
    const pageSize = 9999999
    let region = []
    let pageIndex = 1
    let total = 0
    let readingData = []
    do {
      const data = await this.recordService.top50Publisher({
        ...query,
        pageIndex,
        pageSize,
      })

      const publishers = await this.publisherService.findPublishers(
        data.items.map((item) => item.publisherId)
      )
      const regionReading = await this.recordService.regionReading(query, {
        publisherIds: data.items.map((item) => item.publisherId),
      })
      region = [
        ...new Set([
          ...R.flatten(regionReading.map((item) => item.data.map((r) => r.region))),
          ...region,
        ]),
      ]

      const items = data.items
        .map((item) => {
          const publisher = publishers.find((s) => s.id === item.publisherId)
          if (!publisher) {return undefined}

          return {
            ...publisherExcelData(publisher, local),
            ...regionExcelData(
              regionReading.find((reading) => reading.id === item.publisherId)?.data
            ),
            readingTime: convertTime(item.totalReadingTime ?? 0, local),
          }
        })
        .filter((item) => !!item)
      pageIndex += 1
      total = data.total
      readingData = readingData.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    const file = this.excelService.buildRegionExcel({
      name: `publisherReadingTime.${local}`,
      specification: regionSpecification(region, local),
      data: readingData,
    })

    await this.logService.save('被閱讀時間最長的出版社', admin, query)
    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'export top50 authors' })
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top 50 authors.xlsx')
  @CurrentLocaleHeader()
  @AdminAuth()
  @Get('export/top-50-author')
  async exportTop50AuthurReadingTime(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryReportCountDto,
    @Res() res: Response,
    @CurrentAdmin() admin: any
  ) {
    const pageSize = 10000
    const pageIndex = 1
    let region = []

    const data = await this.recordService.top50Author({
      ...query,
      pageIndex,
      pageSize,
    })

    const regionReading = await this.recordService.regionAuthor(
      query,
      data.items.map((item) => item.id).filter((item) => !!item)
    )
    region = [
      ...new Set([
        ...R.flatten(regionReading.map((item) => item.data.map((r) => r.region))),
        ...region,
      ]),
    ]
    const items = data.items.map((item) => {
      return {
        ...authorExcelData(item, local),
        ...regionExcelData(
          regionReading.find((reading) => reading.authorId === item.id)?.data
        ),
        readingTime: convertTime(item.totalReadingTime ?? 0, local),
      }
    })

    const file = this.excelService.buildRegionExcel({
      name: `top50Authors.${local}`,
      specification: regionSpecification(region, local),
      data: items,
    })

    await this.logService.save('被閱讀時間最長的作者', admin, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/school-with-left-reading-time')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=schools with reading time.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'school with left reading time' })
  async exportSchoolLeftTime(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportTopSchoolLeftReadingTimeDto,
    @Res() res: Response,
    @CurrentAdmin() admin: any
  ) {
    const pageSize = 9999999
    let pageIndex = 1
    let total = 0
    let readingData = []
    let regions = []
    do {
      const data = await this.recordService.top50SchoolLeft({
        ...query,
        pageIndex,
        pageSize,
      })
      const schools = await this.schoolService.findSchools(
        data.items.map((item) => item.schoolId)
      )
      regions = [...new Set([...regions, ...schools.map((item) => item.region ?? 'HK')])]
      const items = data.items.map((item) => {
        const school = schools.find((s) => s.id === item.schoolId)
        const region = school.region ?? 'HK'
        return {
          ...schoolExcelData(school, local),
          [region]: convertTime(item.totalReadingTime ?? 0, local),
          leftReadingTime: convertTime(item.totalReadingTime ?? 0, local),
        }
      })

      pageIndex += 1
      total = data.total
      readingData = readingData.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    const name =
      query.sortDirection === EOrderDirection.DESC
        ? local === ELocaleType.EN_UK
          ? 'Schools with most time'
          : '剩餘閱讀時間最多的學校'
        : local === ELocaleType.EN_UK
          ? 'Schools with least time'
          : '剩餘閱讀時間最少的學校'
    const file = await this.excelService.buildRegionExcel(
      {
        name: `schoolLeftTime.${local}`,
        data: readingData,
        specification: regionLeftTimeSpecification(regions),
      },
      name
    )

    await this.logService.save('剩餘閱讀時間最多的學校', admin, query)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export/top-100-students')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment filename=user-top100.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '导出前100学生 阅读记录及书籍信息' })
  async exportStudentTop100(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryStudentReadingTimeDto,
    @Res() res: Response
  ) {
    const rows = await this.readRecordService.getTop100ReadingStudents(query)
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Top 100 Students Reading Data')

    // 添加表头
    worksheet.columns = [
      { header: '學校名稱', key: 'school_name', width: 15 },
      { header: '開始日期', key: 'start_date', width: 15 },
      { header: '結束日期', key: 'end_date', width: 15 },
      { header: '學生姓名', key: 'student_name', width: 20 },
      { header: '學號', key: 'student_id', width: 15 },
      { header: '電郵', key: 'email', width: 30 },
      { header: '當前所屬年級/班別', key: 'current_grade_class', width: 20 },
      { header: '該學生總閱讀時長（小時)', key: 'total_reading_hours', width: 20 },
    ]
    // 填充数据
    rows.forEach((row) => {
      worksheet.addRow({
        school_name: row.school_name ? row.school_name.zh_HK : '',
        start_date: new Date(query.startTime * 1000).toISOString().slice(0, 10),
        end_date: new Date(query.endTime * 1000).toISOString().slice(0, 10),
        student_name: row.student_name,
        student_id: row.student_id,
        email: row.email,
        current_grade_class: `${row.current_grade}/${row.current_class}`,
        total_reading_hours: row.total_reading_hours,
      })
    })
    // 导出 Excel 文件为 Buffer 并返回
    const buffer = await workbook.xlsx.writeBuffer()
    res.send(buffer)
  }
}
