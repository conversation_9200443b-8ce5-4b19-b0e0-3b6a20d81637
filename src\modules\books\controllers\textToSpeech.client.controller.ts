import {
  Body,
  Controller,
  Header,
  HttpException,
  HttpStatus,
  Post,
  Res,
} from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import * as textToSpeech from '@google-cloud/text-to-speech'
import { ClientAuth, CurrentPlatform, EPlatform } from '@/common'
import { TextToSpeechDto } from '../dto/textToSeepch'

@ApiTags('Books')
@Controller('v1/client/text-to-speech')
export class TextToSpeechController {
  private client: textToSpeech.TextToSpeechClient

  constructor() {
    const googleCloudCredentials = JSON.parse(
      process.env.GOOGLE_CLOUD_ACCOUNT_TEXT_TO_SPEECH || '{}',
    )
    this.client = new textToSpeech.TextToSpeechClient({
      credentials: {
        client_email: googleCloudCredentials.client_email,
        private_key: googleCloudCredentials.private_key, // 处理换行符
      },
    })
  }

  @ApiOperation({ summary: '文字转语音' })
  @Header('Content-Type', 'audio/mpeg')
  @Header('Content-Disposition', 'inline; filename="output.mp3"')
  @Post()
  @ClientAuth()
  async synthesizeSpeech(@Body() data: TextToSpeechDto, @Res() res: Response) {
    const { text, languageCode, ssmlGender } = data
    const maxTextLength = 3000

    try {
      let audioContent = Buffer.alloc(0)

      if (Buffer.byteLength(text, 'utf8') > maxTextLength) {
        console.log('Text is too long, splitting into chunks')
        const chunks = this.splitTextIntoChunks(text, maxTextLength)
        const audioBuffers = await Promise.all(
          chunks.map(async (chunk) => {
            const request: textToSpeech.protos.google.cloud.texttospeech.v1.ISynthesizeSpeechRequest =
              {
                input: { text: chunk },
                voice: {
                  languageCode: languageCode,
                  name: this.getVoiceName(languageCode, ssmlGender),
                },
                audioConfig: { audioEncoding: 'MP3' },
              }

            const [response] = await this.client.synthesizeSpeech(request)
            const audioBuffer = this.convertAudioContentToBuffer(response.audioContent)
            return audioBuffer
          }),
        )
        audioContent = Buffer.concat(audioBuffers)
        res.send(audioContent)
      } else {
        const request: textToSpeech.protos.google.cloud.texttospeech.v1.ISynthesizeSpeechRequest =
          {
            input: { text },
            voice: {
              languageCode: languageCode,
              name: this.getVoiceName(languageCode, ssmlGender),
            },
            audioConfig: { audioEncoding: 'MP3' },
          }

        const [response] = await this.client.synthesizeSpeech(request)
        res.send(response.audioContent)
      }
    } catch (error) {
      console.error('Error during TTS generation:', error)
      throw new HttpException('TTS generation failed', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  getVoiceName(languageCode: string, ssmlGender: string): string {
    if (languageCode === 'cmn-CN') {
      return ssmlGender === 'FEMALE' ? 'cmn-CN-Standard-A' : 'cmn-CN-Standard-B'
    } else if (languageCode === 'yue-HK') {
      return ssmlGender === 'FEMALE' ? 'yue-HK-Standard-A' : 'yue-HK-Standard-D'
    } else if (languageCode === 'en-GB') {
      return ssmlGender === 'FEMALE' ? 'en-GB-Standard-F' : 'en-GB-Standard-B'
    }
    return ''
  }

  // 分割文本为多个块（按字节大小分割）
  splitTextIntoChunks(text: string, maxLength: number): string[] {
    const chunks: string[] = []
    let currentChunk = ''

    for (let i = 0; i < text.length; i++) {
      currentChunk += text[i]

      if (Buffer.byteLength(currentChunk, 'utf8') > maxLength) {
        chunks.push(currentChunk.slice(0, -1))
        currentChunk = text[i]
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk)
    }

    return chunks
  }
  // 将 Base64 编码的音频内容转换为 Uint8Array
  convertAudioContentToBuffer(audioContent: string | Uint8Array): Uint8Array {
    if (typeof audioContent === 'string') {
      return Buffer.from(audioContent, 'base64')
    } else {
      return audioContent
    }
  }
}
