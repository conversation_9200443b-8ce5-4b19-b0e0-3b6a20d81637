import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { ApiPageResult } from '@/common'
import { countries } from '@/enums'
import { SearchCountriesDto } from '@/modules/books/dto'
import { PAGE_SIZE } from '@/modules/constants'
import { MultiLanguage } from '@/interfaces'

@Controller('v1/countries')
export class UtilController {
  @ApiOperation({ summary: 'search countries' })
  @ApiPageResult(MultiLanguage, 200)
  @Get()
  searchCountries(@Query() query: SearchCountriesDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE, keyword } = query

    const data = keyword?.trim()?.length
      ? Object.values(countries).filter(
          (item) =>
            item.en_uk.includes(keyword.trim()) ||
            item.zh_HK.includes(keyword.trim()) ||
            item.zh_cn.includes(keyword.trim()),
        )
      : Object.values(countries)

    const start = (pageIndex - 1) * pageSize
    const end = start + pageSize > data.length ? undefined : start + pageSize

    return {
      pageIndex,
      pageSize,
      total: data.length,
      items: data.slice(start, end).map((item) => ({
        name: item,
        code: Object.keys(countries).find((key) =>
          this.compareCountry(countries[key], item),
        ),
      })),
    }
  }

  private compareCountry(l, r) {
    return l.en_uk === r.en_uk && l.zh_HK === r.zh_HK && l.zh_cn === r.zh_cn
  }
}
