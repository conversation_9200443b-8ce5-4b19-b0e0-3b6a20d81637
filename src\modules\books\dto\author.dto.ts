import { ApiProperty, ApiPropertyOptional, OmitType, PickType } from '@nestjs/swagger'
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common'
import { Author } from '@/entities'

export class CreateAuthorDto extends PickType(Author, [
  'description',
  'name',
  'profileImage',
]) {}

export class AuthorDto extends OmitType(Author, ['deletedAt']) {
  constructor(data: Author) {
    super()
    Object.assign(
      this,
      R.pick(['description', 'name', 'profileImage', 'authorId', 'id'], data),
    )
  }
}

export class QueryAuthorDto extends PageRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string

  withDeleted?: boolean
}

export class SearchAuthorDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string
}

export class DeleteAuthorDto {
  @ApiPropertyOptional()
  @IsArray()
  @IsNumber(undefined, { each: true })
  @IsOptional()
  ids?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsArray()
  @IsNumber(undefined, { each: true })
  @IsOptional()
  exceptions?: number[]
}

export const getAuthorDto = (data: Author) => new AuthorDto(data)

export class AuthorReferenceStatisticDto {
  @ApiProperty()
  booksCount: number

  @ApiProperty()
  copiesCount: number

  @ApiProperty({ description: '阅读人数' })
  usersCount: number

  @ApiProperty({ description: '阅读次数' })
  timesCount: number
}
