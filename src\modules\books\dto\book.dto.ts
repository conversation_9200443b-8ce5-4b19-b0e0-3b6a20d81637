import {
  ApiProperty,
  ApiPropertyOptional,
  IntersectionType,
  OmitType,
  PartialType,
  PickType,
} from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common'
import { Book, BookOperateApplication } from '@/entities'
import {
  ApplicationStatus,
  BookOperationType,
  BookOrderType,
  BookUserType,
  EBookLanguage,
  EBookVersion,
  OnlineOfflineStatus,
} from '@/enums'
import { getISBN } from '@/utils/book.utitl'
import { MultiLanguage } from '@/interfaces'
import { AuthorDto, getAuthorDto } from './author.dto'
import { CategoryDto, getCategoryDto } from './category.dto'
import { ChapterDto, getChapterDto } from './chapter.dto'
import { getLabelDto, LabelDto } from './label.dto'
import { getPublisherDto, PublisherDto } from './publisher.dto'
import { QueryReadingTimeDto } from './readRecord.dto'

type BookField = keyof Book
export const createdBookFields: BookField[] = [
  'name',
  'description',
  'size',
  'isbn',
  'url',
  'bookId',
  'status',
  'level',
  'coverUrl',
  'publisherAddress',
  'publisherGroupName',
  'publishedAt',
  'language',
  'hyperlink',
  'version',
  'price',
  'currency',
  'isScienceRoom',
]

export const updatedBookFiled = createdBookFields.concat([
  'authors',
  'publisher',
  'labels',
  'categories',
  'labelIds',
  'authorIds',
  'locations',
  'bookLevels',
  'categoryIds',
  'hyperlink',
  'pieces',
])

const bookField: (keyof Book)[] = createdBookFields.concat([
  'id',
  'bookId',
  'status',
  'coverUrl',
  'opfUrl',
  'locations',
  'pieces',
  // 'publisherAddress',
  // 'publisherGroupName',
])

export class BookDto extends PickType(Book, [
  'id',
  'bookId',
  'status',
  'name',
  'description',
  'size',
  'isbn',
  'url',
  'publisherGroupName',
  'publishedAt',
  'publisherAddress',
  'coverUrl',
  'level',
  'bookLevels',
  'pieces',
  'version',
  'price',
  'currency',
  'isShown',
  // 'onlineAt',
]) {
  @ApiPropertyOptional({ type: [String] })
  authorNames?: string[]

  @ApiPropertyOptional()
  readingTime?: number

  @ApiPropertyOptional({ type: [CategoryDto] })
  category?: CategoryDto[]

  @ApiPropertyOptional({ type: [AuthorDto] })
  authors?: AuthorDto[]

  @ApiPropertyOptional({ type: [LabelDto] })
  labels: LabelDto[]

  @ApiPropertyOptional({ type: PublisherDto })
  publisher: PublisherDto

  @ApiPropertyOptional({ type: [ChapterDto] })
  chapters?: ChapterDto[]

  @ApiPropertyOptional()
  isHidden?: boolean

  @ApiPropertyOptional()
  isOnShelf?: boolean

  @ApiPropertyOptional()
  pos?: Record<string, any>

  @ApiPropertyOptional({ enum: BookUserType, isArray: true })
  userType?: BookUserType[]

  @ApiPropertyOptional()
  copiesCount?: number

  constructor(data: Book, schoolId?: number) {
    super()
    Object.assign(this, R.pick(bookField, data))
    this.isbn = getISBN(data.isbn)
    this.level = data.level
    // if (data.url?.includes('epub')) this.url = undefined
    this.isOnShelf = data.isOnShelf
    this.isHidden = data.isHidden
    this.isShown = data.isShown
    this.pos = data.pos
    if (data.categories) {
      this.category = data.categories.map((item) => getCategoryDto(item))
    }

    if (data.authors) {
      this.authors = data.authors.map((item) => getAuthorDto(item))
    }

    if (data.publisher) {
      this.publisher = getPublisherDto(data.publisher)
    }

    if (data.labels) {
      this.labels = data.labels.map((item) => getLabelDto(item))
    }

    if (data.chapters) {
      this.chapters = data.chapters.map((item) => getChapterDto(item))
    }

    if (schoolId) {
      this.isHidden = data.hiddeSchoolIds?.includes(schoolId) ?? false
    }

    if (data.bookLevels?.length) this.bookLevels = data.bookLevels

    this.userType = data.userType
    this.copiesCount = data.copiesCount || data.referenceBooks?.[0]?.copiesCount
    this.version = data.version?.split(',')
  }
}

export const getBookDto = (book: Book, schoolId?: number) => new BookDto(book, schoolId)

export class CreateBookDto extends PickType(Book, createdBookFields) {
  @ApiProperty()
  @IsArray()
  // @ArrayMaxSize(3)
  @ArrayMinSize(1)
  @IsOptional()
  categoryId?: number[]

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsNumber(undefined, { each: true })
  authorId: number[]

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsNumber(undefined, { each: true })
  labelIds: number[]

  @ApiProperty()
  @IsNumber()
  publisherId: number

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  authorDescription?: string

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isDuplicated?: boolean

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  iD?: number
}

export class BatchCreateBook {
  @ApiProperty({ type: [CreateBookDto] })
  @ValidateNested({ each: true })
  @IsArray()
  books: CreateBookDto[]
}

export class BookZipDto extends PickType(Book, ['isbn', 'url', 'size']) {
  @ApiPropertyOptional()
  name?: string
}

export class UpdateBookDto extends OmitType(CreateBookDto, ['bookId']) {}

export class BatchUpdateBookItem extends PickType(Book, [
  'id',
  'coverUrl',
  'hyperlink',
  'isScienceRoom',
]) {}

export class BatchUpdateBookDto {
  @ApiProperty({ type: [BatchUpdateBookItem] })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested()
  books: BatchUpdateBookItem[]
}

export class UpdateBookStatusDto {
  @ApiPropertyOptional({ type: [Number] })
  @IsArray()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsOptional()
  ids?: number[]

  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  educationLabelId?: number[]

  @ApiProperty({ enum: OnlineOfflineStatus })
  @IsEnum(OnlineOfflineStatus)
  status: OnlineOfflineStatus

  onlineAt?: Date
  offlineAt?: Date
}

export class BatchUpdateBookVersionDto extends PickType(UpdateBookStatusDto, [
  'ids',
  'exceptions',
  'keyword',
  'authorId',
  'publisherId',
  'categoryIds',
  'parentCategoryId',
  'categoryLabelId',
  'educationLabelId',
]) {
  @ApiProperty({ enum: EBookVersion, isArray: true })
  @IsArray()
  @IsEnum(EBookVersion, { each: true })
  version: EBookVersion[]
}

export class QueryBookDto extends IntersectionType(
  PartialType(PickType(Book, ['isbn'])),
  PageRequest,
) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  bookName?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber(undefined, { each: true })
  authorId?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  publisherId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  categoryId?: number
}

export class QuerySimpleBookDto {
  @ApiPropertyOptional({ type: [String] })
  @IsString({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  isbns: string[]
}

export class QuerySchoolBookDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  educationLabelId?: number[]

  // @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  labelId?: number[]

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isRealTimeSearch?: boolean

  @ApiPropertyOptional({ enum: EBookLanguage })
  @IsEnum(EBookLanguage)
  @IsOptional()
  language?: EBookLanguage

  @ApiPropertyOptional({ enum: OnlineOfflineStatus })
  @IsEnum(OnlineOfflineStatus)
  @IsOptional()
  status?: OnlineOfflineStatus

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value?.split(',')?.map((item) => Number(item)))
  level?: number[]

  @ApiPropertyOptional()
  @IsEnum(BookUserType)
  @IsOptional()
  userType?: BookUserType

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isHidden?: boolean

  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional({ description: '是否为科学活动室书籍', example: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  isScienceRoom?: boolean
}

export class ExportAdminBookDto extends IntersectionType(
  QuerySchoolBookDto,
  QueryReadingTimeDto,
) {}

export class SearchClientBookDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryLabelId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  educationLabelId?: number

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isRealTimeSearch?: boolean

  @ApiPropertyOptional({ enum: EBookLanguage })
  @IsEnum(EBookLanguage)
  @IsOptional()
  language?: EBookLanguage

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  level?: number[]

  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class SearchWebClientBookDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryId?: number[]

  // @ApiPropertyOptional()
  // @IsNumber()
  // @IsOptional()
  // @Type(() => Number)
  // parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  educationLabelId?: number[]

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isRealTimeSearch?: boolean

  @ApiPropertyOptional({ enum: EBookLanguage })
  @IsEnum(EBookLanguage)
  @IsOptional()
  language?: EBookLanguage

  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class DeleteSearchBookDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  educationLabelId?: number[]

  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsOptional()
  exceptions?: number[]
}

export class BatchBookFilesDto {
  @ApiPropertyOptional()
  @IsString()
  isbn?: string

  @ApiPropertyOptional()
  @IsString()
  name?: string
}

export class UploadSignalBookDto extends PickType(Book, ['bookId', 'url', 'size']) {}

export class ClientSearchBookDto extends PageRequest {
  @ApiPropertyOptional({ description: '一级分类id' })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  labelId?: number

  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsEnum(BookOrderType)
  // orderBy?: BookOrderType // todo
}

export class AddReadingTimeDto {
  // bookId: number
  charpterId: number
  pos: string
}

export class AesKeyDto {
  @ApiProperty()
  key: string

  @ApiProperty({ description: 'hex encoding' })
  iv: string
}

export class HideBookDtoV2 {
  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  bookIds?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  educationLabelId?: number[]

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value === 'true'
    else return value
  })
  isRealTimeSearch?: boolean

  @ApiPropertyOptional({ enum: EBookLanguage })
  @IsEnum(EBookLanguage)
  @IsOptional()
  language?: EBookLanguage

  @ApiPropertyOptional({ enum: OnlineOfflineStatus })
  @IsEnum(OnlineOfflineStatus)
  @IsOptional()
  status?: OnlineOfflineStatus

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value.split(',').map((item) => Number(item))
    else return value
  })
  level?: number[]

  @ApiPropertyOptional()
  @IsEnum(BookUserType)
  @IsOptional()
  userType?: BookUserType

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') return value === 'true'
    else return value
  })
  isHidden?: boolean

  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]
}

export class HideBookDto {
  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  bookIds?: number[]

  @ApiPropertyOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsOptional()
  @IsArray()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  // @Type(() => Number)
  educationLabelId?: number[]
}

export class QueryClientBookDto extends PageRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  authorId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  publisherId?: number

  @ApiProperty({ enum: BookOrderType })
  @IsEnum(BookOrderType)
  order: BookOrderType
}

export class CreateBookOperateApplicationRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  authorId?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  @IsOptional()
  publisherId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  parentCategoryId?: number

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  categoryLabelId?: number[]

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  @Transform(({ value }) => value.split(',').map((item) => Number(item)))
  educationLabelId?: number[]

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isRealTimeSearch?: boolean

  @ApiPropertyOptional({ enum: EBookLanguage })
  @IsEnum(EBookLanguage)
  @IsOptional()
  language?: EBookLanguage

  @ApiPropertyOptional({ enum: OnlineOfflineStatus })
  @IsEnum(OnlineOfflineStatus)
  @IsOptional()
  status?: OnlineOfflineStatus

  @ApiPropertyOptional({
    description: '是否全选',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isFullSelected?: boolean

  @ApiPropertyOptional({
    description: '全选情况下，被排除的books数组, isFullSelected = true时有效 ',
    type: () => [Number],
    example: [1, 2],
  })
  @IsOptional()
  @IsNumber({ allowNaN: false }, { each: true })
  excludedBookIds?: number[]

  @ApiPropertyOptional({
    description: '非全选状态下，所勾选的books数组, isFullSelected = false时有效',
    type: () => [Number],
    example: [1, 2],
  })
  @IsOptional()
  @IsNumber({ allowNaN: false }, { each: true })
  specifiedBookIds?: number[]

  @ApiProperty({
    description: '操作类型',
    example: BookOperationType.BATCH_DELETE,
    enum: BookOperationType,
  })
  @IsEnum(BookOperationType)
  bookOperationType: BookOperationType
}

export class PatchBookOperateApplicationRequest {
  @ApiPropertyOptional({
    description: '处理状态',
    example: ApplicationStatus.PENDING,
    enum: ApplicationStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus
}

export class ListBookOperateApplicationRequest {
  @ApiPropertyOptional({
    description: '当前分页',
    example: 1,
    default: 1,
    minimum: 1,
  })
  // @Transform(({ value }) => parseInt(value))
  pageIndex?: number

  @ApiPropertyOptional({
    description: '每页记录数',
    example: 5,
    default: 10,
    maximum: 200,
  })
  // @Transform(({ value }) => parseInt(value))
  pageSize?: number
}

export class BookOperateApplicationPublisherResponse {
  @ApiProperty()
  id: number

  @ApiProperty({
    description: '出版社Id',
  })
  @IsString()
  publisherId: string

  @ApiPropertyOptional({
    description: '出版社名称',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: () => MultiLanguage,
  })
  @Type(() => MultiLanguage)
  name?: MultiLanguage
}

export class BookOperateApplicationAuthorResponse {
  @ApiProperty({
    description: '作者authorId',
    example: 'au_B2D3C435B2F048559CAA2A20E42D57F4',
  })
  @IsString()
  authorId: string

  @ApiPropertyOptional({
    description: '作者名',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: () => MultiLanguage,
  })
  @IsOptional()
  @Type(() => MultiLanguage)
  name?: MultiLanguage
}

export class BookOperateApplicationBookResponse {
  @ApiProperty({
    description: '书籍bookId',
    example: 'b_B2D3C435B2F048559CAA2A20E42D57F4',
  })
  @IsString()
  bookId: string

  @ApiPropertyOptional({
    description: '书籍名称',
    example: {
      zh_HK: '金庸',
      en_uk: '金庸',
    },
    type: () => MultiLanguage,
  })
  @IsOptional()
  @Type(() => MultiLanguage)
  @ValidateNested()
  name?: MultiLanguage

  @ApiPropertyOptional({
    description: '书籍封面',
  })
  @IsOptional()
  @IsString()
  coverUrl?: string

  @ApiPropertyOptional({
    description: '作者',
    type: () => [BookOperateApplicationAuthorResponse],
  })
  @IsOptional()
  @Type(() => BookOperateApplicationAuthorResponse)
  authors: BookOperateApplicationAuthorResponse[]
}
export class BookOperateApplicationResponse extends OmitType(BookOperateApplication, [
  'publisher',
  'books',
]) {
  @ApiPropertyOptional({
    description: '出版社',
    type: () => BookOperateApplicationPublisherResponse,
  })
  @IsOptional()
  @Type(() => BookOperateApplicationPublisherResponse)
  publisher: BookOperateApplicationPublisherResponse

  @ApiPropertyOptional({
    description: '操作书籍',
    type: () => [BookOperateApplicationBookResponse],
  })
  @IsOptional()
  @Type(() => BookOperateApplicationBookResponse)
  books: BookOperateApplicationBookResponse[]
}

export class QueryBookVersionDto {
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class BookIDDto {
  @ApiProperty()
  @IsNumber()
  bookId: number
}

export class ReferenceHotBookDto extends BookDto {
  @ApiProperty()
  viewCount: number
}

export class LimitDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number
}

export class ReferenceTopBookDto extends BookDto {
  @ApiProperty({ description: '阅读人次' })
  total: number
}

export class ContractBookBaseDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  isbn?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  authorNames?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  copiesCount?: number
}

export class DecodeContractBooksFileError {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  rowNumber?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  message?: string
}

export class DecodeContractBooksFileResponse {
  @ApiPropertyOptional({ type: () => [ContractBookBaseDto] })
  @IsOptional()
  @Type(() => ContractBookBaseDto)
  books?: ContractBookBaseDto[]

  @ApiPropertyOptional({ type: () => [DecodeContractBooksFileError] })
  @IsOptional()
  @Type(() => DecodeContractBooksFileError)
  errors?: DecodeContractBooksFileError[]
}

export class QueryAdminReferenceBooksDto extends PageRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string
}
