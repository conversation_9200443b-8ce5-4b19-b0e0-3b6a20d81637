import { PickType } from '@nestjs/swagger'
import { BookLevel } from '@/entities/common/bookLevel.entity'

export class CreateBookLevelDto extends PickType(BookLevel, ['name']) {}

export class BookLevelDto extends PickType(BookLevel, ['name', 'isFixed', 'id']) {
  constructor(data: BookLevel) {
    super()
    this.id = data.id
    this.name = data.name
    this.isFixed = data.isFixed
  }
}

export const getBookLevelDto = (data: BookLevel) => new BookLevelDto(data)
