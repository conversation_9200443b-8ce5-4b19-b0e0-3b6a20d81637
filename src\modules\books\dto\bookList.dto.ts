import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common'
import { BookList } from '@/entities'
import { BookDto, getBookDto } from './book.dto'

type BookListField = keyof BookList

export const modifyBookListField: BookListField[] = [
  'appImage',
  'webImage',
  'status',
  'name',
  'url',
  'description',
]

const bookListField = modifyBookListField.concat(['id', 'offlineAt', 'onlineAt'])

export class QueryBookListDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isBindHomepage?: boolean
}

export class ModifyBookListDto extends PickType(BookList, modifyBookListField) {
  @ApiProperty()
  @IsArray()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @ArrayMinSize(1)
  bookIds: number[]
}

export class UpdateBookListStatusDto extends PickType(BookList, ['status']) {
  @ApiProperty()
  @IsArray()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  ids: number[]
}

export class BookListDto extends PickType(BookList, bookListField) {
  @ApiPropertyOptional({ type: [BookDto] })
  book?: BookDto[]

  @ApiPropertyOptional()
  isBindHomepage: boolean

  @ApiPropertyOptional()
  total?: number

  @ApiPropertyOptional()
  pageIndex?: number

  @ApiPropertyOptional()
  pageSize?: number

  constructor(data: BookList) {
    super()
    Object.assign(this, R.pick(bookListField, data))
    if (data.books) {
      this.book = data.books.map((item) => getBookDto(item))
    }

    this.isBindHomepage = !R.isNil(data.homepage)

    this.total = data.total
    this.pageIndex = data.pageIndex
    this.pageSize = data.pageSize
  }
}

export class ClientQueryBookListDto extends PickType(BookList, ['url']) {
  pageIndex?: number
  pageSize?: number
}

export const getBookListDto = (data: BookList) => new BookListDto(data)
