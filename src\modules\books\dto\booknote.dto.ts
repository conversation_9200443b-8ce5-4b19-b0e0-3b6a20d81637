import { ApiProperty, PickType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNumber } from 'class-validator'
import { BookNote } from '@/entities'

export class CreateBookNoteDto extends PickType(BookNote, ['note']) {
  @ApiProperty()
  @IsNumber()
  bookId: number
}

export class BookNoteDto extends PickType(BookNote, ['note', 'userId', 'id']) {
  constructor(model: BookNote) {
    super()
    this.note = model.note
    this.userId = model.userId
    this.id = model.id
  }
}

export class QueryBookNoteDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  bookId: number
}

export class UpdateBookNoteDto extends PickType(BookNote, ['note']) {}

export const getBookNoteDto = (data: BookNote) => new BookNoteDto(data)
