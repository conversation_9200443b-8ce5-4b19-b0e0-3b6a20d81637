import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator'
import { PageRequest } from '@/common'
import { Bookshelf } from '@/entities'
import { EBookVersion } from '@/enums'

export class EditBookToBookshelDto extends PickType(Bookshelf, ['bookId', 'version']) {}

export class RemoveBookFromBookshelfDto {
  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsNumber(undefined, { each: true })
  bookIds: number[]

  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class SearchBookFromBookshelfDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  isRealTimeSearch?: boolean

  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class BookshelfDto extends PickType(Bookshelf, ['userId', 'id', 'bookId']) {
  constructor(data: Bookshelf) {
    super()
    this.bookId = data.bookId
    this.userId = data.userId
    this.id = data.id
  }
}

export const getBookshelfDto = (data: Bookshelf) => new BookshelfDto(data)
