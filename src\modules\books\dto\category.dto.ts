import { ApiPropertyOptional, IntersectionType, PickType } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { PageRequest } from '@/common'
import { Category } from '@/entities'

export class CreateCategoryDto extends PickType(Category, ['name']) {
  @ApiPropertyOptional()
  parentId?: number
}

export class ListCategoryDto extends IntersectionType(
  PickType(CreateCategoryDto, ['parentId']),
  PageRequest,
) {}

export class ListAdminCategoryDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string
}

export class ListAllCategoryDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string
}

export class CategoryDto extends PickType(Category, ['id', 'name']) {
  // @ApiPropertyOptional({ type: () => CategoryDto })
  // parent?: CategoryDto

  // @ApiPropertyOptional({ type: () => [CategoryDto] })
  // children?: CategoryDto[]

  constructor(data: Category) {
    super()
    this.id = data.id
    this.name = data.name
    // this.children = data.children?.map((item) => new CategoryDto(item))
    // if (data.parent) this.parent = new CategoryDto(data.parent)
  }
}

export const getCategoryDto = (data: Category) => new CategoryDto(data)
