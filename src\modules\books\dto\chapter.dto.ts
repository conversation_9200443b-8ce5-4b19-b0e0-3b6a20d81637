import { ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { Chapter } from '@/entities'

export class ChapterDto extends PickType(Chapter, ['id', 'name', 'sort']) {
  @ApiPropertyOptional({ type: [ChapterDto] })
  children?: ChapterDto[]

  constructor(data: Chapter) {
    super()
    this.id = data.id
    this.name = data.name
    this.sort = data.sort
    if (data.children?.length) {
      this.children = data.children.map((item) => new ChapterDto(item))
    }
  }
}

export const getChapterDto = (data: Chapter) => new ChapterDto(data)
