import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  Max,
  ValidateIf,
  ValidateNested,
} from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common'
import { Homepage } from '@/entities'
import { BookDto, getBookDto } from './book.dto'
import { BookListDto, getBookListDto } from './bookList.dto'

type HomepageField = keyof Homepage

export const modifyHomepageField: HomepageField[] = [
  'appStyle',
  'webStyle',
  'name',
  'status',
  'sort',
  // 'offlineAt',
  // 'onlineAt',
]

export const homepageField = modifyHomepageField.concat(['id'])

export class CreateHomepageDto extends PickType(Homepage, modifyHomepageField) {
  @ApiPropertyOptional({ type: [Number] })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateIf((o) => !o.bookListId)
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  bookIds?: number[]

  @ApiPropertyOptional()
  @IsNumber()
  @ValidateIf((o) => !o.bookIds || o.bookIds.length === 0)
  bookListId?: number
}

export class UpdateHomepageItem {
  @ApiProperty()
  @IsNumber()
  id: number

  @ApiProperty({ type: CreateHomepageDto })
  @ValidateNested()
  data: CreateHomepageDto
}

export class UpdateHomepageDto {
  @ApiProperty({ type: [UpdateHomepageItem] })
  @IsArray()
  @ValidateNested()
  @ArrayMinSize(1)
  items: UpdateHomepageItem[]
}

export class HomepageDto extends PickType(Homepage, [
  'appStyle',
  'webStyle',
  'name',
  'status',
  'sort',
  'id',
  'isPermanent',
  'isFixedSort',
]) {
  @ApiPropertyOptional({ type: BookDto })
  books?: BookDto[]

  @ApiPropertyOptional({ type: BookListDto })
  booklist?: BookListDto

  @ApiPropertyOptional()
  total?: any

  @ApiPropertyOptional()
  pageIndex?: any

  @ApiPropertyOptional()
  pageSize?: any

  constructor(data: Homepage) {
    super()
    Object.assign(
      this,
      R.pick(
        homepageField.concat(['isFixedSort', 'isPermanent', 'offlineAt', 'onlineAt']),
        data,
      ),
    )
    if (data.books) {
      this.books = data.books.map((item) => getBookDto(item))
    }
    if (data.booklist) {
      this.booklist = getBookListDto(data.booklist)
    }

    if (data.total) {
      this.total = data.total
      this.pageIndex = data.pageIndex
      this.pageSize = data.pageSize
    }
  }
}

export class SortHomepageItem extends PickType(Homepage, ['id', 'sort']) {}

export class SortHomepageDto {
  @ApiProperty({ type: [SortHomepageItem] })
  @IsArray()
  @ValidateNested()
  homepages: SortHomepageItem[]
}

export class UpdateHomepageStatusDto extends PickType(Homepage, ['status']) {
  @ApiPropertyOptional({ type: [Number] })
  @IsArray()
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  ids?: number[]
}

export const getHomepageDto = (data: Homepage) => new HomepageDto(data)

export class QueryFixedHomepageDto extends PageRequest {
  @ApiProperty()
  @IsNumber()
  @Max(30)
  @IsOptional()
  @Type(() => Number)
  limit: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  cursor?: number
}

export class DeleteHomepageDto {
  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1)
  ids?: number[]

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1)
  exceptions?: number[]
}
