import { ApiProperty, PickType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsNumber } from 'class-validator'
import { HotSearchWord, RecommendSearchWord } from '@/entities'

export class QuerySearchWordDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  top: number
}

export class SearchWordDto extends PickType(HotSearchWord, [
  'keyword',
  'searchCount',
  'id',
]) {
  constructor(data: HotSearchWord) {
    super()
    this.id = data.id
    this.keyword = data.keyword
    this.searchCount = data.searchCount
  }
}

export const getSearchWordDto = (data: HotSearchWord) => new SearchWordDto(data)

export class RecommendWordDto extends PickType(RecommendSearchWord, [
  'id',
  'keyword',
  'searchCount',
  'offlineAt',
  'onlineAt',
  'status',
]) {
  constructor(data: RecommendSearchWord) {
    super()
    this.id = data.id
    this.keyword = data.keyword
    this.searchCount = data.searchCount
    this.offlineAt = data.offlineAt
    this.onlineAt = data.onlineAt
    this.status = data.status
  }
}

export const getRecommendWordDto = (data: RecommendSearchWord) =>
  new RecommendWordDto(data)
