import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { PageRequest } from '@/common'

export * from './author.dto'
export * from './category.dto'
export * from './publisher.dto'
export * from './label.dto'
export * from './book.dto'
export * from './readRecord.dto'
export * from './bookshelf.dto'
export * from './chapter.dto'
export * from './bookList.dto'
export * from './homepage.dto'
export * from './file.dto'
export * from './hotSearchWord.dto'
export * from './report.dto'
export * from './word.dto'
export * from './booknote.dto'
export * from './schoolHomepage.dto'

export class SearchCountriesDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string
}

// export class CountriesDto {

// }
