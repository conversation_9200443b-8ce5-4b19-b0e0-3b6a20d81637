import { ApiPropertyOptional, IntersectionType, PickType } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common'
import { Label } from '@/entities'

export class LabelDto extends PickType(Label, ['id', 'name', 'type']) {
  constructor(label: Label) {
    super()
    Object.assign(this, R.pick(['id', 'name', 'type'], label))
  }
}

export class ListLabelDto extends IntersectionType(
  PickType(Label, ['type']),
  PageRequest,
) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string
}

export class FindLabelDto extends PickType(Label, ['type']) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string
}

export class CreateLabelDto extends PickType(Label, ['name', 'type']) {}

export const getLabelDto = (label: Label) => new LabelDto(label)
