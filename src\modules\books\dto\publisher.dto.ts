import { ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator'
import { ApiProperty, PageRequest } from '@/common'
import { Publisher } from '@/entities'
import { EStatus } from '@/enums'
import { filterValue } from '@/utils'

type PublisherKey = keyof Publisher

export const publisherField: PublisherKey[] = [
  'name',
  'description',
  'email',
  'logoImage',
  'status',
  'address',
  'publisherGroupName',
  'contactUserName',
]

export class CreatePublisherDto extends PickType(Publisher, publisherField) {
  // @ValidateNested()
  // name?: MultiLanguage
}

export class UpdatePublisherDto extends PickType(Publisher, [
  'name',
  'description',
  'email',
  'logoImage',
  'address',
  'contactUserName',
]) {}

export class UpdatePublisherStatusDto extends PickType(Publisher, ['status']) {
  @ApiProperty({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  ids?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]
}

export class QueryPublisherDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  status?: EStatus
}

export class DeletePublisherDto extends PickType(QueryPublisherDto, ['name', 'status']) {
  @ApiProperty({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  ids?: number[]

  @ApiProperty({ type: [Number] })
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]
}

export class SearchPublisherDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  name?: string
}

export class ExportPublisherReadingDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  startTime: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  endTime: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  publisherId?: number
}

export class PublisherDto extends PickType(Publisher, [
  'description',
  'email',
  // 'expiredAt',
  'logoImage',
  'status',
  'createdAt',
  'publisherId',
  'publisherGroupName',
  'contactUserName',
  'name',
  'id',
  'address',
]) {
  constructor(data: Publisher) {
    super()
    Object.assign(this, filterValue(publisherField.concat(['id', 'createdAt']), data))
  }
}

export const getPublisherDto = (data: Publisher) => new PublisherDto(data)

export class PublisherReferenceStatisticDto {
  @ApiProperty()
  booksCount: number

  @ApiProperty()
  copiesCount: number
}
