import { ApiProperty, IntersectionType } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator'
import { ApiPropertyOptional, PageRequest } from '@/common'
import {
  EBookVersion,
  EGlobalSummarySplitType,
  EOrderDirection,
  EUserType,
  ReadingTimeOrderType,
} from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { BookDto } from './book.dto'

export class ReadingTimeDto {
  @ApiProperty()
  totalBook: number

  @ApiProperty()
  totalUser: number

  @ApiProperty()
  totalReadingTime: number

  @ApiProperty()
  avgPerUser: number

  @ApiProperty()
  avgPerBook: number

  constructor(data: { totalBook: number; totalUser: number; totalReadingTime: number }) {
    this.totalBook = Number(data.totalBook)
    this.totalUser = Number(data.totalUser)
    this.totalReadingTime = Number(data.totalReadingTime)
    this.avgPerBook = Number(data.totalBook)
      ? Number(data.totalReadingTime) / Number(data.totalBook)
      : 0
    this.avgPerUser = Number(data.totalUser)
      ? Number(data.totalReadingTime) / Number(data.totalUser)
      : 0
  }
}

export const getReadingTimeDto = (data: {
  totalBook: number
  totalUser: number
  totalReadingTime: number
}) => new ReadingTimeDto(data)

export const getEmptyReadingTimeDto = () => ({
  totalBook: 0,
  totalUser: 0,
  totalReadingTime: 0,
  avgPerBook: 0,
  avgPerUser: 0,
})

export class QueryReadingTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  startTime: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  endTime: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  publisherId?: number
}

export class AdminQueryReadingTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  schoolId: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  startTime: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  endTime: number
}

export class SchoolReadingTimeCountItem {
  @ApiPropertyOptional()
  grade?: string

  @ApiPropertyOptional()
  class?: number

  @ApiProperty()
  totalReadingTime: number
}

export class ReadingUserCountItem {
  @ApiPropertyOptional()
  grade?: string

  @ApiPropertyOptional()
  class?: number

  @ApiProperty()
  totalUser: number
}

export class SchoolReadingTimeCountDto {
  @ApiProperty({ type: [SchoolReadingTimeCountItem] })
  student: SchoolReadingTimeCountItem[]

  @ApiProperty({ type: SchoolReadingTimeCountItem })
  teacher: SchoolReadingTimeCountItem
}

export class SchoolReadingUserCountDto {
  @ApiProperty({ type: [ReadingUserCountItem] })
  student: ReadingUserCountItem[]

  @ApiProperty({ type: ReadingUserCountItem })
  teacher: ReadingUserCountItem
}

export class SchoolUserCountByDateDto {
  @ApiProperty()
  date: string

  @ApiProperty()
  count: number
}

export class QueryStudentReadingTimeDto extends IntersectionType(
  PageRequest,
  QueryReadingTimeDto,
) {
  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  grade?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  class?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection

  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  allBooksReadingTimeOrderDirection?: EOrderDirection
}

export class AdminQueryStudentReadingTimeDto extends QueryStudentReadingTimeDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  schoolId: number
}

export class ExportStudentReadingTimeDto extends QueryReadingTimeDto {
  @ApiPropertyOptional()
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  grade?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => (value === 'true' ? true : value === 'false' ? false : value))
  byYear?: boolean

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  class?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  principalName?: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  librarianName?: string
}

export class AdminExportStudentReadingTimeDto extends ExportStudentReadingTimeDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  schoolId: number
}

export class StudentReadingTimeDto {
  @ApiProperty()
  name: MultiLanguage

  @ApiProperty()
  class: number

  @ApiProperty()
  grade: string

  @ApiProperty()
  bookName: MultiLanguage

  @ApiProperty()
  totalReadingTime: number

  @ApiPropertyOptional()
  @IsOptional()
  allBooksTotalReadingTime?: number
}
export class AdminQuerySchoolDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  schoolId?: number
}
export class QueryBookReadingTimeDto extends IntersectionType(
  PageRequest,
  QueryReadingTimeDto,
) {
  @ApiProperty({ enum: ReadingTimeOrderType })
  @IsEnum(ReadingTimeOrderType)
  orderBy: ReadingTimeOrderType

  @ApiProperty({ enum: EOrderDirection })
  @IsEnum(EOrderDirection)
  direction: EOrderDirection

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  schoolId?: number

  publisherId?: number

  type?: EUserType

  includeNoReadingBooks?: boolean
}

export class BookReadingTimeDto {
  @ApiProperty()
  name: MultiLanguage

  @ApiProperty()
  authorName: MultiLanguage

  @ApiProperty()
  totalUser: number

  @ApiProperty()
  totalReadingTime: number
}

export class ReadingTimeUserCountDto {
  @ApiProperty()
  date: string

  @ApiProperty()
  totalUser: number
}

export class ListAdminGradeDto extends PageRequest {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  schoolId?: number
}

export class Top10BookOfReadingTime {
  @ApiProperty({ type: BookDto })
  book: BookDto

  @ApiProperty()
  totalReadingTime: number
}

export class ReadingTimeCountDto {
  @ApiProperty()
  date: string

  @ApiProperty()
  totalReadingTime: number

  @ApiProperty()
  totalUser: number
}

export class QueryAdminReadingInDetailDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  labelId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  authorId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  publisherId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  level?: number
}

export class GetGlobalSummaryRequest {
  @ApiPropertyOptional({
    example: EGlobalSummarySplitType.DAY,
    enum: EGlobalSummarySplitType,
  })
  @IsOptional()
  @IsEnum(EGlobalSummarySplitType)
  splitType?: EGlobalSummarySplitType

  @ApiPropertyOptional({
    type: () => Number,
    example: 1688888888,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  startTime?: number

  @ApiPropertyOptional({
    type: () => Number,
    example: 1698888888,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  endTime?: number
}

export class GlobalSummaryPayload {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  periodTotalReadingTime: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  globalTotalReadingTime: number
}

export class GlobalSummaryItemPayload {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  year?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  month?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  realMonth?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  day?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  realDay?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  hour?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Type(() => String)
  realHour?: string

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  totalReadingTime?: number
}

export class GetGlobalSummaryResponse {
  @ApiPropertyOptional({
    example: EGlobalSummarySplitType.DAY,
    enum: EGlobalSummarySplitType,
  })
  @IsOptional()
  @IsEnum(EGlobalSummarySplitType)
  splitType?: EGlobalSummarySplitType

  @ApiPropertyOptional({
    type: () => [GlobalSummaryItemPayload],
  })
  @IsOptional()
  @Type(() => GlobalSummaryItemPayload)
  items?: GlobalSummaryItemPayload[]

  @ApiPropertyOptional({
    type: () => GlobalSummaryItemPayload,
  })
  @IsOptional()
  @Type(() => GlobalSummaryItemPayload)
  summary?: GlobalSummaryItemPayload
}

export class QueryReadingTimeCountDto extends QueryReadingTimeDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  labelId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  authorId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  publisherId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  bookId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  schoolId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  level?: number
}

export class QuerySchoolPublisherReadingDto extends QueryReadingTimeDto {
  @ApiPropertyOptional()
  @IsNumber()
  @Type(() => Number)
  publisherId: number
}

export class QueryReadingTimeAndUserDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  labelId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  categoryId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  authorId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  publisherId?: number

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  bookId?: number
}

export class ReadingTimeAndUserDto {
  @ApiProperty()
  count: number

  @ApiProperty()
  totalReadingTime: number

  @ApiProperty()
  totalUser: number
}

export class QueryReadingTimeOfStudentByBookDto extends IntersectionType(
  QueryReadingTimeDto,
  PageRequest,
) {
  @IsEnum(ReadingTimeOrderType)
  @IsOptional()
  orderBy?: ReadingTimeOrderType

  @IsOptional()
  @IsEnum(EOrderDirection)
  direction?: EOrderDirection
}
