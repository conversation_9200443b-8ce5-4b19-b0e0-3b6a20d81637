import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { PageRequest } from '@/common'
import { EOrderDirection } from '@/enums'
import { QueryReadingTimeDto } from './readRecord.dto'

// export class ReferenceBookReadByUserCountItemDto {

// }

// export class SchoolReferenceBookReadByUserCountDto {
//   @ApiProperty()
//   students:
//   @ApiProperty()
//   teacherCount: number
// }
export enum ESchoolReferenceBookStatisticOrderType {
  READ_COUNT = 'READ_COUNT', // 阅读次数
  TOTAL_READ_COUNT = 'TOTAL_READ_COUNT', // 总阅读次数
  TOTAL_READ_BOOK = 'TOTAL_READ_BOOK', // 总阅读书籍数目
}
export class QuerySchoolReferenceBookStatisticDto extends IntersectionType(
  QueryReadingTimeDto,
  PageRequest,
) {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  grade?: number

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  class?: number

  @ApiProperty()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional({ enum: ESchoolReferenceBookStatisticOrderType })
  @IsOptional()
  @IsEnum(ESchoolReferenceBookStatisticOrderType)
  orderType?: ESchoolReferenceBookStatisticOrderType

  @ApiPropertyOptional({ enum: EOrderDirection })
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: 'ASC' | 'DESC'
}
