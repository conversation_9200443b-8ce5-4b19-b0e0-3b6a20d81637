import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator'
import { PageRequest } from '@/common'
import { EBookVersion, EOrderDirection } from '@/enums'
import { MultiLanguage } from '@/interfaces'

export class UserCountDto {
  @ApiProperty()
  studentCount: number

  @ApiProperty()
  teacherCount: number

  @ApiProperty()
  publisherCount: number

  @ApiProperty()
  bookCount: number

  @ApiProperty()
  schoolCount: number

  @ApiProperty()
  adminCount: number
}

export class SchoolUserCountDto {
  @ApiProperty()
  date: string

  @ApiProperty()
  teacher: number

  @ApiProperty()
  student: number
}

export class AdminUserCountDto {
  @ApiProperty()
  date: string

  @ApiProperty()
  platformCount: number

  @ApiProperty()
  schoolCount: number
}

export class QueryReportCountDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  startTime: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  endTime: number
}

export class QueryTopReadingTimeDto extends IntersectionType(
  QueryReportCountDto,
  PageRequest,
) {}

export class ExportTopSchoolLeftReadingTimeDto {
  @ApiProperty()
  @IsEnum(EOrderDirection)
  sortDirection: EOrderDirection
}

export class QueryTopSchoolLeftReadingTimeDto extends IntersectionType(
  PageRequest,
  ExportTopSchoolLeftReadingTimeDto,
) {}

export class TopReadingTimeDto {
  @ApiProperty()
  totalReadingTime: number

  @ApiProperty()
  name: MultiLanguage
}

export class QuerySomeCountDto {
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  hasScienceRoom?: boolean
}

export class QueryUserDistributionDto extends QueryReportCountDto {
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  hasScienceRoom?: boolean
}
