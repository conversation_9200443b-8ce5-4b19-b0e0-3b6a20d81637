import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { IsArray, IsEnum, IsNumber, IsOptional } from 'class-validator'
import R from 'ramda'
import { SchoolHomepage } from '@/entities'
import { EUserType } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { BookDto, getBookDto } from './book.dto'

type HomepageField = keyof SchoolHomepage

export const modifySchoolHomepageField: HomepageField[] = [
  'appStyle',
  'webStyle',
  'name',
  'status',
  'type',
  'classIds',
  'gradeIds',
  'optionName',
  'version',
]

export const schoolHomepageField = modifySchoolHomepageField.concat(['id'])

export class CreateSchoolHomepageDto extends PickType(SchoolHomepage, [
  'appStyle',
  'webStyle',
  'name',
  'status',
  'classIds',
  'gradeIds',
  'optionName',
  'version',
]) {
  @ApiPropertyOptional({ type: [Number] })
  @IsArray()
  // @ArrayMinSize(1)
  @IsOptional()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  bookIds?: number[]

  @ApiProperty({
    example: EUserType.STUDENT,
    enum: EUserType,
  })
  @IsEnum(EUserType)
  @IsOptional()
  type?: EUserType
}

export class SchoolHomepageDto extends PickType(SchoolHomepage, [
  'appStyle',
  'webStyle',
  'name',
  'status',
  'id',
]) {
  @ApiPropertyOptional({ type: BookDto })
  books?: BookDto[]

  @ApiProperty({ type: MultiLanguage })
  defaultName?: MultiLanguage

  constructor(data: SchoolHomepage) {
    super()
    Object.assign(
      this,
      R.pick(schoolHomepageField.concat(['offlineAt', 'onlineAt']), data),
    )
    if (data.books) {
      this.books = data.books.map((item) => getBookDto(item))
    }
  }
}

export const getSchoolHomepageDto = (data: SchoolHomepage) => new SchoolHomepageDto(data)
