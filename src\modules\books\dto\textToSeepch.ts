import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsString } from 'class-validator'
import { ETextToSpeechLanguage, ETextToSpeechSsmlGender } from '@/enums'

export class TextToSpeechDto {
  @ApiProperty()
  @IsString()
  text: string

  @ApiProperty({
    description: '语言：cmn-CN ｜ yue-HK ｜ en-GB',
  })
  @IsEnum(ETextToSpeechLanguage)
  languageCode: ETextToSpeechLanguage

  @ApiProperty({
    description: '声音：MALE ｜ FEMALE',
  })
  @IsEnum(ETextToSpeechSsmlGender)
  ssmlGender: ETextToSpeechSsmlGender
}
