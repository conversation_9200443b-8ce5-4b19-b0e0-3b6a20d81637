import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { IsEnum, IsOptional } from 'class-validator'
import { PageRequest } from '@/common'
import { ECountType, EOrderDirection, EViewBookGroupType } from '@/enums'
import { MultiLanguage } from '@/interfaces'
import { QueryReadingTimeDto } from './readRecord.dto'

export class QueryPlatformStatisticViewBookDto extends IntersectionType(
  QueryReadingTimeDto,
  PageRequest,
) {
  @ApiPropertyOptional({ enum: EViewBookGroupType })
  @IsEnum(EViewBookGroupType)
  @IsOptional()
  group: EViewBookGroupType

  @ApiPropertyOptional({ enum: EOrderDirection })
  @IsEnum(EOrderDirection)
  @IsOptional()
  orderDirection?: EOrderDirection

  @ApiPropertyOptional({ enum: ECountType })
  @IsEnum(ECountType)
  @IsOptional()
  countType?: ECountType
}

export class PlatformStatisticViewBookDto {
  @ApiPropertyOptional()
  school?: MultiLanguage

  @ApiPropertyOptional()
  publisher?: MultiLanguage

  @ApiPropertyOptional()
  author?: MultiLanguage

  @ApiProperty()
  count: number
}
