import { EBookLanguage, EBookVersion, OnlineOfflineStatus } from '@/enums'

export interface ICountBooks {
  categoryId?: number[]
  publisherId?: number
  authorId?: number
  labelId?: number
  level?: number
}

export interface IBaseSearchBook {
  pageIndex?: number
  pageSize?: number
  parentCategoryId?: number
  categoryIds?: number[]
  orderBy?: string // todo cindy
  publisherId?: number[]
  categoryLabelId?: number[]
  educationLabelId?: number[]
  labelId?: number[]
  authorId?: number[]
  status?: OnlineOfflineStatus
  ids?: number[]
  excludedIds?: number[]
  isbn?: string[]
}

export interface IListBook {
  pageIndex?: number
  pageSize?: number
  categoryId?: number[]
  publisherId?: number[]
  labelId?: number
  authorId?: number[]
}

export interface ISearchBook extends IBaseSearchBook {
  name?: string
  // pageIndex?: number
  // pageSize?: number
  // parentCategoryId?: number
  // categoryId?: number
  // authorName?: string
  // bookName?: string
  // publisherName?: string
  // orderBy?: string // todo cindy
  // publisherId?: number[]
  // labelId?: number
  // authorId?: number[]
}

export interface IKeywordSearchBook extends IBaseSearchBook {
  keyword?: string
  isRealTimeSearch?: boolean
  schoolId?: number
  language?: EBookLanguage
  level?: number[]
  isHidden?: boolean
  version?: EBookVersion
  referenceSchoolId?: number
  isScienceRoom?: boolean
}
