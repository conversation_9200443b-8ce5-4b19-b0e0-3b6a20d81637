export interface QueryReadingTimeAdmin {
  categoryId?: number
  publisherId?: number
  authorId?: number
  labelId?: number
  level?: number
  // startTime: number
  // endTime: number
}

export interface QueryReadingTime {
  startTime: number

  endTime: number

  authorId?: number

  publisherId?: number

  bookId?: number

  bookIds?: number[]

  schoolId?: number
}

export interface QueryReadingTimeAndUser {
  authorId?: number

  publisherId?: number

  bookId?: number

  bookIds?: number[]
}
