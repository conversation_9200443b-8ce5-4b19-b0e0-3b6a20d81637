import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Inject, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Cache } from 'cache-manager'
import path from 'path'
import { Repository } from 'typeorm'
import { School } from '@/entities'
import { BookType, EUserType, OnlineOfflineStatus } from '@/enums'
import { getSubscriptionNewestKey } from '@/modules/constants'
import { ChapterService } from '../services'
import { BookRepository } from '../services/book.repository'
import { BaseProvider } from './base.provider'
import { EpubProvider } from './epub.provider'
import { PdfProvider } from './pdf.provider'

@Injectable()
export class BookProvider {
  constructor(
    private readonly epubProvider: EpubProvider,
    private readonly pdfProvider: PdfProvider,
    private readonly chapterService: ChapterService,
    private readonly bookRepositories: BookRepository,
    @Inject(CACHE_MANAGER) private readonly cache: Cache,
    @InjectRepository(School) private readonly schoolRepository: Repository<School>
  ) {}

  async handle(file: Buffer, id: number, url: string) {
    const book = await this.bookRepositories.getBookWithoutRelation({ id })
    const fileType = path.extname(book.url).split('.').pop()
    const provider = this.getProvider(fileType as BookType)
    const {
      bookmarks,
      coverUrl,
      opfUrl = null,
      locations,
      pieces,
    } = await provider.handleBook(file, book.bookId, url)

    if (bookmarks?.length) {
      await this.chapterService.deleteChapters(book.id)
      await this.chapterService.saveChapters(bookmarks, book)
    }
    const data: any = { opfUrl }
    if (!book.coverUrl) {
      data.coverUrl = coverUrl
    }
    const parseStatus =
      (fileType === BookType.EPUB && opfUrl) || fileType === BookType.PDF

    if (book.status === OnlineOfflineStatus.OFFLINE && parseStatus) {
      data.status = OnlineOfflineStatus.ONLINE
      data.onlineAt = new Date()
      await this.clearNewestCache()
    }
    if (locations) {
      data.locations = locations
    }
    if (pieces) {data.pieces = pieces}
    await this.bookRepositories.updateBook(book.id, data)
    return (
      bookmarks?.length > 0 &&
      data.coverUrl &&
      ((fileType === BookType.EPUB && opfUrl) || false)
    )
  }

  private getProvider(type: BookType): BaseProvider {
    return type === BookType.EPUB ? this.epubProvider : this.pdfProvider
  }

  private async clearNewestCache() {
    const schools = await this.schoolRepository.query(
      `select id from schools where deleted_at is null`
    )

    await Promise.all(
      schools.map((item) =>
        this.cache.del(getSubscriptionNewestKey(item.id, EUserType.STUDENT))
      )
    )

    await Promise.all(
      schools.map((item) =>
        this.cache.del(getSubscriptionNewestKey(item.id, EUserType.TEACHER))
      )
    )
  }
}
