import { Injectable } from '@nestjs/common'
import AdmZip from 'adm-zip'
import path from 'path'
import R from 'ramda'
import { parseStringPromise } from 'xml2js'
import { S3_BOOK_COVER_IMAGE, S3_BOOK_UNZIP_DIR } from '../../constants'
import { BookS3Service } from '../services'
import { BaseProvider } from './base.provider'
import { EPubLocationsService } from './epubLocations.service'

@Injectable()
export class EpubProvider extends BaseProvider {
  constructor(
    private readonly s3Service: BookS3Service,
    private readonly epubLocationService: EPubLocationsService,
  ) {
    super()
  }

  async handleBook(file: Buffer, bookId: string, bookUrl: string) {
    const opfUrl = await this.unZipAndUploadFiles(file, bookId)
    const { locations, toc } = await this.epubLocationService.parse(bookUrl, bookId)
    try {
      const { bookmarks, coverUrl } = await this.parseEpub(file, bookId)
      return { bookmarks, coverUrl, opfUrl, locations }
      // return { bookmarks, coverUrl }
    } catch (err) {
      console.log(err)
    }
    return { bookmarks: undefined, coverUrl: undefined, opfUrl: null, locations: null }
    // return { bookmarks: undefined, coverUrl: undefined, opfUrl, locations }
  }

  private async parseEpub(file: Buffer, bookId: string) {
    const xmlParseOptions = {
      explicitArray: false,
      mergeAttrs: true,
    }
    const zip = new AdmZip(file)
    const zipEntries = zip.getEntries()

    // await Promise.all(
    //   zipEntries.map((item) =>
    //     item.entryName.endsWith('/')
    //       ? ''
    //       : fs.writeFile(`book/${item.entryName}`, item.getData()),
    //   ),
    // )

    const containerFile = zipEntries.find((x) => x.entryName === 'META-INF/container.xml')
    const containerJson = (await parseStringPromise(
      containerFile.getData().toString('utf-8'),
      xmlParseOptions,
    )) as any

    const opfFileName = containerJson?.container?.rootfiles?.rootfile['full-path']
    const opfFile = zipEntries.find(
      (x) => x.entryName === opfFileName || x.entryName === decodeURI(opfFileName),
    )
    const opfJson = (await parseStringPromise(
      opfFile.getData().toString('utf-8'),
      xmlParseOptions,
    )) as any

    const opfFilePath = path.dirname(opfFileName)
    console.log({ opfFileName })

    console.log(opfJson?.package?.metadata)

    const meta = opfJson?.package?.metadata?.meta
    const id = Array.isArray(meta)
      ? opfJson?.package?.metadata?.meta?.find((item) => item.name === 'cover')?.content
      : meta.content
    const coverFileName = opfJson?.package?.manifest?.item?.find(
      (list) => list?.id === id,
    )?.href

    let coverUrl = null
    if (coverFileName && opfFilePath) {
      const coverFile = zipEntries.find(
        (item) =>
          item.entryName === path.join(opfFilePath, coverFileName) ||
          item.entryName === decodeURI(path.join(opfFilePath, coverFileName)),
      )

      console.log({ coverFileName })

      coverUrl = coverFile
        ? await this.s3Service.upload({
            fileName: bookId + path.extname(coverFileName),
            file: coverFile.getData(),
            path: S3_BOOK_COVER_IMAGE,
          })
        : undefined
    }

    const tocItem = opfJson?.package?.manifest?.item?.filter(
      (list) =>
        list?.id === 'toc' ||
        list?.id === 'ncx' ||
        list?.id === 'nav' ||
        list?.id === 'navid',
    )
    console.log({ tocItem })

    let bookmarks
    if (tocItem?.length) {
      const bookmarksData = await Promise.all(
        tocItem.map((item) => this.parseBookmark(opfFilePath, item, zipEntries)),
      )

      const validBookmarks = bookmarksData.filter((item) => !R.isNil(item))
      if (validBookmarks.length === 1) {
        bookmarks = validBookmarks[0]
      } else if (validBookmarks.length > 1) {
        bookmarks = validBookmarks[0]
        console.log(validBookmarks)
        console.log('book marks error')
      } else {
        console.log('no book marks')
      }
    }
    // const tocFileName = path.join(opfFilePath, tocItem.href)
    // console.log({ tocFileName })
    // const tocFile = zipEntries.find(
    //   (item) =>
    //     item.entryName === tocFileName || item.entryName === decodeURI(tocFileName),
    // )
    // const tocJson = (await parseStringPromise(
    //   tocFile.getData().toString('utf-8'),
    //   xmlParseOptions,
    // )) as any

    // console.log(tocJson?.html?.body)
    // console.log(tocJson?.html?.head)

    // let bookmarks
    // if (tocItem.id === 'toc') {
    //   const toc = Array.isArray(tocJson?.html?.body[tocItem.properties])
    //     ? tocJson?.html?.body[tocItem.properties]?.find((item) => item.id === 'toc')
    //     : tocJson?.html?.body[tocItem.properties]
    //   bookmarks = toc.ol?.li
    //     ?.map((item) => item.a)
    //     ?.map((item) => ({ name: item['_'], url: item.href }))
    // } else {
    //   bookmarks = Array.isArray(tocJson?.ncx?.navMap?.navPoint)
    //     ? tocJson?.ncx?.navMap?.navPoint?.map((item) => ({
    //         name: item.navLabel.text,
    //         url: item.content.src,
    //       }))
    //     : undefined
    // }

    return { bookmarks, coverUrl }
  }

  private async unZipAndUploadFiles(file: Buffer, bookId: string): Promise<string> {
    const zip = new AdmZip(file)
    const zipEntries = zip.getEntries()

    const urls = await Promise.all(
      zipEntries.map(async (entity) => {
        const fileName = entity.entryName.replace(/^.*[\\\/]/, '')
        if (!fileName) {
          return ''
        }
        const dirname = path.dirname(entity.entryName)
        return this.s3Service.upload({
          fileName,
          path:
            dirname === '.'
              ? `${S3_BOOK_UNZIP_DIR}/${bookId}`
              : `${S3_BOOK_UNZIP_DIR}/${bookId}/${dirname}`,
          file: entity.getData(),
        })
      }),
    )
    return urls.find((item: string) => item.includes('.opf')) as string
  }

  private getFileNamefromPath(path: string) {
    return path.replace(/^.*[\\\/]/, '')
  }

  private async parseBookmark(opfFilePath: string, tocItem: any, zipEntries: any) {
    const tocFileName = path.join(opfFilePath, tocItem.href)
    console.log({ tocFileName })
    const tocFile = zipEntries.find(
      (item) =>
        item.entryName === tocFileName || item.entryName === decodeURI(tocFileName),
    )
    const xmlParseOptions = {
      explicitArray: false,
      mergeAttrs: true,
    }
    const tocJson = (await parseStringPromise(
      tocFile.getData().toString('utf-8'),
      xmlParseOptions,
    )) as any

    console.log(tocJson?.html?.body)
    console.log(tocJson?.html?.head)

    let bookmarks
    if (tocItem.id === 'toc' || tocItem.id === 'nav') {
      const toc = Array.isArray(tocJson?.html?.body[tocItem.properties])
        ? tocJson?.html?.body[tocItem.properties]?.find((item) => item.id === 'toc')
        : tocJson?.html?.body[tocItem.properties]
      console.log('toc', toc)
      const li = toc.ol?.li
      bookmarks =
        li instanceof Array
          ? li
              ?.map((item) => item.a)
              ?.map((item) => ({ name: item['_'], url: item.href }))
          : li?.ol?.li
              ?.map((item) => item.a)
              ?.map((item) => ({ name: item['_'], url: item.href }))
      // if (
      //   (!bookmarks || bookmarks.length === 0) &&
      //   Array.isArray(toc.lo) &&
      //   toc.lo.length
      // ) {
      // }
    } else {
      bookmarks = Array.isArray(tocJson?.ncx?.navMap?.navPoint)
        ? tocJson?.ncx?.navMap?.navPoint?.map((item) => ({
            name: item.navLabel.text,
            url: item.content.src,
          }))
        : undefined
    }

    return bookmarks
  }
}
