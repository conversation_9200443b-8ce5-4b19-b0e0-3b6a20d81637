import { Injectable } from '@nestjs/common'
import puppeteer from 'puppeteer'
import { S3_EPUB_PARSE_HTML_DIR } from '@/modules/constants'
import { BookS3Service } from '../services'

@Injectable()
export class EPubLocationsService {
  constructor(private readonly bookS3Service: BookS3Service) {}

  async parse(epubfile: string, bookId: string) {
    const locationsHtml = `
    <!DOCTYPE html>
    <html> 
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>EPUB.js Spreads Example</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>
        <script src="https://images-dev.enrichculturegroup.com/public/epubjs/dist/4F303359D4B7482DB239AB257D48ECEA.js"></script>
        <link rel="stylesheet" type="text/css" href="examples.css"></head>
      
      <body>
        <div id="title"></div>
        <script>
          // Load the opf
          var book = ePub("${epubfile}") 
          book.ready.then(function() {
            return book.locations.generate(1600);
          }).then(function(locations) {
            // Save out the generated locations to JSON
            localStorage.setItem('book-locations', book.locations.save())
            localStorage.setItem('has-locations', true)
          });
          book.loaded.navigation.then((nav) =>{
            localStorage.setItem('book-toc', JSON.stringify(nav.toc))
          })</script>
      </body>
    </html>`

    const locationsHtmlPath = await this.bookS3Service.upload({
      fileName: `${bookId}.html`,
      file: Buffer.from(locationsHtml),
      path: S3_EPUB_PARSE_HTML_DIR,
      contentType: 'html',
    })

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      ignoreDefaultArgs: ['--disable-extensions'],
    })
    const page = await browser.newPage()
    await page.goto(locationsHtmlPath)

    let hasLocation = await page.evaluate(() =>
      window.localStorage.getItem('has-locations'),
    )
    console.log(hasLocation)
    let i = 0
    while (!hasLocation && i < 4) {
      hasLocation = await page.evaluate(() =>
        window.localStorage.getItem('has-locations'),
      )
      i++
    }
    const locations = await page.evaluate(() =>
      window.localStorage.getItem('book-locations'),
    )
    const toc = await page.evaluate(() => window.localStorage.getItem('book-toc'))

    console.log(locations)
    await browser.close()
    return { locations, toc }
  }
}
