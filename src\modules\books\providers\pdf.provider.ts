import { Injectable } from '@nestjs/common'
import puppeteer from 'puppeteer'
import { encrypt } from '@/common'
import {
  S3_BOOK_COVER_IMAGE,
  S3_BOOK_ORIGNAL_DIR,
  S3_BOOK_PDF_OUTLINE_DIR,
} from '@/modules/constants'
import { getBookmarks } from '@/utils/pdf.util'
import { BookS3Service } from '../services'
import { convertPDFToImage, splitFileBuffer } from '../utils'
import { BaseProvider } from './base.provider'

@Injectable()
export class PdfProvider extends BaseProvider {
  constructor(private readonly bookS3Service: BookS3Service) {
    super()
  }

  async handleBook(file: Buffer, bookId: string, bookUrl: string): Promise<any> {
    const coverUrl = await this.uploadCover(file, bookId)
    // let bookmarks = await this.parsePdf(file, bookId, bookUrl)

    const bookmarks = await getBookmarks({ fileBuffer: file })

    // if (!bookmarks || bookmarks.length <= 0)
    //   bookmarks = await getBookmarks({ fileBuffer: file })

    // 分片加密上传
    const pieces = splitFileBuffer(file, file.length, 2 * 1024 * 1024)

    const encryptedPieces = pieces.map((x) => encrypt(process.env.AES_KEY, x))

    encryptedPieces.forEach(async (buffer, index) => {
      await this.bookS3Service.upload({
        fileName: `${index}.pdf`,
        path: `${S3_BOOK_ORIGNAL_DIR}/${bookId}`,
        file: buffer,
      })
    })

    return { coverUrl, bookmarks, pieces: pieces.length }
  }
  async uploadCover(file: Buffer, bookId: string) {
    const imageBuffer = await convertPDFToImage(Buffer.from(file))
    return this.bookS3Service.upload({
      fileName: `${bookId}.jpeg`,
      path: S3_BOOK_COVER_IMAGE,
      file: imageBuffer,
      contentType: 'image/jpeg',
    })
  }

  async parsePdf(file: Buffer, bookId: string, bookUrl: string) {
    async function waiting(ms: number) {
      return new Promise((resolve) => {
        setTimeout(() => resolve(undefined), ms)
      })
    }

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      ignoreDefaultArgs: ['--disable-extensions'],
    })
    const page = await browser.newPage()
    // const uint8Array = new Uint8Array(file)
    // await page.setContent(this.getOutlineHtml(uint8Array))
    // todo refactor using butter instead
    console.log({ bookUrl })
    const outlineUrl = await this.uploadOutlineHtml(bookUrl, bookId)
    console.log({ outlineUrl })
    // todo refactor like this or setContent??
    await page.goto(outlineUrl)
    // await page.goto(`data:text/html,${outlineHtml}`, {
    //   waitUntil: 'networkidle0',
    // })

    let count = 0
    do {
      await waiting(1000)
      const load: any = await page.evaluate(() =>
        window.localStorage.getItem('load:finish'),
      )
      console.log({ load })
      const outline: any = await page.evaluate(() =>
        window.localStorage.getItem('outline:finish'),
      )
      console.log({ outline })
      const data: any = await page.evaluate(() =>
        window.localStorage.getItem('book:chapter'),
      )
      const loadingError: any = await page.evaluate(() =>
        window.localStorage.getItem('load:error'),
      )
      const outlineError: any = await page.evaluate(() =>
        window.localStorage.getItem('outline:error'),
      )
      console.log({ outlineError })
      console.log({ loadingError })

      if (loadingError || outlineError) {
        return []
      }

      if (data) {
        return JSON.parse(data).chapters
      }
      count += 1
    } while (count <= (2 * 60 * 1000) / 1000)

    await browser.close()
    return undefined
  }

  private async uploadOutlineHtml(url: string, bookId: string) {
    const html = this.getOutlineHtml(url)
    return this.bookS3Service.upload({
      fileName: `${bookId}.html`,
      file: Buffer.from(html),
      path: S3_BOOK_PDF_OUTLINE_DIR,
      contentType: 'html',
    })
  }

  private getOutlineHtml(url: string) {
    return `<!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>PDF.js Spreads Example</title>
        <script src="https://images-dev.enrichculturegroup.com/public/images/pdf.js"></script>
      </head> 
      <body>
        <div id="title"></div>
        <div id="viewer" class="spreads"></div>
        <a id="prev" href="#prev" class="arrow">‹</a>
        <a id="next" href="#next" class="arrow">›</a>
        <div id="controls">
          <input id="current-percent" size="3" value="0" />%</div>
        <div id="result"></div>
        <script type="module">var pdfjsLib = window['pdfjs-dist/build/pdf'] 
          pdfjsLib.GlobalWorkerOptions.workerSrc =
            'https://images-dev.enrichculturegroup.com/public/images/pdf.worker.js'
          // Your loadingTask
          // var data = 
          // var loadingTask = pdfjsLib.getDocument(new Uint8Array(data))
          var loadingTask = pdfjsLib.getDocument('${url}')
          
          loadingTask.promise.then(function (pdf) {
            localStorage.setItem('load:finish', JSON.stringify({ loadFinished: true }))
            function getPdfOutline(outline) {
              return outline.map((item) => ({
                name: item.title,
                children: item.items.length ? getPdfOutline(item.items) : undefined,
              }))
            }
            // Get the tree outline
            pdf.getOutline().then(async function (outline) {
              localStorage.setItem('outline:finish', JSON.stringify({ outlineFinished: true }))
              const chapters = getPdfOutline(outline)
              console.log({ chapters })
              localStorage.setItem('book:chapter', JSON.stringify({ already: true, chapters }))
            }, function(err) {
              localStorage.setItem('outline:error', JSON.stringify({ outline: reason }))
            })
          }, function (reason) {
            // PDF loading error
            localStorage.setItem('load:error', JSON.stringify({ loading: reason }))
            console.error(reason);
          })
          </script>
      </body>
    </html>`
  }
}
