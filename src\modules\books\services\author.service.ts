import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Excel from 'exceljs'
import moment from 'moment'
import R from 'ramda'
import { Readable } from 'stream'
import { FindManyOptions, In, Repository } from 'typeorm'
import { ELocaleType, EmailService, ExcelService, generateUniqueId } from '@/common'
import { Author, Book, ReadRecord } from '@/entities'
import { FileException } from '@/modules/account/exception'
import { AUTHOR_PROFILE_IMAGE, PAGE_SIZE } from '@/modules/constants'
import { DuplicateAuthorException } from '@/modules/exception'
import { LogService } from '@/modules/system'
import { IAuthorService } from '@/modules/shared/interfaces'
import {
  allNameCondition,
  getCellValue,
  multiNameCondition,
  nameLikeCondition,
  nameLikeWhere,
} from '@/utils'
import { MultiLanguage, PageData } from '@/interfaces'
import { CreateAuthorDto, getAuthorDto, QueryAuthorDto, SearchAuthorDto } from '../dto'
import { getAuthorNameZh } from '../utils/bookExcel.util'
import { authorValidator } from '../validators'

@Injectable()
export class AuthorService implements IAuthorService {
  constructor(
    @InjectRepository(Author)
    private readonly authorRepository: Repository<Author>,
    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,
    @InjectRepository(ReadRecord)
    private readonly recordRepository: Repository<ReadRecord>,
    private readonly excelService: ExcelService,
    private readonly mailService: EmailService,
    private readonly logService: LogService
  ) {}

  async find(options?: FindManyOptions<Author>) {
    return this.authorRepository.find(options)
  }

  async findAuthorNames(bookIds: number[]) {
    const books = await this.bookRepository.find({
      where: {
        id: In(bookIds),
      },
      select: ['id', 'authorIds'],
    })
    if (!books || books.length <= 0) {return []}
    const authors = await this.authorRepository.find({
      where: {
        id: In(R.flatten(books.map((x) => x.authorIds)).filter((x) => !!x)),
      },
      select: ['id', 'name'],
    })
    if (!authors || authors.length <= 0) {return []}

    return books.map((x) => ({
      id: x.id,
      authors: authors.filter((y) => x.authorIds.includes(y.id)),
    }))
  }

  async exportAuthors(options: {
    email: string
    local: ELocaleType
    query: QueryAuthorDto
    user: any
  }) {
    const { email, local, query } = options
    let pageIndex = 1
    const pageSize = 30
    let total = 0
    let execel = []
    do {
      const data = await this.listAuthor(
        { ...query, pageIndex, pageSize, withDeleted: true },
        ['books', 'books.publisher']
      )
      const items = []
      for (const item of data.items) {
        let reading = []
        if (item.books?.length) {
          reading = await this.recordRepository.query(
            `select t1.book_id as id, COUNT(distinct(t1.user_id)) as totalUser, SUM(t1.reading_time) as totalReadingTime 
            from read_record t1, books t2 
            where t1.book_id = t2.id and t1.publisher_id = t2.publisher_id and t1.reading_time > 0 
              and  ${item.id} MEMBER OF(t1.author_id) = 1 group by t1.book_id`
          )
        }

        const author = item.books?.length
          ? item.books.map((book) => {
            const time = reading.find((r) => r.id == book.id)
            return {
              ...getAuthorDto(item),
              totalUser: time?.totalUser ?? 0,
              totalReadingTime: Number(time?.totalReadingTime ?? 0) / 3600,
              bookName: book?.name?.zh_HK || book?.name?.en_uk,
              publisher:
                  book.publisher?.name?.zh_HK ?? book.publisher?.name?.en_uk ?? '',
              companyName:
                  book.publisherGroupName?.zh_HK ?? book.publisherGroupName?.en_uk ?? '',
            }
          })
          : [{ ...getAuthorDto(item) }]
        items.push(author)
      }

      const excelData = R.flatten(items).map((item) => ({
        ...item,
        name: `${getAuthorNameZh(item.name)} ${item.name?.en_uk ?? ''}`,
        description: item.description?.zh_HK ?? '',
      }))
      pageIndex += 1
      total = data.total
      execel = execel.concat(excelData)
    } while ((pageIndex - 1) * pageSize < total)

    const file = this.excelService.buildExcel({ name: `authors.${local}`, data: execel })

    await this.mailService.sendPrepared(email, 'dataExport', {
      attachments: [
        {
          content: file,
          filename: `AuthorsExportedFile_${moment()
            .tz('Asia/Hong_Kong')
            .format('YYYYMMDDHHmmss')}.csv`,
        },
      ],
    })
    await this.logService.save('下载作者报表', options.user, query)
  }

  async createAuthor(data: CreateAuthorDto, admin: any) {
    const [duplicated] = await this.findAuthorsByName([data.name])
    if (duplicated) {
      throw new DuplicateAuthorException()
    }

    return this.authorRepository.save({
      ...R.pick(['name', 'description'], data),
      authorId: generateUniqueId('au_'),
      profileImage: data.profileImage ?? AUTHOR_PROFILE_IMAGE,
      createdBy: admin,
    })
  }

  async batchAddAuthors(file: Express.Multer.File, operator: any) {
    const result = []
    const data = new Excel.Workbook()
    const errors = []
    try {
      await data.xlsx.load(file.buffer)
    } catch (error) {
      try {
        const stream = Readable.from(file.buffer)
        await data.csv.read(stream)
      } catch (err) {
        throw new FileException()
      }
    }
    // use workbook
    data.getWorksheet(1).eachRow((row, rowNumber) => {
      if (rowNumber !== 1) {
        result.push({
          name: getCellValue(row.getCell(1)),
          nameEn: getCellValue(row.getCell(2)),
          nameCn: getCellValue(row.getCell(3)),
          description: getCellValue(row.getCell(4)),
          descriptionEn: getCellValue(row.getCell(5)),
          descriptionCn: getCellValue(row.getCell(6)),
          rowNumber,
        })
      }
    })

    const authors = []
    for (let i = 0; i < result.length; i++) {
      const item = result[i]
      const error = this.checkData(item, item.rowNumber)
      if (error) {errors.push(error)}
      else
      {authors.push({
        name: { zh_HK: item.name, zh_cn: item.nameCn, en_uk: item.nameEn },
        description: {
          zh_HK: item.description,
          zh_cn: item.descriptionCn,
          en_uk: item.descriptionEn,
        },
      })}
    }

    if (authors.length <= 0) {
      return { errors, authors }
    }

    const res = await this.findOrCreateAuthor(authors)

    return { errors, authors: res }
  }

  async batchSaveAuthor(
    data: {
      name: MultiLanguage
      description: MultiLanguage
    }[]
  ) {
    return this.authorRepository.save(
      data.map((item) => ({
        name: item.name,
        authorId: generateUniqueId('au_'),
        profileImage: AUTHOR_PROFILE_IMAGE,
        description: item.description,
      }))
    )
  }

  async batchCreateAuthor(data: Partial<Author>[]) {
    return this.authorRepository.save(
      data.map((item) => ({
        ...item,
        authorId: generateUniqueId('au_'),
      }))
    )
  }

  async findOrCreateAuthor(
    data: {
      name: MultiLanguage
      description: MultiLanguage
    }[]
  ): Promise<Author[]> {
    const names = []
    for (const item of data) {
      const exit = names.find(
        (n) =>
          n.zh_HK === item.name.zh_HK &&
          n.en_uk === item.name.en_uk &&
          item.name.zh_cn === n.zh_cn
      )
      if (!exit) {
        names.push(item)
      }
    }
    const authors = await this.findAuthorsByName(names.map((item) => item.name))

    const notExistAuthors = names.filter(
      (item) =>
        authors.findIndex(
          (a) =>
            a.name.en_uk === item.name.en_uk &&
            a.name.zh_HK === item.name.zh_HK &&
            a.name.zh_cn === item.name.zh_cn
        ) === -1
    )

    await Promise.all(
      authors.map(async (item) => {
        const index = names.findIndex(
          (a) =>
            a.name.en_uk === item.name.en_uk &&
            a.name.zh_HK === item.name.zh_HK &&
            a.name.zh_cn === item.name.zh_cn
        )
        if (
          index !== -1 &&
          (names[index].description.zh_cn ||
            names[index].description.zh_HK ||
            names[index].description.zh_en)
        ) {
          await this.authorRepository.update(
            { id: item.id },
            { description: names[index].description }
          )
        }
      })
    )

    if (notExistAuthors.length) {
      const savedAuthors = await this.batchSaveAuthor(notExistAuthors)
      return authors.concat(savedAuthors)
    }
    return authors
  }

  async patchAuthor(id: number, options: CreateAuthorDto): Promise<Author> {
    const author = await this.authorRepository.findOne({ where: { id  } })
    authorValidator(author).exist()
    if (options.name) {
      const [duplicated] = await this.findAuthorsByName([options.name])
      if (duplicated && duplicated.id !== id) {
        throw new DuplicateAuthorException()
      }
    }
    Object.assign(author, R.pick(['name', 'description', 'profileImage'], options))
    return this.authorRepository.save(author)
  }

  async getAuthor(id: number): Promise<Author> {
    const author = await this.authorRepository.findOne({ where: { id  } })
    authorValidator(author).exist()
    return author
  }

  async getAuthors(ids: number[], withDeleted = false): Promise<Author[]> {
    return this.authorRepository.find({ where: { id: In(ids) }, withDeleted })
  }

  async findAuthor(name: string[]): Promise<Author[]> {
    const alias = 'a'
    const condition = multiNameCondition(name, alias)

    return this.authorRepository
      .createQueryBuilder(alias)
      .where(condition.where, condition.parameter)
      .getMany()
  }

  async findAuthorsByName(names: MultiLanguage[]) {
    const allName = names.map((item) => allNameCondition(item)).join(' OR ')
    return this.authorRepository
      .createQueryBuilder('author')
      .where(allName)
      .getMany()
  }

  async searchAuthor(name: string) {
    const alias = 'a'

    const builder = this.authorRepository.createQueryBuilder(alias)
    if (name) {
      const condition = nameLikeCondition(name, alias)
      builder.where(condition.where, condition.parameter)
    }
    return builder.getMany()
  }

  async getAllAuthor() {
    return this.authorRepository.find()
  }

  async listAuthor(
    query: QueryAuthorDto,
    relations?: string[]
  ): Promise<PageData<Author>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE, name } = query
    const alias = 'a'

    const queryBuilder = this.authorRepository.createQueryBuilder(alias)
    if (query.withDeleted) {queryBuilder.withDeleted()}
    if (relations?.includes('books')) {
      queryBuilder.leftJoinAndSelect(`${alias}.books`, 'books')
    }
    if (relations?.includes('books.publisher')) {
      queryBuilder.leftJoinAndSelect(`books.publisher`, 'publisher')
    }
    if (name) {
      const condition = nameLikeCondition(name, alias)
      queryBuilder.where(condition.where, condition.parameter)
    }

    const total = await queryBuilder.getCount()
    queryBuilder.take(pageSize).skip((pageIndex - 1) * pageSize)
    if (!relations) {queryBuilder.orderBy({ created_at: 'DESC' })}
    const data = await queryBuilder.getMany()

    return { pageIndex, pageSize, total, items: data }
  }

  async listClientAuthor(
    query: SearchAuthorDto,
    schoolId?: number
  ): Promise<PageData<Author>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    let where = `books.status = 'online' and books.deleted_at is null and authors.deleted_at is null `
    if (schoolId)
    {where += `AND (${schoolId} MEMBER OF(\`books\`.\`hidde_school_ids\`)) = 0`}
    if (query.name) {
      where += ` AND (${nameLikeWhere(query.name, 'authors')})`
    }
    const ids = await this.authorRepository.query(
      `select distinct authors.id 
      from authors inner join books_authors_authors on authors.id = books_authors_authors.authors_id 
      inner join books on books_authors_authors.books_id = books.id where ${where} limit ${pageSize} offset ${
  (pageIndex - 1) * pageSize
} `
    )
    const items = await this.authorRepository.find({ where: { id: In(ids) } })
    const [total] = await this.authorRepository.query(
      `select  count(distinct authors.id) as count 
      from authors inner join books_authors_authors on authors.id = books_authors_authors.authors_id 
      inner join books on books_authors_authors.books_id = books.id where ${where} `)

    return { pageIndex, pageSize, total: Number(total.count), items }
  }

  private checkData(author: any, row: number) {
    const fields = []
    if (!author.name || author.name.trim().length === 0) {
      fields.push('姓名')
    }

    return fields.length > 0 ? `第${row}行${fields.join(',')}有錯誤` : undefined
  }
}
