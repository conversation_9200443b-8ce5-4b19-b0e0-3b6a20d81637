import { HttpService } from '@nestjs/axios'
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { snakeCase } from 'lodash'
import moment from 'moment'
import R from 'ramda'
import {
  DataSource,
  DeepPartial, 
  EntityManager,
  FindManyOptions,
  FindOneOptions,
  In,
  Repository,
} from 'typeorm'
import {IBookRepo} from '@/modules/shared/books'
import { BOOK_DELETED_FLAG } from '@/utils/book.utitl'
import { generateUniqueId, RedisService } from '../../../common'
import { Book } from '../../../entities'
import { EBookOrderType, EBookVersion, EUserType } from '../../../enums'
import { PageData } from '../../../interfaces'
import { convertKeyword, detailLikeWhere, nameLikeWhere } from '../../../utils'
import { PAGE_SIZE } from '../../constants'
import { OperationLogService } from '../../system'
import { createdBookFields, QueryAdminReferenceBooksDto, updatedBookFiled } from '../dto'
import { ICountBooks, IKeywordSearchBook } from '../interfaces'
import { getBookVersion } from '../utils/book.util'
import { bookValidator } from '../validators'
import { InitLeaderBoardService } from './initLeaderBoard.service'

@Injectable()
export class BookRepository implements IBookRepo{
  constructor(
    @InjectRepository(Book) private readonly bookModel: Repository<Book>,
    private readonly initLeaderBoardService: InitLeaderBoardService,
    private readonly redisService: RedisService,
    private readonly httpService: HttpService,
    private readonly logService: OperationLogService,
    private readonly dataSource: DataSource
  ) {}

  async find(options?: FindManyOptions<Book>) {
    return this.bookModel.find(options)
  }

  async findOne(options?: FindOneOptions<Book>) {
    return this.bookModel.findOne(options)
  }

  async batchSaveBook(data: DeepPartial<Book>[]): Promise<Book[]> {
    return this.bookModel.save(data)
  }

  async getBook(
    filter: { id?: number; bookId?: string },
    options: { withDeleted?: boolean; relations?: string[] } = { withDeleted: false }
  ): Promise<Book> {
    const {
      relations = [
        'categories',
        // 'category.parent',
        'authors',
        'publisher',
        'labels',
        'bookLevels',
        'chapters',
        'chapters.children',
        'referenceBooks',
        'referenceBooks.school',
      ],
    } = options

    const book = await this.bookModel.findOne({
      where: R.pick(['id', 'bookId'], filter),
      relations,
      withDeleted: options.withDeleted,
    })
    bookValidator(book).exist()

    if (relations.includes('chapters')) {
      const childrenIds = R.flatten(
        book.chapters.map((item) => item.children?.map((c) => c?.id))
      )
      book.chapters = book.chapters.filter((item) => !childrenIds.includes(item.id))
    }

    return book
  }

  async getBookWithoutRelation(
    filter: {
      id?: number
      bookId?: string
      isbn?: string
    },
    options: { withDeleted: boolean } = { withDeleted: false }
  ): Promise<Book> {
    const book = await this.bookModel.findOne({
      where: R.pick(['id', 'bookId', 'isbn', 'version'], filter),
      withDeleted: options.withDeleted,
    })
    bookValidator(book).exist()
    return book
  }

  async searchBookByName(name: string) {
    return this.bookModel.query(`select id from books where ${nameLikeWhere(name)}`)
  }

  async saveBook(data: Partial<Book>): Promise<Book> {
    const existedBook = await this.bookModel.findOne({ where: { isbn: data.isbn } })
    bookValidator(existedBook).duplicate()

    const book = new Book({
      ...R.pick(
        createdBookFields.concat([
          'authorIds',
          'authors',
          'publisher',
          'categories',
          'labels',
          'labelIds',
          'categoryIds',
          'bookLevels',
        ]),
        data
      ),
      bookId: generateUniqueId('bo'),
      hiddeSchoolIds: [],
    })

    return this.bookModel.save(book)
  }

  async updateBookVisible(ids: number[], schoolId: number, isHidden: boolean) {
    const books = await this.searchBooks({ ids }, undefined, {
      fields: ['id', 'hiddeSchoolIds'],
    })
    if (books.length !== ids.length) {
      throw new BadRequestException('wrong book ids')
    }
    return this.bookModel.save(
      books.map((item) => {
        let hiddeSchoolIds
        if (isHidden) {
          hiddeSchoolIds = item.hiddeSchoolIds?.length
            ? [...new Set(item.hiddeSchoolIds.concat([schoolId]))]
            : [schoolId]
        } else {
          hiddeSchoolIds = item.hiddeSchoolIds?.length
            ? item.hiddeSchoolIds.filter((id) => id !== schoolId)
            : []
        }

        return { ...item, hiddeSchoolIds }
      })
    )
  }

  async updateBook(
    id: number,
    data: Partial<Book>,
    manager?: EntityManager
  ): Promise<Book> {
    const book = await this.getBookWithoutRelation({ id })

    const bookVersion = book.version.split(',')

    if (data.version) {
      const [removeVersion] = R.difference(bookVersion, data.version)
      const [addVersion] = R.difference(data.version, bookVersion)

      if (removeVersion) {
        await this.removeBookRelations(
          [id],
          { version: removeVersion as EBookVersion },
          manager
        )

        if (removeVersion === EBookVersion.SUBSCRIPTION) {
          await this.initLeaderBoardService.removeBookFromReadingRanking(id)
        }
        // else {
        //   await this.initLeaderBoardService.removeBooksFromReferenceRanking([id])
        // }
      }

      if (addVersion) {
        if (addVersion === EBookVersion.SUBSCRIPTION) {
          await this.initLeaderBoardService.initBookReadingTime(id)
        } else {
          // todo init reference ranking
        }
      }
    }

    if (data.version) {data.version = getBookVersion(data.version)}

    if (manager) {
      await manager.save(Book, { ...book, ...R.pick(updatedBookFiled, data) })
    } else {
      await this.bookModel.save({
        ...book,
        ...R.pick(updatedBookFiled.concat(['opfUrl', 'hyperlink']), data),
      })
    }
    return this.getBookWithoutRelation({ id })
  }

  async updateBooks(ids: number[], data: Partial<Book>, manager?: EntityManager) {
    const books = await this.bookModel.find({
      where: { id: In(ids) },
      select: ['id', 'version', 'name'],
    })
    // const books = await this.searchBookConditionV2({ ids }, ['id', 'version', 'name'])

    if (books.length !== ids.length) {
      throw new NotFoundException('book not found')
    }

    if (data.version) {
      const versionData = books.map((item) => {
        const bookVersion = item.version.split(',')

        const [removeVersion] = R.difference(bookVersion, data.version)
        const [addVersion] = R.difference(data.version, bookVersion)
        return { removeVersion, addVersion, id: item.id }
      })

      const removeFromSubscription = versionData.filter(
        (item) => item.removeVersion === EBookVersion.SUBSCRIPTION
      )

      const removeFromReference = versionData.filter(
        (item) => item.removeVersion === EBookVersion.REFERENCE
      )

      const addToSubscription = versionData.filter(
        (item) => item.addVersion === EBookVersion.SUBSCRIPTION
      )

      const addToReference = versionData.filter(
        (item) => item.addVersion === EBookVersion.REFERENCE
      )

      if (removeFromSubscription.length) {
        await this.removeBookRelations(
          removeFromSubscription.map((item) => item.id),
          { version: EBookVersion.SUBSCRIPTION },
          manager
        )
        await this.initLeaderBoardService.removeBooksFromReadingRanking(
          removeFromSubscription.map((item) => item.id)
        )
      }

      if (removeFromSubscription.length) {
        await this.removeBookRelations(
          removeFromSubscription.map((item) => item.id),
          { version: EBookVersion.REFERENCE },
          manager
        )

        // await this.initLeaderBoardService.removeBooksFromReferenceRanking(
        //   removeFromReference.map((item) => item.id),
        // )
      }

      if (addToSubscription.length) {
        await this.initLeaderBoardService.initBooksReadingTime(
          addToSubscription.map((item) => item.id)
        )
      }

      if (addToReference.length) {
        // todo init reference ranking
      }
    }

    if (data.version) {
      data.version = getBookVersion(data.version)
    }

    if (manager) {
      await manager.update(
        Book,
        { id: In(ids) },
        R.pick(['status', 'onlineAt', 'offlineAt', 'categories', 'version'], data)
      )
    } else
    {await this.bookModel.update(
      { id: In(ids) },
      R.pick(['status', 'onlineAt', 'offlineAt', 'categories', 'version'], data)
    )}

    return books
  }

  async countBooks(
    query: ICountBooks,
    options?: { version?: EBookVersion; hasScienceRoom?: boolean }
  ): Promise<number> {
    const filter: IKeywordSearchBook = {}
    if (query.authorId) {
      filter.authorId = [query.authorId]
    }

    if (query.publisherId) {
      filter.publisherId = [query.publisherId]
    }

    if (query.labelId) {
      filter.educationLabelId = [query.labelId]
    }

    if (query.categoryId?.length) {
      filter.categoryIds = query.categoryId
    }

    if (query.level) {
      filter.level = [query.level]
    }

    if (options?.version) {
      filter.version = options.version
    }

    if (!R.isNil(options?.hasScienceRoom)) {
      filter.isScienceRoom = options.hasScienceRoom
    }

    const builder = await this.searchBookConditionV2(filter)
    return builder.getCount()
  }

  async listBooks(
    filter: { ids: number[] },
    options?: {
      relations?: string[]
      fields?: string[]
      withDeleted?: boolean
      referenceSchoolId?: number
    }
  ) {
    if (!filter.ids || filter.ids?.length === 0) {return []}

    const builder = this.bookModel.createQueryBuilder('book')
    const {
      relations = ['authors', 'publisher', 'labels', 'categories', 'bookLevels'],
      fields = [
        'id',
        'bookId',
        'name',
        'description',
        'language',
        'status',
        'size',
        'isbn',
        'url',
        'coverUrl',
        'publishedAt',
        'opfUrl',
        'level',
        'publisherGroupName',
        'publisherAddress',
        'offlineAt',
        'onlineAt',
        'createdAt',
        'hiddeSchoolIds',
        'locations',
        'version',
        'price',
        'currency',
        'isScienceRoom',
      ],
      withDeleted = false,
    } = options || {}

    const relationFields = []

    if (relations.includes('authors')) {
      builder.leftJoin('book.authors', 'authors')
      relationFields.push(...['authors.id', 'authors.name', 'authors.description'])
    }

    if (relations.includes('publisher')) {
      builder.leftJoin('book.publisher', 'publisher')
      relationFields.push(
        ...[
          'publisher.id',
          'publisher.name',
          'publisher.publisherGroupName',
          'publisher.description',
        ]
      )
    }

    if (relations.includes('labels')) {
      builder.leftJoin('book.labels', 'labels')
      relationFields.push(...['labels.id', 'labels.name', 'labels.type'])
    }

    if (relations.includes('categories')) {
      builder.leftJoin('book.categories', 'categories')
      relationFields.push(...['categories.id', 'categories.name'])
    }

    if (relations.includes('bookLevels')) {
      builder.leftJoin('book.bookLevels', 'bookLevels')
      relationFields.push(...['bookLevels.id', 'bookLevels.name'])
    }

    if (relations.includes('referenceBooks')) {
      builder.leftJoin(
        'book.referenceBooks',
        'referenceBooks',
        options.referenceSchoolId
          ? 'referenceBooks.school_id = :referenceSchoolId'
          : undefined,
        { referenceSchoolId: options.referenceSchoolId }
      )
      relationFields.push(...['referenceBooks.id', 'referenceBooks.copiesCount'])
    }

    builder.where('book.id IN (:...ids)', { ids: filter.ids })

    builder.select(fields.map((item) => `book.${item}`))

    builder.addSelect(relationFields)

    if (withDeleted) {builder.withDeleted()}
    return builder.getMany()
  }

  //获取热门书籍对应的level bookid
  async getLevelHotBookIds(
    level: number[] | undefined,
    allIds: number[],
    skip: number,
    pageSize: number,
    schoolId: number,
    isTeacher: boolean
  ) {
    const levelArray = Array.isArray(level) ? level : []

    const levelConditions =
      levelArray.length > 0
        ? levelArray.map(() => `JSON_CONTAINS(books.level, ?)`).join(' OR ')
        : ''

    // 学生过滤隐藏书籍的
    const isHiddenConditions = isTeacher
      ? ''
      : ` (${schoolId} MEMBER OF(\`hidde_school_ids\`)) = 0`
    const sql = `
    SELECT id 
    FROM books 
    WHERE books.id IN (${allIds.map(() => '?').join(',')}) 
      ${levelConditions ? `AND (${levelConditions})` : ''}
      ${isHiddenConditions ? `AND (${isHiddenConditions})` : ''}
    ORDER BY FIELD(books.id, ${allIds.join(',')})
    LIMIT ?, ?
  `

    const parameters = [
      ...allIds,
      ...levelArray.map((levelValue) => JSON.stringify(levelValue)),
      skip,
      pageSize,
    ]

    const results = await this.bookModel.query(sql, parameters)

    return results.map((result) => result.id)
  }

  async listReferenceBooks(
    schoolId: number,
    query?: QueryAdminReferenceBooksDto,
    options?: { relations?: string[]; fields?: string[] }
  ) {
    const builder = await this.searchBookConditionV2(
      {
        keyword: query?.keyword,
        version: EBookVersion.REFERENCE,
        referenceSchoolId: schoolId,
      },
      { withDeleted: true }
    )

    const total = await builder.getCount()

    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    if (total === 0) {return { total, items: [], pageIndex, pageSize }}

    const bookIds = await builder
      .take(pageSize)
      .skip((pageIndex - 1) * pageSize)
      .getMany()

    const books = await this.listBooks(
      { ids: bookIds.map((item) => item.id) },
      {
        withDeleted: true,
        relations: options.relations,
        fields: options.fields,
        referenceSchoolId: schoolId,
      }
    )

    return {
      total,
      items: bookIds.map((book) => ({
        ...book,
        ...(books.find((item) => item.id === book.id) || {}),
      })),
      pageIndex,
      pageSize,
    }
  }

  async deleteBook(ids: number[], user: any, manager?: EntityManager) {
    if (ids.length === 0) {return}

    if (manager) {
      await this.deleteBookWithReferences(ids, undefined, manager)
    } else {
      await this.dataSource.transaction(
        async (manager) => await this.deleteBookWithReferences(ids, undefined, manager)
      )
    }

    await this.initLeaderBoardService.removeBooksFromReadingRanking(ids)

    const books = await this.bookModel.find({
      where: { id: In(ids.slice(0, 3)) },
      select: ['id', 'name'],
    })
    await this.logService.createLog({
      user,
      operation: `${ids.length > 3 ? `批量删除` : '删除書籍'}${books
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${ids.length > 3 ? `等${ids.length}本書籍` : ''}`,
      metaData: { id: ids },
    })

    return books
  }

  async findBooks(options: {
    ids?: number[]
    authorIds?: number[]
    publisherId?: number[]
    categoryIds?: number[]
    labelId?: number
    isbn?: string[]
    level?: number
    withDeleted?: boolean
  }): Promise<Book[]> {
    const builder = this.bookModel.createQueryBuilder('book')
    if (options.withDeleted) {builder.withDeleted()}
    if (options.authorIds?.length) {
      builder.innerJoinAndSelect(
        'book.authors',
        'authors',
        'authors.id IN (:...authorIds)',
        {
          authorIds: options.authorIds,
        }
      )
    } else {builder.leftJoinAndSelect('book.authors', 'authors')}

    if (options.publisherId) {
      builder.innerJoin(
        'book.publisher',
        'publisher',
        'publisher.id IN (:...publisherId)',
        { publisherId: options.publisherId }
      )
    } else {
      builder.leftJoinAndSelect('book.publisher', 'publisher')
    }

    if (options.categoryIds?.length) {
      builder.innerJoin(
        'book.categories',
        'categories',
        'categories.id IN (:...categoryIds)',
        {
          categoryIds: options.categoryIds,
        }
      )
    }

    if (options.labelId) {
      builder.innerJoinAndSelect('book.labels', 'labels', 'labels.id = :labelId', {
        labelId: options.labelId,
      })
    }
    const where: any = {}
    if (options.ids?.length) {
      where.id = In(options.ids)
    }
    if (options.isbn?.length) {
      where.isbn = In(options.isbn)
    }

    if (options.level) {
      builder.innerJoin('book.bookLevels', 'bookLevels', 'bookLevels.id = :level', {
        level: options.level,
      })
    }

    return builder.where(where).getMany()
  }

  async searchRandomBooksByPage(
    query: IKeywordSearchBook,
    filter: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    } = {},
    options: {
      seed?: number
    } = {}
  ): Promise<PageData<Book>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const { seed } = options

    const builder = await this.searchBookConditionV2Client(query, filter)

    const total = await builder.getCount()

    let items = []

    if (total > 0) {
      const books = await builder
        .orderBy(`rand(${seed ? seed : ''})`)
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize)
        .getMany()

      const noSortBooks = await this.listBooks(
        { ids: books.map((item) => item.id) },
        {
          withDeleted: filter.withDeleted,
          fields: [
            'id',
            'bookId',
            'name',
            'coverUrl',
            'isbn',
            'description',
            'level',
            'hiddeSchoolIds',
          ],
          relations: ['authors', 'labels', 'categories'],
        }
      )
      items = books.map((item) => noSortBooks.find((book) => book.id === item.id))
    }

    return { pageIndex, pageSize, total, items }
  }

  async searchBooksByPage(
    query: IKeywordSearchBook,
    filter: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    } = {},
    options: { fields?: string[]; relations?: string[] } = {}
  ): Promise<PageData<Book> & { allBookIds: number[] }> {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const builder = await this.searchBookConditionV2(query, filter)

    const total = await builder.getCount()
    let items = []
    let allBookIds = []

    if (total > 0) {
      const books = await builder
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize)
        .getMany()

      allBookIds = books.map((item) => item.id)
      const noSortBooks = await this.listBooks(
        { ids: allBookIds },
        {
          withDeleted: filter.withDeleted,
          fields: options.fields,
          relations: options.relations,
        }
      )
      items = books.map((item) => noSortBooks.find((book) => book.id === item.id))
    }

    return {
      pageIndex,
      pageSize,
      total,
      items,
      allBookIds,
    }
  }

  async searchBooks(
    query: IKeywordSearchBook,
    filter: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    } = {},
    options: { fields?: string[]; relations?: string[] } = {}
  ) {
    const { fields = [], relations = [] } = options
    const builder = await this.searchBookConditionV2(
      query,
      filter,
      relations.length === 0 ? fields : []
    )

    const data = await builder.getMany()

    if (relations.length === 0 || data.length === 0) {return data}

    const noSortBooks = await this.listBooks(
      { ids: data.map((item) => item.id) },
      { withDeleted: filter.withDeleted, fields, relations }
    )
    return data.map((item) => noSortBooks.find((book) => book.id === item.id))
  }

  async deleteBooksOnPublisherDeleted(publisherIds: number[]) {
    const books = await this.bookModel.find({
      where: {
        publisher: {
          id: In(publisherIds),
        },
      },
      select: ['id'],
    })
    return this.deleteBookWithReferences(books.map((item) => item.id))
  }

  async deleteBookWithReferences(
    books: number[],
    options?: { version?: EBookVersion },
    entityManager?: EntityManager
  ) {
    const manager = entityManager || this.bookModel.manager
    const now = moment().unix()
    await manager.query(
      `update books set isbn = CONCAT(isbn, '${BOOK_DELETED_FLAG}${now}'), status = 'offline', category_ids = null, deleted_at = NOW() where id IN (${books.join(
        ','
      )})`
    )
    await this.removeBookRelations(books, options, manager)
  }

  // 订阅版
  async removeBookRelationsForSchool(
    schoolId: number,
    excludes: number[],
    userType: EUserType,
    entityManager?: EntityManager
  ) {
    const manager = entityManager || this.bookModel.manager

    const users = await manager.query(
      `select id from users where school_id = ${schoolId} and type = '${userType}'`
    )

    await manager.query(
      `delete from bookshelves where book_id NOT IN (${excludes.join(
        ','
      )}) and version = '${EBookVersion.SUBSCRIPTION}' and user_id IN (${users
        .map((item) => item.id)
        .join(',')})`
    )

    const schoolHomepages = await manager.query(
      `select id from school_homepage where version = '${
        EBookVersion.SUBSCRIPTION
      }' and school_id = ${schoolId} and type ${
        userType === EUserType.STUDENT ? '= ' : '!='
      } '${EUserType.STUDENT}'`
    )

    if (schoolHomepages.length)
    {await manager.query(
      `
        delete from
          books_school_homepages_school_homepage 
        where
          books_id NOT IN (${excludes.join(',')}) 
          and school_homepage_id in (${schoolHomepages
    .map((item) => item.id)
    .join(',')})`
    )}

    await this.deleteEmptyBookListAndHomepage(manager)
  }

  async removeBookRelations(
    books: number[],
    options: { version?: EBookVersion } = {},
    entityManager?: EntityManager
  ) {
    if (!books || books.length <= 0) {return}

    const manager = entityManager || this.bookModel.manager

    const { version } = options

    if (R.isNil(version) || version === EBookVersion.SUBSCRIPTION) {
      await manager.query(
        `delete from books_book_lists_book_lists where books_id IN (${books.join(',')})`
      )

      await manager.query(
        `delete from books_homepages_homepage where books_id IN (${books.join(',')})`
      )
    }

    // 将书籍的版本从参考馆中移除时，参考馆书架，推荐书籍不受影响
    if (version === EBookVersion.SUBSCRIPTION) {
      await manager.query(
        `delete from bookshelves where book_id IN (${books.join(',')}) and version = '${
          EBookVersion.SUBSCRIPTION
        }'`
      )
      const schoolHomepages = await manager.query(
        `select id from school_homepage where version = '${version}'`
      )

      if (schoolHomepages.length)
      {await manager.query(
        `
        delete from
          books_school_homepages_school_homepage 
        where
          books_id IN (${books.join(',')}) 
          ${
  R.isNil(version)
    ? ''
    : `and school_homepage_id in (${schoolHomepages
      .map((item) => item.id)
      .join(',')})`
}
      `
      )}
    }

    await this.deleteEmptyBookListAndHomepage(manager)
  }

  async searchBookConditionV2(
    query: IKeywordSearchBook,
    filter: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    } = {},
    fields: string[] = []
  ) {
    const { keyword, orderBy = EBookOrderType.NEWEST } = query

    const builder = this.bookModel.createQueryBuilder('book')

    if (query.authorId?.length) {
      builder.innerJoin('book.authors', 'authors', 'authors.id IN (:...authorIds)', {
        authorIds: query.authorId,
      })
    } else if (filter.authorIds?.length) {
      builder.leftJoin('book.authors', 'authors')
    }

    if (query.publisherId?.length) {
      builder.innerJoin(
        'book.publisher',
        'publisher',
        'publisher.id IN (:...publisherId)',
        { publisherId: query.publisherId }
      )
    } else if (filter.publisherIds?.length) {
      builder.leftJoin('book.publisher', 'publisher')
    }

    if (query.categoryIds?.length) {
      builder.innerJoin(
        'book.categories',
        'categories',
        'categories.id IN (:...categoryIds)',
        { categoryIds: query.categoryIds }
      )
    }

    if (query.categoryLabelId?.length) {
      builder.innerJoin('book.labels', 'cLabels', 'cLabels.id IN (:...categoryLabelId)', {
        categoryLabelId: query.categoryLabelId,
      })
    }

    if (query.educationLabelId?.length) {
      builder.innerJoin('book.labels', 'labels', 'labels.id IN (:...educationLabelIds)', {
        educationLabelIds: query.educationLabelId,
      })
    }

    if (query.labelId?.length) {
      builder.innerJoin('book.labels', 'ALLlabels', 'ALLlabels.id IN (:...labelId)', {
        labelId: query.labelId,
      })
    }

    if (query.level?.length) {
      builder.innerJoin('book.bookLevels', 'bookLevels', 'bookLevels.id IN (:...level)', {
        level: query.level,
      })
    }

    if (query.version === EBookVersion.REFERENCE && query.referenceSchoolId) {
      builder.innerJoin(
        'book.referenceBooks',
        'referenceBooks',
        'referenceBooks.school_id = :schoolId',
        {
          schoolId: query.referenceSchoolId,
        }
      )
    }

    if (query.schoolId && orderBy === EBookOrderType.TIMES_OF_SCHOOL_READING) {
      builder.leftJoin(
        'book.readingTime',
        'readingTime',
        'readingTime.schoolId = :schoolId',
        {
          schoolId: query.schoolId,
        }
      )
    }

    if (filter.withDeleted) {builder.withDeleted()}

    const condition = []

    if (keyword) {
      condition.push(
        `
        (
          (${nameLikeWhere(keyword, 'book')}
          or isbn LIKE '%${convertKeyword(keyword)}%'
          or ${detailLikeWhere(keyword, 'book')} COLLATE 'utf8mb4_unicode_ci')
          ${
  filter.authorIds?.length
    ? `or authors.id IN (${filter.authorIds.join(',')})`
    : ''
} 
          ${
  filter.publisherIds?.length
    ? `or publisher.id IN (${filter.publisherIds.join(',')})`
    : ''
}
        )
        `
      )
    }

    if (query.schoolId && !R.isNil(query.isHidden)) {
      condition.push(
        `(${query.schoolId} MEMBER OF(\`hidde_school_ids\`)) = ${
          query.isHidden ? 1 : 0
        } `
      )
    }

    if (query.status) {
      condition.push(`book.status = '${query.status}'`)
    }

    if (
      query.version &&
      !(query.version === EBookVersion.REFERENCE && query.referenceSchoolId)
    ) {
      condition.push(`book.version like '%${query.version}%'`)
    }

    if (query.language) {
      condition.push(`book.language In ('${query.language}', 'zh_en')`)
    }

    if (query.isbn?.length) {
      condition.push(`book.isbn IN (:...isbn)`)
    }

    if (query.ids?.length) {
      condition.push(`book.id IN (:...ids)`)
    }

    if (query.excludedIds?.length) {
      condition.push(`book.id NOT IN (:...excludedIds)`)
    }

    if (!R.isNil(query.isScienceRoom)) {
      condition.push(`book.is_science_room = ${query.isScienceRoom}`)
    }

    if (condition.length)
    {builder.where(condition.join(' AND '), {
      isbn: query.isbn,
      ids: query.ids,
      excludedIds: query.excludedIds,
    })}

    const extraFields = []
    if (orderBy === EBookOrderType.TIMES_OF_SCHOOL_READING && query.schoolId) {
      builder.orderBy('readingTime.count', 'DESC')
      extraFields.push('readingTime.count')
    } else {
      builder.orderBy('book.createdAt', 'DESC')
      fields.push('createdAt')
    }

    const relationIds = ['publisherId']
    const relationIdFields = R.intersection(fields, relationIds)

    let select = ['id']
    select = select
      .concat(fields)
      .filter((field) => !relationIds.includes(field))
      .map((field) => `book.${field}`)
      .concat(extraFields)

    if (relationIdFields.length) {
      select = select.concat(relationIdFields.map((field) => `book.${snakeCase(field)}`))
    }

    builder.select(select)

    return builder
  }

  async searchBookConditionV2Client(
    query: IKeywordSearchBook,
    filter: {
      authorIds?: number[]
      publisherIds?: number[]
      withDeleted?: boolean
    } = {},
    fields: string[] = []
  ) {
    const { keyword, orderBy = EBookOrderType.NEWEST } = query

    const builder = this.bookModel.createQueryBuilder('book')

    if (query.authorId?.length) {
      builder.innerJoin('book.authors', 'authors', 'authors.id IN (:...authorIds)', {
        authorIds: query.authorId,
      })
    } else if (filter.authorIds?.length) {
      builder.leftJoin('book.authors', 'authors')
    }

    if (query.publisherId?.length) {
      builder.innerJoin(
        'book.publisher',
        'publisher',
        'publisher.id IN (:...publisherId)',
        { publisherId: query.publisherId }
      )
    } else if (filter.publisherIds?.length) {
      builder.leftJoin('book.publisher', 'publisher')
    }

    if (query.categoryIds?.length) {
      builder.innerJoin(
        'book.categories',
        'categories',
        'categories.id IN (:...categoryIds)',
        { categoryIds: query.categoryIds }
      )
    }

    if (query.categoryLabelId?.length) {
      builder.innerJoin('book.labels', 'cLabels', 'cLabels.id IN (:...categoryLabelId)', {
        categoryLabelId: query.categoryLabelId,
      })
    }

    if (query.educationLabelId?.length) {
      builder.innerJoin('book.labels', 'labels', 'labels.id IN (:...educationLabelIds)', {
        educationLabelIds: query.educationLabelId,
      })
    }

    if (query.labelId?.length) {
      builder.innerJoin('book.labels', 'ALLlabels', 'ALLlabels.id IN (:...labelId)', {
        labelId: query.labelId,
      })
    }

    if (query.level?.length) {
      builder.innerJoin('book.bookLevels', 'bookLevels', 'bookLevels.id IN (:...level)', {
        level: query.level,
      })
    }

    if (query.version === EBookVersion.REFERENCE && query.referenceSchoolId) {
      builder.innerJoin(
        'book.referenceBooks',
        'referenceBooks',
        'referenceBooks.school_id = :schoolId',
        {
          schoolId: query.referenceSchoolId,
        }
      )
    }

    if (query.schoolId && orderBy === EBookOrderType.TIMES_OF_SCHOOL_READING) {
      builder.leftJoin(
        'book.readingTime',
        'readingTime',
        'readingTime.schoolId = :schoolId',
        {
          schoolId: query.schoolId,
        }
      )
    }

    if (filter.withDeleted) {builder.withDeleted()}

    const condition = []
    if (keyword) {
      const bookIds = await this.redisService.ftSearch(
        'booksKeywordIndex',
        keyword,
        'NOCONTENT'
      )
      if (bookIds.length === 0) {
        return builder.where('1 = 0')
      }
      condition.push(`book.id IN (${bookIds.map((id) => `${id}`).join(', ')})`)
    }

    if (query.schoolId && !R.isNil(query.isHidden)) {
      condition.push(
        `(${query.schoolId} MEMBER OF(\`hidde_school_ids\`)) = ${
          query.isHidden ? 1 : 0
        } `
      )
    }

    if (query.status) {
      condition.push(`book.status = '${query.status}'`)
    }

    if (
      query.version &&
      !(query.version === EBookVersion.REFERENCE && query.referenceSchoolId)
    ) {
      condition.push(`book.version like '%${query.version}%'`)
    }

    if (query.language) {
      condition.push(`book.language In ('${query.language}', 'zh_en')`)
    }

    if (query.isbn?.length) {
      condition.push(`book.isbn IN (:...isbn)`)
    }

    if (query.ids?.length) {
      condition.push(`book.id IN (:...ids)`)
    }

    if (query.excludedIds?.length) {
      condition.push(`book.id NOT IN (:...excludedIds)`)
    }

    if (!R.isNil(query.isScienceRoom)) {
      condition.push(`book.is_science_room = ${query.isScienceRoom}`)
    }

    if (condition.length)
    {builder.where(condition.join(' AND '), {
      isbn: query.isbn,
      ids: query.ids,
      excludedIds: query.excludedIds,
    })}

    const extraFields = []
    if (orderBy === EBookOrderType.TIMES_OF_SCHOOL_READING && query.schoolId) {
      builder.orderBy('readingTime.count', 'DESC')
      extraFields.push('readingTime.count')
    } else {
      builder.orderBy('book.createdAt', 'DESC')
      fields.push('createdAt')
    }

    const relationIds = ['publisherId']
    const relationIdFields = R.intersection(fields, relationIds)

    let select = ['id']
    select = select
      .concat(fields)
      .filter((field) => !relationIds.includes(field))
      .map((field) => `book.${field}`)
      .concat(extraFields)

    if (relationIdFields.length) {
      select = select.concat(relationIdFields.map((field) => `book.${snakeCase(field)}`))
    }

    builder.select(select)

    return builder
  }

  private async deleteEmptyBookListAndHomepage(manager: EntityManager) {
    await manager.query(`
      delete from
        book_lists
      where
        id In(
          select
            id
          from
            (
              select
                book_lists.id,
                IFNULL(t.books, 0) as books
              from
                book_lists
                left join (
                  select
                    count(*) as books,
                    book_lists_id as id
                  from
                    books_book_lists_book_lists
                  group by
                    book_lists_id
                ) as t on t.id = book_lists.id
            ) as list
          where
            books = 0
        );
    `)

    await manager.query(`
      delete from
        school_homepage
      where
        id In(
          select
            id
          from
            (
              select
                school_homepage.id,
                IFNULL(t.books, 0) as books
              from
                school_homepage
                left join (
                  select
                    count(*) as books,
                    school_homepage_id as id
                  from
                    books_school_homepages_school_homepage
                  group by
                    school_homepage_id
                ) as t on t.id = school_homepage.id
              where
                school_homepage.type is not null
            ) as list
          where
            books = 0
        );
    `)

    await manager.query(`
      delete from
        homepage
      where
        id in(
          select
            id
          from
            (
              select
                homepage.id,
                IFNULL(t.books, 0) as books,
                ifnull(t1.lists, 0) as lists
              from
                homepage
                left join (
                  select
                    count(*) as books,
                    homepage_id as id
                  from
                    books_homepages_homepage
                  group by
                    homepage_id
                ) as t on t.id = homepage.id
                left join(
                  select
                    count(*) as lists,
                    homepage_id as id
                  from
                    book_lists
                  group by
                    homepage_id
                ) as t1 on t1.id = homepage.id
              where
                homepage.is_permanent = 0
            ) as t2
          where
            books = 0
            and lists = 0
        );
    `)
  }
}
