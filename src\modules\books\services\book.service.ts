import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Decimal from 'decimal.js'
import moment from 'moment'
import path from 'path'
import R from 'ramda'
import { In, Repository } from 'typeorm'
import { encrypt, generateUniqueId, PageRequest, RedisService } from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { Book, BookOperateApplication, School } from '@/entities'
import { BookType, bookTypeValues, EBookVersion, OnlineOfflineStatus } from '@/enums'
import { UnsupportedBookFileException } from '@/modules/exception'
import { OperationLogService } from '@/modules/system'
import { getMultiLanuageKey } from '@/utils'
import { PageData } from '@/interfaces'
import { PAGE_SIZE, S3_BOOK_ORIGNAL_DIR } from '../../constants'
import {
  CreateBookDto,
  ListBookOperateApplicationRequest,
  PatchBookOperateApplicationRequest,
  UpdateBookDto,
  UploadSignalBookDto,
} from '../dto'
import { IKeywordSearchBook } from '../interfaces'
import { convertCodeToLanguage } from '../utils'
import { getBookVersion } from '../utils/book.util'
import { AuthorService } from './author.service'
import { BookRepository } from './book.repository'
import { BookLevelService } from './bookLevel.service'
import { BookS3Service } from './bookS3.service'
import { CategoryService } from './category.service'
import { LabelService } from './label.service'
import { PublisherService } from './publisher.service'

@Injectable()
export class BookService {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly s3Service: BookS3Service,
    private readonly taskService: TaskService,
    private readonly authorService: AuthorService,
    private readonly categoryService: CategoryService,
    private readonly publisherService: PublisherService,
    private readonly labelService: LabelService,
    private readonly bookLevelService: BookLevelService,
    @InjectRepository(BookOperateApplication)
    private readonly bookOperateApplicationRepository: Repository<BookOperateApplication>,
    private readonly redisService: RedisService,
    private readonly logService: OperationLogService
  ) {}

  async findBookTitles(ids: number[], limit = 3) {
    const books = await this.bookRepositories.find({
      where: { id: In(ids) },
      select: ['id', 'name'],
      take: limit,
    })
    return books.map((x) => `《${x.name.zh_HK}》`).join('')
  }

  async saveBook(data: CreateBookDto): Promise<Book> {
    const book: Partial<Book> = {}
    if (data.authorId?.length) {
      book.authorIds = data.authorId
      book.authors = await this.authorService.getAuthors(data.authorId)
      if (data.authorDescription && data.authorId.length === 1) {
        await Promise.all(
          data.authorId.map(async (id) => {
            await this.authorService.patchAuthor(id, {
              description: getMultiLanuageKey(data.authorDescription),
            })
          })
        )
      }
    }
    if (data.publisherId) {
      book.publisher = await this.publisherService.getPublisher({ id: data.publisherId })
    }
    if (data.categoryId?.length) {
      book.categories = await this.categoryService.getCategories(data.categoryId)
      book.categoryIds = book.categories?.map((item) => item.id)
    }
    if (data.labelIds?.length) {
      book.labelIds = data.labelIds
      book.labels = await this.labelService.getLabels(data.labelIds)
    }

    if (data.level?.length) {
      book.bookLevels = await this.bookLevelService.getByIds(data.level)
      book.level = book.bookLevels?.map((item) => item.id)
    }

    if (data.version) {
      book.version = getBookVersion(data.version)
    }

    if (data.isScienceRoom) {
      book.isScienceRoom = data.isScienceRoom
    }

    const lan = convertCodeToLanguage(data.language)
    const res = await this.bookRepositories.saveBook({
      ...R.omit(['categoryId', 'publisherId', 'authorId', 'level'], data),
      ...book,
      language: lan.name ? data.language : null,

      status: OnlineOfflineStatus.OFFLINE,
    })
    await this.taskService.deliver(
      ETaskType.PARSE_BOOK_TASK,
      { bookId: res.id },
      { delay: 1000 }
    )
    return res
  }

  async createUpdateBookOperateLog(originalBook: Book, newBook: Book, admin: any) {
    if (originalBook.version !== newBook.version) {
      const isSubscription = newBook.version.indexOf(EBookVersion.SUBSCRIPTION) !== -1
      const isReference = newBook.version.indexOf(EBookVersion.REFERENCE) !== -1
      const isBoth = isSubscription && isReference
      if (isBoth)
      {await this.logService.createLog({
        user: admin,
        operation: `把《${newBook.name.zh_HK}》書籍設置為參考館/訂閱版書籍。`,
        metaData: { bookId: newBook.id },
      })}
      else {
        if (isSubscription)
        {await this.logService.createLog({
          user: admin,
          operation: `把《${newBook.name.zh_HK}》書籍設置為訂閱版書籍。`,
          metaData: { bookId: newBook.id },
        })}

        if (isReference)
        {await this.logService.createLog({
          user: admin,
          operation: `把《${newBook.name.zh_HK}》書籍設置為參考館書籍。`,
          metaData: { bookId: newBook.id },
        })}
      }
    }

    if (originalBook.price !== newBook.price) {
      await this.logService.createLog({
        user: admin,
        operation: `把《${newBook.name.zh_HK}》書籍價格設置為${new Decimal(newBook.price)
          .div(100)
          .toNumber()}元。`,
        metaData: { bookId: newBook.id, price: newBook.price },
      })
    }
  }

  async updateBook(id: number, data: UpdateBookDto, admin: any) {
    const book: Partial<Book> = {}
    if (data.authorId?.length) {
      book.authorIds = data.authorId
      book.authors = await this.authorService.getAuthors(data.authorId)
      if (data.authorDescription && data.authorId.length === 1) {
        await Promise.all(
          data.authorId.map(async (id) => {
            await this.authorService.patchAuthor(id, {
              description: getMultiLanuageKey(data.authorDescription),
            })
          })
        )
      }
    }
    if (data.publisherId) {
      book.publisher = await this.publisherService.getPublisher({ id: data.publisherId })
    }
    if (data.categoryId?.length) {
      book.categories = await this.categoryService.getCategories(data.categoryId)
      book.categoryIds = book.categories.map((item) => item.id)
    } else {
      book.categories = null
      book.categoryIds = []
    }
    if (data.labelIds?.length) {
      book.labelIds = data.labelIds
      book.labels = await this.labelService.getLabels(data.labelIds)
    } else {
      book.labelIds = []
      book.labels = null
    }

    if (data.level?.length) {
      book.bookLevels = await this.bookLevelService.getByIds(data.level)
      book.level = book.bookLevels?.map((item) => item.id)
    }

    const editBook = await this.bookRepositories.getBookWithoutRelation({ id })
    let language = editBook.language
    if (data.language && convertCodeToLanguage(data.language).name) {
      language = data.language
    }

    const originalBook = await this.bookRepositories.getBook({ id })

    await this.bookRepositories.updateBook(id, {
      ...R.omit(['categoryId', 'publisherId', 'authorId', 'bookId'], data),
      ...book,
      language,
    })

    const newBook = await this.bookRepositories.getBook({ id })

    await this.createUpdateBookOperateLog(originalBook, newBook, admin)
    if (data.url && data.url !== editBook.url) {
      await this.taskService.deliver(
        ETaskType.PARSE_BOOK_TASK,
        { bookId: id },
        { delay: 1000 }
      )
    }
    return this.bookRepositories.getBook({ id })
  }

  async updateBookVisible(ids: number[], schoolId: number, isHidden: boolean) {
    const books = await this.bookRepositories.updateBookVisible(ids, schoolId, isHidden)
    return books
  }

  async uploadSignalBook(file: Express.Multer.File): Promise<UploadSignalBookDto> {
    const suffixName = path.extname(file.originalname)
    if (!bookTypeValues.includes(suffixName.split('.').pop() as BookType)) {
      throw new UnsupportedBookFileException()
    }
    const size = Math.floor((file.size / (1024 * 1024)) * 100) / 100 + 'MB'
    const bookId = generateUniqueId('BOOK_')

    // const isPdf = file.mimetype === 'application/pdf'
    const buffer = encrypt(process.env.AES_KEY, file.buffer)
    // const buffer = file.buffer

    // const dBuffer =
    //   file.mimetype === 'application/pdf' ? decrypt(process.env.AES_KEY, buffer) : buffer

    // fs.writeFile(file.originalname, dBuffer)
    const url = await this.s3Service.upload({
      fileName: bookId + suffixName,
      file: buffer,
      path: S3_BOOK_ORIGNAL_DIR,
    })

    const orignalPdf = await this.s3Service.upload({
      fileName: '1' + bookId + suffixName,
      file: file.buffer,
      path: S3_BOOK_ORIGNAL_DIR,
    })
    console.log({ orignalPdf })

    return {
      bookId,
      url,
      size,
    }
  }

  async searchRandomBooksByKeyword(
    query: IKeywordSearchBook,
    options: {
      userId: number
      withDeleted?: boolean
    }
  ): Promise<PageData<Book>> {
    console.log('searchRandomBooksByKeyword query:', query)
    const filter: any = { withDeleted: options.withDeleted }
    if (query.keyword) {
      query.keyword = String(query.keyword).trim()
    }
    // todo should in specific services
    if (query.keyword && query.isRealTimeSearch) {
      await this.taskService.deliver(
        ETaskType.HOT_SEARCH_WORD_TASK,
        { keyword: query.keyword },
        { delay: 1000 }
      )
    }

    const { pageIndex = 1, pageSize = PAGE_SIZE, ...rst } = query
    const { userId } = options

    const seedCacheKey = `searchBooks.seed.${userId}`

    const cacheSeed = await this.redisService.get(seedCacheKey)
    let seed = Number(cacheSeed || 0)
    if (!cacheSeed || pageIndex === 1) {
      seed = moment().unix()
      await this.redisService.set(seedCacheKey, seed, 1800)
    }

    const data = await this.bookRepositories.searchRandomBooksByPage(
      { pageIndex, pageSize, ...rst },
      filter,
      {
        seed,
      }
    )
    return data
  }

  async searchKeyword(
    query: IKeywordSearchBook,
    options: {
      userId: number
      withDeleted?: boolean
    }
  ) {
    console.log('searchKeyword query:', query)
    if (!query.keyword) {
      return []
    }
    // if (query.keyword && query.isRealTimeSearch) {
    //   await this.taskService.deliver(
    //     ETaskType.HOT_SEARCH_WORD_TASK,
    //     { keyword: query.keyword },
    //     { delay: 1000 },
    //   )
    // }
    query.keyword = String(query.keyword).trim()
    const data = await this.redisService.ftSearch(
      'booksKeywordIndex',
      query.keyword,
      'WITH_FIELDS'
    )
    const filteredBooks = data.filter((book) => {
      if (!book) {return false}
      if (
        query.schoolId &&
        !R.isNil(query.isHidden) &&
        book.hidde_school_ids?.includes(query.schoolId)
      ) {
        return false
      }
      if (query.status && book.status !== query.status) {
        return false
      }
      if (query.version && !book.version.split(',').includes(query.version)) {
        return false
      }
      if (query.level && !query.level.some((level) => book.level.includes(level))) {
        return false
      }
      return true
    })
    return filteredBooks || []
  }

  async searchBooksByPage(
    query: IKeywordSearchBook,
    options: { withDeleted: boolean } = { withDeleted: false }
  ): Promise<
    PageData<Book> & {
      allBookIds: number[]
    }
  > {
    console.log('searchBooksByKeyword query:', query)
    const filter: any = { withDeleted: options.withDeleted }
    if (query.keyword) {
      query.keyword = String(query.keyword).trim()
    }
    if (query.keyword) {
      const [authors, publishers] = await Promise.all([
        this.authorService.searchAuthor(query.keyword),
        this.publisherService.searchPublisher(query.keyword),
      ])
      Object.assign(filter, {
        authorIds: authors.map((item) => item.id),
        publisherIds: publishers.map((item) => item.id),
      })
    }

    // if (query.categoryIds?.length) {
    //   filter.categoryIds = query.categoryIds
    // }

    if (query.keyword && query.isRealTimeSearch) {
      await this.taskService.deliver(
        ETaskType.HOT_SEARCH_WORD_TASK,
        { keyword: query.keyword },
        { delay: 1000 }
      )
    }
    const data = await this.bookRepositories.searchBooksByPage(query, filter)
    return data
  }

  async searchBookIds(
    query: IKeywordSearchBook,
    options: { withDeleted: boolean } = { withDeleted: false }
  ): Promise<number[]> {
    const filter: any = { withDeleted: options.withDeleted }
    if (query.parentCategoryId) {
      query.categoryIds = [query.parentCategoryId]
    }

    const builder = await this.bookRepositories.searchBookConditionV2(query, filter)
    const data = await builder.getMany()
    return data.map((item) => item.id)
  }

  async createBookOperateApplication(
    data: Partial<BookOperateApplication>,
    options: {
      operator: any
    }
  ) {
    return this.bookOperateApplicationRepository.save({
      ...data,
      createdBy: options.operator,
      createdAt: new Date(),
    })
  }

  async listBookOperateApplication(query: ListBookOperateApplicationRequest) {
    const { pageIndex = 1, pageSize = 10 } = query

    const total = await this.bookOperateApplicationRepository.count()

    const items = await this.bookOperateApplicationRepository.find({
      relations: ['publisher', 'books', 'books.authors'],
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    })

    return {
      pageIndex,
      total,
      items: items.map((x) => ({
        ...x,
        books: (x.books || []).map((y) => ({
          id: y.id,
          name: y.name,
          coverUrl: y.coverUrl,
          authors: (y.authors || []).map((z) => ({
            id: z.id,
            name: z.name,
          })),
        })),
      })),
      pageSize,
    }
  }

  async getBookOperateApplication(id: number) {
    const entity = await this.bookOperateApplicationRepository.findOne({
      where: { id },
      relations: ['publisher', 'books', 'books.authors'],
    })

    return {
      ...entity,
      books: (entity.books || []).map((y) => ({
        id: y.id,
        name: y.name,
        coverUrl: y.coverUrl,
        authors: (y.authors || []).map((z) => ({
          id: z.id,
          name: z.name,
        })),
      })),
    }
  }

  async patchBookOperateApplication(
    id: number,
    data: PatchBookOperateApplicationRequest,
    options: {
      operator: any
    }
  ) {
    const { operator } = options
    const entity = await this.bookOperateApplicationRepository.findOne({
      where: { id },
      relations: ['books'],
    })
    entity.status = data.status
    entity.updatedBy = operator
    entity.updatedAt = new Date()

    return this.bookOperateApplicationRepository.save(entity)
  }

  async getBookIdsForStudent(school: Partial<School>) {
    const builder = await this.bookRepositories.searchBookConditionV2({
      status: OnlineOfflineStatus.ONLINE,
      level:
        !school.isAllLevelForStudent && school.studentLevelIds?.length
          ? school.studentLevelIds
          : undefined,
      schoolId: school.id,
      isHidden: false,
      version: EBookVersion.SUBSCRIPTION,
    })
    const data = await builder.getMany()
    return data.map((item) => item.id)
  }

  async getBookIdsForTeacher(school: Partial<School>) {
    const builder = await this.bookRepositories.searchBookConditionV2({
      status: OnlineOfflineStatus.ONLINE,
      level:
        !school.isAllLevelForStaff && school.staffLevelIds?.length
          ? school.staffLevelIds
          : undefined,
      version: EBookVersion.SUBSCRIPTION,
    })

    const data = await builder.getMany()
    return data.map((item) => item.id)
  }

  async getNewestBooks(schoolId: number, data: PageRequest, levels?: number[]) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = data

    const filter: IKeywordSearchBook = {
      status: OnlineOfflineStatus.ONLINE,
      version: EBookVersion.SUBSCRIPTION,
      level: levels,
    }
    if (schoolId) {
      filter.schoolId = schoolId
      filter.isHidden = false
    }

    const builder = await this.bookRepositories.searchBookConditionV2(filter)
    const total = await builder.getCount()
    const books = await builder
      .skip((pageIndex - 1) * pageSize)
      .take(data.pageSize)
      .getMany()

    const items = await this.bookRepositories.listBooks(
      {
        ids: books.map((item) => item.id),
      },
      {
        fields: [
          'id',
          'name',
          'bookId',
          'coverUrl',
          'isbn',
          'description',
          'url',
          'level',
          'hiddeSchoolIds',
        ],
        relations: ['authors', 'categories', 'labels'],
      }
    )
    return {
      pageIndex,
      pageSize,
      total,
      items: books.map((item) => items.find((book) => book.id === item.id)),
    }
  }

  getLevels(levels: number[], user: any) {
    if (user.isTeacher) {
      return user.isAllLevelForStaff ||
        R.isNil(user.staffLevelIds) ||
        user.staffLevelIds.length === 0
        ? levels
        : levels?.length
          ? R.intersection(levels, user.staffLevelIds)
          : user.staffLevelIds
    }
    return user.isAllLevelForStudent || R.isNil(user.levels) || user.levels.length === 0
      ? levels
      : levels?.length
        ? R.intersection(levels, user.levels)
        : user.levels
  }
}
