import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Zip from 'adm-zip-iconv' // todo replace it
import { isNumber } from 'class-validator'
import Decimal from 'decimal.js'
import Excel from 'exceljs'
import mime from 'mime'
import moment from 'moment'
import path from 'path'
import R from 'ramda'
import { Readable } from 'stream'
import { In, Repository } from 'typeorm'
import { ELocaleType, encrypt, ExcelService, generateUniqueId } from '@/common'
import { TaskService } from '@/common/components/task'
import { AssistantFiles, Author, Category, Label, Publisher } from '@/entities'
import { BookLevel } from '@/entities/common/bookLevel.entity'
import { countries, EBookVersion, ECurrency, OnlineOfflineStatus } from '@/enums'
import { LabelType } from '@/enums/label.enum'
import { FileException } from '@/modules/account/exception'
import { S3_BOOK_ORIGNAL_DIR } from '@/modules/constants'
import { LogService } from '@/modules/system'
import {
  convertTime,
  getCellValue,
  getName,
  regionExcelData,
  regionSpecification,
} from '@/utils'
import { UnSupportedBookFileException } from '../../exception'
import {
  BatchBookFilesDto,
  BookZipDto,
  QueryReadingTimeDto,
  QuerySchoolBookDto,
} from '../dto'
import { convertCodeToLanguage } from '../utils'
import { booksToExcel } from '../utils/bookExcel.util'
import { AuthorService } from './author.service'
import { BookRepository } from './book.repository'
import { BookLevelService } from './bookLevel.service'
import { BookS3Service } from './bookS3.service'
import { CategoryService } from './category.service'
import { LabelService } from './label.service'
import { PublisherService } from './publisher.service'
import { ReadRecordService } from './readRecord.service'
import { ReferenceBookService } from './referenceBook.service'

const dataExample = [
  {
    isbn: '例: 9789888599356',
    bookName: '例: 自遊人生,旅居藍圖',
    authorName: '例: 岑皓軒,馬漪楠',
    authorDescription:
      '例: \n岑皓軒（Matt Shum）\n全職爸爸，靈氣導師，物業投資者；崇尚綠色\n樂活主義（LOHAS），達致財務自由後，不再\n為錢工作，與太太育有兩名兒子。'+
        '現時旅居日\n本沖繩。著有《佔領資產》、《劈炮吾撈》、\n《全家變泰》等',
    companyName: '例: 天窗文化集團',
    publisherName: '例: 天窗出版社',
    publisherDescription:
      '例:\n打開天窗 敢說亮話 天窗一直秉承「精品出\n版」的理念，嚴選書籍主題，為讀者出版值得\n珍藏的好書。天窗現已推出超過三百種書目，\n屢屢掀起城中熱話，為愛書人帶來驚喜。\n' +
        '天窗出版社於2018年，跟你分享世界之精采。\n單車旅者阿翔，由香港大埔直踩到南非好望角，\n名副其實《滾動到世界盡頭》；行走中亞國\n家的赫赤，於《行走吧！旅孩》道出她在遠方',
    description:
      '例: 你不想受老闆氣，不想為五斗米而折腰，但如何達至\n「可做可不做」的自由？你不想久留此城，但又不想連根\n拔起、一走了之，但如何達至「可留可不留」的自由？\n岑皓軒、馬漪楠這一對「港式中產」夫婦，' +
        '由「日捱夜捱」\n到選擇「劈炮吾撈」，到開始建立多元收入來源，再跳\n出香港，帶著兩個囝囝，旅居中日泰三地體驗生活，享受\n真正的「自遊人生」，已超過五年！',
    publishedAt: '2017/11/01',
    level: '中四至中六',
    firstCategoryName: '旅遊與款待',
    // secondCategoryName: '公民與社會發展科',
    categorylabels: '教育',
    educationLabels: '尊重他人',
    version: ' SJRC,SJRC+',
  },
]
@Injectable()
export class BookFileService {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly excelService: ExcelService,
    private readonly categoryService: CategoryService,
    private readonly labelService: LabelService,
    private readonly publisherService: PublisherService,
    private readonly authorService: AuthorService,
    private readonly taskService: TaskService,
    private readonly bookS3Service: BookS3Service,
    private readonly bookLevelService: BookLevelService,
    private readonly readRecordService: ReadRecordService,
    private readonly referenceBookService: ReferenceBookService,
    private readonly logService: LogService,
    @InjectRepository(AssistantFiles)
    private readonly assistantFilesRepository: Repository<AssistantFiles>
  ) {}

  async uploadBookTemplateFile(
    file: Express.Multer.File,
    operator: any,
    local: ELocaleType
  ) {
    const workbook = new Excel.Workbook()
    const result = []

    try {
      await workbook.xlsx.load(file.buffer)
    } catch (error) {
      try {
        const stream = Readable.from(file.buffer)
        await workbook.csv.read(stream)
      } catch (err) {
        throw new FileException()
      }
    }

    const errors = []
    const excelInfo = []
    // use workbook
    workbook.getWorksheet(1).eachRow((row, rowNumber) => {
      if (rowNumber !== 1) {
        // const lvalue = getCellValue(row.getCell(19))
        // const l = lvalue?.split(',')
        const rowData = {
          version: getCellValue(row.getCell(1)),
          isbn: getCellValue(row.getCell(2)),
          name: getCellValue(row.getCell(3)),
          nameCn: getCellValue(row.getCell(4)),
          nameEn: getCellValue(row.getCell(5)),
          authorName: getCellValue(row.getCell(6)),
          authorNameCn: getCellValue(row.getCell(7)),
          authorNameEn: getCellValue(row.getCell(8)),
          authorDescription: getCellValue(row.getCell(9)),
          authorDescriptionCn: getCellValue(row.getCell(10)),
          authorDescriptionEn: getCellValue(row.getCell(11)),
          companyName: getCellValue(row.getCell(12)),
          companyNameCn: getCellValue(row.getCell(13)),
          companyNameEn: getCellValue(row.getCell(14)),
          publisherName: getCellValue(row.getCell(15)),
          publisherNameCn: getCellValue(row.getCell(16)),
          publisherNameEn: getCellValue(row.getCell(17)),
          description: getCellValue(row.getCell(18)),
          descriptionCn: getCellValue(row.getCell(19)),
          descriptionEn: getCellValue(row.getCell(20)),
          publishedAt: getCellValue(row.getCell(21)),
          publishAddress: getCellValue(row.getCell(22)),
          price: getCellValue(row.getCell(23)),
          language: getCellValue(row.getCell(24)),
          level: getCellValue(row.getCell(25)),
          firstCategoryName: getCellValue(row.getCell(26)),
          categoryLabels: getCellValue(row.getCell(27)),
          educationLabels: getCellValue(row.getCell(28)),
          hyperlink: getCellValue(row.getCell(29)),
        }
        excelInfo.push({ rowNumber, rowData })
      }
    })

    for (const data of excelInfo) {
      const error = this.checkBookInformation(data.rowData, data.rowNumber, local)
      if (error) {errors.push(error)}
      else {
        result.push({
          ...data.rowData,
          rowNumber: data.rowNumber,
          hyperlink: data?.rowData?.hyperlink,
        })
      }
    }

    if (errors.length) {return { errors, books: undefined }}

    const isbns = [...new Set(result.map((item) => String(item.isbn)))]
    const validBooks = isbns.map((isbn) =>
      result.find((item) => String(item.isbn) === isbn)
    )

    if (validBooks.length === 0) {return { errors, books: validBooks }}

    let allAuthors = await this.findAuthors(validBooks)
    const publishers = await this.findPublishers(validBooks)
    const allLabels = await this.findLabels(validBooks)
    const categories = await this.findCategories(validBooks)
    const levels = await this.findLevels(validBooks)

    const resourceErrors = this.checkResources(
      validBooks,
      publishers,
      levels,
      allLabels,
      categories,
      local
    )

    await this.updateAuthorDescription(validBooks, allAuthors)
    const createdAuthors = await this.batchCreateAuthors(validBooks, allAuthors)
    allAuthors = allAuthors.concat(createdAuthors)

    if (resourceErrors.length) {return { errors: resourceErrors, books: undefined }}

    const existedBook = await this.bookRepositories.searchBooks(
      {
        isbn: isbns,
      },
      undefined,
      { fields: ['id', 'isbn'] }
    )

    const duplicatedIsbns = existedBook.map((item) => item.isbn)

    // console.log(validBooks)

    const books = validBooks.map((item) => {
      const {
        publisherName,
        firstCategoryName,
        categoryLabels,
        educationLabels,
        publishAddress,
        language,
        hyperlink,
      } = item

      const pName = getName(
        item.publisherName,
        item.publisherNameEn,
        item.publisherNameCn
      )
      const publisher = publishers.find(
        (item) =>
          item.name.zh_HK === pName.zh_HK &&
          item.name.en_uk === pName.en_uk &&
          item.name.zh_cn === pName.zh_cn
      )
      let categoryName = []
      if (firstCategoryName) {
        categoryName = firstCategoryName
          .split(',')
          .map((item) => item.trim().toUpperCase())
      }
      const parentCategory = categories.filter(
        (item) => categoryName.includes(item.name.zh_HK.toUpperCase())
        // categoryName.includes(item.name.en_uk.toUpperCase()) ||
        // categoryName.includes(item.name.zh_cn.toUpperCase()),
      )
      // console.log(categoryName)
      // console.log(categorys)
      // console.log(parentCategory)
      const cLabels = categoryLabels?.split(',') ?? []
      const eLabels = educationLabels?.split(',') ?? []
      const l = [...cLabels, ...eLabels].map((item) => item.toUpperCase())
      const labels = allLabels.filter(
        (item) =>
          l.includes(item.name.zh_HK.toUpperCase()) ||
          l.includes(item.name.en_uk.toUpperCase()) ||
          l.includes(item.name.zh_cn.toUpperCase())
      )

      const authorNames = this.parseAuthorNames(
        item.authorName,
        item.authorNameEn,
        item.authorNameCn
      )
      const authors = allAuthors.filter(
        (a) =>
          !R.isNil(
            authorNames.find(
              (b) =>
                b.zh_HK === a.name.zh_HK &&
                b.en_uk === a.name.en_uk &&
                b.zh_cn === a.name.zh_cn
            )
          )
      )
      const levelNames = item.level.split(',').map((item) => item?.trim())

      const level = levels.filter(
        (item) =>
          levelNames.includes(item.name.en_uk) ||
          levelNames.includes(item.name.zh_HK) ||
          levelNames.includes(item.name.zh_cn)
      )

      const lan = language?.includes(',')
        ? { name: '中英文', languageCode: 'zh_en' }
        : convertCodeToLanguage(language)

      return {
        version: item.version
          .trim()
          .split(',')
          .filter((v) => v === 'SJRC' || v === 'SJRC+') // 过滤掉 PSAR
          .map((v) =>
            v === 'SJRC' ? EBookVersion.SUBSCRIPTION : EBookVersion.REFERENCE
          ),
        isbn: String(item.isbn),
        bookId: generateUniqueId('bo'),
        name: getName(item.name, item.nameEn, item.nameCn),
        description: {
          zh_HK: item.description,
          en_uk: item.descriptionEn,
          zh_cn: item.descriptionCn,
        },
        isScienceRoom: item.version.includes('PSAR') ? 1 : 0,
        publisherAddress: countries[publishAddress.toUpperCase()],
        publisherGroupName: {
          zh_HK: item.companyName,
          en_uk: item.companyNameEn,
          zh_cn: item.companyNameCn,
        },
        publishedAt: item.publishedAt ? new Date(item.publishedAt) : null,
        level: level.map((item) => item.id) ?? null,
        publisherId: publisher.id,
        status: OnlineOfflineStatus.OFFLINE,
        categoryId: parentCategory.map((item) => item.id),
        authorId: authors.map((item) => item.id),
        labelIds: labels.map((item) => item.id),
        labels,
        authors,
        publisher,
        bookLevels: level,
        categorys: parentCategory,
        price: item.price ? new Decimal(item.price).mul(100).toNumber() : null,
        currency: item.price ? ECurrency.HKD : null,
        language: lan.name ? lan.languageCode : null,
        isDuplicated: duplicatedIsbns.includes(String(item.isbn)),
        iD: existedBook.find((b) => b.isbn === String(item.isbn))?.id,
        hyperlink,
      }
    })
    return { errors: undefined, books }
  }

  async uploadBookZip(
    file: Express.Multer.File,
    books?: BatchBookFilesDto[]
  ): Promise<BookZipDto[]> {
    const suffixName = path.extname(file.originalname)
    if (suffixName !== '.zip') {
      throw new UnSupportedBookFileException()
    }
    const zip = new Zip(file.buffer, 'GBK')
    const entriesWithFile = zip
      .getEntries()
      .filter((x) => !x.entryName.endsWith('/') && !x.entryName.startsWith('__MACOSX'))
    // if (books.length !== entriesWithFile.length) {
    //   throw new FileCountNotMatchWithBookException()
    // }

    return Promise.all(
      books.map(async (bookInfo) => {
        const { name, isbn } = bookInfo
        const matchEntry = entriesWithFile.filter((entry) => {
          const fileFullName = entry.entryName.replace(/^.*[\\\/]/, '')
          const [fileName] = fileFullName.split('.')
          return fileName === name || fileName === isbn
        })

        if (!matchEntry || matchEntry.length > 1) {
          return { isbn, name, url: null }
        }

        const [data] = matchEntry
        const fileString = data.getData().toString('utf-8')
        const size = Math.floor((fileString.length / (1024 * 1024)) * 100) / 100 + 'MB'
        const bookFileName = generateUniqueId('BOOK_')
        const suffixName = path.extname(data.entryName)

        console.log(file.mimetype, file.size, size)
        const contentType = mime.getType(suffixName) || 'application/octet-stream'
        const buffter =
          contentType === 'application/pdf'
            ? encrypt(process.env.AES_KEY, matchEntry[0].getData())
            : matchEntry[0].getData()

        // const buffter = matchEntry[0].getData()

        const url = await this.bookS3Service.upload({
          fileName: bookFileName + suffixName,
          file: buffter,
          path: S3_BOOK_ORIGNAL_DIR,
          contentType,
        })
        return {
          isbn,
          name,
          url,
          size,
        }
      })
    )
  }

  async downloadTemplateFile(local: ELocaleType = ELocaleType.EN_UK) {
    return this.excelService.buildExcel([
      {
        name: `bulkUploadBookInformationV2.${local}`,
        data: dataExample,
      },
    ])
  }

  async exportBooks(
    local: ELocaleType = ELocaleType.ZH_HK,
    query: QuerySchoolBookDto = {},
    templateName: string,
    user: any
  ) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let books = []

    do {
      const data = await this.bookRepositories.searchBooksByPage({
        ...query,
        pageIndex,
        pageSize,
      })
      pageIndex += 1
      total = data.total
      let reference = []
      if (query.version === EBookVersion.REFERENCE) {
        reference = await this.referenceBookService.getCountByBook(
          data.items.map((item) => item.id)
        )
      }
      const items = data.items.map((item) => {
        const ref = reference.find((r) => r.id === item.id)
        return booksToExcel({ ...item, copiesCount: ref?.copiesCount || 0 } as any, {
          local,
        })
      })
      books = books.concat(items)
    } while ((pageIndex - 1) * pageSize < total)

    await this.logService.save('下载书籍', user, query)

    return this.excelService.buildExcel(
      [
        {
          name: `${templateName}.${local}`,
          data: books,
        },
      ],
      local === ELocaleType.EN_UK ? 'Books' : '書籍'
    )
  }

  async exportBooksWithReading(
    admin: any,
    local: ELocaleType = ELocaleType.ZH_HK,
    query: QuerySchoolBookDto = {},
    time?: QueryReadingTimeDto
  ) {
    const pageSize = 100
    let pageIndex = 1
    let total = 0
    let books = []
    let region = []

    do {
      const data = await this.bookRepositories.searchBooksByPage({
        ...query,
        pageIndex,
        pageSize,
      })

      const isAIBooks = await this.assistantFilesRepository.find({
        where: {
          bookId: In(data.allBookIds),
        },
      })
      const reading = data.items.length
        ? await this.readRecordService.regionReading(time, {
          bookIds: data.items.map((item) => item.id),
        })
        : []
      region = [
        ...new Set([
          ...R.flatten(reading.map((item) => item.data.map((r) => r.region))),
          ...region,
        ]),
      ]
      pageIndex += 1
      total = data.total
      const items = data.items
        .filter((item) => item.version.includes(EBookVersion.SUBSCRIPTION))
        .map((item) => {
          const read = reading.find((reading) => reading.id === item.id)?.data
          return {
            isAIBook: isAIBooks.some((ai) => ai.bookId === item.id)
              ? 'AI Wisereader'
              : '',
            startTime: moment
              .tz(time.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(time.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            ...booksToExcel(item),
            ...regionExcelData(read),
            readingTime: read ? convertTime(read?.[0]?.totalReadingTime) : '',
          }
        })
      books = books.concat(items)
      // console.log(books)
    } while ((pageIndex - 1) * pageSize < total)

    await this.logService.save('书籍管理', admin, { query, time })
    return this.excelService.buildBookRegionExcel(
      {
        name: `bookInformation.${local}`,
        specification: regionSpecification(region),
        data: books,
      },
      local === ELocaleType.EN_UK ? 'Books' : '書籍'
    )
  }

  checkBookInformation(data: any, rowNumber: number, local: ELocaleType) {
    const fields = []
    if (
      !data.version ||
      (() => {
        const versions = data.version.split(',').map((item) => item.trim())
        const hasSJRC = versions.includes('SJRC')
        const hasSJRCPlus = versions.includes('SJRC+')
        const hasPSAR = versions.includes('PSAR')

        if (!hasSJRC && !hasSJRCPlus) {return true}
        if (versions.some((item) => !['SJRC', 'SJRC+', 'PSAR'].includes(item))) {
          return true
        }
        if (hasPSAR && !hasSJRC) {return true}
        return false
      })()
    ) {
      fields.push('所屬系統')
    }

    if (!data.isbn) {
      fields.push('ISBN')
      // throw new BookInformationException(`文件第${rowNumber}行ISBN缺失`)
    }
    if (!data.name && !data.nameEn && !data.nameCn) {
      fields.push(local === ELocaleType.EN_UK ? 'title' : '書名')
      // throw new BookInformationException(`文件第${rowNumber}行書名缺失`)
    }
    const authorName = data.authorName?.split(',') || []
    const authorNameEn = data.authorNameEn?.split(',') || []
    const authorNameCn = data.authorNameCn?.split(',') || []
    if (
      (!data.authorName && !data.authorNameEn && !data.authorNameCn) ||
      (authorName.length !== authorNameEn.length &&
        authorName.length &&
        authorNameEn.length) ||
      (authorName.length !== authorNameCn.length &&
        authorName.length &&
        authorNameCn.length) ||
      (authorNameEn.length !== authorNameCn.length &&
        authorNameEn.length &&
        authorNameCn.length)
    ) {
      fields.push(local === ELocaleType.EN_UK ? 'author' : '作者')
      // throw new BookInformationException(`文件第${rowNumber}行作者缺失`)
    }
    if (!data.companyName && !data.companyNameEn && !data.companyNameCn) {
      fields.push(local === ELocaleType.EN_UK ? 'publishing group' : '出版集團')
      // throw new BookInformationException(`文件第${rowNumber}行出版集團缺失`)
    }
    if (!data.publisherName && !data.publisherNameEn && !data.publisherNameCn) {
      fields.push(local === ELocaleType.EN_UK ? 'publisher' : '出版社')
      // throw new BookInformationException(`文件第${rowNumber}行出版社缺失`)
    }
    if (!data.description && !data.descriptionEn && !data.descriptionCn) {
      fields.push(local === ELocaleType.EN_UK ? 'book introduction' : '書籍簡介')
      // throw new BookInformationException(`文件第${rowNumber}行書籍簡介缺失`)
    }
    if (data?.firstCategoryName && data.firstCategoryName.split(',').length > 10) {
      fields.push(
        local === ELocaleType.EN_UK ? 'Curriculum Guidelines Category' : '課程綱要分類'
      )
      // throw new BookInformationException(
      //   `文件第${rowNumber}行課程綱要分類*(主項 - 必填)缺失`,
      // )
    }

    if (!data.publishAddress || R.isNil(countries[data.publishAddress.toUpperCase()])) {
      fields.push(local === ELocaleType.EN_UK ? 'place of publication' : '出版地')
      // throw new BookInformationException(`文件第${rowNumber}行出版地缺失`)
    }

    if (!data.categoryLabels) {
      fields.push(local === ELocaleType.EN_UK ? 'books category' : '圖書品種分類')
      // throw new BookInformationException(`文件第${rowNumber}行圖書品種分類缺失`)
    }

    if (!data.level) {
      fields.push(local === ELocaleType.EN_UK ? 'target readers' : '目標閱讀群組')
      // throw new BookInformationException(`文件第${rowNumber}行目標閱讀群組缺失`)
    }

    if (!data.publishedAt) {
      fields.push(local === ELocaleType.EN_UK ? 'publication date' : '出版日期')
      // throw new BookInformationException(`文件第${rowNumber}行目出版日期缺失`)
    }

    if (
      !R.isNil(data.price) &&
      (!isNumber(Number(data.price), { allowNaN: false, allowInfinity: false }) ||
        Number(data.price) < 0)
    ) {
      fields.push(local === ELocaleType.EN_UK ? 'List price' : '價格')
    }
    return fields.length
      ? local === ELocaleType.EN_UK
        ? `Line ${rowNumber} has an error in the ${fields.join(',')}`
        : `第${rowNumber}行${fields.join(',')}有错误`
      : undefined
  }

  private checkResources(
    validBooks: any[],
    publishers: Publisher[],
    levels: BookLevel[],
    allLabels: Label[],
    categorys: Category[],
    local: ELocaleType
  ) {
    const resourceErrors = []
    for (const info of validBooks) {
      const fields = []

      if (
        publishers.findIndex(
          (p) =>
            p.name.en_uk === info.publisherNameEn?.trim() &&
            p.name.zh_HK === info.publisherName?.trim() &&
            p.name.zh_cn === info.publisherNameCn?.trim()
        ) === -1
      ) {
        fields.push(local === ELocaleType.ZH_HK ? '出版社' : 'publisher')
      }

      const levelNames = info.level?.split(',').map((item) => item?.trim())
      if (
        levelNames?.some(
          (name) =>
            levels.findIndex(
              (item) =>
                name === item.name.en_uk ||
                name === item.name.zh_HK ||
                name === item.name.zh_cn
            ) === -1
        )
      ) {
        const names = levelNames?.filter(
          (name) =>
            levels.findIndex(
              (item) =>
                name === item.name.en_uk ||
                name === item.name.zh_HK ||
                name === item.name.zh_cn
            ) === -1
        )
        fields.push(
          local === ELocaleType.ZH_HK
            ? `目標閱讀群組-${names.join(',')}`
            : `target readers-${names.join(',')}`
        )
      }

      const eLabelNames = info.educationLabels?.split(',').map((item) => item?.trim())
      if (
        eLabelNames?.some(
          (name) =>
            allLabels.findIndex(
              (item) =>
                (name === item.name.en_uk ||
                  name === item.name.zh_HK ||
                  name === item.name.zh_cn) &&
                item.type === LabelType.EDUCATION
            ) === -1
        )
      ) {
        const names = eLabelNames?.filter(
          (name) =>
            allLabels.findIndex(
              (item) =>
                (name === item.name.en_uk ||
                  name === item.name.zh_HK ||
                  name === item.name.zh_cn) &&
                item.type === LabelType.EDUCATION
            ) === -1
        )
        fields.push(
          local === ELocaleType.ZH_HK
            ? `價值觀教育標籤-${names.join(',')}`
            : `Values Education Tags-${names.join(',')}`
        )
      }

      const cLabelNames = info.categorylabels?.split(',').map((item) => item?.trim())
      if (
        cLabelNames?.some(
          (name) =>
            allLabels.findIndex(
              (item) =>
                (name === item.name.en_uk ||
                  name === item.name.zh_HK ||
                  name === item.name.zh_cn) &&
                item.type === LabelType.CATEGORY
            ) === -1
        )
      ) {
        const names = cLabelNames?.filter(
          (name) =>
            allLabels.findIndex(
              (item) =>
                (name === item.name.en_uk ||
                  name === item.name.zh_HK ||
                  name === item.name.zh_cn) &&
                item.type === LabelType.CATEGORY
            ) === -1
        )
        fields.push(
          local === ELocaleType.ZH_HK
            ? `圖書品種分類-${names.join(',')}`
            : `books category-${names.join(',')}`
        )
      }

      const categoryNames = info.firstCategoryName?.split(',').map((item) => item?.trim())

      if (
        categoryNames?.some(
          (name) =>
            categorys.findIndex(
              (item) =>
                name === item.name.en_uk ||
                name === item.name.zh_HK ||
                name === item.name.zh_cn
            ) === -1
        )
      ) {
        const names = categoryNames?.filter(
          (name) =>
            categorys.findIndex(
              (item) =>
                name === item.name.en_uk ||
                name === item.name.zh_HK ||
                name === item.name.zh_cn
            ) === -1
        )
        fields.push(
          local === ELocaleType.ZH_HK
            ? `課程綱要分類-${names.join(',')}`
            : `Curriculum Guidelines Category-${names.join(',')}`
        )
      }

      if (fields.length)
      {resourceErrors.push(
        local === ELocaleType.EN_UK
          ? `Line ${info.rowNumber} ${fields.join(',')} can not be found`
          : `第${info.rowNumber}行${fields.join(',')}找不到`
      )}
    }
    return resourceErrors
  }

  private async convertLevel(name: string[]) {
    return this.bookLevelService.getByName(name)
    // switch (level) {
    //   // case '小一至小六':
    //   //   return BookLevel.PRIMARY_SCHOOL_LEVEL
    //   case '小一至小三':
    //     return BookLevel.PRIMARY_ONE_TO_THREE
    //   case '小四至小六':
    //     return BookLevel.PRIMARY_FOUR_TO_SIX
    //   case '中一至中三':
    //     return BookLevel.JUNIOR_HIGH_SCHOOL
    //   case '中四至中六':
    //     return BookLevel.HIGH_SCHOOL_LEVEL
    //   case '教職員':
    //     return BookLevel.TEACHER_LEVEL
    //   default:
    //     return undefined
    // }
  }

  private async findLabels(validBooks: any[]) {
    const categoryLabels = [
      ...new Set(
        R.flatten(validBooks.map((item) => item.categoryLabels?.split(','))).map((item) =>
          item.trim()
        )
      ),
    ].filter((item) => !!item)
    const educationLabels = [
      ...new Set(
        R.flatten(validBooks.map((item) => item.educationLabels?.split(','))).map(
          (item) => item?.trim()
        )
      ),
    ].filter((item) => !!item)

    const clabels = categoryLabels.length
      ? await this.labelService.searchLabel(categoryLabels, LabelType.CATEGORY)
      : []
    const elabels = educationLabels.length
      ? await this.labelService.searchLabel(educationLabels, LabelType.EDUCATION)
      : []
    return clabels.concat(elabels)
  }

  private async findAuthors(validBooks: any[]) {
    const authors = this.getAuthorNames(validBooks)
    return this.authorService.findAuthorsByName(authors)
  }

  private getAuthorNames(validBooks: any[]) {
    const data = R.flatten(
      validBooks.map((item) =>
        this.parseAuthorNames(item.authorName, item.authorNameEn, item.authorNameCn)
      )
    )
    const authors = []
    for (const item of data) {
      if (
        authors.findIndex(
          (a) =>
            a.en_uk === item.en_uk && a.zh_HK === item.zh_HK && a.zh_cn === item.zh_cn
        ) === -1
      ) {
        authors.push(item)
      }
    }
    return authors
  }

  private async findPublishers(validBooks: any[]) {
    const data = validBooks.map((item) => ({
      name: item.publisherName?.trim(),
      nameEn: item.publisherNameEn?.trim(),
      nameCn: item.publisherNameCn?.trim(),
    }))

    const names = []
    for (const item of data) {
      const pName = getName(item.name, item.nameEn, item.nameCn)
      if (
        names.findIndex(
          (n) =>
            n.zh_HK === pName.zh_HK && n.en_uk === pName.en_uk && n.zh_cn === pName.zh_cn
        ) === -1
      )
      {names.push({
        zh_HK: pName.zh_HK,
        en_uk: pName.en_uk,
        zh_cn: pName.zh_cn,
      })}
    }

    return this.publisherService.getPublishers(names)
  }

  private async findLevels(validBooks: any[]) {
    const names = validBooks.map((item) =>
      item.level.split(',').map((item) => item?.trim())
    )
    return this.convertLevel([...new Set(R.flatten(names))])
  }

  private async findCategories(validBooks: any[]) {
    const data = validBooks.map((item) => String(item.firstCategoryName).split(','))
    return this.categoryService.searchCategory([...new Set(R.flatten(data))])
  }

  private parseAuthorNames(
    authorName: string,
    authorNameEn: string,
    authorNameCn: string
  ) {
    const zh = authorName?.length
      ? authorName
      : authorNameEn.length
        ? authorNameEn
        : authorNameCn
    const en = authorNameEn?.length
      ? authorNameEn
      : authorName?.length
        ? authorName
        : authorNameCn
    const cn = authorNameCn?.length
      ? authorNameCn
      : authorName?.length
        ? authorName
        : authorNameEn

    const zhs = zh.split(',').map((item) => item.trim())
    const ens = en.split(',').map((item) => item.trim())
    const cns = cn.split(',').map((item) => item.trim())
    return zhs.map((zh_HK, index) => getName(zh_HK, ens[index], cns[index]))
  }

  private async updateAuthorDescription(validBooks: any[], authors: Author[]) {
    const data = this.parseAuthorDescription(validBooks)

    if (data.length) {
      const validAuthors = data
        .map((item) => {
          const author = authors.find(
            (a) =>
              a.name.en_uk === item.name.en_uk &&
              a.name.zh_HK === item.name.zh_HK &&
              a.name.zh_cn === item.name.zh_cn
          )
          if (author) {
            return { id: author.id, description: item.description }
          }
          return undefined
        })
        .filter((item) => !!item)

      if (validAuthors.length) {
        for (const author of validAuthors) {
          await this.authorService.patchAuthor(author.id, {
            description: author.description,
          })
        }
      }
    }
  }

  private async batchCreateAuthors(validBooks: any, authors: Author[]) {
    const names = await this.getAuthorNames(validBooks)
    const notExistNames = names.filter(
      (item) =>
        authors.findIndex(
          (a) =>
            a.name.en_uk === item.en_uk &&
            a.name.zh_HK === item.zh_HK &&
            a.name.zh_cn === item.zh_cn
        ) === -1
    )
    if (notExistNames.length === 0) {return []}
    const authorDescriptions = this.parseAuthorDescription(validBooks)
    const batchAuthors = notExistNames.map((item) => {
      const description = authorDescriptions.find(
        (d) =>
          d.name.zh_HK === item.zh_HK &&
          d.name.en_uk === item.en_uk &&
          d.name.zh_cn === item.zh_cn
      )

      return description ? description : { name: item }
    })
    return this.authorService.batchCreateAuthor(batchAuthors)
  }

  private parseAuthorDescription(validBooks: any[]) {
    const authors = []
    for (const item of validBooks) {
      const isManyAuthors =
        item.authorName?.includes(',') ||
        item.authorNameEn?.includes(',') ||
        item.authorNameCn?.includes(',')
      if (
        (!isManyAuthors &&
          (item.authorDescriptionEn?.trim()?.length ||
            item.authorDescription?.trim()?.length)) ||
        item.authorDescriptionCn?.trim()?.length
      ) {
        const index = authors.findIndex(
          (a) =>
            a.name.zh_HK === item.authorName?.trim() &&
            a.name.en_uk === item.authorNameEn?.trim() &&
            a.name.zh_cn === item.authorNameCn?.trim()
        )
        if (index === -1) {
          authors.push({
            name: {
              zh_HK: item.authorName?.trim(),
              en_uk: item.authorNameEn?.trim(),
              zh_cn: item.authorNameCn?.trim(),
            },
            description: {
              zh_HK: item.authorDescription?.trim(),
              en_uk: item.authorDescriptionEn?.trim(),
              zh_cn: item.authorDescriptionCn?.trim(),
            },
          })
        } else {
          authors[index].description = {
            zh_HK: item.authorDescription?.trim(),
            en_uk: item.authorDescriptionEn?.trim(),
            zh_cn: item.authorDescriptionCn?.trim(),
          }
        }
      }
    }
    return authors
  }
}
