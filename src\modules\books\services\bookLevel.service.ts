import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Not, Repository } from 'typeorm'
import { PageRequest } from '@/common'
import { BookLevel } from '@/entities/common/bookLevel.entity'
import { PAGE_SIZE } from '@/modules/constants'
import {IBookLevelService} from '@/modules/shared/interfaces'
import { DuplicateBookLevelException, NotExistBookLevelException } from '../../exception'
import { CreateBookLevelDto } from '../dto/bookLevel.dto'

@Injectable()
export class BookLevelService implements IBookLevelService {
  constructor(
    @InjectRepository(BookLevel) private readonly bookLevelModel: Repository<BookLevel>
  ) {}

  async create(data: CreateBookLevelDto) {
    if (!data.name.en_uk) {
      data.name.en_uk = data.name.zh_HK
    }
    // 使用 createQueryBuilder 进行复杂查询
    const duplicated = await this.bookLevelModel
      .createQueryBuilder('bookLevel')
      .where(
        `(bookLevel.name->'$.zh_HK' = :zh_HK OR bookLevel.name->'$.en_uk' = :en_uk OR bookLevel.name->'$.zh_cn' = :zh_cn)`,
        {
          zh_HK: data.name.zh_HK,
          en_uk: data.name.en_uk,
          zh_cn: data.name.zh_cn,
        }
      )
      .getOne()

    if (duplicated) {
      throw new DuplicateBookLevelException()
    }

    const max = await this.bookLevelModel.findOne({
      order: { sequence: 'DESC' },
    })

    return this.bookLevelModel.save({
      name: data.name,
      sequence: max ? max.sequence + 1 : 1,
    })
  }

  async update(id: number, data: CreateBookLevelDto) {
    const exist = await this.bookLevelModel.findOne({ where: { id  } })
    if (!exist) {
      throw new NotExistBookLevelException()
    }
    if (!data.name.en_uk) {
      data.name.en_uk = data.name.zh_HK
    }
    // 使用 createQueryBuilder 进行复杂查询
    const duplicated = await this.bookLevelModel
      .createQueryBuilder('bookLevel')
      .where(
        `(bookLevel.name->'$.zh_HK' = :zh_HK OR bookLevel.name->'$.en_uk' = :en_uk OR bookLevel.name->'$.zh_cn' = :zh_cn)`,
        {
          zh_HK: data.name.zh_HK,
          en_uk: data.name.en_uk,
          zh_cn: data.name.zh_cn,
        }
      )
      .getOne()

    if (duplicated && duplicated.id !== id) {
      throw new DuplicateBookLevelException()
    }

    return this.bookLevelModel.save({
      ...exist,
      id,
      name: data.name,
    })
  }

  findLevels(excludes: number[]) {
    return this.bookLevelModel.find({ where: { id: Not(In(excludes)) } })
  }

  async get(id: number) {
    const exist = await this.bookLevelModel.findOne({ where: { id  } })
    if (!exist) {
      throw new NotExistBookLevelException()
    }
    return exist
  }

  async getByIds(id: number[]) {
    return this.bookLevelModel.find({ where: { id: In(id) } })
  }

  async getByName(name: string[]) {
    const whereCondition = name
      .map(
        (item) =>
          `name->'$.zh_HK' = '${item}' OR name->'$.en_uk' = '${item}' OR name->'$.zh_cn' = '${item}' `
      )
      .join(' OR ')

    const exist = await this.bookLevelModel
      .createQueryBuilder('bookLevel')
      .where(whereCondition)
      .getMany()

    // if (!exist) {
    //   throw new NotExistBookLevelException()
    // }
    return exist
  }

  async delete(id: number) {
    const exist = await this.bookLevelModel.findOne({ where: { id  } })
    if (!exist) {
      throw new NotExistBookLevelException()
    }
    await this.bookLevelModel.softDelete({ id })
    await this.bookLevelModel.query(
      `delete from book_levels_books where book_levels_id = ${id}`
    )
    return exist
  }

  async list(query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const total = await this.bookLevelModel.count()
    const items = await this.bookLevelModel.find({
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
      order: { sequence: 'ASC' },
    })
    return { pageIndex, pageSize, total, items }
  }
}
