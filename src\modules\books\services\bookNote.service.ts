import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { BookNote } from '@/entities'
import { CreateBookNoteDto } from '../dto'
import { BookRepository } from './book.repository'

@Injectable()
export class BookNoteService {
  constructor(
    @InjectRepository(BookNote)
    private readonly bookNoteRepository: Repository<BookNote>,
    private readonly bookRepository: BookRepository
  ) {}

  async createBooknote(data: CreateBookNoteDto, userId: number) {
    const book = await this.bookRepository.getBookWithoutRelation(
      { id: data.bookId },
      { withDeleted: true }
    )
    return this.bookNoteRepository.save({
      userId,
      note: data.note,
      book,
    })
  }

  async updateBooknote(id: number, data: Partial<BookNote>) {
    const note = await this.getBooknote(id)
    Object.assign(note, { note: data.note })
    return this.bookNoteRepository.save(note)
  }

  async getBooknote(id: number) {
    const note = await this.bookNoteRepository.findOne({ where: { id  } })
    if (!note) {
      throw new NotFoundException('can not found book note')
    }
    return note
  }

  async listBooknote(bookId: number, userId: number) {
    return this.bookNoteRepository
      .createQueryBuilder('note')
      .withDeleted()
      .innerJoin('note.book', 'book', 'book.id = :bookId', { bookId })
      .where({ userId })
      .getMany()
  }

  async deleteBooknote(id: number) {
    const booknote = await this.getBooknote(id)
    await this.bookNoteRepository.delete({ id })
    return booknote
  }
}
