import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { DataSource, EntityManager, In, Repository } from 'typeorm'
import { generateUniqueId } from '@/common'
import { Category } from '@/entities'
import { PAGE_SIZE } from '@/modules/constants'
import { DuplicateCategoryException } from '@/modules/exception'
import { ICategoryService } from '@/modules/shared/interfaces'
import { allNameCondition, multiNameCondition, nameLikeWhere, nameWhere } from '@/utils'
import { PageData } from '@/interfaces'
import { CreateCategoryDto, ListAdminCategoryDto, ListAllCategoryDto } from '../dto'
import { FilterCategory } from '../interfaces'
import { categoryValidator } from '../validators'

@Injectable()
export class CategoryService implements ICategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    private readonly dataSource: DataSource
  ) {}

  async createCategory(data: CreateCategoryDto): Promise<Category> {
    // 使用 createQueryBuilder 进行复杂查询
    const dupliateCategory = await this.categoryRepository
      .createQueryBuilder('category')
      .where(
        `(category.name->'$.zh_HK' = :zh_HK OR category.name->'$.en_uk' = :en_uk OR category.name->'$.zh_cn' = :zh_cn)`,
        {
          zh_HK: data.name.zh_HK,
          en_uk: data.name.en_uk,
          zh_cn: data.name.zh_cn,
        }
      )
      .getOne()
    if (dupliateCategory) {
      console.log(data)
      throw new DuplicateCategoryException()
    }
    // const parent = data.parentId ? await this.getCategory({ id: data.parentId }) : null
    return this.categoryRepository.save({
      ...R.pick(['name'], data),
      categoryId: generateUniqueId('ca'),
      // parent,
    })
  }

  async updateCategory(id: number, data: CreateCategoryDto): Promise<Category> {
    const category = await this.getCategory({ id })
    if (data.parentId) {
      // category.parent = await this.getCategory({ id: data.parentId })
    }
    if (
      data.name &&
      (category.name.en_uk !== data.name.en_uk ||
        category.name.zh_HK !== data.name.zh_HK ||
        category.name.zh_cn !== data.name.zh_cn)
    ) {
      // 使用 createQueryBuilder 进行复杂查询
      const duplicateCategory = await this.categoryRepository
        .createQueryBuilder('category')
        .where(
          `(category.name->'$.zh_HK' = :zh_HK OR category.name->'$.en_uk' = :en_uk OR category.name->'$.zh_cn' = :zh_cn)`,
          {
            zh_HK: data.name.zh_HK,
            en_uk: data.name.en_uk,
            zh_cn: data.name.zh_cn,
          }
        )
        .getOne()
      if (duplicateCategory && duplicateCategory.id !== id) {
        throw new DuplicateCategoryException()
      }

      category.name = data.name
    }
    return this.categoryRepository.save(category)
  }

  async getCategories(ids: number[]) {
    return this.categoryRepository.find({ where: { id: In(ids)  } })
  }

  async getCategory(options: FilterCategory): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: this.getFilter(options),
    })
    //   , {
    //   relations: ['children'],
    // })
    categoryValidator(category).exist()
    return category
  }

  async getCategoryWithBook(options: FilterCategory): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: this.getFilter(options),
      // relations: ['children', 'children.books', 'books'],
      relations: ['books'],
    })
    categoryValidator(category).exist()
    return category
  }

  async searchCategory(name: string[]): Promise<Category[]> {
    const alias = 'c'
    const condition = multiNameCondition(name, alias)

    const whereCondition = name.map((item) => `all_name like '%"${item}"%'`).join(' OR ')
    return this.categoryRepository
      .createQueryBuilder('category')
      .where(whereCondition)
      .getMany()
  }

  async listCategory(query: ListAdminCategoryDto): Promise<PageData<Category>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const sql = `select p.name as name, p.id as id from categories as p where p.deleted_at is null ${
      query.keyword ? `and ${nameLikeWhere(query.keyword)}` : ''
    }  limit ${pageSize} offset ${(pageIndex - 1) * pageSize}`

    const countSql = `select count(*) as count from categories as p where p.deleted_at is null  ${
      query.keyword ? `and ${nameLikeWhere(query.keyword)}` : ''
    } `

    const data = await this.dataSource.query(sql)
    const [total] = await this.dataSource.query(countSql)
    return { pageIndex, pageSize, items: data, total: total.count }
  }

  async listAdminCategory(query: ListAdminCategoryDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE, keyword } = query

    // let child = []
    let parents = []
    const [total] = await this.dataSource.query(
      `select count(*) as count from categories where deleted_at is null ${
        keyword ? `and ${nameLikeWhere(keyword)}` : ''
      } `
    )
    if (total.count) {
      parents = await this.dataSource.query(
        `select * from categories where deleted_at is null ${
          keyword ? `and ${nameLikeWhere(keyword)}` : ''
        } limit ${pageSize} offset ${(pageIndex - 1) * pageSize}`
      )
    }

    // console.log(child)

    const items = parents.length
      ? parents.map((item) => {
        // const children = child.filter((c) => c.parent_id === item.id)
        return {
          id: item.id,
          name: item.name,
          // children: children.map((item) => ({
          //   id: item.id,
          //   name: item.name,
          // })),
        }
      })
      : []

    // console.log(items)
    return {
      total: Number(total.count),
      items,
      pageIndex,
      pageSize,
    }
  }

  async getAllCategory(query: ListAllCategoryDto = {}): Promise<Category[]> {
    const data = await this.dataSource.query(
      `select p.name as name, p.id as id from categories as p where p.deleted_at is null ${
        query.keyword ? `and ${nameLikeWhere(query.keyword)}` : ''
      } `
    )
    const parentIds = [...new Set(data.map((item) => item.id))]
    return parentIds.map((id) => {
      const parent = data.find((item) => item.id === id)

      return { id, name: parent.name }
    }) as Category[]
  }

  async deleteCategory(id: number, manager: EntityManager) {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['books'],
    })
    categoryValidator(category).exist()
    if (category.books?.length) {
      await manager.query(`delete from categories_books where category_id = ${id}`)
    }

    return manager.delete(Category, { id })
  }

  private getFilter(options: FilterCategory) {
    return R.pick(['categoryId', 'id'], options)
  }
}
