import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { DataSource, Repository } from 'typeorm'
import { EBookVersion, EUserType } from '@/enums'
import { ReadRecord } from '../../../entities'
import { LeaderBoardService } from './leaderboard.service'

@Injectable()
export class InitLeaderBoardService {
  constructor(
    private readonly leaderBoardService: LeaderBoardService,
    @InjectRepository(ReadRecord)
    private readonly readRecordRepositories: Repository<ReadRecord>,
    private readonly dataSource: DataSource
  ) {}

  async initBookReadingTime(bookId: number, schoolId?: number) {
    let schoolIds = []

    await this.dataSource.transaction(async (manager) => {
      if (schoolId) {
        schoolIds.push(schoolId)
      } else {
        const schools = await manager.query(
          `select id from schools where deleted_at is null`
        )
        schoolIds = schools.map((item) => item.id)
      }

      const data = await manager.query(
        `select book_id as bookId, sum(reading_time) as readingTime, school_id as schoolId from \`read_record\` where school_id in (${schoolIds.join(
          ','
        )}) and book_id = ${bookId} group by book_id, school_id`
      )
      const [book] = await manager.query(
        `select hidde_school_ids as ids from books where id = ${bookId}`
      )

      const readingTime = schoolIds.map((schoolId) => ({
        schoolId,
        readingTime: data.find((item) => item.schoolId === schoolId)?.readingTime || 0,
        bookId,
        isHidden: book?.ids?.includes(schoolId),
      }))

      // await this.leaderBoardService.removeBookForReadingTime(bookId, [schoolId])
      await this.leaderBoardService.addBooksToReadingRanking(readingTime)
    })
  }

  async initBooksReadingTime(bookIds: number[]) {
    const schools = await this.readRecordRepositories.query(
      `select id from schools where deleted_at is null`
    )

    const data = await this.readRecordRepositories.query(
      `select book_id as bookId, sum(reading_time) as readingTime, school_id as schoolId from \`read_record\` where school_id in (${schools
        .map((item) => item.id)
        .join(',')}) and book_id in (${bookIds.join(',')}) group by book_id, school_id`
    )
    const books = await this.readRecordRepositories.query(
      `select hidde_school_ids as schools, id from books where id in (${bookIds.join(
        ','
      )})`
    )
    await this.leaderBoardService.addBooksToReadingRanking(
      data.map((item) => ({
        bookId: item.bookId,
        readingTime: item.readingTime || 0,
        schoolId: item.schoolId,
        isHidden: books
          .find((book) => book.id === item.bookId)
          ?.schools?.includes(item.schoolId),
      }))
    )
  }

  async synchronizeReadingTimeRankingForSchool(
    schoolId: number,
    booksForStudent: number[],
    booksForStaff: number[]
  ) {
    const ids = [...new Set([...booksForStudent, ...booksForStaff])]

    const data = await this.readRecordRepositories.query(`
      select
        book_id as bookId,
        sum(reading_time) as readingTime
      from
        \`read_record\`
      where
        school_id = ${schoolId} and book_id in (${ids.join(',')})
      group by
        book_id
    `)

    await this.leaderBoardService.addBooksToReadingRankingByUserType(
      schoolId,
      EUserType.STUDENT,
      booksForStudent.map((id) => ({
        bookId: id,
        readingTime:
          data.find((item) => Number(item.bookId) === Number(id))?.readingTime || 0,
      }))
    )

    await this.leaderBoardService.addBooksToReadingRankingByUserType(
      schoolId,
      EUserType.TEACHER,
      booksForStaff.map((id) => ({
        bookId: id,
        readingTime: data.find((item) => item.bookId == id)?.readingTime || 0,
      }))
    )
  }

  async initReadingTimeWhenCreateSchool(
    schoolId: number,
    bookIds: number[],
    bookIdsForStaff: number[]
  ) {
    await this.leaderBoardService.addBookToReadingRankingForSchool(
      schoolId,
      EUserType.STUDENT,
      bookIds.map((id) => ({ bookId: String(id), readingTime: 0 }))
    )

    await this.leaderBoardService.addBookToReadingRankingForSchool(
      schoolId,
      EUserType.TEACHER,
      bookIdsForStaff.map((id) => ({ bookId: String(id), readingTime: 0 }))
    )
  }

  async syncReferenceRanking(schoolId: number) {
    // no need to remove reference ranking for school
    // await this.leaderBoardService.removeReferenceRankingForSchool(schoolId)

    const data = await this.readRecordRepositories.query(`
      select
        book_id as bookId,
        ifnull(t.total, 0) as total
      from
        reference_books
        left join (
          select
            book_id as bookId,
            count(*) as total
          from
            \`reference_read_record\`
          where
            school_id = ${schoolId}
          group by
            book_id
        ) as t on reference_books.book_id = t.bookId
      where
        reference_books.school_id = ${schoolId}
    `)

    await this.leaderBoardService.addBooksToReferenceRanking(schoolId, data)
  }

  async removeBookFromReadingRanking(bookId: number) {
    await this.dataSource.transaction(async (manager) => {
      const schools = await manager.query(`select id from schools`)
      const schoolIds = schools.map((item) => item.id)
      await this.leaderBoardService.removeBookFromReadingRanking(bookId, schoolIds)
    })
  }

  async removeBooksFromReadingRanking(bookIds: number[]) {
    await this.dataSource.manager.transaction(async (manager) => {
      const schools = await manager.query(`select id from schools`)
      const schoolIds = schools.map((item) => item.id)
      await this.leaderBoardService.removeBooksForReadingRanking(bookIds, schoolIds)
    })
  }
}
