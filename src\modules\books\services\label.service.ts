import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { EntityManager, In, Repository } from 'typeorm'
import { generateUniqueId } from '@/common'
import { Label } from '@/entities'
import { LabelType } from '@/enums/label.enum'
import { PAGE_SIZE } from '@/modules/constants'
import {
  DuplicateCategoryLabelException,
  DuplicateEducationLabelException,
} from '@/modules/exception'
import {
  getMultiLanuageKey,
  multiLabelNameCondition,
  nameLikeWhere,
  nameWhere,
} from '@/utils'
import { PageData } from '@/interfaces'
import { CreateLabelDto, ListLabelDto } from '../dto/label.dto'
import { labelValidator } from '../validators'

@Injectable()
export class LabelService {
  constructor(
    @InjectRepository(Label) private readonly labelRepository: Repository<Label>
  ) {}

  async createLabel(data: CreateLabelDto): Promise<Label> {
    const condition = nameWhere(data.name.zh_HK)
    const duplicateLabel = await this.labelRepository
      .createQueryBuilder('label')
      .where(`${condition} and type = '${data.type}'`)
      .getOne()
    if (duplicateLabel) {
      if (data.type === LabelType.CATEGORY) {throw new DuplicateCategoryLabelException()}
      else {throw new DuplicateEducationLabelException()}
    }
    return this.labelRepository.save({
      labelId: generateUniqueId('la'),
      name: data.name,
      type: data.type,
    })
  }

  async batchCreateLabel(names: string[], type: LabelType): Promise<Label[]> {
    return this.labelRepository.save(
      names.map((item) => ({
        labelId: generateUniqueId('la'),
        name: getMultiLanuageKey(item),
        type,
      }))
    )
  }

  async updateLabel(id: number, data: CreateLabelDto): Promise<Label> {
    const condition = nameWhere(data.name.zh_HK)
    const duplicateLabel = await this.labelRepository
      .createQueryBuilder('label')
      .where(`${condition} and type = '${data.type}'`)
      .getOne()
    if (duplicateLabel && duplicateLabel.id !== id) {
      if (data.type === LabelType.CATEGORY) {throw new DuplicateCategoryLabelException()}
      else {throw new DuplicateEducationLabelException()}
    }

    const label = await this.getLabel(id)
    return this.labelRepository.save({ ...label, name: data.name })
  }

  async getLabel(id: number): Promise<Label> {
    const label = await this.labelRepository.findOne({ where: { id } })
    labelValidator(label).exist()
    return label
  }

  async getLabels(ids: number[]): Promise<Label[]> {
    return this.labelRepository.find({ where: { id: In(ids) } })
  }

  async searchLabel(names: string[], type: LabelType): Promise<Label[]> {
    const alias = 'l'
    const condition = multiLabelNameCondition(names, type, alias)
    return this.labelRepository
      .createQueryBuilder(alias)
      .where(
        `(${names
          .map((item) => `all_name like '%"${item}"%'`)
          .join(' OR ')}) AND type = :type`,
        { ...condition.parameter, type }
      )
      .getMany()
  }

  async listLabel(query: ListLabelDto): Promise<PageData<Label>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE, type } = query

    let queryBuilder = this.labelRepository
      .createQueryBuilder('label')
      .where('label.type = :type', { type })
      .orderBy('label.createdAt', 'DESC')
      .take(pageSize)
      .skip((pageIndex - 1) * pageSize)

    if (query.keyword) {
      queryBuilder = queryBuilder.andWhere(nameLikeWhere(query.keyword))
    }

    const data = await queryBuilder.getMany()
    const total = await this.labelRepository.countBy({ type: query.type })
    return { pageIndex, pageSize, items: data, total }
  }

  async listAllLabel(type: LabelType, keyword?: string): Promise<Label[]> {
    let queryBuilder = this.labelRepository
      .createQueryBuilder('label')
      .where('label.type = :type', { type })

    if (keyword) {
      queryBuilder = queryBuilder.andWhere(nameLikeWhere(keyword))
    }

    return queryBuilder.getMany()
  }

  async deleteLabel(id: number, manager: EntityManager) {
    await manager.delete(Label, { id })
  }
}
