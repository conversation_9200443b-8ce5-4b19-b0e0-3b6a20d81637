import { Injectable } from '@nestjs/common'
import R from 'ramda'
import {ILeaderBoardService} from '@/modules/shared/interfaces'
import { RedisService } from '../../../common'
import { EUserType } from '../../../enums'
import { BOOK_DETAIL_BOARD_ZSET, READING_BOARD_ZSET } from '../../constants'

@Injectable()
export class LeaderBoardService implements ILeaderBoardService{
  constructor(private readonly redisService: RedisService) {}

  async increaseReadingTime(
    schoolId: number,
    readingTime: number,
    bookId: number,
    isHidden: boolean
  ) {
    if (readingTime <= 0) {return} 
    if (!isHidden)
    {await this.redisService.instance.zincrby(
      this.getReadingRankingSetName(schoolId, EUserType.STUDENT),
      readingTime,
      String(bookId)
    )}
    await this.redisService.instance.zincrby(
      this.getReadingRankingSetName(schoolId, EUserType.TEACHER),
      readingTime,
      String(bookId)
    )
  }

  async addBooksToReadingRanking(
    options: {
      schoolId: number
      readingTime: number
      bookId: number
      isHidden: boolean
    }[]
  ) {
    const pipeline = this.redisService.instance.pipeline()
    for (const item of options) {
      if (item.readingTime <= 0) {continue}
      if (!item.isHidden) {
        pipeline.zadd(
          this.getReadingRankingSetName(item.schoolId, EUserType.STUDENT),
          item.readingTime,
          String(item.bookId)
        )
      }
      pipeline.zadd(
        this.getReadingRankingSetName(item.schoolId, EUserType.TEACHER),
        item.readingTime,
        String(item.bookId)
      )
    }
    await pipeline.exec()
  }

  async addBooksToReadingRankingByUserType(
    schoolId: number,
    userType: EUserType,
    data: { bookId: number; readingTime: number }[]
  ) {
    await this.redisService.instance.zadd(
      this.getReadingRankingSetName(schoolId, userType),
      R.flatten(data.map((item) => [Number(item.readingTime), String(item.bookId)]))
    )
  }

  async increaseReferenceCount(schoolId: number, bookId: number) {
    await this.redisService.instance.zincrby(
      this.getReferenceRankingSetName(schoolId),
      1,
      String(bookId)
    )
  }

  async getTopReadingRanking(
    schoolId: number,
    type: EUserType,
    start: number,
    end: number
  ) {
    const data = await this.redisService.instance.zrevrange(
      this.getReadingRankingSetName(schoolId, type),
      start,
      end,
      'WITHSCORES'
    )
    return this.convertToReadingRanking(data)
  }

  async getTopReferenceRanking(schoolId: number, start: number, end: number) {
    const data = await this.redisService.instance.zrevrange(
      this.getReferenceRankingSetName(schoolId),
      start,
      end,
      'WITHSCORES'
    )
    return this.covertToReferenceRanking(data)
  }

  async removeReadingRankingForSchool(schoolId: number) {
    await this.redisService.instance.del(
      this.getReadingRankingSetName(schoolId, EUserType.STUDENT)
    )
    await this.redisService.instance.del(
      this.getReadingRankingSetName(schoolId, EUserType.TEACHER)
    )
  }

  async removeBookFromReadingRanking(bookId: number, schoolIds: number[]) {
    const pipeline = this.redisService.instance.pipeline()
    for (const schoolId of schoolIds) {
      pipeline.zrem(
        this.getReadingRankingSetName(schoolId, EUserType.STUDENT),
        String(bookId)
      )
      pipeline.zrem(
        this.getReadingRankingSetName(schoolId, EUserType.TEACHER),
        String(bookId)
      )
    }
    await pipeline.exec()
  }

  async removeBooksForReadingRanking(
    bookIds: number[],
    schoolIds: number[],
    type?: EUserType
  ) {
    const pipeline = this.redisService.instance.pipeline()
    for (const schoolId of schoolIds) {
      for (const bookId of bookIds) {
        if (R.isNil(type) || type === EUserType.STUDENT)
        {pipeline.zrem(
          this.getReadingRankingSetName(schoolId, EUserType.STUDENT),
          String(bookId)
        )}
        if (R.isNil(type) || type === EUserType.TEACHER)
        {pipeline.zrem(
          this.getReadingRankingSetName(schoolId, EUserType.TEACHER),
          String(bookId)
        )}
      }
    }
    await pipeline.exec()
  }

  async removeBooksFromReference(bookIds: number[], schoolIds: number[]) {
    const pipeline = this.redisService.instance.pipeline()
    for (const schoolId of schoolIds) {
      for (const bookId of bookIds) {
        pipeline.zrem(this.getReferenceRankingSetName(schoolId), String(bookId))
      }
    }
    await pipeline.exec()
  }

  async getTotalOfReadingRanking(schoolId: number, type: EUserType) {
    return this.redisService.instance.zcard(this.getReadingRankingSetName(schoolId, type))
  }
  async getReadingRankingByBookId(bookId: string, schoolId: number, type: EUserType) {
    return this.redisService.instance.zscore(
      this.getReadingRankingSetName(schoolId, type),
      bookId
    )
  }

  async getTotalOfReferenceRanking(schoolId: number) {
    return this.redisService.instance.zcard(this.getReferenceRankingSetName(schoolId))
  }

  async removeFromReadingRankingForStudent(bookId: number, schoolId: number) {
    await this.redisService.instance.zrem(
      this.getReadingRankingSetName(schoolId, EUserType.STUDENT),
      String(bookId)
    )
  }

  async addBookToReadingRankingForSchool(
    schoolId: number,
    type: EUserType,
    data: { readingTime: number; bookId: string }[]
  ) {
    await this.redisService.instance.zadd(
      this.getReadingRankingSetName(schoolId, type),
      R.flatten(data.map((item) => [Number(item.readingTime), String(item.bookId)]))
    )
  }

  async addBooksToReferenceRanking(
    schoolId: number,
    data: { total: number; bookId: string }[]
  ) {
    await this.redisService.instance.zadd(
      this.getReferenceRankingSetName(schoolId),
      R.flatten(data.map((item) => [Number(item.total), String(item.bookId)]))
    )
  }

  // async removeReferenceRankingForSchool(schoolId: number) {
  //   await this.redisService.instance.del(this.getReferenceRankingSetName(schoolId))
  // }

  private getReadingRankingSetName(schoolId: number, type: EUserType) {
    return `${READING_BOARD_ZSET}:school:${schoolId}:type:${type}`
  }

  private getReferenceRankingSetName(schoolId: number) {
    return `${BOOK_DETAIL_BOARD_ZSET}:school:${schoolId}`
  }

  private convertToReadingRanking(data: string[]) {
    const result = []
    for (let i = 0; i < data?.length; i += 2) {
      result.push({ bookId: Number(data[i]), readingTime: Number(data[i + 1]) })
    }
    return result
  }

  private covertToReferenceRanking(data: string[]) {
    const result = []
    for (let i = 0; i < data?.length; i += 2) {
      result.push({ bookId: Number(data[i]), viewCount: Number(data[i + 1]) })
    }
    return result
  }
}
