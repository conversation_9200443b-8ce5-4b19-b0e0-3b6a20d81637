import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { In, Repository } from 'typeorm'
import { ELocaleType, ExcelService, generateUniqueId } from '@/common'
import { Publisher } from '@/entities'
import { EBookVersion, EStatus } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { DuplicatePublisherException } from '@/modules/exception'
import { LogService } from '@/modules/system'
import { allNameCondition, nameCondition, nameLikeCondition } from '@/utils'
import { PageData } from '@/interfaces'
import {
  CreatePublisherDto,
  publisherField,
  QueryPublisherDto,
  UpdatePublisherDto,
} from '../dto'
import { publisherValidator } from '../validators'

@Injectable()
export class PublisherService {
  constructor(
    @InjectRepository(Publisher)
    private readonly publisherRepository: Repository<Publisher>,
    private readonly excelService: ExcelService,
    private readonly logService: LogService
  ) {}

  async createPublisher(data: CreatePublisherDto): Promise<Publisher> {
    const duplicated = await this.findPublisher(data.name.zh_HK)
    if (duplicated) {
      throw new DuplicatePublisherException()
    }
    return this.publisherRepository.save({
      ...R.pick(publisherField, data),
      publisherId: generateUniqueId('pu'),
    })
  }

  async updatePublisher(id: number, data: UpdatePublisherDto) {
    const publisher = await this.getPublisher({ id })
    if (data.name) {
      const duplicated = await this.findPublisher(data.name.zh_HK)
      if (duplicated && duplicated.id != publisher.id) {
        throw new DuplicatePublisherException()
      }
    }
    Object.assign(publisher, R.pick(publisherField, data))
    return this.publisherRepository.save(publisher)
  }

  async updatePublisherStatus(ids: number[], status: EStatus) {
    const publishers = await this.findPublishers(ids)
    if (publishers.length !== ids.length) {
      throw new NotFoundException('not found publishers')
    }
    await this.publisherRepository.update({ id: In(ids) }, { status })
    return publishers
  }

  async batchSavePublisher(options) {
    return this.publisherRepository.save(
      options.map((item) => ({
        name: { zh_HK: item.zh_HK, en_uk: item.en_uk, zh_cn: item.zh_cn },
        publisherId: generateUniqueId('pu'),
        description:
          options.description || options.descriptionEn
            ? {
                zh_HK: options.description,
                en_uk: options.descriptionEn,
                zh_cn: options.descriptionCn,
              }
            : null,
      }))
    )
  }

  async getPublisher(options: { publisherId?: string; id?: number }): Promise<Publisher> {
    const publisher = await this.publisherRepository.findOne({
      where: R.pick(['publisherId', 'id'], options),
    })
    publisherValidator(publisher).exist()
    return publisher
  }

  async findPublisher(name: string): Promise<Publisher> {
    const alias = 'p'
    const condition = nameCondition(name, 'p')
    return this.publisherRepository
      .createQueryBuilder(alias)
      .where(condition.where, condition.parameter)
      .getOne()
  }

  async getPublishers(names): Promise<Publisher[]> {
    const whereCondition = names.map((name) => allNameCondition(name)).join(' OR ')
    return this.publisherRepository
      .createQueryBuilder('publisher')
      .where(whereCondition)
      .getMany()
  }

  async findPublishers(ids: number[], relations?: string[]) {
    return this.publisherRepository.find({
      where: { id: In(ids) },
      relations,
      withDeleted: true,
    })
  }

  async searchPublisher(name: string): Promise<Publisher[]> {
    const alias = 'p'
    const condition = nameLikeCondition(name, 'p')
    return this.publisherRepository
      .createQueryBuilder(alias)
      .where(condition.where, condition.parameter)
      .getMany()
  }

  async getAllPublisher() {
    return this.publisherRepository.find()
  }

  async listPublisher(query: QueryPublisherDto): Promise<PageData<Publisher>> {
    const { pageIndex = 1, pageSize = PAGE_SIZE, name } = query
    const alias = 'p'
    const condition = query.name
      ? nameLikeCondition(name, alias)
      : { where: {}, parameter: undefined }

    if (query.status) {
      condition.where = query.name
        ? condition.where + ` AND status = '${query.status}'`
        : ` status = '${query.status}'`
    }

    const queryBuilder = this.publisherRepository
      .createQueryBuilder(alias)
      .where(condition.where, condition.parameter)

    const total = await queryBuilder.getCount()
    const data = await queryBuilder
      .take(pageSize)
      .skip((pageIndex - 1) * pageSize)
      .orderBy({ created_at: 'DESC' })
      .getMany()
    return { pageIndex, pageSize, total, items: data }
  }

  async listPublisherIds(name: string) {
    const alias = 'p'
    const condition = name
      ? nameLikeCondition(name, alias)
      : { where: {}, parameter: undefined }

    const queryBuilder = this.publisherRepository
      .createQueryBuilder(alias)
      .where(condition.where, condition.parameter)

    const data = await queryBuilder.select([`${alias}.id`]).getMany()
    return data.map((item) => item.id)
  }

  async count(options: { version?: EBookVersion; hasScienceRoom?: boolean }) {
    const data = await this.publisherRepository.query(
      `select distinct(publisher_id) as publisher from books where ${
        options.version ? `version like '%${options.version}%'` : ''
      } ${
        R.isNil(options.hasScienceRoom)
          ? ''
          : `is_science_room = ${options.hasScienceRoom}`
      } and deleted_at is null`
    )

    if (data.length === 0) {
      return 0
    }

    const [count] = await this.publisherRepository.query(
      `select count(*) as total from publishers where id in (${data
        .map((item) => item.publisher)
        .filter((item) => !!item)
        .join(',')}) and deleted_at is null`
    )

    return Number(count?.total || 0)
  }

  async export(local: ELocaleType = ELocaleType.ZH_HK, user: any) {
    const ids = await this.publisherRepository.query(
      `select distinct(publisher_id) as id from books where deleted_at is null and version like '%${EBookVersion.SUBSCRIPTION}%'`
    )

    const publishers = await this.publisherRepository.find({
      where: { id: In(ids.map((item) => item.id)) },
    })

    const data = publishers.map((publisher) => ({
      name: publisher.name[local],
      publisherGroupName: publisher.publisherGroupName?.[local] ?? '',
      description: publisher.description?.[local] ?? '',
      contactUserName: publisher.contactUserName ?? '',
      email: publisher.email ?? '',
      address: publisher.address ?? '',
    }))
    await this.logService.save('下载出版商总数量', user)
    return this.excelService.buildExcel({ name: `publishers.${local}`, data })
  }

  async softDeleted(publishers: Publisher[]) {
    return Promise.all(
      publishers.map(async (publisher) => {
        publisher.isDeleted = true
        publisher.name = {
          en_uk: `${publisher?.name?.en_uk || ''}「Deleted」`,
          zh_HK: `${publisher?.name?.en_uk || ''}「已删除」`,
          zh_cn: `${publisher?.name?.en_uk || ''}「已删除」`,
        }
        await this.publisherRepository.update(
          {
            id: publisher.id,
          },
          {
            isDeleted: publisher.isDeleted,
            name: publisher.name,
          }
        )
      })
    )
  }

  async softRemove(ids: number[]) {
    return this.publisherRepository.update(
      {
        id: In(ids),
      },
      {
        deletedAt: new Date(),
      }
    )
  }
}
