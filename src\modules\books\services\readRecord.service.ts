import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import R from 'ramda'
import { Repository } from 'typeorm'
import { getPageResponse } from '@/common'
import { Book, ReadRecord } from '@/entities'
import {EGlobalSummarySplitType, EOrderDirection, EUserType, ReadingTimeOrderType,
} from '@/enums'
import { PatchMultipleUsersPayload } from '@/modules/account/dto/user.dto'
import { PAGE_SIZE } from '@/modules/constants'
import { regionExcelData } from '@/utils'
import { BOOK_DELETED_FLAG, getISBN } from '@/utils/book.utitl'
import {
  getEmptyReadingTimeDto,
  GetGlobalSummaryRequest,
  getReadingTimeDto,
  QueryReadingTimeDto,
  QueryReadingTimeOfStudentByBookDto,
  QueryReportCountDto,
  QuerySchoolPublisherReadingDto,
  QueryStudentReadingTimeDto,
  QueryTopReadingTimeDto,
  QueryTopSchoolLeftReadingTimeDto,
  ReadingTimeDto,
} from '../dto'
import { QueryReadingTime, QueryReadingTimeAdmin } from '../interfaces'
import {
  fillMissingDays,
  fillMissingHours,
  fillMissingMonths,
  fillMissingYears,
} from '../utils'
import { getAuthorNameZh } from '../utils/bookExcel.util'
import { BookRepository } from './book.repository'

@Injectable()
export class ReadRecordService { 
  constructor(
    @InjectRepository(ReadRecord)
    private readonly readRecordRepository: Repository<ReadRecord>,
    private readonly bookRepositories: BookRepository,

    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>
  ) {}

  async getUserReadingBookCount(userId: number): Promise<number> {
    const [data] = await this.readRecordRepository.query(
      `select COUNT(distinct(book_id)) as count from read_record where user_id = ${userId} and reading_time > 0`
    )
    return Number(data.count)
  }

  async adminStatistics(query: QueryReadingTimeAdmin): Promise<ReadingTimeDto> {
    let where
    if (query.categoryId) {
      const books = await this.bookRepositories.searchBooks(
        {
          categoryIds: [query.categoryId],
        },
        { withDeleted: true }
      )
      if (books.length === 0) {return getEmptyReadingTimeDto()}

      where = `t1.book_id IN (${books.map((item) => item.id).join(',')})`
    }

    if (query.publisherId) {
      where = `t1.publisher_id = ${query.publisherId}`
    }

    if (query.authorId) {
      where = `${query.authorId} MEMBER OF(t1.author_id) = 1`
    }

    if (query.labelId) {
      const books = await this.bookRepositories.searchBooks({ labelId: [query.labelId] })
      if (books.length === 0) {return getEmptyReadingTimeDto()}

      where = `t1.book_id IN (${books.map((item) => item.id).join(',')})`
    }

    if (query.level) {
      const books = await this.bookRepositories.searchBooks({ level: [query.level] })
      if (books.length === 0) {return getEmptyReadingTimeDto()}

      where = `t1.book_id IN (${books.map((item) => item.id).join(',')})`
    }

    const [data] = await this.readRecordRepository.query(
      `select 
       COUNT(DISTINCT t1.user_id) as totalUser, 
       COUNT(DISTINCT t1.book_id) AS totalBook,
       SUM(t1.reading_time) as totalReadingTime
    from read_record t1, books t2 where ${where} and t1.reading_time > 0 and t1.book_id = t2.id and t1.publisher_id = t2.publisher_id`
    )

    return getReadingTimeDto(data)
  }

  async readingUserCountByHour(schoolId: number, query: QueryReadingTimeDto) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND school_id = ${schoolId}`

    return this.readRecordRepository.query(
      `
        select
          date,
          COUNT(distinct(user_id)) as count,
          SUM(reading_time) as totalReadingTime
        from
          (
            select
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date,
              reading_time,
              user_id
            from
              read_record
            where
              ${where}
          ) t
        group by
          date
      `
    )
  }

  async userReadingTime(userId: number, query: QueryReadingTimeDto) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND user_id = ${userId}`

    return await this.readRecordRepository.query(
      `
        select
          date,
          COUNT(distinct(user_id)) as count,
          SUM(reading_time) as totalReadingTime
        from
          (
            select
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date,
              reading_time,
              user_id
            from
              read_record
            where
              ${where}
          ) t
        group by
          date
      `
    )
  }

  async readingUserCountByClass(schoolId: number, query: QueryReadingTimeDto) {
    const where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND t1.school_id = ${schoolId}`

    const studentWhere = `${where} AND t1.user_type ='${EUserType.STUDENT}'`
    const student = await this.readRecordRepository.query(
      `
        select
          COUNT(DISTINCT t1.user_id) as totalUser,
          t2.grade_id as grade,
          t2.id as class
        from
          read_record t1,
          user_class t2
        where
          t1.class = t2.id
          and ${studentWhere}
        group by
          t2.grade_id,
          t2.id
      `
    )

    const teacherWhere = `${where} AND user_type ='${EUserType.TEACHER}'`
    const [teacher] = await this.readRecordRepository.query(
      `select COUNT(DISTINCT t1.user_id) as totalUser from read_record t1 where ${teacherWhere}`
    )

    const totalUser = teacher.totalUser ?? 0
    return { student, teacher: { totalUser } }
  }

  async readingTimeByClass(schoolId: number, query: QueryReadingTimeDto) {
    const where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND t1.school_id = ${schoolId}`

    const studentWhere = `${where} AND t1.user_type ='${EUserType.STUDENT}'`

    const student = await this.readRecordRepository.query(
      `
        select
          SUM(t1.reading_time) as totalReadingTime,
          t2.grade_id as grade,
          t2.id as class
        from
          read_record t1,
          user_class t2
        where
          t1.class = t2.id
          and ${studentWhere}
        group by
          t2.grade_id,
          t2.id 
      `
    )

    const teacherWhere = `${where} AND t1.user_type ='${EUserType.TEACHER}'`
    const [teacher] = await this.readRecordRepository.query(
      `select SUM(t1.reading_time) as totalReadingTime from read_record t1 where ${teacherWhere}`
    )

    const totalReadingTime = teacher.totalReadingTime ?? 0
    return { student, teacher: { totalReadingTime } }
  }

  async readingTimeBySchool(query: QueryReadingTimeDto) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    return this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, school_id as schoolId from read_record where ${where} group by school_id`
    )
  }

  async readingCount(query: QueryReadingTime) {
    let where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`

    if (query.authorId) {
      where = where + ` AND ${query.authorId} MEMBER OF(\`author_id\`)`
    }

    if (query.bookId) {
      where = where + ` AND book_id = ${query.bookId}`
    } else if (query.bookIds) {
      where = where + ` AND book_id IN (${query.bookIds.join(',')})`
    }

    if (query.publisherId) {
      where = where + ` AND publisher_id = ${query.publisherId}`
    }

    if (query.schoolId) {
      where = where + ` AND school_id = ${query.schoolId}`
    }

    return this.readRecordRepository.query(`
      select
        date,
        COUNT(distinct(user_id)) as totalUser,
        SUM(reading_time) as totalReadingTime
      from
        (
          select
            DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date,
            reading_time,
            user_id
          from
            read_record
          where
            ${where}
        ) t
      group by
        date
    `)
  }

  async readingTimeCount(bookIds: number[]) {
    return this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, book_id as bookId from read_record where book_id In (${bookIds.join(
        ','
      )}) group by book_id`
    )
  }

  async readingTimeAndUser(query) {
    let where = undefined

    if (query.authorId) {
      where = `${query.authorId} MEMBER OF(\`author_id\`)`
    }

    if (query.bookId) {
      where = `book_id = ${query.bookId}`
    } else if (query.bookIds) {
      where = `book_id IN (${query.bookIds.join(',')})`
    }

    if (query.publisherId) {
      where = `publisher_id = ${query.publisherId}`
    }

    where = where ? `where ${where}` : ''

    const [data] = await this.readRecordRepository.query(
      `select COUNT(DISTINCT user_id) as totalUser, SUM(reading_time) as totalReadingTime from read_record ${where}`
    )

    return {
      totalUser: data.totalUser ?? 0,
      totalReadingTime: data.totalReadingTime ?? 0,
    }
  }

  async countBookReadingUsers(schoolId: number, bookIds: number[]) {
    const where = `school_id = ${schoolId} AND book_id IN (${bookIds.join(',')})`

    const data = await this.readRecordRepository.query(
      `select count(DISTINCT user_id) as totalUser, book_id as bookId from read_record where ${where} group by book_id`
    )

    return data.map((item) => ({
      totalUser: Number(item.totalUser),
      bookId: Number(item.bookId),
    }))
  }

  async listStudentReading(
    schoolId: number,
    query: QueryStudentReadingTimeDto,
    userIds: number[] = [],
    bookIds: number[] = []
  ) {
    let where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND t1.school_id = ${schoolId} AND t1.user_type = 'student' and t1.reading_time > 0 `

    if (query.grade) {
      where = where + ` AND t3.grade_id = ${query.grade}`
    }

    if (query.class) {
      where = where + ` AND t2.user_class_id = ${query.class}`
    }

    if (userIds.length || bookIds.length) {
      let condition = userIds.length ? `t1.user_id IN (${userIds.join(',')})` : ''
      condition += bookIds.length
        ? condition.length
          ? ` OR t1.book_id IN (${bookIds.join(',')})`
          : `t1.book_id IN (${bookIds.join(',')})`
        : ''
      where = where + ` AND (${condition} )`
    }

    const {
      pageIndex,
      pageSize,
      allBooksReadingTimeOrderDirection = EOrderDirection.DESC,
      orderDirection = EOrderDirection.DESC,
    } = query
    const limitCondition =
      pageIndex && pageSize
        ? `limit ${pageSize} offset ${(pageIndex - 1) * pageSize}`
        : ''

    const orderBy = query.allBooksReadingTimeOrderDirection
      ? `allBooksTotalReadingTime ${allBooksReadingTimeOrderDirection}, t1.bookId ${allBooksReadingTimeOrderDirection}`
      : `totalReadingTime ${orderDirection}, t1.bookId ${orderDirection}`

    const [{ total }] = await this.readRecordRepository.query(`
      select count(*) as total from (
        select t1.user_id as userId, SUM(t1.reading_time) as totalReadingTime, t1.book_id as bookId 
        from read_record t1, users t2, user_class t3 where  t1.user_id = t2.id and t2.user_class_id = t3.id and ${where} 
        group by t1.book_id, t1.user_id
      ) t
    `)

    if (!total || total === 0)
    {return getPageResponse({ pageIndex, pageSize, items: [], total: 0 })}

    const items = await this.readRecordRepository.query(`
      select t1.classId,t1.gradeId,t1.userId, t1.totalReadingTime, t1.bookId, t2.allBooksTotalReadingTime 
      from (
        select t1.class as classId,t1.grade as gradeId,t1.user_id as userId, SUM(t1.reading_time) as totalReadingTime, t1.book_id as bookId 
        from read_record t1, users t2, user_class t3  
        where t1.user_id = t2.id and t2.user_class_id = t3.id and ${where} 
        group by t1.book_id, t1.user_id,t1.class,t1.grade
      ) t1 left join (
        select t1.user_id as userId, SUM(t1.reading_time) as allBooksTotalReadingTime 
        from read_record t1, users t2, user_class t3  
        where t1.user_id = t2.id and t2.user_class_id = t3.id and ${where} 
        group by t1.user_id
      ) t2 on t1.userId = t2.userId
      order by ${orderBy} ${limitCondition};
      `)

    if (!items || items.length <= 0)
    {return getPageResponse({ pageIndex, pageSize, items: [], total: 0 })}

    return getPageResponse({ pageIndex, pageSize, items, total })
  }

  /**
   * 获取所有阅读过的书籍 userIds
   * @param schoolId
   */
  async getReadingUserIdsBySchool(schoolId: number, query: any) {
    const sql = `
        SELECT
          RR.user_id
        FROM
          read_record RR
        WHERE
          RR.school_id = ${schoolId}
          AND RR.user_type = 'student'
          AND RR.reading_time > 0
          AND RR.created_at BETWEEN "${new Date(
    query.startTime * 1000
  ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}"
    `
    const userIds = await this.readRecordRepository.query(sql)
    return userIds.map((record) => record.user_id)
  }

  /**
   * 获取所有阅读过的书籍
   * @param userId
   */
  async listReadBooks(userId: number, schoolId: number, query: any) {
    const sql = `
    WITH
      record_with_year AS (
        SELECT
          RR.book_id,
          B.\`name\`,
          CASE
            WHEN MONTH(RR.created_at) >= 9 THEN CONCAT(YEAR(RR.created_at), '-', YEAR(RR.created_at) + 1)
            ELSE CONCAT(YEAR(RR.created_at) - 1, '-', YEAR(RR.created_at))
          END AS school_year,
          RR.created_at,
          RR.reading_time,
          G.grade as old_grade,
          UC.class as old_class
        FROM
          read_record RR
        LEFT JOIN
          books B ON B.id = RR.book_id
        LEFT JOIN
          user_class UC ON UC.id = RR.class
        LEFT JOIN
          grades G ON G.id = UC.grade_id
        WHERE
          RR.user_id = ${userId}
          AND RR.school_id = ${schoolId}
          AND RR.user_type = 'student'
          AND RR.reading_time > 0
          AND RR.created_at BETWEEN "${new Date(
    query.startTime * 1000
  ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}"
      ),
      grouped_by_year AS (
        SELECT
          book_id,
          name,
          school_year,
          SUM(reading_time) AS total_reading_time,
          MIN(created_at) AS start_date,
          MAX(created_at) AS end_date,
          old_grade,
          old_class
        FROM
          record_with_year
        GROUP BY
          book_id,
          name,
          school_year,
          old_grade,
          old_class
      )
      SELECT
        book_id,
        name,
        school_year,
        total_reading_time,
        start_date,
        end_date,
          old_grade,
          old_class
      FROM
        grouped_by_year
      ORDER BY
        MAX(total_reading_time) OVER (PARTITION BY book_id) DESC,
        book_id,
        total_reading_time desc;                
    `

    const books = await this.readRecordRepository.query(sql)
    return books
  }

  /**
   * 获取所有阅读过的书籍
   * @param userId
   * @param schoolId
   * @param query
   * @returns
   */
  async listReadBooksPage(userId: number, schoolId: number, query: any) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    await this.readRecordRepository.query('SET SESSION group_concat_max_len = 10000')
    // 子查询：计算每本书的总阅读时间
    const subQuery = this.readRecordRepository
      .createQueryBuilder('readRecord')
      .select([
        'readRecord.book_id AS bookId',
        'SUM(readRecord.reading_time) AS totalReadingTime',
      ])
      .where('readRecord.user_id = :userId', { userId })
      .andWhere('readRecord.reading_time > 0')
      .andWhere('readRecord.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('readRecord.book_id')

    // 构建 book 子查询，包含软删除数据
    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .withDeleted()
      .leftJoin('book.authors', 'authors')
      .select([
        'book.id',
        'book.name',
        'book.cover_url',
        'GROUP_CONCAT(DISTINCT authors.name) AS authorsList',
      ])
      .groupBy('book.id, book.name, book.cover_url')

    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    const queryBuilder = this.readRecordRepository
      .createQueryBuilder('readRecord')
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        'book.book_id = readRecord.book_id',
        bookSubQueryParams
      )
      .innerJoin(
        '(' + subQuery.getQuery() + ')',
        'readingTimeData',
        'readingTimeData.bookId = readRecord.book_id'
      )
      .select([
        'book.book_id AS id',
        'book.book_name AS name',
        'book.cover_url AS cover_url',
        'book.authorsList AS authors',
        'readingTimeData.totalReadingTime AS reading_time',
      ])
      .groupBy('book.book_id, book.book_name, readingTimeData.totalReadingTime')
      .orderBy('reading_time', orderDirection)
      .offset(offset)
      .limit(pageSize)
      .setParameters(subQuery.getParameters())

    const totalQueryBuilder = this.readRecordRepository
      .createQueryBuilder('readRecord')
      .select('COUNT(DISTINCT readRecord.book_id)', 'count')
      .innerJoin(
        '(' + subQuery.getQuery() + ')',
        'readingTimeData',
        'readingTimeData.bookId = readRecord.book_id'
      )
      .where('readRecord.user_id = :userId', { userId })
      .andWhere('readRecord.reading_time > 0')
      .andWhere('readRecord.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .setParameters(subQuery.getParameters())

    const total = await totalQueryBuilder.getRawOne()
    const books = await queryBuilder.getRawMany()

    const totalReadingTime = await this.readRecordRepository
      .createQueryBuilder('readRecord')
      .select(['SUM(readRecord.reading_time) AS totalReadingTime'])
      .where('readRecord.user_id = :userId', { userId })
      .andWhere('readRecord.reading_time > 0')
      .andWhere('readRecord.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .getRawOne()

    return {
      totalReadingTime: parseInt(totalReadingTime.totalReadingTime) ?? 0,
      items: books.map((book) => ({
        ...book,
        authors: book.authors ? JSON.parse('[' + book.authors + ']') : [],
      })),
      total: total.count,
      pageIndex,
      pageSize,
    }
  }

  async getPublisherReadingTimeCSV(query: QueryReadingTimeDto, books: number[]) {
    const [count] = await this.readRecordRepository.query(`
    select
      count(*) as total
    from
    (
      select
        r.book_id
      from
        read_record r
      where
        r.book_id in (${books.join(',')})
        and r.publisher_id = ${query.publisherId}
        and (r.created_at BETWEEN "${new Date(
    query.startTime * 1000
  ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}")
        and r.reading_time > 0
      group by
        r.book_id,
        r.region
    ) as t
  `)

    const total = Number(count?.total) || 0
    let pageIndex = 1
    const pageSize = 200
    let items = []
    while ((pageIndex - 1) * pageSize < total) {
      const data = await this.readRecordRepository.query(`
      select
        COUNT(DISTINCT r.user_id) as users,
        SUM(r.reading_time) as readingTime,
        r.book_id as bookId,
        r.region
      from
        read_record r
      where
        r.book_id in (${books.join(',')})
        and r.publisher_id = ${query.publisherId}
        and (r.created_at BETWEEN "${new Date(
    query.startTime * 1000
  ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}")
        and r.reading_time > 0
      group by
        r.book_id,
        r.region
      limit ${pageSize}
      offset ${(pageIndex - 1) * pageSize}
  `)
      items = items.concat(data)
      pageIndex += 1
    }

    return items
  }

  async exportPublisherReadingTimeCSV(query: QueryReadingTimeDto) {
    const conditions: string[] = [
      `r.reading_time > 0`,
      `r.created_at BETWEEN "${new Date(
        query.startTime * 1000
      ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}"`,
    ]

    // 根据 query 中的 publisherId 动态添加条件
    if (query.publisherId) {
      conditions.push(`r.publisher_id = "${query.publisherId}"`)
    }

    const [count] = await this.readRecordRepository.query(`
      select
        count(*) as total
      from
      (
        select
          r.book_id
        from
          read_record r
        where
          ${conditions.join(' and ')} 
        group by
          r.book_id,
          r.region
      ) as t
    `)
    const total = Number(count?.total) || 0
    let pageIndex = 1
    const pageSize = 200
    let items = []
    while ((pageIndex - 1) * pageSize < total) {
      const data = await this.readRecordRepository.query(`
        select
          COUNT(DISTINCT r.user_id) as users,
          SUM(r.reading_time) as readingTime,
          r.book_id as bookId,
          r.region
        from
          read_record r
        where
          ${conditions.join(' and ')} 
        group by
          r.book_id,
          r.region
        limit ${pageSize}
        offset ${(pageIndex - 1) * pageSize}
    `)
      items = items.concat(data)
      pageIndex += 1
    }

    const ids = [...new Set(items.map((item) => item.bookId))]
    let books = []
    pageIndex = 1
    while ((pageIndex - 1) * pageSize < ids.length) {
      const data = await this.bookRepositories.listBooks(
        {
          ids: ids.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
        },
        {
          relations: ['publisher', 'authors'],
          fields: ['isbn', 'name', 'publishedAt', 'id'],
          withDeleted: true,
        }
      )
      books = books.concat(data)
      pageIndex += 1
    }

    books.sort((a, b) => a.publisher?.id - b.publisher?.id)
    const regions = [...new Set(items.map((item) => item.region))]

    return books.map((book) => {
      const reading = items.filter((item) => Number(item.bookId) === book.id)

      return {
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        name: book.publisher?.name?.zh_HK,
        publisherGroupName: book.publisher?.publisherGroupName?.zh_HK ?? '',
        isDelete: book.isbn.includes(BOOK_DELETED_FLAG) ? '已删除' : '',
        isbn: getISBN(book.isbn),
        bookName: book.name.zh_HK,
        author: book.authors?.map((x) => getAuthorNameZh(x.name)).join(','),
        publisherAt: moment.tz(book.publishedAt, 'Asia/Hong_Kong').format('YYYY'),
        userCount: reading.reduce((acc, cur) => acc + Number(cur.users), 0) || 0,
        readingTime:
          reading.reduce((acc, cur) => acc + Number(cur.readingTime), 0) / 3600 || 0,
        ...regionExcelData(
          reading.map((x) => ({
            region: x.region,
            totalReadingTime: Number(x.readingTime),
            totalUser: Number(x.users),
          }))
        ),
        regions,
      }
    })
  }

  async listStudentReadingByBook(
    query: QueryReadingTimeOfStudentByBookDto,
    schoolId: number
  ) {
    let where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") and t1.reading_time > 0 AND t1.user_type = '${EUserType.STUDENT}'`

    if (schoolId) {
      where += ` AND t1.school_id = ${schoolId}`
    }

    const { pageIndex, pageSize, orderBy, direction = EOrderDirection.DESC } = query

    const limit =
      pageIndex && pageSize
        ? `limit ${pageSize} offset ${(pageIndex - 1) * pageSize}`
        : ''
    let items = []
    const data = await this.readRecordRepository.query(
      `SELECT 
         t1.book_id as bookId, 
         t1.publisher_id as publisherId, 
         p.name as publisherName,
         p.publisher_group_name as publisherGroupName 
       FROM read_record t1
       LEFT JOIN publishers p ON t1.publisher_id = p.id
       WHERE ${where}
       GROUP BY t1.book_id, t1.publisher_id, p.name, p.publisher_group_name`
    )

    if (data?.length) {
      const sql = `  
        select COUNT(DISTINCT t1.user_id) as totalUser, 
          SUM(t1.reading_time) as totalReadingTime,
          t1.book_id as bookId,
          t1.publisher_id as publisherId,
          p.name as publisherName,
          p.publisher_group_name as publisherGroupName 
        from read_record t1
        LEFT JOIN publishers p ON t1.publisher_id = p.id
        where ${where}
        group by t1.book_id, t1.publisher_id, t1.publisher_id, p.name, p.publisher_group_name
        order by ${
  orderBy === ReadingTimeOrderType.TOTAL_USER ? 'totalUser' : 'totalReadingTime'
} ${direction} ${limit}
      `
      items = await this.readRecordRepository.query(sql)
    }

    return getPageResponse({ pageIndex, pageSize, items, total: data?.length ?? 0 })
  }

  async timeDistribution(schoolId: number, query: QueryReadingTimeDto) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") AND school_id = ${schoolId}`

    const items = await this.readRecordRepository.query(
      `
        select
          date,
          COUNT(distinct(user_id)) as totalUser,
          SUM(reading_time) as totalReadingTime
        from
          (
            select
              DATE(CONVERT_TZ(created_at, 'UTC', 'Asia/Hong_Kong')) as date,
              reading_time,
              user_id
            from
              read_record
            where
              ${where}
          ) t
        group by
          date
      `
    )

    return items
  }

  async top50School(query: QueryTopReadingTimeDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`
    const data = await this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, school_id as schoolId from read_record  where ${where} group by school_id order by totalReadingTime desc limit ${pageSize} offset ${
        (pageIndex - 1) * pageSize
      }`
    )

    const [count] = await this.readRecordRepository.query(
      `select COUNT(distinct(school_id)) as total from read_record  where ${where}`
    )
    return {
      total: Number(count.total),
      pageIndex,
      pageSize,
      items: data.map((item) => ({
        ...item,
        totalReadingTime: Number(item.totalReadingTime),
      })),
    }
  }

  async regionReading(
    query: QueryReportCountDto,
    options: { publisherIds?: number[]; bookIds?: number[] }
  ) {
    let where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`
    let group = ''

    let feilds =
      ' SUM(t1.reading_time) as totalReadingTime, count(distinct(t1.user_id)) as totalUser, t1.school_id as schoolId'
    if (options.publisherIds?.length) {
      feilds += ', t1.publisher_id as id '
      where += `and t1.publisher_id in (${options.publisherIds.join(',')}) `
      group = 't1.publisher_id, t1.school_id'
    } else if (options.bookIds.length) {
      feilds += ', t1.book_id as id '
      where += `and t1.book_id in (${options.bookIds.join(',')}) `
      group = 't1.book_id, t1.school_id'
    }
    const regionReading = await this.readRecordRepository.query(
      `select ${feilds} from read_record t1, books t2  where ${where} and  t1.book_id = t2.id and t1.publisher_id = t2.publisher_id  group by ${group}`
    )

    const schoolIds = [...new Set(regionReading.map((item) => item.schoolId))] as number[]

    const schools = schoolIds.length
      ? await this.readRecordRepository.query(
        `select id, region from schools where id in (${schoolIds.join(',')})`
      )
      : []

    const readingWithRegion = regionReading.map((item) => ({
      ...item,
      totalReadingTime: Number(item.totalReadingTime),
      totalUser: Number(item.totalUser),
      region: schools.find((school) => school.id === item.schoolId)?.region ?? 'HK',
    }))
    const readData = []
    for (const item of readingWithRegion) {
      const index = readData.findIndex(
        (read) => read.id === item.id && read.region === item.region
      )
      if (index === -1) {
        readData.push(item)
      } else {
        readData[index].totalReadingTime += item.totalReadingTime
        readData[index].totalUser += item.totalUser
      }
    }
    const ids = options.bookIds?.length ? options.bookIds : options.publisherIds
    return ids.map((id) => ({
      id,
      data: readData.filter((item) => item.id === id),
    }))
  }

  async top50Publisher(query: QueryTopReadingTimeDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`
    const data = await this.readRecordRepository.query(
      `select SUM(t1.reading_time) as totalReadingTime, t1.publisher_id as publisherId 
      from read_record t1, books t2  where ${where} and  t1.book_id = t2.id and t1.publisher_id = t2.publisher_id  
      group by t1.publisher_id order by totalReadingTime desc limit ${pageSize} offset ${
  (pageIndex - 1) * pageSize
}`
    )

    const [count] = await this.readRecordRepository.query(
      `select COUNT(distinct(t1.publisher_id)) as total from read_record t1, books t2  where ${where} and t1.book_id = t2.id and t1.publisher_id = t2.publisher_id`
    )
    return {
      total: Number(count.total),
      pageIndex,
      pageSize,
      items: data.map((item) => ({
        ...item,
        totalReadingTime: Number(item.totalReadingTime),
      })),
    }
  }

  async regionAuthor(query: QueryTopReadingTimeDto, authorIds: number[]) {
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") and (${authorIds
      .map((item) => ` ${item} MEMBER OF (\`author_id\`) `)
      .join(' OR ')})`
    const data = await this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, author_id as authorId, school_id as schoolId from read_record  where ${where} group by author_id, school_id order by totalReadingTime desc`
    )

    const regionReading = R.flatten(
      data.map((item) =>
        item.authorId.map((authorId) => ({
          authorId,
          totalReadingTime: Number(item.totalReadingTime),
          schoolId: item.schoolId,
        }))
      )
    )

    const schoolIds = [...new Set(regionReading.map((item) => item.schoolId))] as number[]
    const schools = schoolIds.length
      ? await this.readRecordRepository.query(
        `select id, region from schools where id in (${schoolIds.join(',')})`
      )
      : []
    const readingWithRegion = regionReading.map((item) => ({
      ...item,
      totalReadingTime: Number(item.totalReadingTime),
      region: schools.find((school) => school.id === item.schoolId)?.region ?? 'HK',
    }))
    const readData = []
    for (const item of readingWithRegion) {
      const index = readData.findIndex(
        (read) => read.authorId === item.authorId && read.region === item.region
      )
      if (index === -1) {
        readData.push(item)
      } else {
        readData[index].totalReadingTime += item.totalReadingTime
      }
    }
    return authorIds.map((authorId) => ({
      authorId,
      data: readData.filter((item) => item.authorId === authorId),
    }))
  }

  async top50Author(query: QueryTopReadingTimeDto) {
    const { pageIndex = 1 } = query
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(query.endTime * 1000).toISOString()}")`
    const data = await this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, author_id as authorId from read_record  where ${where} group by author_id order by totalReadingTime desc`
    )

    if (data.length === 0)
    {return { total: 0, pageIndex, pageSize: query.pageSize, items: [] }}

    const signalData = R.flatten(
      data.map((item) =>
        item.authorId.map((authorId) => ({
          authorId,
          totalReadingTime: Number(item.totalReadingTime),
        }))
      )
    )

    const items = []
    const readingData = signalData.reduce((pre, curr) => {
      const index = pre.findIndex((item) => item.authorId === curr.authorId)
      if (index === -1) {
        pre.push(curr)
      } else {
        pre[index].totalReadingTime = pre[index].totalReadingTime + curr.totalReadingTime
      }
      return pre
    }, items)

    const sortedData = readingData.sort((a, b) => b.totalReadingTime - a.totalReadingTime)

    const pageSize = query.pageSize || sortedData.length

    const skip = (pageIndex - 1) * pageSize
    const page = sortedData.slice(skip, skip + pageSize)
    const ids = page.map((item) => item.authorId)
    // const authors = await this.authorService.getAuthors(ids, true)

    const authors = await this.readRecordRepository.query(
      `select id, name,description from authors where id in (${ids.join(',')})`
    )

    return {
      total: readingData.length || 0,
      pageIndex,
      pageSize,
      items: page.map((item) => {
        const author = authors.find((a) => Number(a.id) === Number(item.authorId))
        return {
          id: author?.id,
          name: author?.name,
          description: author?.description,
          totalReadingTime: Number(item.totalReadingTime),
        }
      }),
    }
  }

  async top50SchoolLeft(query: QueryTopSchoolLeftReadingTimeDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    const sql = `
      select 
        t1.total_bought_quota as totalBoughtQuota,
        t1.school_id as schoolId,
        ifnull(t2.used_quota, 0) as usedQuota
      from school_balances t1
      left join (
          select SUM(reading_time) as used_quota, school_id from read_record group by school_id
      ) t2 on t1.school_id = t2.school_id
      order by (totalBoughtQuota - usedQuota) ${query.sortDirection}
      limit ${pageSize} offset ${(pageIndex - 1) * pageSize};
    `

    const data = await this.readRecordRepository.query(sql)

    const [count] = await this.readRecordRepository.query(
      `select COUNT(*) as total from school_balances`
    )
    return {
      total: Number(count.total),
      pageIndex,
      pageSize,
      items: data.map((item) => ({
        ...item,
        totalReadingTime: Number(item.totalBoughtQuota) - Number(item.usedQuota),
      })),
    }
  }

  async schoolPublisherReading(query: QuerySchoolPublisherReadingDto) {
    const where = `(t1.created_at BETWEEN "${new Date(
      query.startTime * 1000
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000
    ).toISOString()}") and t1.publisher_id = ${query.publisherId}`
    return this.readRecordRepository.query(
      `select SUM(t1.reading_time) as totalReadingTime, t1.school_id as schoolId 
      from read_record t1, books t2  where t1.book_id = t2.id and t1.publisher_id = t2.publisher_id and ${where} 
      group by school_id order by totalReadingTime desc`
    )
  }

  async getSchoolUsedTime(schoolId: number) {
    const [data] = await this.readRecordRepository.query(
      `select SUM(reading_time) as totalReadingTime, school_id as schoolId from read_record  where school_id = ${schoolId}`
    )
    return Number(data?.totalReadingTime ?? 0)
  }

  async getGlobalSummary(query: GetGlobalSummaryRequest) {
    const { splitType = EGlobalSummarySplitType.DAY, startTime, endTime } = query

    const summary = await this.readRecordRepository.query(
      `select sum(reading_time) as globalTotalReadingTime from read_record;`
    )

    if (splitType === EGlobalSummarySplitType.HOUR) {
      const items = await this.readRecordRepository.query(`
      select sum(reading_time) as totalReadingTime, real_hour, hour 
      from (
        select reading_time, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%H') as hour,  date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%Y/%m/%d %H') as real_hour,
        unix_timestamp(created_at) as created_at 
        from read_record where unix_timestamp(created_at) > ${startTime} and  unix_timestamp(created_at) < ${endTime}
      ) t 
      group by real_hour, hour;`)

      if (!items || items.length <= 0)
      {return {
        splitType,
        items: [],
        summary: {
          periodTotalReadingTime: 0,
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }}

      return {
        splitType,
        items: fillMissingHours(
          startTime,
          endTime,
          items.map((x) => ({
            hour: x.hour,
            realHour: x.real_hour,
            totalReadingTime:
              Number(((Number(x.totalReadingTime) / 3600) * 100).toFixed()) / 100,
          })),
          { totalReadingTime: 0 }
        ),
        summary: {
          periodTotalReadingTime: R.sum(items.map((x) => Number(x.totalReadingTime))),
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }
    }

    if (splitType === EGlobalSummarySplitType.DAY) {
      const items = await this.readRecordRepository.query(`
      select sum(reading_time) as totalReadingTime, real_day, day 
      from (
        select reading_time, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%d/%m') as day, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%Y/%m/%d') as real_day,
        unix_timestamp(created_at) as created_at 
        from read_record where unix_timestamp(created_at) > ${startTime}  and  unix_timestamp(created_at) < ${endTime}
      ) t 
      group by real_day, day;`)

      if (!items || items.length <= 0)
      {return {
        splitType,
        items: [],
        summary: {
          periodTotalReadingTime: 0,
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }}

      return {
        splitType,
        items: fillMissingDays(
          startTime,
          endTime,
          items.map((x) => ({
            day: x.day,
            realDay: x.real_day,
            totalReadingTime:
              Number(((Number(x.totalReadingTime) / 3600) * 100).toFixed()) / 100,
          })),
          { totalReadingTime: 0 }
        ),
        summary: {
          periodTotalReadingTime: R.sum(items.map((x) => Number(x.totalReadingTime))),
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }
    }

    if (splitType === EGlobalSummarySplitType.MONTH) {
      const items = await this.readRecordRepository.query(`
      select sum(reading_time) as totalReadingTime, real_month, month 
      from (
        select reading_time, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%m') as month, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%Y/%m') as real_month,
        unix_timestamp(created_at) as created_at 
        from read_record where unix_timestamp(created_at) > ${startTime}  and  unix_timestamp(created_at) < ${endTime}
      ) t 
      group by real_month, month;`)

      if (!items || items.length <= 0)
      {return {
        splitType,
        items: [],
        summary: {
          periodTotalReadingTime: 0,
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }}

      return {
        splitType,
        items: fillMissingMonths(
          startTime,
          endTime,
          items.map((x) => ({
            month: x.month,
            realMonth: x.real_month,
            totalReadingTime:
              Number(((Number(x.totalReadingTime) / 3600) * 100).toFixed()) / 100,
          })),
          { totalReadingTime: 0 }
        ),
        summary: {
          periodTotalReadingTime: R.sum(items.map((x) => Number(x.totalReadingTime))),
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }
    }

    if (splitType === EGlobalSummarySplitType.YEAR) {
      const items = await this.readRecordRepository.query(`
      select sum(reading_time) as totalReadingTime, year 
      from (
        select reading_time, date_format(CONVERT_TZ(created_at, 'SYSTEM', '+8:00'), '%Y') as year,
        unix_timestamp(created_at) as created_at 
        from read_record where unix_timestamp(created_at) > ${startTime}  and  unix_timestamp(created_at) < ${endTime}
      ) t 
      group by year`)

      if (!items || items.length <= 0)
      {return {
        splitType,
        items: [],
        summary: {
          periodTotalReadingTime: 0,
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }}

      return {
        splitType,
        items: fillMissingYears(
          startTime,
          endTime,
          items.map((x) => ({
            year: x.year,
            totalReadingTime:
              Number(((Number(x.totalReadingTime) / 3600) * 100).toFixed()) / 100,
          })),
          { totalReadingTime: 0 }
        ),
        summary: {
          periodTotalReadingTime: R.sum(items.map((x) => Number(x.totalReadingTime))),
          globalTotalReadingTime: Number(summary[0].globalTotalReadingTime),
        },
      }
    }
  }

  async patchUsersReadRecord(schoolId: number, items: PatchMultipleUsersPayload[]) {
    if (items.length <= 0) {return}
    let sql = ``
    const currentYear = new Date().getFullYear()
    const startOfSchoolYear = `${currentYear}-09-01 00:00:00`
    const endOfSchoolYear = `${currentYear + 1}-08-31 23:59:59`
    const students = items.filter((x) => x.type === EUserType.STUDENT)
    if (students.length > 0) {
      // class
      sql = `
        UPDATE read_record 
        SET class = CASE user_id 
      `
      for (const item of students) {
        const { id, userClassId } = item
        sql = `${sql}
          WHEN ${id} THEN ${userClassId}
          `
      }
      sql = `${sql}
        END   
      `
      // grade
      sql = `${sql},
        grade = CASE user_id
      `
      for (const item of students) {
        const { id, userGradeId } = item
        sql = `${sql}
          WHEN ${id} THEN ${userGradeId}
          `
      }
      sql = `${sql}
        END   
      `
    }

    sql = `${sql}
        WHERE user_id IN ( ${items.map((x) => x.id).join(',')}  )
        AND created_at BETWEEN '${startOfSchoolYear}' AND '${endOfSchoolYear}'
        AND school_id = ${schoolId}
        `
    // console.log('patchUsersReadRecord sql:', sql)
    return this.readRecordRepository.query(sql)
  }

  async getTop100ReadingStudents(query: QueryStudentReadingTimeDto) {
    const where = `rr.created_at BETWEEN ? AND ?`
    const params = [
      new Date(query.startTime * 1000).toISOString(),
      new Date(query.endTime * 1000).toISOString(),
    ]

    return await this.readRecordRepository.query(
      `
      WITH TopStudents AS (
          SELECT 
              u.id AS student_id,
              u.given_name AS student_name,
              u.email AS email,
              g.grade AS current_grade,
              uc.class AS current_class,
              sch.name AS school_name,
              ROUND(SUM(rr.reading_time) / 3600, 2) AS total_reading_hours,
              COUNT(DISTINCT rr.book_id) AS total_books_read
          FROM 
              users u
          LEFT JOIN 
              read_record rr ON u.id = rr.user_id
          LEFT JOIN 
              user_class uc ON u.user_class_id = uc.id
          LEFT JOIN 
              grades g ON uc.grade_id = g.id
          LEFT JOIN 
              schools sch ON uc.school_id = sch.id
          WHERE 
              ${where}
          GROUP BY 
              u.id, g.grade, uc.class
          ORDER BY 
              total_reading_hours DESC
          LIMIT 100
      )
      SELECT 
          ts.school_name,
          ts.student_name, 
          ts.student_id, 
          ts.email, 
          ts.current_grade,
          ts.current_class,
          ts.total_reading_hours AS total_reading_hours
      FROM 
          TopStudents ts
      ORDER BY 
          ts.total_reading_hours DESC;
      `,
      params
    )
  }
}
