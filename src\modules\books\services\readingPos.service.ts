import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Repository } from 'typeorm'
import { BookReadingPos } from '@/entities'
import {IReadingPosService} from '@/modules/shared/books'

@Injectable()
export class ReadingPosService implements IReadingPosService {
  constructor(
    @InjectRepository(BookReadingPos)
    private readonly readingPosRepository: Repository<BookReadingPos>,
  ) {}

  async create(data: Partial<BookReadingPos>) {
    await this.readingPosRepository.query(`
      INSERT INTO
        book_reading_pos (book_id, user_id, pos, platform)
      VALUES
        (${data.bookId}, ${data.userId}, '${JSON.stringify(data.pos)}', '${
      data.platform
    }') ON DUPLICATE KEY
      UPDATE
        pos = '${JSON.stringify(data.pos)}',
        platform = '${data.platform}';
    `)
    // const pos = await this.readingPosRepository.findOne({
    //   where: {
    //     userId: data.userId,
    //     bookId: data.bookId,
    //   },
    //   select: ['id'],
    // })
    // if (pos) {
    //   pos.pos = data.pos
    //   await this.readingPosRepository.update(
    //     { id: pos.id },
    //     { pos: data.pos, platform: data.platform },
    //   )
    // } else await this.readingPosRepository.save(data)
  }

  async getReadingPos(bookIds: number[], userId: number) {
    return this.readingPosRepository.find({
      where: { bookId: In(bookIds), userId },
      select: ['bookId', 'pos', 'userId'],
    })
  }
}
