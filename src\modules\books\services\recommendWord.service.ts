import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Repository } from 'typeorm'
import { PageRequest, RedlockService } from '@/common'
import { RecommendSearchWord } from '@/entities'
import { OnlineOfflineStatus } from '@/enums'
import { PAGE_SIZE, RECOMMEND_SEARCH_WORD_KEY } from '@/modules/constants'
import { DuplicatedRecommendWordException } from '@/modules/exception'

@Injectable()
export class RecommendWordService {
  constructor(
    @InjectRepository(RecommendSearchWord)
    private readonly recommendWordModel: Repository<RecommendSearchWord>,
    private readonly redlockService: RedlockService
  ) {}

  async AddSearchCount(keyword: string) {
    return this.redlockService.lockWrapper(
      `${RECOMMEND_SEARCH_WORD_KEY}${keyword}`,
      10 * 1000,
      async () => {
        const existed = await this.recommendWordModel.findOne({ where: { keyword } })
        if (!existed) {
          return
        }
        await this.recommendWordModel.update(
          { id: existed.id },
          { searchCount: existed.searchCount + 1 }
        )
      }
    )
  }

  async createWord(keyword: string) {
    return this.redlockService.lockWrapper(
      `${RECOMMEND_SEARCH_WORD_KEY}${keyword}`,
      10 * 1000,
      async () => {
        const duplicated = await this.recommendWordModel.find({ where: { keyword } })
        if (duplicated.length) {
          throw new DuplicatedRecommendWordException()
        }
        return this.recommendWordModel.save({ keyword })
      }
    )
  }

  async modifyWord(id: number, status: OnlineOfflineStatus) {
    const word = await this.recommendWordModel.findOne({ where: { id } })
    if (!word) {throw new NotFoundException('recommend word not found')}
    if (status === OnlineOfflineStatus.OFFLINE) {
      word.offlineAt = new Date()
    } else {
      word.onlineAt = new Date()
      word.offlineAt = null
    }
    word.status = status
    return this.recommendWordModel.save(word)
  }

  async listWord(query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const total = await this.recommendWordModel.count()
    const items = await this.recommendWordModel.find({
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
    })
    return { pageIndex, pageSize, total, items }
  }

  async listAllWordIds() {
    const words = await this.recommendWordModel.find({ select: ['id'] })
    return words.map((item) => item.id)
  }

  async listTopNWord(top: number) {
    return this.recommendWordModel.find({
      where: { status: OnlineOfflineStatus.ONLINE },
      order: { searchCount: 'DESC' },
      take: top,
    })
  }

  async getWords(ids: number[]) {
    return this.recommendWordModel.find({ where: { id: In(ids) } })
  }

  async deleteWord(ids: number[]) {
    await this.recommendWordModel.delete({ id: In(ids) })
  }
}
