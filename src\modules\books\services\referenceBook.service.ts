import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ReferenceBook } from '@/entities'

@Injectable()
export class ReferenceBookService {
  constructor(
    @InjectRepository(ReferenceBook)
    private readonly referenceBookRepository: Repository<ReferenceBook>,
  ) {}

  async getCopiesCountForBook(bookIds: number[], schoolId: number) {
    if (bookIds.length === 0 || !schoolId) return []
    return this.referenceBookRepository.query(
      `
        select
          book_id as id,
          copies_count as copiesCount
        from
          reference_books
        where
          book_id in (${bookIds.join(',')})
          and school_id = ${schoolId}
      `,
    )
  }

  async getCount(bookIds: number[]) {
    const [data] = await this.referenceBookRepository.query(
      `
        select
          count(distinct(book_id)) as booksCount,
          sum(copies_count) as copiesCount
        from
          reference_books
        where
          book_id in (${bookIds.join(',')})
        `,
    )

    return {
      booksCount: Number(data.booksCount),
      copiesCount: Number(data.copiesCount),
    }
  }

  async getCountByBook(bookIds: number[]) {
    const data = await this.referenceBookRepository.query(
      `
        select
          book_id as id,
          sum(copies_count) as copiesCount
        from
          reference_books
        where
          book_id in (${bookIds.join(',')})
        group by
          book_id
        `,
    )

    return data.map((item) => ({
      id: Number(item.id),
      copiesCount: Number(item.copiesCount),
    }))
  }
}
