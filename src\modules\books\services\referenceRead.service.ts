import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Repository } from 'typeorm'
import { RedisService, RedlockService } from '@/common'
import { Book, ReferenceBook, ReferenceReadRecord, ViewBookDetail } from '@/entities'
import { EOrderDirection, EUserType } from '@/enums'
import { IReferenceReadService, IUserRepo } from '@/modules/shared/interfaces'
import { PAGE_SIZE } from '@/modules/constants'
import {
  BookNotExistInReferenceException,
  TooManyUsersReadException,
} from '@/modules/exception'
import { QueryReadingTimeDto } from '../dto'
import {
  ESchoolReferenceBookStatisticOrderType,
  QuerySchoolReferenceBookStatisticDto,
} from '../dto/referenceBook.dto'
import { BookRepository } from './book.repository'
import { LeaderBoardService } from './leaderboard.service'

@Injectable()
export class ReferenceReadService implements IReferenceReadService {
  constructor(
    @InjectRepository(ReferenceReadRecord)
    private readonly readRecordRepository: Repository<ReferenceReadRecord>,
    @InjectRepository(ReferenceBook)
    private readonly referenceBookRepository: Repository<ReferenceBook>,
    private readonly redisService: RedisService,
    private readonly redlockService: RedlockService,
    private readonly bookRepositories: BookRepository,
    private readonly userRepositories: IUserRepo,
    private readonly leaderBoardService: LeaderBoardService,
    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,
  ) {}

  async startRead(bookId: number, userId: number, schoolId: number) {
    const [book] = await this.referenceBookRepository.query(
      `select copies_count as copiesCount from reference_books where book_id = ${bookId} and school_id = ${schoolId}`,
    )

    if (!book || book.copiesCount <= 0) {
      throw new BookNotExistInReferenceException()
    }
    await this.redlockService.lockWrapper(
      this.getBookSetName(schoolId, bookId),
      1000,
      async () => {
        const total = await this.getTotalUser(schoolId, bookId)

        if (total >= book.copiesCount) {
          throw new TooManyUsersReadException()
        }
        await this.addUser(schoolId, bookId, userId)

        // await this.readRecordRepository.save({
        //   user: { id: userId },
        //   book: { id: bookId },
        //   school: { id: schoolId },
        // })
        await this.readRecordRepository.query(
          `INSERT INTO reference_read_record(id, created_at, updated_at, user_id, book_id, school_id) VALUES (DEFAULT, DEFAULT, DEFAULT, ${userId}, ${bookId}, ${schoolId})`,
        )

        await this.readRecordRepository.query(
          `update user_balances set read_reference_books = concat(read_reference_books, ',', ${bookId}) where user_id = ${userId} and read_reference_books not like '%,${bookId},%'`,
        )
      },
    )
  }

  async reading(bookId: number, userId: number, schoolId: number) {
    await this.redlockService.lockWrapper(
      this.getBookSetName(schoolId, bookId),
      1000,
      async () => {
        await this.addUser(schoolId, bookId, userId)
        await this.removeExpireUsers(schoolId, bookId)
      },
    )
  }

  async endRead(bookId: number, userId: number, schoolId: number) {
    await this.redlockService.lockWrapper(
      this.getBookSetName(schoolId, bookId),
      1000,
      async () => {
        await this.redisService.instance.zrem(
          this.getBookSetName(schoolId, bookId),
          String(userId),
        )
        await this.removeExpireUsers(schoolId, bookId)
      },
    )
  }

  async getTotalBooks(userId: number) {
    const [data] = await this.readRecordRepository.query(
      `select count(distinct book_id) as total from reference_read_record where user_id = ${userId}`,
    )

    return Number(data.total ?? 0)
  }

  async getTopBooks(schoolId: number, limit: number) {
    const data = await this.leaderBoardService.getTopReferenceRanking(
      schoolId,
      0,
      limit - 1,
    )

    const books = await this.bookRepositories.listBooks(
      {
        ids: data.map((item) => item.bookId),
      },
      { withDeleted: true },
    )

    return data.map((item) => ({
      ...item,
      ...(books.find((book) => book.id === item.bookId) || {}),
      total: item.viewCount,
    }))
  }

  async groupByClass(schoolId: number, query: QueryReadingTimeDto) {
    const where = `(reference_read_record.created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000,
    ).toISOString()}") AND users.school_id = ${schoolId}`

    const data = await this.readRecordRepository.query(
      `
      select
        count(distinct id) as userCount,
        classId
      from
        (
          select
            users.id as id,
            user_class.id as classId
          from
            users
            inner join user_class on users.user_class_id = user_class.id
            inner join reference_read_record on users.id = reference_read_record.user_id
          where
            ${where}
        ) as read_record
      group by
        classId;
      `,
    )

    const classes = await this.readRecordRepository.query(
      `select id, class, sequence, grade_id as gradeId from user_class where school_id = ${schoolId} and deleted_at is null order by sequence asc`,
    )

    const grades = await this.readRecordRepository.query(
      `select id, grade from grades where school_id = ${schoolId} and deleted_at is null order by sequence asc`,
    )

    const [teachers] = await this.readRecordRepository.query(`
      select
        count(distinct users.id) as userCount
      from
        users
        inner join reference_read_record on users.id = reference_read_record.user_id
      where
        users.type = '${EUserType.TEACHER}' and ${where}
    `)

    return {
      students: grades.map((grade) => ({
        id: grade.id,
        grade: grade.grade,
        classes: classes
          .filter((item) => item.gradeId === grade.id)
          .sort((a, b) => a.sequence - b.sequence)
          .map((item) => ({
            id: item.id,
            class: item.class,
            userCount: data.find((record) => record.classId === item.id)?.userCount || 0,
          })),
      })),
      teachers: {
        userCount: teachers?.userCount || 0,
      },
    }
  }

  async statistic(schoolId: number, query: QuerySchoolReferenceBookStatisticDto) {
    const {
      pageIndex = 1,
      pageSize = PAGE_SIZE,
      orderDirection = 'ASC',
      orderType,
    } = query

    const timeWhere = `(reference_read_record.created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000,
    ).toISOString()}") AND reference_read_record.school_id = ${schoolId}`

    let where = timeWhere
    if (query.grade) {
      where += ` and user_class.grade_id = ${query.grade}`
    }

    if (query.class) {
      where += ` and user_class.id = ${query.class}`
    }

    if (query.keyword) {
      const accounts = await this.userRepositories.getUsers(schoolId, query.keyword)
      if (accounts.length === 0) {
        return { pageIndex, pageSize, total: 0, items: [] }
      }
      where += ` and users.id in (${accounts.map((item) => item.id).join(',')})`
    }

    const sql = `
      select
        reference_read_record.user_id,
        reference_read_record.book_id,
        users.given_name as displayName,
        count(*) as total,
        t.totalBookCount as totalBookCount,
        t.totalReadCount as totalReadCount,
        user_class.class as class,
        user_class.grade_id as gradeId,
        books.name as bookName
      from
        reference_read_record
        inner join user_balances on user_balances.user_id = reference_read_record.user_id
        inner join users on users.id = reference_read_record.user_id
        inner join user_class on user_class.id = users.user_class_id
        inner join books on books.id = reference_read_record.book_id
        left join (select count(distinct(book_id)) as totalBookCount, count(*) as totalReadCount, user_id from reference_read_record where ${timeWhere} group by user_id) as t on t.user_id = reference_read_record.user_id
      where
        ${where}
      group by
        reference_read_record.user_id,
        reference_read_record.book_id
    `
    const [total] = await this.readRecordRepository.query(`
      select count(*) as total from (${sql}) as record
    `)

    if (Number(total.total) === 0) {
      return { pageIndex, pageSize, total: 0, items: [] }
    }

    const orderMap = {
      [ESchoolReferenceBookStatisticOrderType.READ_COUNT]: 'total',
      [ESchoolReferenceBookStatisticOrderType.TOTAL_READ_BOOK]: 'totalBookCount',
      [ESchoolReferenceBookStatisticOrderType.TOTAL_READ_COUNT]: 'totalReadCount',
    }
    const orderField = orderMap[orderType]
    const items = await this.readRecordRepository.query(`
      ${sql}
      ${orderField ? `order by ${orderField} ${orderDirection}` : ''}
      limit ${pageSize}
      offset ${(pageIndex - 1) * pageSize}
    `)
    return { pageIndex, pageSize, total: Number(total.total), items }
  }

  async exportUsers(schoolId: number, query: QuerySchoolReferenceBookStatisticDto) {
    const timeWhere = `(reference_read_record.created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND  "${new Date(
      query.endTime * 1000,
    ).toISOString()}") AND reference_read_record.school_id = ${schoolId}`

    let where = timeWhere
    if (query.grade) {
      where += ` and user_class.grade_id = ${query.grade}`
    }

    if (query.class) {
      where += ` and user_class.id = ${query.class}`
    }

    if (query.keyword) {
      const accounts = await this.userRepositories.getUsers(schoolId, query.keyword)
      if (accounts.length === 0) {
        return []
      }
      where += ` and users.id in (${accounts.map((item) => item.id).join(',')})`
    }

    const [total] = await this.readRecordRepository.query(`
      select
        count(*) as total
      from
      (
          select
            reference_read_record.user_id,
            reference_read_record.book_id
          from
            reference_read_record
            inner join users on users.id = reference_read_record.user_id
            inner join user_class on user_class.id = users.user_class_id
            inner join books on books.id = reference_read_record.book_id
          where
            ${where}
          group by
            reference_read_record.user_id,
            reference_read_record.book_id
        ) as t
    `)

    if (Number(total.total) === 0) {
      return []
    }

    let pageIndex = 1
    const pageSize = 20
    let data = []
    while ((pageIndex - 1) * pageSize < Number(total.total)) {
      const items = await this.readRecordRepository.query(`
      select
        reference_read_record.user_id as userId,
        reference_read_record.book_id as bookId,
        users.given_name as displayName,
        users.serial_no as serialNo,
        users.email as email,
        user_class.class as class,
        grades.grade as grade,
        books.isbn as isbn,
        books.name as bookName,
        publishers.name as publisherName,
        count(*) as total,
        count(distinct(reference_read_record.book_id)) as books,
        t.totalBookCount as totalBookCount,
        t.totalReadCount as totalReadCount,
        t2.totalBookByGrade as totalBookByGrade
      from
        reference_read_record
        inner join users on users.id = reference_read_record.user_id
        inner join user_class on user_class.id = users.user_class_id
        inner join books on books.id = reference_read_record.book_id
        left join publishers on publishers.id = books.publisher_id
        left join grades on grades.id = user_class.grade_id
        left join (
          select
            count(distinct(book_id)) as totalBookCount,
            count(*) as totalReadCount,
            user_id
          from
            reference_read_record
          where
            ${timeWhere}
          group by
            user_id
        ) as t on t.user_id = reference_read_record.user_id
        left join (
          select
            count(distinct(bookId)) as totalBookByGrade,
            gradeId
          from
            (
              select
                book_id as bookId,
                user_class.grade_id as gradeId
              from
                reference_read_record
                inner join users on users.id = reference_read_record.user_id
                inner join user_class on user_class.id = users.user_class_id
              where
                ${where}
            ) as t1
          group by
            gradeId
        ) as t2 on user_class.grade_id = t2.gradeId
      where
        ${where}
      group by
        reference_read_record.user_id,
        reference_read_record.book_id
    `)
      pageIndex += 1
      data = data.concat(items)
    }
    return data
  }

  async getCountsByUser(userId: number, query: any) {
    console.log(query)
    const where = `(created_at BETWEEN "${new Date(
      query.startTime * 1000,
    ).toISOString()}" AND "${new Date(
      query.endTime * 1000,
    ).toISOString()}") AND user_id = ${userId}`

    const data = await this.referenceBookRepository.query(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m-%d') as date,
        COUNT(*) as timesCount
      FROM 
        reference_read_record
      WHERE 
        ${where}
      GROUP BY 
        DATE_FORMAT(created_at, '%Y-%m-%d')
    `)

    return data
  }

  /**
   * 获取所有阅读过的书籍 userIds
   * @param schoolId
   */
  async getReadingUserIdsBySchool(schoolId: number, query: any) {
    const sql = `
        SELECT
          RR.user_id
        FROM
          reference_read_record RR
        WHERE
          RR.school_id = ${schoolId}
          AND RR.created_at BETWEEN "${new Date(
            query.startTime * 1000,
          ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}"
    `
    const userIds = await this.readRecordRepository.query(sql)
    return userIds.map((record) => record.user_id)
  }

  /**
   * 获取所有阅读过的书籍
   * @param userId
   * @param schoolId
   * @param query
   * @returns
   */
  async listReadBooks(userId: number, schoolId: number, query: any) {
    const sql = `
    WITH
      record_with_year AS (
        SELECT
          RR.book_id,
          B.\`name\`,
          CASE
            WHEN MONTH(RR.created_at) >= 9 THEN CONCAT(YEAR(RR.created_at), '-', YEAR(RR.created_at) + 1)
            ELSE CONCAT(YEAR(RR.created_at) - 1, '-', YEAR(RR.created_at))
          END AS school_year,
          RR.created_at,
          G.grade as old_grade,
          uc.class as old_class
        FROM
          reference_read_record RR
        LEFT JOIN
          books B ON B.id = RR.book_id
        LEFT JOIN
          users u ON u.id = RR.user_id
        LEFT JOIN
          user_class uc ON uc.id = u.user_class_id
        LEFT JOIN
          grades G ON G.id = uc.grade_id
        WHERE
          RR.user_id = ${userId}
          AND RR.school_id = ${schoolId}
          AND u.type = 'student'
          AND RR.created_at BETWEEN "${new Date(
            query.startTime * 1000,
          ).toISOString()}" AND "${new Date(query.endTime * 1000).toISOString()}"
      ),
      grouped_by_year AS (
        SELECT
          book_id,
          name,
          school_year,
          count(1) AS total_reading_count,
          MIN(created_at) AS start_date,
          MAX(created_at) AS end_date,
          old_grade,
          old_class
        FROM
          record_with_year
        GROUP BY
          book_id,
          name,
          school_year,
          old_grade,
          old_class
      )
      SELECT
        book_id,
        name,
        school_year,
        total_reading_count,
        start_date,
        end_date,
          old_grade,
          old_class
      FROM
        grouped_by_year
      ORDER BY
        MAX(total_reading_count) OVER (PARTITION BY book_id) DESC,
        book_id,
        total_reading_count desc;                
    `
    const books = await this.readRecordRepository.query(sql)
    console.log('books', books)
    return books
  }

  /**
   * 获取所有阅读过的书籍
   * @param userId
   * @param schoolId
   * @param query
   * @returns
   */
  async listReadBooksPage(userId: number, schoolId: number, query: any) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    await this.readRecordRepository.query('SET SESSION group_concat_max_len = 10000')
    const subQuery = this.readRecordRepository
      .createQueryBuilder('referenceReadRecord')
      .select([
        'referenceReadRecord.book_id AS bookId',
        'COUNT(referenceReadRecord.book_id) AS count',
      ])
      .where('referenceReadRecord.user_id = :userId', { userId })
      .andWhere('referenceReadRecord.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('referenceReadRecord.book_id')

    // 构建 book 子查询，包含软删除数据
    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .withDeleted()
      .leftJoin('book.authors', 'authors')
      .select([
        'book.id',
        'book.name',
        'book.cover_url',
        'GROUP_CONCAT(DISTINCT authors.name) AS authorsList',
      ])
      .groupBy('book.id, book.name, book.cover_url')

    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    const queryBuilder = this.readRecordRepository
      .createQueryBuilder('referenceReadRecord')
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        'book.book_id = referenceReadRecord.book_id',
        bookSubQueryParams,
      )
      .innerJoin(
        '(' + subQuery.getQuery() + ')',
        'recordCount',
        'recordCount.bookId = referenceReadRecord.book_id',
      )
      .select([
        'book.book_id AS id',
        'book.book_name AS name',
        'book.cover_url AS cover_url',
        'book.authorsList AS authors',
        'recordCount.count AS count',
      ])
      .groupBy('book.book_id, book.book_name, recordCount.count')
      .orderBy('count', orderDirection)
      .offset(offset)
      .limit(pageSize)
      .setParameters(subQuery.getParameters())

    const totalQueryBuilder = this.readRecordRepository
      .createQueryBuilder('referenceReadRecord')
      .select('COUNT(DISTINCT referenceReadRecord.book_id)', 'count')
      .innerJoin(
        '(' + subQuery.getQuery() + ')',
        'readingTimeData',
        'readingTimeData.bookId = referenceReadRecord.book_id',
      )
      .where('referenceReadRecord.user_id = :userId', { userId })
      .andWhere('referenceReadRecord.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .setParameters(subQuery.getParameters())

    const total = await totalQueryBuilder.getRawOne()

    const books = await queryBuilder.getRawMany()
    return {
      items: books.map((book) => ({
        ...book,
        authors: book.authors ? JSON.parse('[' + book.authors + ']') : [],
      })),
      total: total.count,
      pageIndex,
      pageSize,
    }
  }

  async getCounts(bookIds: number[]) {
    const [data] = await this.referenceBookRepository.query(
      `
        select
          count(distinct(user_id)) as usersCount,
          count(*) as timesCount
        from
          reference_read_record
        where
          book_id in (${bookIds.join(',')})
        `,
    )

    return {
      usersCount: Number(data.usersCount),
      timesCount: Number(data.timesCount),
    }
  }

  private async getTotalUser(schoolId: number, bookId: number) {
    const now = moment().unix()
    const total = await this.redisService.instance.zcount(
      this.getBookSetName(schoolId, bookId),
      now - 3600,
      now,
    )

    // const users = await this.redisService.instance.zcount(
    //   this.getBookSetName(schoolId, bookId),
    //   0,
    //   'inf',
    // )

    // console.log({ total, users })
    await this.removeExpireUsers(schoolId, bookId)
    return total
  }

  private addUser(schoolId: number, bookId: number, userId: number) {
    const now = moment().unix()
    return this.redisService.instance.zadd(
      this.getBookSetName(schoolId, bookId),
      now,
      String(userId),
    )
  }

  private removeExpireUsers(schoolId: number, bookId: number) {
    const now = moment().unix()
    return this.redisService.instance.zremrangebyscore(
      this.getBookSetName(schoolId, bookId),
      0,
      now - 3600,
    )
  }

  private getBookSetName(schoolId: number, bookId: number) {
    return `reference:school:${schoolId}:book:${bookId}:set`
  }
}
