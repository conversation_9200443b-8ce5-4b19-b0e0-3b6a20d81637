// import { Injectable } from '@nestjs/common'
// import { DataSource } from 'typeorm'
//
// @Injectable()
// export class Sql {
//   constructor(private readonly dataSource: DataSource) {}
//
//   async migrate() {
//     const manager = this.dataSource.manager
//     const data = await manager.query(
//       `select id, category_ids, JSON_ARRAY(categories_books.category_id) as c_ids
//       from books inner join categories_books on categories_books.book_id = books.id
//       where JSON_ARRAY(categories_books.category_id) != category_ids;
//       `
//     )
//
//     for (const item of data) {
//       console.log(item)
//       await manager.query(
//         `update books set category_ids = '${JSON.stringify(item.c_ids)}' where id = ${
//           item.id
//         }`
//       )
//     }
//   }
// }
