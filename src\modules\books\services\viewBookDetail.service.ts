import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import Decimal from 'decimal.js'
import moment from 'moment-timezone'
import R from 'ramda'
import { Repository } from 'typeorm' 
import { ELocaleType, ExcelService } from '@/common'
import { ViewBookDetail } from '@/entities'
import { ECountType, EOrderDirection, EViewBookGroupType } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { IViewBookDetailService } from '@/modules/shared/interfaces'
import { getISBN, getName } from '@/utils/book.utitl'
import { QueryReadingTimeDto } from '../dto'
import { QueryPlatformStatisticViewBookDto } from '../dto/viewBook.dto'

@Injectable()
export class ViewBookDetailService implements IViewBookDetailService{
  constructor(
    @InjectRepository(ViewBookDetail)
    private readonly viewBookDetailRepository: Repository<ViewBookDetail>,
    private readonly excelService: ExcelService
  ) {}

  async viewBookDetail(bookId: number, userId: number, schoolId: number) {
    await this.viewBookDetailRepository.save({
      user: {
        id: userId,
      },
      book: {
        id: bookId,
      },
      school: {
        id: schoolId,
      },
    })

    await this.viewBookDetailRepository.query(
      `update user_balances set view_reference_books = view_reference_books + 1 where user_id = ${userId}`
    )
  }

  async statisticsForPlatform(query: QueryPlatformStatisticViewBookDto) {
    const {
      group = EViewBookGroupType.SCHOOL,
      orderDirection = EOrderDirection.DESC,
      pageIndex = 1,
      pageSize = PAGE_SIZE,
      countType = ECountType.USER,
      startTime,
      endTime,
    } = query

    let groupField = ''
    switch (group) {
      case EViewBookGroupType.SCHOOL:
        groupField = 'views.school_id'
        break
      case EViewBookGroupType.PUBLISHER:
        groupField = 'books.publisher_id'
        break
      case EViewBookGroupType.AUTHOR:
        groupField = 'authors.authors_id'
        break
    }

    const sql = `
      select
        ${groupField} as groupId,
        ${
  countType === ECountType.USER
    ? `count(distinct(user_id)) as viewCount`
    : `count(*) as viewCount`
}
      from
        view_book_detail as views
        inner join books on books.id = views.book_id
        ${
  group === EViewBookGroupType.AUTHOR
    ? 'inner join books_authors_authors as authors on books.id = authors.books_id'
    : ''
}
      where
        unix_timestamp(views.created_at) > ${startTime}  and  unix_timestamp(views.created_at) < ${endTime}
      group by
        ${groupField}
      order by
        viewCount ${orderDirection}
    `

    const [total] = await this.viewBookDetailRepository.query(
      `select count(*) as total from (${sql}) as t`
    )

    if (Number(total.total) === 0) {
      return {
        pageIndex,
        pageSize,
        total: 0,
        items: [],
      }
    }
    const items = await this.viewBookDetailRepository.query(
      `${sql} 
      limit
        ${pageSize} 
      offset
        ${(pageIndex - 1) * pageSize}
      `
    )

    return {
      pageIndex,
      pageSize,
      total: Number(total.total),
      items,
    }
  }

  async count() {
    const [totalViewCount, totalViewUsers] = await Promise.all([
      this.viewBookDetailRepository.count(),
      this.viewBookDetailRepository.query(
        `select count(distinct(user_id)) as total from view_book_detail`
      ),
    ])

    return {
      totalViewCount: Number(totalViewCount),
      totalViewUsers: Number(totalViewUsers[0].total),
    }
  }

  async export(query: QueryPlatformStatisticViewBookDto, locale: ELocaleType) {
    const { group = EViewBookGroupType.SCHOOL, startTime, endTime } = query

    let fields = ''
    let groupField = ''
    let templateName = ''
    switch (group) {
      case EViewBookGroupType.SCHOOL:
        fields =
          'schools.name as schoolName, schools.address as address, schools.joined_at as joinedAt'
        groupField = 'view_book_detail.school_id'
        templateName = `platformSchoolReading.${locale}`
        break
      case EViewBookGroupType.PUBLISHER:
        fields =
          'publishers.name as publisherName, publishers.publisher_group_name as publisherGroup'
        groupField = 'books.publisher_id'
        templateName = `platformPublisherReading.${locale}`
        break
    }

    const data = await this.viewBookDetailRepository.query(`
      select
        total,
        users,
        t.region,
        ${fields},
        groupId
      from
      (
        select
          count(*) as total,
          count(distinct user_id) as users,
          schools.region as region,
          ${groupField} as groupId
        from
          view_book_detail
          inner join schools on view_book_detail.school_id = schools.id
          ${
  group === EViewBookGroupType.PUBLISHER
    ? `inner join books  on books.id = view_book_detail.book_id`
    : ''
}
          where
            unix_timestamp(view_book_detail.created_at) > ${startTime}  and  unix_timestamp(view_book_detail.created_at) < ${endTime}
          group by
            schools.region,
            ${groupField}
        ) as t 
        ${
  group === EViewBookGroupType.SCHOOL
    ? 'inner join schools on schools.id = t.groupId'
    : ''
} 
        ${
  group === EViewBookGroupType.PUBLISHER
    ? 'inner join publishers on publishers.id = t.groupId'
    : ''
} 
    `)

    const groups = [...new Set(data.map((item) => item.groupId))]
    const items = groups.map((groupId) => {
      const groupData = data.filter((item) => item.groupId === groupId)
      return {
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        schoolName: getName(groupData[0].schoolName),
        address: getName(groupData[0].address),
        joinedAt: groupData[0].joinedAt
          ? moment.tz(groupData[0].joinedAt * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD')
          : '',
        publisherName: getName(groupData[0].publisherName),
        publisherGroup: getName(groupData[0].publisherGroup),
        readingUsers: groupData.reduce((prev, curr) => prev + Number(curr.users), 0),
        readingCounts: groupData.reduce((prev, curr) => prev + Number(curr.total), 0),
        ...groupData.reduce((prev, curr) => {
          prev[`${curr.region}ReadingUsers`] = Number(curr.users)
          prev[`${curr.region}ReadingCounts`] = Number(curr.total)
          return prev
        }, {}),
      }
    })

    return this.excelService.buildExcel({ name: templateName, data: items })
  }

  async exportByAuthorBooks(query: QueryReadingTimeDto, locale: ELocaleType) {
    const views = await this.viewBookDetailRepository.query(`
      select
        count(*) as total,
        count(distinct user_id) as users,
        schools.region,
        book_authors.authors_id as authorId,
        view_book_detail.book_id as bookId
      from
        view_book_detail
        inner join schools on view_book_detail.school_id = schools.id
        inner join books_authors_authors as book_authors on view_book_detail.book_id = book_authors.books_id
      where
        unix_timestamp(view_book_detail.created_at) > ${query.startTime}
        and unix_timestamp(view_book_detail.created_at) < ${query.endTime}
      group by
        schools.region,
        book_authors.authors_id,
        view_book_detail.book_id
    `)

    const ids = [...new Set(views.map((item) => item.bookId))]
    const books = ids.length
      ? await this.viewBookDetailRepository.query(`
      select
        authors.id as authorId,
        authors.name as authorName,
        books.id as bookId,
        books.name as bookName,
        books.isbn as isbn,
        books.price as price,
        publishers.name as publisherName,
        publishers.publisher_group_name as publisherGroup,
        t.copies
      from
        books
        left join books_authors_authors as book_authors on books.id = book_authors.books_id
        left join authors on authors.id = book_authors.authors_id
        left join publishers on publishers.id = books.publisher_id
        left join (
          select
            book_id,
            sum(reference_books.copies_count) as copies
          from
            reference_books
          where
            book_id in (${ids.join(',')})
          group by
            book_id
        ) as t on t.book_id = books.id
      where
        books.id in (${ids.join(',')})
    `)
      : []

    const authorIds = [...new Set(views.map((item) => item.authorId))]

    const authors = authorIds.length
      ? await this.viewBookDetailRepository.query(`
      select id, name from authors where id in (${authorIds.join(',')})
    `)
      : []

    const items = authorIds
      .map((authorId) => {
        const authorData = views.filter((item) => item.authorId === authorId)
        const bookIds = [...new Set(authorData.map((item) => item.bookId))]

        return bookIds.map((bookId) => {
          const bookData = authorData.filter((item) => item.bookId === bookId)
          const book = books.find((item) => item.bookId === bookId)
          const author = authors.find((item) => item.id === authorId)
          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            author: getName(author.name),
            isbn: getISBN(book.isbn),
            name: getName(book.bookName),
            price: R.isNil(book.price) ? '' : new Decimal(book.price).div(100).toNumber(),
            publisherName: getName(book.publisherName),
            publisherGroup: getName(book.publisherGroup),
            copies: book.copies || 0,
            readingUsers: bookData.reduce((prev, curr) => prev + Number(curr.users), 0),
            readingCounts: bookData.reduce((prev, curr) => prev + Number(curr.total), 0),
            ...bookData.reduce((prev, curr) => {
              prev[`${curr.region}ReadingUsers`] = Number(curr.users)
              prev[`${curr.region}ReadingCounts`] = Number(curr.total)
              return prev
            }, {}),
          }
        })
      })
      .flat()

    return this.excelService.buildExcel({
      data: items,
      name: `platformAuthorReading.${locale}`,
    })
  }
}
