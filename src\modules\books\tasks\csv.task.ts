import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import { EmailService } from '@/common'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { BookProvider } from '../providers/book.provider'
import { BookS3Service } from '../services'
import { BookRepository, ReadRecordService } from '../services/index1'

@Processor(TASK_QUEUE_NAME)
export class ExportCsvTask {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly taskService: TaskService,
    private readonly bookS3Service: BookS3Service,
    private readonly bookProvider: BookProvider,
    private readonly emailService: EmailService,
    private readonly readRecordService: ReadRecordService,
  ) {}

  @Process(ETaskType.CSV)
  async handleBook(job: Job<any>) {
    console.log({ parseBookTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { bookId } = job.data
    })
  }
}
