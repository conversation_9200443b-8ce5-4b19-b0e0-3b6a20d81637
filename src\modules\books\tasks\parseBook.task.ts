import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import fs from 'fs/promises'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { BookProvider } from '../providers/book.provider'
import { BookS3Service, BookRepository } from '../services'

@Processor(TASK_QUEUE_NAME)
export class ParseBookTask {
  constructor(
    private readonly bookRepositories: BookRepository,
    private readonly taskService: TaskService,
    private readonly bookS3Service: BookS3Service,
    private readonly bookProvider: BookProvider,
  ) {}

  @Process(ETaskType.PARSE_BOOK_TASK)
  async handleBook(job: Job<any>) {
    console.log({ parseBookTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { bookId } = job.data
      const book = await this.bookRepositories.getBookWithoutRelation({ id: bookId })
      // const isPdf = book.url.includes('.pdf')
      const index = book.url.lastIndexOf('/') + 1
      const url = `${book.url.substring(0, index)}1${book.url.substring(index)}`

      console.log({ url })
      const file = (await this.bookS3Service.fetch(url)) as any

      // const buffer =
      //   file.ContentType === 'application/pdf'
      //     ? decrypt(process.env.AES_KEY, file.Body as Buffer)
      //     : file.Body
      const buffer = file.Body

      const result = await this.bookProvider.handle(buffer, bookId, url)
      if (result) await this.bookS3Service.remove(url)
      console.log({ task: `${bookId} parse success` })
    })
  }

  async test(bookId: number) {
    // const book = await this.bookRepositories.getBookWithoutRelation({ id: bookId })
    // const isPdf = book.url.includes('.pdf')
    // const index = book.url.lastIndexOf('/') + 1
    // const url = `${book.url.substring(0, index)}1${book.url.substring(index)}`

    // console.log({ url })
    // const file = (await this.bookS3Service.fetch(url)) as any

    // const buffer =
    //   file.ContentType === 'application/pdf'
    //     ? decrypt(process.env.AES_KEY, file.Body as Buffer)
    //     : file.Body
    // const buffer = file.Body

    const buffer = await fs.readFile(
      '/Users/<USER>/Downloads/1BOOK_f2ef3580d14f4d38b4b9d8d389520364.epub',
    )
    const url = ''
    const result = await this.bookProvider.handle(buffer, bookId, url)
    if (result) await this.bookS3Service.remove(url)
    console.log({ task: `${bookId} parse success` })
  }
}
