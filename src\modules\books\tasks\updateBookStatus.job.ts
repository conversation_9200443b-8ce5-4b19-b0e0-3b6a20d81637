import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import R from 'ramda'
import { EmailService } from '@/common'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { Administrator } from '@/entities'
import { EAdministratorStatus } from '@/enums'
import { IAdminRepo } from '@/modules/shared/interfaces'

@Processor(TASK_QUEUE_NAME)
export class UpdateBookStatusTask {
  constructor(
    private readonly taskService: TaskService,

    private readonly adminRepo: IAdminRepo,
    private readonly emailService: EmailService
  ) {}

  @Process(ETaskType.UPDATE_BOOK_STATUS)
  async handleBook(job: Job<any>) {
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const admins = []
      let pageIndex = 1
      let total = 0
      const pageSize = 50
      do {
        const data = await this.adminRepo.listAdministrators({
          status: EAdministratorStatus.ACTIVE,
          pageIndex,
          pageSize,
        })
        total = data.total
        admins.push(data.items)
        pageIndex += 1
      } while ((pageIndex - 1) * pageSize < total)

      // console.log(admins)
      const permission = 'bookManage:email'
      const validAdmins = R.flatten(admins).filter((item: Administrator) =>
        R.flatten(item.roles?.map((r) => r?.permissions?.map((p) => p?.code))).includes(
          permission
        )
      )

      const { userName, bookNames, operation, date } = job.data
      await Promise.all(
        validAdmins.map((admin) => {
          try {
            this.emailService.sendPrepared(admin.email, 'updateBookStatusTemplate', {
              userName,
              bookNames,
              operation,
              date,
            })
          } catch (err) {
            console.log({ msg: 'send email error', err })
          }
        })
      )
    })
  }

  @Process(ETaskType.OPERATE_APPLICATION)
  async operateApply(job: Job<any>) {
    await this.taskService.runTask(job?.data?.taskId, async () => {
      let admins = []
      let pageIndex = 1
      let total = 0
      const pageSize = 50
      do {
        const data = await this.adminRepo.listAdministrators({
          status: EAdministratorStatus.ACTIVE,
          pageIndex,
          pageSize,
        })
        total = data.total
        admins.push(data.items)
        pageIndex += 1
      } while ((pageIndex - 1) * pageSize < total)

      // 只取Platform Admin
      admins = admins.filter((x) => !x.publishers || x.publishers.length <= 0)

      // console.log(admins)
      const permission = 'bookManage:email'
      const validAdmins = R.flatten(admins).filter((item: Administrator) =>
        R.flatten(item.roles?.map((r) => r?.permissions?.map((p) => p?.code))).includes(
          permission
        )
      )

      const { publisherName, operationName, description, url } = job.data
      await Promise.all(
        validAdmins.map((admin) => {
          try {
            this.emailService.sendPrepared(admin.email, 'operateApplicationTemplate', {
              publisherName,
              operationName,
              description,
              url,
            })
          } catch (err) {
            console.log({ msg: 'send email error', err })
          }
        })
      )
    })
  }
}
