import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import fs from 'fs/promises'
import R from 'ramda'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { EUserType } from '@/enums'
import { ISchoolService } from '@/modules/shared/interfaces'
import {  LeaderBoardService } from '../services'

@Processor(TASK_QUEUE_NAME)
export class UpdateHotBookLevelTask {
  constructor(
    private readonly taskService: TaskService,
    private readonly schoolService: ISchoolService,
    private readonly leadBoardService: LeaderBoardService,
  ) {}

  // 剔除热门书籍(学生)列表里面的教职员书籍
  @Process(ETaskType.UPDATE_HOT_BOOK_LEVEL_TASK)
  async handleHotBookLevelTask(job: Job<any>) {
    console.log({ handleHotBookLevel: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const { bookId, bookLevel } = job.data
      //遍历获取所有学校
      const schools = await this.schoolService.listAllSchool()
      try {
        for (const school of schools) {
          const { id, isAllLevelForStudent, studentLevelIds } = school
          // 情况一：要剔除
          // 1、只有 教职员 的书籍 需要去对应的热门书籍剔除  不考虑学生群组包含教职员的情况
          // 2、书籍目标群组 与 学校的目标阅读群组 无交集
          if (
            (bookLevel.length === 1 && bookLevel[0] === 1) ||
            (studentLevelIds &&
              studentLevelIds.length > 0 &&
              R.intersection(bookLevel, studentLevelIds).length == 0)
          ) {
            if (!isAllLevelForStudent && studentLevelIds && studentLevelIds.length > 0) {
              // console.log('del  bookId:',bookId,'school_id:',id,"studentLevelIds:",studentLevelIds)
              await this.leadBoardService.removeBooksForReadingRanking(
                [bookId],
                [id],
                EUserType.STUDENT,
              )
            }
          }
          // 情况二：要添加
          // 1、学校设置为全部的群组 教职员类书籍可以看到
          // 2、交集群组 可看到
          if (
            (isAllLevelForStudent && bookLevel.includes(1)) ||
            (studentLevelIds &&
              studentLevelIds.length > 0 &&
              R.intersection(bookLevel, studentLevelIds).length > 0)
          ) {
            // console.log('add  bookId:',bookId,'school_id:',id,"studentLevelIds:",studentLevelIds)
            const readingTime = await this.leadBoardService.getReadingRankingByBookId(
              bookId,
              id,
              EUserType.STUDENT,
            )
            await this.leadBoardService.addBookToReadingRankingForSchool(
              id,
              EUserType.STUDENT,
              [
                {
                  bookId: String(bookId),
                  readingTime: readingTime ? Number(readingTime) : 0,
                },
              ],
            )
          }
        }
      } catch (error) {
        console.log('UPDATE_HOT_BOOK_LEVEL error:', error)
      }

      console.log({ task: `bookId:${bookId} UPDATE_HOT_BOOK_LEVEL success` })
    })
  }
}
