import { AuthSchema } from '@/common'
import { REDIS_KEY } from '@/common/constants'
import { EUserType } from '@/enums'

export const AUTHOR_PROFILE_IMAGE = ''
export const PAGE_SIZE = 10

export const S3_BOOK_UNZIP_DIR = 'public/books/unzip'
export const S3_BOOK_ORIGNAL_DIR = 'public/books/orignal'
export const S3_BOOK_PDF_OUTLINE_DIR = 'public/books/outline'
export const S3_EPUB_PARSE_HTML_DIR = 'public/books/epubjs'
export const S3_BOOK_COVER_IMAGE = 'public/books/cover'

export const S3_IMAGE_DIR = 'public/images'
export const S3_OPENAI_DIR = 'public/files'

export const PARSE_BOOK_TASK = 'parse_book_task:'
export const HOT_SEARCH_WORD_KEY = 'hot_search_word:key:'
export const RECOMMEND_SEARCH_WORD_KEY = 'recommend_search_word:key:'

export const CREATE_SCHOOL_KEY = 'create_school:key:'

export const ASSISTANTS_BOOKS_KEY = `assistant_books`
export const ASSISTANTS_INFO_KEY = `assistant_info`

export const VERIFICATION_CODE_EMAIL_IMAGE = process.env.VERIFICATION_CODE_EMAIL_IMAGE
export const COPY_EMAIL_IMAGE = process.env.COPY_EMAIL_IMAGE
export const SJRC_LOGO_JPG = 'https://admin.sjrc.club/assets/school_logo.8a29653c.svg'

export const LOCK_USER_BALANCE_REDIS_KEY = 'readingTime:balance:key:' //
export const LOCK_BALANCE_REDIS_SCHOOL_KEY = 'readingTime:balance:school:key:' //

export const USER_READING_SESSION_KEY = 'readingTime:session:' //
export const READING_BOARD_ZSET = 'reading:board:zset' //
export const BOOK_DETAIL_BOARD_ZSET = 'book:detail:board:zset' //

// need to flush to db
export const FLUSH_USER_READING_RECORD = 'flush:reading:user:record:'
export const FLUSH_USER_READING_BALANCE = 'flush:reading:user:balance:'
export const FLUSH_SCHOOL_READING_BALANCE = 'flush:reading:school:balance:'
export const FLUSH_SCHOOL_READING_BALANCE_QUEUE = 'flush:reading:school:balance:queue:'

export const READING_SESSION_SET = 'readingTime:set:session:'

export const getSchoolRoom = (
  schoolId: number,
  gradeId: number,
  classId: number,
  authSchema: AuthSchema,
) =>
  `school:room:${schoolId}:${gradeId ? gradeId : ''}:${
    classId ? classId : ''
  }:${authSchema}`

export const getUserRoom = (userId: number, authSchema: AuthSchema) =>
  `user:room:${userId}:${authSchema}`

export const getAssistantInfoKey = (assistantId: string) =>
  `${ASSISTANTS_INFO_KEY}:${assistantId}`

export const getUserAssistantThreadRoom = (userId: number, authSchema: AuthSchema) =>
  `user:assistant:room:${userId}:${authSchema}`

export const getUserAssistantThreadKey = (schoolId: number) =>
  `assistant_user_thread_school_${schoolId}`

export const getBooksKeywordKey = (bookId: number) => `booksKeyword:${bookId}`

export const getReadingTimeLockKey = (schoolId: number) => `readingTime:lock:${schoolId}`

export const getStudentReadingTimeLockKey = (schoolId: number, userId: number) =>
  `readingTime:lock:${schoolId}:${userId}`

export const getReadingUsersSetName = (schoolId: number) =>
  `readingTime:set:users:${schoolId}`

export const getReadingSchoolsSetName = () => `readingTime:set:schools`

export const getReadRecordSetName = (schoolId: number) =>
  `readingTime:set:readRecord:${schoolId}`

export const getUserBalanceCacheKey = (userId: number) =>
  `${FLUSH_USER_READING_BALANCE}${userId}`

export const getSchoolBalanceCacheKey = (schoolId: number) =>
  `${FLUSH_SCHOOL_READING_BALANCE}${schoolId}`

export const getSchoolBalanceQueueCacheKey = (schoolId: number) =>
  `${FLUSH_SCHOOL_READING_BALANCE_QUEUE}${schoolId}`

export const getReadingRecordCacheKey = (userId: number, id: number) =>
  `${FLUSH_USER_READING_RECORD}${id}:user:${userId}`

export const getSessionBindKey = (sessionId: string) => `session:bind:${sessionId}`

export const getRedisKey = (key: string) => `${REDIS_KEY}.${key}`

export const getUserCacheKey = (userId: number) => `user:information:${userId}`

export const getSubscriptionNewestKey = (schoolId: number, userType: EUserType) =>
  `subscription:newest:school:${schoolId}:userType:${userType}`

export const getSubscriptionRecommendKey = (
  schoolId: number,
  userType: EUserType,
  gradeId?: number,
  classId?: number,
) =>
  userType === EUserType.STUDENT
    ? `subscription:recommend:school:${schoolId}:userType:${userType}:grade:${gradeId}:class:${classId}`
    : `subscription:recommend:school:${schoolId}:userType:${userType}`

export const getReferenceRecommendKey = (
  schoolId: number,
  userType: EUserType,
  gradeId?: number,
  classId?: number,
) =>
  userType === EUserType.STUDENT
    ? `reference:recommend:school:${schoolId}:userType:${userType}:grade:${gradeId}:class:${classId}`
    : `reference:recommend:school:${schoolId}:userType:${userType}`

export const getSubscriptionHomepageKey = (schoolId: number, userType: EUserType) =>
  `subscription:homepage:school:${schoolId}:userType:${userType}`

export const getSubscriptionHomepageBookListKey = (
  schoolId: number,
  userType: EUserType,
  homepageId: number,
) =>
  `subscription:homepageBookListKey:school:${schoolId}:userType:${userType}:homepageId:${homepageId}`

export const getTaskLockKey = (taskName: string) => `system:task:lock:${taskName}`
