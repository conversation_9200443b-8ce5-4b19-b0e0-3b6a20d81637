import { getExceptionGroup } from '@/common/decorators'
import { ExceptionGroup } from '@/common/enums'
import { BaseException } from '@/common/exceptions'

const BookExceptionMeta = getExceptionGroup(ExceptionGroup.BOOK)

@BookExceptionMeta(1, {
  en_us: 'author not exist',
  zh_HK: '作者不存在',
  zh_cn: '作者不存在',
})
export class AuthorNotExistException extends BaseException {}

@BookExceptionMeta(2, {
  en_us: 'category not exist',
  zh_HK: '分類不存在',
  zh_cn: '分类不存在',
})
export class CategoryNotExistException extends BaseException {}

@BookExceptionMeta(3, {
  en_us: 'category has children',
  zh_HK: '分類有子分類',
  zh_cn: '分类有子分类',
})
export class CategoryHasChildrenException extends BaseException {}

@BookExceptionMeta(4, {
  en_us: 'publisher not exist',
  zh_HK: '出版社不存在',
  zh_cn: '出版社不存在',
})
export class PublisherNotExistException extends BaseException {}

@BookExceptionMeta(5, {
  en_us: 'label not exist',
  zh_HK: '標籤不存在',
  zh_cn: '标签不存在',
})
export class LabelNotExistException extends BaseException {}

@BookExceptionMeta(6, {
  en_us: 'label not exist',
  zh_HK: '書籍不存在',
  zh_cn: '书籍不存在',
})
export class BookNotExistException extends BaseException {}

@BookExceptionMeta(7, {
  en_us: 'unsupported book file',
  zh_HK: '僅支持zip格式的書籍文件',
  zh_cn: '仅支持zip格式的书籍文件',
})
export class UnSupportedBookFileException extends BaseException {}

@BookExceptionMeta(8, {
  en_us: 'file count not match book',
  zh_HK: '文件數目與書籍數目不匹配',
  zh_cn: '文件数目与书籍数目不匹配',
})
export class FileCountNotMatchWithBookException extends BaseException {}

@BookExceptionMeta(9, {
  en_us: 'book file not found',
  zh_HK: '書籍文件沒找到',
  zh_cn: '书籍文件没找到',
})
export class BookFileNotFoundException extends BaseException {}

@BookExceptionMeta(10, {
  en_us: 'too mandy book files',
  zh_HK: '書籍文件匹數數量超過1本',
  zh_cn: '书籍文件匹数数量超过1本',
})
export class TooManyBookFileException extends BaseException {}

@BookExceptionMeta(11, {
  en_us: 'unsupported book file',
  zh_HK: '不支持的書籍類型',
  zh_cn: '不支持的书籍类型',
})
export class UnsupportedBookFileException extends BaseException {}

@BookExceptionMeta(12, {
  en_us: 'duplicate book',
  zh_HK: '重複的書籍',
  zh_cn: '重复的书籍',
})
export class DuplicateBookException extends BaseException {}

@BookExceptionMeta(13, {
  en_us: 'this book is already on your bookshelf',
  zh_HK: '已將書籍加入書架',
  zh_cn: '已将书籍加入书架',
})
export class AlreadyOnBookshelfException extends BaseException {}

@BookExceptionMeta(14, {
  en_us: 'book list not exist',
  zh_HK: '書籍列表不存在',
  zh_cn: '书籍列表不存在',
})
export class BookListNotExistException extends BaseException {}

@BookExceptionMeta(15, {
  en_us: 'carousel not exist',
  zh_HK: '輪播圖不存在',
  zh_cn: '轮播图不存在',
})
export class CarouselNotExistException extends BaseException {}

@BookExceptionMeta(16, {
  en_us: 'homepage not exist',
  zh_HK: '首頁推薦不存在',
  zh_cn: '首页推荐不存在',
})
export class HomepageNotExistException extends BaseException {}

@BookExceptionMeta(17, {
  en_us: 'duplicated recommend word',
  zh_HK: '重複搜索詞',
  zh_cn: '重复搜索词',
})
export class DuplicatedRecommendWordException extends BaseException {}

@BookExceptionMeta(18, {
  en_us: 'The content of the fixed recommendation cannot be modified',
  zh_HK: '固定推薦位内容不可修改',
  zh_cn: '固定推荐位内容不可修改',
})
export class RecommendationCanNotUpdateException extends BaseException {}

@BookExceptionMeta(19, {
  en_us: 'duplicate category',
  zh_HK: '重複課程綱要分類',
  zh_cn: '重复课程纲要分类',
})
export class DuplicateCategoryException extends BaseException {}

@BookExceptionMeta(20, {
  en_us: 'duplicate category label',
  zh_HK: '重複圖書品種標籤',
  zh_cn: '重复图书品种标签',
})
export class DuplicateCategoryLabelException extends BaseException {}

@BookExceptionMeta(21, {
  en_us: 'duplicate education label',
  zh_HK: '重複價值觀教育標籤',
  zh_cn: '重复价值观教育标签',
})
export class DuplicateEducationLabelException extends BaseException {}

@BookExceptionMeta(22, {
  en_us: 'A book list can only be added to one recommendation',
  zh_HK: '一個書籍列表只能添加至一個推薦位，請勿重複配置',
  zh_cn: '一个书籍列表只能添加至一个推荐位，请勿重复配置',
})
export class DuplicateHomepageBookListException extends BaseException {}

@BookExceptionMeta(23, {
  en_us: 'A book list url must be unique',
  zh_HK: '一個書籍列表鏈接地址必須是唯一的',
  zh_cn: '一个书籍列表链接地址必须是唯一的',
})
export class DuplicateBookListUrlException extends BaseException {}

@BookExceptionMeta(24, {
  en_us: 'duplicate author',
  zh_HK: '作者名字重複',
  zh_cn: '作者名字重复',
})
export class DuplicateAuthorException extends BaseException {}

@BookExceptionMeta(25, {
  en_us: 'book information error',
  zh_HK: '書籍信息錯誤',
  zh_cn: '书籍信息错误',
})
export class BookInformationException extends BaseException {
  constructor(message: string) {
    super(message)
  }
}

@BookExceptionMeta(26, {
  en_us: 'duplicate publisher',
  zh_HK: '出版社名字重複',
  zh_cn: '出版社名字重复',
})
export class DuplicatePublisherException extends BaseException {}

@BookExceptionMeta(27, {
  en_us: 'Only png and jpg images are supported',
  zh_HK: '僅支持png和jpg圖片類型',
  zh_cn: '仅支持png和jpg图片类型',
})
export class OnlySupportPngOrJpgException extends BaseException {}

@BookExceptionMeta(28, {
  en_us: 'homepage duplicate',
  zh_HK: '推薦位已存在',
  zh_cn: '推荐位已存在',
})
export class HomepageDuplicatedException extends BaseException {}

@BookExceptionMeta(29, {
  en_us: 'duplicate book level',
  zh_HK: '目標閱讀群組名字重複',
  zh_cn: '目标阅读群组名字重复',
})
export class DuplicateBookLevelException extends BaseException {}

@BookExceptionMeta(30, {
  en_us: ' book level not exist',
  zh_HK: '目標閱讀群組不存在',
  zh_cn: '目标阅读群组不存在',
})
export class NotExistBookLevelException extends BaseException {}

@BookExceptionMeta(31, {
  en_us: ' book level can not delete',
  zh_HK: '目標閱讀群組擁有多本書籍不可刪除',
  zh_cn: '目标阅读群组拥有多本书籍不可删除',
})
export class BookLevelDeleteException extends BaseException {}

@BookExceptionMeta(32, {
  en_us: 'homepage duplicate',
  zh_HK: '推薦位重复',
  zh_cn: '推荐位重复',
})
export class StudentHomepageDuplicatedException extends BaseException {
  constructor(message: string) {
    super(message)
  }
}

@BookExceptionMeta(33, {
  en_us: 'book not exist',
  zh_HK: '參考館無此書籍',
  zh_cn: '参考馆无此书籍',
})
export class BookNotExistInReferenceException extends BaseException {}

@BookExceptionMeta(34, {
  en_us: 'Please wait to read',
  zh_HK: '請稍候閱讀',
  zh_cn: '请稍候阅读',
})
export class TooManyUsersReadException extends BaseException {}

@BookExceptionMeta(35, {
  en_us: 'Only mp3 and mp4 images are supported',
  zh_HK: '僅支持mp3和mp4類型',
  zh_cn: '仅支持mp3和mp4类型',
})
export class OnlySupportMP3OrMP4Exception extends BaseException {}

@BookExceptionMeta(36, {
  en_us: 'Only audio are supported',
  zh_HK: '僅支持aduio類型',
  zh_cn: '仅支持audio类型',
})
export class OnlySupportAudioException extends BaseException {}

@BookExceptionMeta(37, {
  en_us: 'Upload Audio failed',
  zh_HK: '上传aduio失败',
  zh_cn: '上传audio失败',
})
export class UploadAudioException extends BaseException {}
