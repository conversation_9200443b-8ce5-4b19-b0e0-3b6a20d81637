import { ELocaleType } from '@/common'
import { EGrade, ELessonDocumentType, SchoolType } from '../../enums'

export const REMAINING_READING_TIME_REMINDER_TITLE = {
  [ELocaleType.ZH_HK]: `閱讀時數已用完`,
  [ELocaleType.ZH_CN]: `阅读时数已用完`,
  [ELocaleType.EN_UK]: `Hours have been used up`,
}

export const REMAINING_READING_TIME_REMINDER_MESSAGE = {
  [ELocaleType.ZH_HK]: `你的閱讀時數不足，請點擊以下申請更多時數。`,
  [ELocaleType.ZH_CN]: `你的阅读时数不足，请点击以下申请更多时数。`,
  [ELocaleType.EN_UK]: `Your reading hour balance is low. Please click below to apply more reading hours.`,
}

export const SCHOOL_CONTRACTS = {
  hasScienceRoom: 1,
  hasAssistant: 1,
}

export const GRADE<PERSON>_LOCALE: {
  [k in ELocaleType]: { [k in EGrade]: string }
} = {
  [ELocaleType.ZH_HK]: {
    KINDERGARTEN: '幼稚園',
    FIRST_PRIMARY_GRADE: '小一',
    SECOND_PRIMARY_GRADE: '小二',
    THIRD_PRIMARY_GRADE: '小三',
    FOURTH_PRIMARY_GRADE: '小四',
    FIFTH_PRIMARY_GRADE: '小五',
    SIXTH_PRIMARY_GRADE: '小六',
    FIRST_MIDDLE_GRADE: '中一',
    SECOND_MIDDLE_GRADE: '中二',
    THIRD_MIDDLE_GRADE: '中三',
    FOURTH_MIDDLE_GRADE: '中四',
    FIFTH_MIDDLE_GRADE: '中五',
    SIXTH_MIDDLE_GRADE: '中六',
  },
  [ELocaleType.ZH_CN]: {
    KINDERGARTEN: '幼稚園',
    FIRST_PRIMARY_GRADE: '小一',
    SECOND_PRIMARY_GRADE: '小二',
    THIRD_PRIMARY_GRADE: '小三',
    FOURTH_PRIMARY_GRADE: '小四',
    FIFTH_PRIMARY_GRADE: '小五',
    SIXTH_PRIMARY_GRADE: '小六',
    FIRST_MIDDLE_GRADE: '中一',
    SECOND_MIDDLE_GRADE: '中二',
    THIRD_MIDDLE_GRADE: '中三',
    FOURTH_MIDDLE_GRADE: '中四',
    FIFTH_MIDDLE_GRADE: '中五',
    SIXTH_MIDDLE_GRADE: '中六',
  },
  [ELocaleType.EN_UK]: {
    KINDERGARTEN: 'Kindergarten',
    FIRST_PRIMARY_GRADE: 'P1',
    SECOND_PRIMARY_GRADE: 'P2',
    THIRD_PRIMARY_GRADE: 'P3',
    FOURTH_PRIMARY_GRADE: 'P4',
    FIFTH_PRIMARY_GRADE: 'P5',
    SIXTH_PRIMARY_GRADE: 'P6',
    FIRST_MIDDLE_GRADE: 'S1',
    SECOND_MIDDLE_GRADE: 'S2',
    THIRD_MIDDLE_GRADE: 'S3',
    FOURTH_MIDDLE_GRADE: 'S4',
    FIFTH_MIDDLE_GRADE: 'S5',
    SIXTH_MIDDLE_GRADE: 'S6',
  },
}

export const ELESSON_DOCUMENT: {
  [k in ELocaleType]: { [k in ELessonDocumentType]: string }
} = {
  [ELocaleType.ZH_HK]: {
    INTRODUCTION: '簡介', //简介
    PDAR: 'PDAR', //PDAR
    ASSESSMENT_QUESTIONS: '評估題目', //评估题目
    SIMULATION_EXPERIMENT_VIDEO: '模擬實驗視頻', //模拟实验视频
    SUGGESTED_CLASSROOM_EXPERIMENTS: '建議課堂實驗', //建议课堂实验
    SUGGESTED_CLASSROOM_ACTIVITIES: '建議課堂活動', //建议课堂活动
    TEACHING_MATERIAL: '教材', //教材
    EXTENDED_EXPERIMENT: '延伸實驗', //延伸實驗
    EXTENDED_ACTIVITIES: '延伸活動', //延伸活动
  },
  [ELocaleType.ZH_CN]: {
    INTRODUCTION: '简介', //简介
    PDAR: 'PDAR', //PDAR
    ASSESSMENT_QUESTIONS: '评估题目', //评估题目
    SIMULATION_EXPERIMENT_VIDEO: '模拟实验视频', //模拟实验视频
    SUGGESTED_CLASSROOM_EXPERIMENTS: '建议课堂实验', //建议课堂实验
    SUGGESTED_CLASSROOM_ACTIVITIES: '建议课堂活动', //建议课堂活动
    TEACHING_MATERIAL: '教材', //教材
    EXTENDED_EXPERIMENT: '延伸實驗', //延伸實驗
    EXTENDED_ACTIVITIES: '延伸活动', //延伸活动
  },
  [ELocaleType.EN_UK]: {
    INTRODUCTION: 'INTRODUCTION', //简介
    PDAR: 'PDAR', //PDAR
    ASSESSMENT_QUESTIONS: 'ASSESSMENT_QUESTIONS', //评估题目
    SIMULATION_EXPERIMENT_VIDEO: 'SIMULATION_EXPERIMENT_VIDEO', //模拟实验视频
    SUGGESTED_CLASSROOM_EXPERIMENTS: 'SUGGESTED_CLASSROOM_EXPERIMENTS', //建议课堂实验
    SUGGESTED_CLASSROOM_ACTIVITIES: 'SUGGESTED_CLASSROOM_ACTIVITIES', //建议课堂活动
    TEACHING_MATERIAL: 'TEACHING_MATERIAL', //教材
    EXTENDED_EXPERIMENT: 'EXTENDED_EXPERIMENT', //延伸實驗
    EXTENDED_ACTIVITIES: 'EXTENDED_ACTIVITIES', //延伸活动
  },
}

export function convertSchoolTypeToName(type?: SchoolType) {
  switch (type) {
    case SchoolType.COLLEGES:
      return '大專院校'
    case SchoolType.KINDERGARTEN:
      return '幼稚園'
    case SchoolType.MIDDLE:
      return '中學'
    case SchoolType.PRIMARY:
      return '小學'
    default:
      return ''
  }
}
