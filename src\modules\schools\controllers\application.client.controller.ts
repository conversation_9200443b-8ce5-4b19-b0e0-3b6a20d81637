import { Body, Controller, Get, Param, ParseIntPipe, Post } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiBaseResult, ClientAuth, CurrentUser } from '@/common'
import { ApplicationDto, CreateApplicationDto, getApplicationDto } from '../dto'
import { ApplicationService } from '../services/application.service'

@ApiTags('Applications')
@Controller('v1/client/applications')
export class ApplicationClientController {
  constructor(private readonly applicationService: ApplicationService) {}

  @Post()
  @ClientAuth()
  @ApiOperation({ summary: 'create an application' })
  @ApiBaseResult(ApplicationDto, 200)
  async createApplication(@Body() data: CreateApplicationDto, @CurrentUser() user: any) {
    const application = await this.applicationService.createApplication(data, user)
    return getApplicationDto(application)
  }

  @Get('/:messageId')
  @ClientAuth()
  @ApiOperation({ summary: 'Get an application' })
  @ApiBaseResult(ApplicationDto, 200)
  async getApplication(@Param('messageId', ParseIntPipe) messageId: number) {
    const application = await this.applicationService.getApplicationByMessageId(
      Number(messageId)
    )
    if (application) {
      return getApplicationDto(application)
    }
  }
}
