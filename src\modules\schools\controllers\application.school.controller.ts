import { Body, Controller, Get, Param, ParseIntPipe, Patch, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { WebsocketGateway } from 'src/modules/websocket'
import {
  ApiPageResult,
  AuthSchema,
  CurrentUser,
  ELocaleType,
  PageRequest,
  SchoolAdminAuth,
} from '@/common'
import { OneSignalService } from '@/common/services/oneSignal.service'
import { ApplicationStatus } from '@/enums'
import { IUserRepo } from '@/modules/shared/interfaces'
import { ApplicationDto, getApplicationDto, UpdateApplicationDto } from '../dto'
import { UserBalanceService } from '../services'
import { ApplicationService, ReadingTimeManagerService } from '../services/index1'
import { MessageService } from '../services/index2'

@ApiTags('Applications')
@Controller('v1/school-admin/applications')
export class ApplicationSchoolAdminController {
  constructor(
    private readonly applicationService: ApplicationService,
    private readonly messageService: MessageService,
    private readonly userBalanceService: UserBalanceService,
    private readonly readingTimeManager: ReadingTimeManagerService,
    private readonly webSocketGateway: WebsocketGateway,
    private readonly oneSignalService: OneSignalService,
    private readonly userRepository: IUserRepo
  ) {}

  @Get()
  @ApiOperation({ summary: 'list applications' })
  @ApiPageResult(ApplicationDto, 200)
  @SchoolAdminAuth()
  async listApplication(@Query() query: PageRequest, @CurrentUser() user: any) {
    const data = await this.applicationService.getApplications(user.schoolId, query)
    return { ...data, items: data.items.map((item) => getApplicationDto(item)) }
  }

  @Patch(':id')
  @SchoolAdminAuth()
  @ApiOperation({ summary: 'patch an application' })
  @ApiPageResult(ApplicationDto, 200)
  async updateApplication(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateApplicationDto,
    @CurrentUser() user: any
  ) {
    const application = await this.applicationService.updateApplication(id, data, user)
    if (data.status === ApplicationStatus.APPROVED) {
      await this.readingTimeManager.distributionTimeToUsers(user, {
        time: data.approvalTime,
        userIds: [application.userId],
      })
    }
    const balance = await this.userBalanceService.getUserBalance(application.userId)
    const status = data.status === ApplicationStatus.APPROVED ? '批核' : '拒絕'
    const statusCn = data.status === ApplicationStatus.APPROVED ? '批核' : '拒绝'
    const statusEn = data.status === ApplicationStatus.APPROVED ? 'approved' : 'rejected'
    const title = {
      [ELocaleType.ZH_HK]: `您的時數申請已${status}`,
      [ELocaleType.ZH_CN]: `您的时数申请已${statusCn}`,
      [ELocaleType.EN_UK]: `Your request has been ${statusEn}`,
    }
    const time = (application.time / 3600).toFixed(2)
    const leftTime =
      balance.totalQuota > balance.usedQuota
        ? (Math.abs(balance.totalQuota - balance.usedQuota) / 3600).toFixed(2)
        : '0.00'
    const approvalTime =
      data.approvalTime > 0 && data.approvalTime !== Number(application.time)
        ? `管理員修改為${(data.approvalTime / 3600).toFixed(2)}小時，`
        : ''
    const approvalTimeEn =
      data.approvalTime > 0 && data.approvalTime !== Number(application.time)
        ? `administrator modified it to ${(data.approvalTime / 3600).toFixed(2)} hours,`
        : ''
    const content = {
      [ELocaleType.ZH_HK]: `您的時數申請已${status}，您申請時數為${time}小時，您帳戶剩餘閱讀時數為${leftTime}小時，請繼續享受美好的閱讀時光。`,
      [ELocaleType.ZH_CN]: `您的时数申请已${status}，您申请时数为${time}小时，您账户剩余阅读时数为${leftTime}小时，请继续享受美好的阅读时光。`,
      [ELocaleType.EN_UK]: `Your request for reading hours has been ${statusEn}, your application hours are `+
          `${time} hours, the current reading hours remaining on your account is ${leftTime} hours,Please continue to have a great reading time. `,
    }
    const contact = {
      [ELocaleType.ZH_HK]: `如有疑問，請聯繫學校管理員。學校管理員聯絡郵箱：${user.email}`,
      [ELocaleType.ZH_CN]: `您如有疑问，请联系学校管理员。学校管理员联系邮箱：${user.email}`,
      [ELocaleType.EN_UK]: `If in doubt, please contact the school administrator. School administrator contact email: ${user.email}`,
    }
    const approvalTimeContent = {
      [ELocaleType.ZH_HK]: `${approvalTime}`,
      [ELocaleType.ZH_CN]: `${approvalTime}`,
      [ELocaleType.EN_UK]: `${approvalTimeEn}`,
    }

    const meta = {
      contact: contact,
      approvalTime: application.approvalTime,
      approvalTimeContent: approvalTimeContent,
      time: application.time,
      leftTime:
        balance.totalQuota - balance.usedQuota < 0
          ? 0
          : balance.totalQuota - balance.usedQuota,
      applicateAt: application.createdAt.getTime() / 1000,
      status: data.status,
      remarks: data.remarks,
    }
    const message = await this.messageService.createMessage(
      {
        title,
        message: content,
        toUserIds: [application.userId],
        applicationId: application.id,
        type:
          data.status === ApplicationStatus.APPROVED
            ? 'APPLICATION_APPROVED'
            : data.status === ApplicationStatus.REJECTED
              ? 'APPLICATION_REJECTED'
              : 'APPLICATION',
        meta,
      },
      user.schoolId,
      user
    )
    const account = await this.userRepository.findOne({ where: { id: application.userId  } })
    if (account.playerId)
    {await this.oneSignalService.sendNotification('common.oneSignal', {
      playerIds: [account.playerId],
      headings: {
        en: message.title.en_uk,
        'zh-Hant': message.title.zh_HK,
        'zh-Hans': message.title.zh_cn,
      },
      contents: {
        en: message.message.en_uk,
        'zh-Hant': message.message.zh_HK,
        'zh-Hans': message.message.zh_cn,
      },
      data: {
        id: message.id,
      },
    })}
    await this.webSocketGateway.sendNotifications(application.userId, AuthSchema.CLIENT, {
      title: message.title,
      message: message.message,
      createdAt: message.createdAt,
      type: message.type,
      meta,
      application,
    })
    return getApplicationDto(application)
  }
}
