import { Body, Controller, Get, Param, ParseInt<PERSON><PERSON><PERSON>, Post } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import moment from 'moment'
import R from 'ramda'
import { AdminAuth, ApiBaseResult, CurrentAdmin } from '@/common'
import { IReadRecordService } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system'
import { BuyOrRevokeReadingTimeDto, getSchoolBalanceDto, SchoolBalanceDto } from '../dto'
import { SchoolBalanceService } from '../services'

@Controller('v1/admin/balance')
export class BalanceAdminController {
  constructor(
    private readonly schoolBalance: SchoolBalanceService,
    private readonly logger: OperationLogService,
    private readonly readRecordService: IReadRecordService
  ) {}

  @ApiOperation({ summary: 'buy reading time' })
  // @ApiBaseResult()
  @AdminAuth()
  @Post('buy-reading-time')
  async buyReadingTime(
    @Body() body: BuyOrRevokeReadingTimeDto,
    @CurrentAdmin() user: any
  ) {
    const school = await this.schoolBalance.buyReadingTime(
      body.schoolId,
      body.time,
      body.contractNumber
    )
    await this.logger.createLog({
      user,
      operation: `${user.roles} ${R.isNil(user.givenName) ? '' : user.givenName} ${
        R.isNil(user.familyName) ? '' : user.familyName
      } 於 ${moment.tz('Asia/Hong_Kong').format('YYYY-MM-DD HH:mm:ss')} 給 ${
        school.name.zh_HK
      } 充值閱讀時數 ${(body.time / 3600).toFixed(2)} 小時 }`,
    })
    return school
  }

  // @ApiOperation({ summary: 'revoke reading time' })
  // // @ApiBaseResult()
  // @AdminAuth()
  // @Post('revoke-reading-time')
  // async revokeReadingTime(@Body() body: BuyOrRevokeReadingTimeDto) {
  //   const data = await this.schoolBalance.revokeSchoolReadingTime(
  //     body.schoolId,
  //     body.time,
  //   )
  //   return data
  // }

  @ApiOperation({ summary: 'get school balance' })
  @AdminAuth()
  @ApiBaseResult(SchoolBalanceDto, 200)
  @Get('school/:schoolId')
  async getSchoolBalance(@Param('schoolId', ParseIntPipe) schoolId: number) {
    const balance = await this.schoolBalance.getSchoolBalance(schoolId)
    const recharge = await this.schoolBalance.getNewestRecharge(schoolId)

    const usedQuota = await this.readRecordService.getSchoolUsedTime(schoolId)

    return {
      ...getSchoolBalanceDto({ ...balance, usedQuota }),
      currentBuyReadingTime: recharge?.buyOrRevokeTime ?? 0,
    }
  }
}
