import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Header,
  Post,
  Query,
  Res,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import {
  ApiBaseResult,
  ApiPageResult,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  SchoolAdminAuth,
} from '@/common'
import { Recharge } from '@/entities'
import { EUserType } from '@/enums'
import { SearchUserBalanceDto } from '@/modules/account/dto'
import { PAGE_SIZE } from '@/modules/constants'
import { IReadRecordService,IUserRepo } from '@/modules/shared/interfaces'
import { LogService, OperationLogService } from '@/modules/system'
import {
  DistributionTimeDto,
  DistributionTimeToSearchUserDto,
  getSchoolBalanceDto,
  QueryLogDto,
  RevokeReadingTimeDto,
  SchoolBalanceDto,
  UserBalanceDto,
} from '../dto'
import { MiniumDistributeReadingTimeLimit } from '../exception'
import { SchoolBalanceService, SchoolService, UserBalanceService } from '../services'
import { GradeService } from '../services/grade.service'
import { ReadingTimeManagerService } from '../services/readingTimeManager.service'

@ApiTags('Balance')
@ApiExtraModels(SchoolBalanceDto, UserBalanceDto, RevokeReadingTimeDto)
@Controller('v1/school-admin/balance')
export class BalanceSchoolController {
  constructor(
    private readonly userRepository: IUserRepo,
    private readonly schoolBalanceService: SchoolBalanceService,
    private readonly userBalanceService: UserBalanceService,
    private readonly logService: OperationLogService,
    private readonly gradeService: GradeService,
    private readonly schoolService: SchoolService,
    private readonly readingTimeManagerService: ReadingTimeManagerService,
    private readonly readRecordService: IReadRecordService,
    private readonly excelService: ExcelService,
    private readonly sysLogService: LogService
  ) {}

  @ApiOperation({ summary: 'get school balance' })
  @SchoolAdminAuth()
  @ApiBaseResult(SchoolBalanceDto, 200)
  @Get('school')
  async getSchoolBalance(@CurrentSchoolAdmin() user: any) {
    const balance = await this.schoolBalanceService.getSchoolBalance(user.schoolId)
    // const usedQuota2 = await this.readRecordService.getSchoolUsedTime(user.schoolId) // data from table read_record
    const usedQuota = await this.userBalanceService.getSchoolUsed(user.schoolId) // data from table user_balances
    return {
      ...getSchoolBalanceDto({ ...balance, usedQuota }),
    }
  }

  @SchoolAdminAuth()
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=time.xlsx')
  @CurrentLocaleHeader()
  @Get('/export')
  // get data from user balance and export to excel
  async exportBalance(
    @CurrentSchoolAdmin() user: any,
    @Query() query: SearchUserBalanceDto,
    @CurrentLocale() local = ELocaleType.ZH_HK,
    @Res() res: Response
  ) {
    const school = await this.schoolService.findOne({ where: { id: user.schoolId  } })
    let total = 0
    let pageIndex = 1
    const pageSize = 30
    let excelData = []
    do {
      const data = await this.queryBalance(user, { ...query, pageIndex, pageSize })
      const excel = data.items.map((item) => ({
        email: item.user.email ?? '',
        name: `${item.user.givenName ?? ''} ${item.user.familyName ?? ''}`,
        grade: item.user.userClass?.grade ?? '',
        class: item.user.userClass?.class ?? '',
        type: item.user.type === EUserType.STUDENT ? '學生' : '教職員',
        totalQuota: Number(item.totalQuota ?? 0) / 3600,
        usedQuota: Number(item.usedQuota ?? 0) / 3600,
        totalUsedQuota: Number(item.totalUsedQuota ?? 0) / 3600,
        leftQuota: school.isSharingTime
          ? '-'
          : Number(Number(item.totalQuota ?? 0) - Number(item.usedQuota ?? 0)) / 3600,
      }))
      total = data.total
      pageIndex += 1
      excelData = excelData.concat(excel)
    } while ((pageIndex - 1) * pageSize < total)

    const file = await this.excelService.buildExcel({
      name: `readingTime.${local}`,
      data: excelData,
    })

    await this.sysLogService.save('下载時數分配', user, query)
    res.send(Buffer.from(file))
  }

  @ApiOperation({ summary: 'list balance' })
  @SchoolAdminAuth()
  @ApiPageResult(UserBalanceDto, 200)
  @Get()
  async listBalance(
    @CurrentSchoolAdmin() user: any,
    @Query() query: SearchUserBalanceDto
  ) {
    return this.queryBalance(user, query)
  }

  @ApiOperation({ summary: 'distribution time to search users' })
  @SchoolAdminAuth()
  @Post('distribue-to-search-users')
  async distributionTimeToSearchUsers(
    @CurrentSchoolAdmin() user: any,
    @Body() data: DistributionTimeToSearchUserDto
  ) {
    if (data.time < 3600) {throw new MiniumDistributeReadingTimeLimit()}

    const users = await this.userRepository.searchUserIds(user.schoolId, data)
    let userIds = users.map((item) => item.id)
    if (data.exceptions?.length) {
      userIds = userIds.filter((id) => !data.exceptions.includes(id))
    }

    if (userIds.length === 0) {
      throw new BadRequestException('No user found')
    }

    await this.readingTimeManagerService.distributionTimeToUsers(user, {
      time: data.time,
      userIds,
    })
  }

  @ApiOperation({ summary: 'distribution time' })
  @SchoolAdminAuth()
  @Post('distribution')
  async distributionTimeToUsers(
    @CurrentSchoolAdmin() user: any,
    @Body() data: DistributionTimeDto
  ) {
    await this.readingTimeManagerService.distributionTimeToUsers(user, data)
  }

  @ApiOperation({ summary: 'revoke time to search users' })
  @ApiBaseResult(RevokeReadingTimeDto, 200)
  @SchoolAdminAuth()
  @Post('revoke-to-search-users')
  async revokeReadingTimeToSearchUsers(
    @CurrentSchoolAdmin() user: any,
    @Body() data: DistributionTimeToSearchUserDto
  ) {
    const users = await this.userRepository.searchUserIds(user.schoolId, data)
    let userIds = users.map((item) => item.id)
    if (data.exceptions?.length) {
      userIds = userIds.filter((id) => !data.exceptions.includes(id))
    }

    return this.readingTimeManagerService.revokeTimeFromUsers(user, {
      time: data.time,
      userIds,
    })
  }

  @ApiOperation({ summary: 'revoke time' })
  @ApiBaseResult(RevokeReadingTimeDto, 200)
  @SchoolAdminAuth()
  @Post('revoke')
  async revokeReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Body() data: DistributionTimeDto
  ) {
    return this.readingTimeManagerService.revokeTimeFromUsers(user, data)
  }

  @ApiOperation({ summary: 'list recharge ' })
  @ApiPageResult(Recharge, 200)
  @SchoolAdminAuth()
  @Get('recharge')
  async listRecharge(@CurrentSchoolAdmin() user: any, @Query() query: QueryLogDto) {
    return this.schoolBalanceService.listRecharge(user.schoolId, query)
  }

  private async queryBalance(
    @CurrentSchoolAdmin() user: any,
    @Query() query: SearchUserBalanceDto
  ) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const data = await this.userRepository.searchUserBalances(user.schoolId, {
      ...query,
      pageIndex,
      pageSize,
    })
    if (data.items.length === 0) {
      return { ...data, items: [] }
    }
    const grades = await this.gradeService.listGrades(
      data.items.map((item) => item.userClass?.gradeId)
    )

    const result = {
      ...data,
      items: data.items.map((item) => new UserBalanceDto(item, item.balance)),
    }
    const items = result.items.map((item) => {
      return {
        ...item,
        user: {
          ...item.user,
          userClass: {
            ...item.user.userClass,
            grade: grades.find((g) => g.id === item.user?.userClass?.gradeId)?.grade,
          },
        },
      }
    })

    return {
      ...result,
      items,
    }
  }
}
