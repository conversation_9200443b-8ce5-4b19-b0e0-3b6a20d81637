import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  BooleanResponse,
  CurrentAdmin,
  DeleteDto,
  getPageResponse,
} from '@/common'
import { EListStatus } from '@/enums'
import { OperationLogService } from '@/modules/system'
import {
  BookListDto,
  getBookListDto,
  ModifyBookListDto,
  QueryBookListDto,
  UpdateBookListStatusDto,
} from '../../books/dto'
import { BookListService } from '../services'

@ApiTags('BookLists')
@ApiExtraModels(BookListDto)
@Controller('v1/admin/book-lists')
export class BookListAdminController {
  constructor(
    private readonly bookListService: BookListService,
    private readonly logService: OperationLogService
  ) {}

  @ApiOperation({ summary: 'create a book list' })
  @AdminAuth()
  @ApiBaseResult(BookListDto, 200)
  @Post()
  async createBookList(
    @Body() data: ModifyBookListDto,
    @CurrentAdmin() user: any
  ): Promise<BookListDto> {
    const booklist = await this.bookListService.createBookList(data)
    await this.logService.createLog({
      operation: `新增書籍列表“${booklist.name.zh_HK}”`,
      metaData: { booklistId: booklist.id },
      user,
    })
    return getBookListDto(booklist)
  }

  @ApiOperation({ summary: 'update a book list' })
  @AdminAuth()
  @ApiBaseResult(BookListDto, 200)
  @Patch(':id')
  async updateBookList(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: ModifyBookListDto
  ): Promise<BookListDto> {
    const booklist = await this.bookListService.updateBookList(id, data)
    return getBookListDto(booklist)
  }

  @ApiOperation({ summary: 'update a book list status' })
  @AdminAuth()
  @ApiListResult(BookListDto, 200)
  @Patch()
  async updateBookListStatus(
    @Body() data: UpdateBookListStatusDto,
    @CurrentAdmin() user: any
  ): Promise<BookListDto[]> {
    // const bookLists = await this.bookListService.findBookLists({ ids: data.ids })
    const lists = await this.bookListService.updateBookListStatus(data.ids, data.status)
    const bookLists = lists.filter((item) => !!item)
    const operation =
      data.status === EListStatus.OFFLINE
        ? bookLists.length > 3
          ? `批量下架`
          : '下架書籍列表'
        : bookLists.length > 3
          ? `批量上架`
          : '上架書籍列表'

    await this.logService.createLog({
      operation: `${operation}${bookLists
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${bookLists.length > 3 ? `等${bookLists.length}個書籍列表` : ''}`,
      metaData: { booklistIds: data.ids },
      user,
    })
    return bookLists.map((item) => getBookListDto(item))
  }

  @ApiOperation({ summary: 'find book list' })
  @AdminAuth()
  @ApiBaseResult(BookListDto, 200)
  @Get(':id')
  async getBookList(@Param('id', ParseIntPipe) id: number) {
    const data = await this.bookListService.findBookList({ id })
    return getBookListDto(data)
  }

  @ApiOperation({ summary: 'list book list' })
  @AdminAuth()
  @ApiPageResult(BookListDto, 200)
  @Get()
  async listBookList(@Query() query: QueryBookListDto) {
    const data = await this.bookListService.listBookList(query)
    return getPageResponse(data, data.items, getBookListDto)
  }

  @ApiOperation({ summary: 'delete book list' })
  @AdminAuth()
  @ApiBaseResult(BooleanResponse, 200)
  @Delete()
  async deleteBookList(@Body() body: DeleteDto, @CurrentAdmin() user: any) {
    let ids = body.ids?.length ? body.ids : []
    if (ids.length <= 0) {
      ids = await this.bookListService.listAllBookListIds()
      if (body.exceptions?.length) {
        ids = ids.filter((id) => !body.exceptions.includes(id))
      }
    }
    const booklists = await this.bookListService.findBookLists({ ids })
    await this.bookListService.deleteBookList(ids)
    await this.logService.createLog({
      operation: `${booklists.length > 3 ? `批量删除` : '删除書籍列表'}${booklists
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${booklists.length > 3 ? `等${booklists.length}個書籍列表` : ''}`,
      metaData: { boolistIds: ids },
      user,
    })
    return { status: true }
  }
}
