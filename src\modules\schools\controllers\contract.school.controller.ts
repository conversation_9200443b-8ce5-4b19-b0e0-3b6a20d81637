import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { ApiPageResult, CurrentSchoolAdmin, SchoolAdminAuth } from '@/common'
import { ContractDto, getContractDto, QuerySchoolContractDto } from '../dto'
import { ContractService } from '../services'

@Controller('v1/school-admin/contracts')
export class ContractSchoolAdminController {
  constructor(private readonly contractService: ContractService) {}

  @ApiOperation({ summary: '获取合约记录' })
  @SchoolAdminAuth()
  @ApiPageResult(ContractDto, 200)
  @Get()
  async listContracts(
    @CurrentSchoolAdmin() admin: any,
    @Query() query: QuerySchoolContractDto
  ) {
    const data = await this.contractService.listContracts({
      ...query,
      schoolId: admin.schoolId,
    })
    return {
      ...data,
      items: data.items.map((item) => getContractDto(item)),
    }
  }
}
