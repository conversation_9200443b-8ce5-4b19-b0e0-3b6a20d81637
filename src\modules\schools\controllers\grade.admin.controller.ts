import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  CurrentLocale,
  CurrentSchoolAdmin,
  ELocaleType,
  PageRequest,
  SchoolAdminAuth,
} from '@/common'
import { GRADES_LOCALE } from '../constants'
import { CreateGradeDto, SortGradeDto, UpdateGradeDto } from '../dto'
import { DeleteGradeException } from '../exception'
import { GradeService } from '../services'
import { UserClassService } from '../services/index1'

@ApiTags('grades')
@ApiExtraModels(CreateGradeDto)
@Controller('v1/school-admin/grades')
export class GradeSchoolController {
  constructor(
    private readonly gradeService: GradeService,
    private readonly classService: UserClassService
  ) {}

  @ApiOperation({ summary: 'create grade' })
  // @ApiPageResult(GradeDto, 200)
  @SchoolAdminAuth()
  @Post()
  createGrade(@CurrentSchoolAdmin() admin: any, @Body() body: CreateGradeDto) {
    return this.gradeService.createGrade(admin.schoolId, body)
  }

  @ApiOperation({ summary: 'list all grade' })
  // @ApiPageResult(GradeDto, 200)
  @SchoolAdminAuth()
  @Get('all')
  listAllGrades(@CurrentSchoolAdmin() admin: any, @Query() query: CreateGradeDto) {
    return this.gradeService.listAllGrade(admin.schoolId, query.grade)
  }

  @ApiOperation({ summary: 'update grade' })
  @SchoolAdminAuth()
  @Patch('update')
  updateGrades(
    @CurrentSchoolAdmin() admin: any,
    @Body('id', ParseIntPipe) id: number,
    @Body() body: UpdateGradeDto
  ) {
    console.log(body)
    return this.gradeService.updateGrade(admin.schoolId, id, body)
  }

  @ApiOperation({ summary: '' })
  // @ApiPageResult(GradeDto, 200)
  @SchoolAdminAuth()
  @Patch('sort')
  sortGrades(@CurrentSchoolAdmin() admin: any, @Body() query: SortGradeDto) {
    return this.gradeService.updateGradeSequence(query)
  }

  @ApiOperation({ summary: 'list grade' })
  // @ApiPageResult(GradeDto, 200)
  @SchoolAdminAuth()
  @Get()
  async listGrades(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() admin: any,
    @Query() query: PageRequest
  ) {
    const data = await this.gradeService.listGrade(admin.schoolId, query)
    const items = await Promise.all(
      data.items.map(async (item) => {
        const classes = await this.classService.searchClass(admin.schoolId, {
          gradeId: item.id,
        })
        return { ...item, gradeCodeText: GRADES_LOCALE[local][item.gradeCode], classes }
      })
    )
    return { ...data, items }
  }

  @ApiOperation({ summary: 'delete grade' })
  // @ApiPageResult(GradeDto, 200)
  @SchoolAdminAuth()
  @Delete(':id')
  async deleteGrade(
    @CurrentSchoolAdmin() admin: any,
    @Param('id', ParseIntPipe) gradeId: number
  ) {
    const classes = await this.classService.searchClass(admin.schoolId, {
      gradeId,
    })
    if (classes.length) {
      throw new DeleteGradeException()
    }

    return this.gradeService.deleteGrade(gradeId, admin)
  }
}
