import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Cache } from 'cache-manager'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  BooleanResponse,
  CurrentAdmin,
  getPageResponse,
  PageRequest,
  PageResponse,
} from '@/common'
import { Homepage } from '@/entities'
import { EListStatus } from '@/enums'
import { DuplicateHomepageBookListException } from '@/modules/exception'
import { OperationLogService } from '@/modules/system'
import {
  CreateHomepageDto,
  DeleteHomepageDto,
  getHomepageDto,
  HomepageDto,
  UpdateHomepageDto,
} from '../../books/dto'
import { BookListService } from '../services'
import { HomepageService } from '../services/homepage.service'

@ApiTags('Homepage')
@ApiExtraModels(HomepageDto)
@Controller('v1/admin/homepage')
export class HomepageAdminController {
  constructor(
    private readonly homepageService: HomepageService,
    private readonly logService: OperationLogService,
    private readonly booklistService: BookListService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @AdminAuth()
  @ApiOperation({ summary: 'create homepage' })
  @ApiBaseResult(HomepageDto, 201)
  @Post()
  async createHomepage(
    @Body() data: CreateHomepageDto,
    @CurrentAdmin() user: any
  ): Promise<HomepageDto> {
    const homepage = await this.homepageService.createHomepage(data)
    await this.logService.createLog({
      operation: `新增推薦位“${homepage.name.zh_HK}”`,
      metaData: { homepageId: homepage.id },
      user,
    })
    await this.homepageService.clearCustomCache()
    return getHomepageDto(homepage)
  }

  @AdminAuth()
  @ApiOperation({ summary: 'update homepages' })
  @ApiBaseResult(HomepageDto, 200)
  @Patch()
  async updateHomepage(
    @Body() body: UpdateHomepageDto,
    @CurrentAdmin() user: any
  ): Promise<HomepageDto[]> {
    const booklistIds = body.items
      .map((item) => item.data.bookListId)
      .filter((id) => !!id)
    const editBooklistids = [...new Set(booklistIds)]

    if (editBooklistids.length !== booklistIds.length) {
      throw new DuplicateHomepageBookListException()
    }

    const booklists = await this.booklistService.findBookLists(
      {
        ids: editBooklistids,
      },
      ['homepage']
    )

    const editHompageIds = body.items.map((item) => item.id)
    const homepageIds = booklists
      .map((item) => item.homepage?.id)
      .filter((id) => !!id && !editHompageIds.includes(id))

    if (homepageIds.length) {
      throw new DuplicateHomepageBookListException()
    }

    const homepages = await Promise.all(
      body.items.map(async ({ id, data }) => {
        const homepage = await this.homepageService.updateHomepage(id, data)
        if (data.status) {
          const operation =
            data.status === EListStatus.OFFLINE ? '下架推薦位' : '上架推薦位'
          await this.logService.createLog({
            operation: `${operation}${homepage.name.zh_HK}`,
            metaData: { homepageId: id },
            user,
          })
          return homepage
        }
      })
    )

    if (
      homepages.map((item) => item.id).includes(1) ||
      homepages.filter(
        (item) =>
          item.isPermanent &&
          ['大專院校', '幼稚園', '中學', '小學'].includes(item.name.zh_HK)
      ).length
    ) {
      await this.homepageService.clearRecommendCache()
    }
    if (body.items.map((item) => item.id).filter((id) => id > 3).length) {
      await this.homepageService.clearCustomCache()
    }
    return homepages.map((item) => getHomepageDto(item))
  }

  @AdminAuth()
  @ApiOperation({ summary: 'get homepage' })
  @ApiListResult(HomepageDto, 200)
  @Get('today')
  async getTodayHomepage(): Promise<any> {
    const homepage = await this.homepageService.getTodayHomepageWithRelation()
    const [defaultHomepage] = homepage
    return homepage.map((item) => ({
      ...getHomepageDto(item),
      defaultName: defaultHomepage.name,
    }))
  }

  @AdminAuth()
  @ApiOperation({ summary: 'get homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get(':id')
  async getHomepage(@Param('id', ParseIntPipe) id: number): Promise<any> {
    let homepage = await this.homepageService.getHomepageWithRelation(id)
    let defaultHomepage
    if (homepage.parentId === 1) {
      defaultHomepage = await this.homepageService.getHomepage(1)
    } else if (homepage.id === 1) {
      defaultHomepage = homepage
    }
    if (homepage?.books) {
      homepage = await this.homepageService.sortHomepageBooks(homepage)
    }

    if (homepage?.booklist?.books) {
      homepage = {
        ...homepage,
        booklist: await this.booklistService.sortBooklistBooks({
          ...homepage.booklist,
          books: homepage.booklist.books,
        }),
      }
    }

    return {
      ...getHomepageDto(homepage),
      defaultName: defaultHomepage?.name,
    }
  }

  @AdminAuth()
  @ApiOperation({ summary: 'list homepage' })
  @ApiBaseResult(HomepageDto, 200)
  @Get()
  async listHomepage(@Query() query: PageRequest) {
    const data = await this.homepageService.listHomepage(query)
    return getPageResponse<HomepageDto, Homepage>(
      data as unknown as Partial<PageResponse<HomepageDto, Homepage>>,
      data.items,
      getHomepageDto
    )
  }

  @AdminAuth()
  @ApiOperation({ summary: 'delete homepage' })
  @ApiBaseResult(BooleanResponse, 200)
  @Delete()
  async deleteHomepage(@Body() query: DeleteHomepageDto, @CurrentAdmin() user: any) {
    let ids = query.ids?.length ? query.ids : []
    if (ids.length <= 0) {
      ids = await this.homepageService.listAllHomepageIds()
      if (query.exceptions?.length) {
        ids = ids.filter((id) => !query.exceptions.includes(id))
      }
    }
    const homepages = await this.homepageService.getHomepages(ids)
    await this.homepageService.deleteHomepage(ids)
    // await this.cacheManager.del(getSubscriptionHomepageKey())
    await this.logService.createLog({
      operation: `${homepages.length > 3 ? `批量删除` : '删除推薦位'}${homepages
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${homepages.length > 3 ? `等${homepages.length}個推薦位` : ''}`,
      metaData: { homepageId: ids },
      user,
    })
    return { status: true }
  }
}
