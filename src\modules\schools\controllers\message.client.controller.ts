import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseIntPipe,
  <PERSON>,
  Query,
} from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiPageResult,
  ClientAuth,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentUser,
  ELocaleType,
  PageRequest,
} from '@/common'
import { Message } from '@/entities'
import { IUserRepo } from '@/modules/shared/interfaces'
import { MessageDto, ReadMessageDto } from '../dto/message.dto'
import { MessageService } from '../services/index2'

@ApiTags('Messages')
@Controller('v1/client/message')
export class MessageClientController {
  constructor(
    private readonly messageService: MessageService,
    private readonly userRepositories: IUserRepo
  ) {}

  @Get('count')
  @ApiOperation({ summary: 'list message' })
  @ApiPageResult(Message, 200)
  @CurrentLocaleHeader()
  @ClientAuth()
  async getMessageCount(@CurrentUser() user: any) {
    const client = await this.userRepositories.findOne({
      where: { id: user.userId },
      relations: ['userClass'],
    })
    return this.messageService.getUnReadCount(user.schoolId, client)
  }

  @Get(':id')
  @ApiOperation({ summary: 'get message' })
  @ApiPageResult(Message, 200)
  @CurrentLocaleHeader()
  @ClientAuth()
  async getMessage(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
    @CurrentLocale() local = ELocaleType.ZH_HK
  ) {
    const message = await this.messageService.getMessage(id)
    if (message && (!message.toUserIds || message.toUserIds?.includes(user.userId))) {
      return message
    }
    throw new NotFoundException('message not found')
  }

  @Get()
  @ApiOperation({ summary: 'list message' })
  @ApiPageResult(Message, 200)
  @CurrentLocaleHeader()
  @ClientAuth()
  async listMessage(
    @Query() query: MessageDto,
    @CurrentUser() user: any,
    @CurrentLocale() local = ELocaleType.ZH_HK
  ) {
    const client = await this.userRepositories.findOne({
      where: { id: user.userId },
      relations: ['userClass'],
    })
    return this.messageService.listClientMessage(user.schoolId, client, query, local)
  }

  @Patch('read')
  @ApiOperation({ summary: 'read message' })
  @ApiPageResult(Message, 200)
  @ClientAuth()
  async readMessage(@CurrentUser() user: any, @Body() data: ReadMessageDto) {
    return this.messageService.readMessage(user.schoolId, user.userId, data.id)
  }
}
