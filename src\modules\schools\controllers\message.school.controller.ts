import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import R from 'ramda'
import { WebsocketGateway } from 'src/modules/websocket'
import {
  ApiBaseResult,
  ApiPageResult,
  AuthSchema,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  SchoolAdminAuth,
} from '@/common'
import { OneSignalService } from '@/common/services/oneSignal.service'
import { Message, UserClass } from '@/entities'
import { EUserType } from '@/enums'
import { IUserRepo } from '@/modules/shared/interfaces'
import { QueryLogDto } from '../dto'
import { CreateMessageDto } from '../dto/message.dto'
import { UserClassService } from '../services/index1'
import { MessageService } from '../services/index2'

@ApiTags('Messages')
@Controller('v1/school-admin/message')
export class MessageSchoolController {
  constructor(
    private readonly messageService: MessageService,
    private readonly webSocketGateway: WebsocketGateway,
    private readonly userRepository: IUserRepo,
    private readonly oneSignalService: OneSignalService,
    private readonly userClassService: UserClassService
  ) {}

  @Post()
  @ApiOperation({ summary: 'create a  message' })
  @SchoolAdminAuth()
  @ApiBaseResult(Message, 201)
  async createMessage(@Body() body: CreateMessageDto, @CurrentSchoolAdmin() admin: any) {
    const message = await this.messageService.createMessage(body, admin.schoolId, admin)
    let classes: UserClass[] = []

    if (body.userType === EUserType.ALL) {
      classes = await this.userClassService.listAllClass(admin.schoolId)
      await this.webSocketGateway.sendNotification(
        admin.schoolId,
        undefined,
        undefined,
        AuthSchema.CLIENT,
        {
          title: message.title,
          message: message.message,
          createdAt: message.createdAt,
        }
      )
    } else if (body.userType === EUserType.TEACHER) {
      await this.webSocketGateway.sendNotification(
        admin.schoolId,
        undefined,
        undefined,
        AuthSchema.CLIENT,
        {
          title: message.title,
          message: message.message,
          createdAt: message.createdAt,
        }
      )
    } else if (!body.classIds?.length) {
      classes = await this.userClassService.searchClass(admin.schoolId, {
        gradeIds: body.gradeIds,
      })
    } else {
      classes = await this.userClassService.getClassByIds(body.classIds)
    }

    console.log(classes)
    for (const c of classes)
    {await this.webSocketGateway.sendNotification(
      admin.schoolId,
      c.gradeId,
      c.id,
      AuthSchema.CLIENT,
      {
        title: message.title,
        message: message.message,
        createdAt: message.createdAt,
      }
    )}

    const options: any = {}
    if (body.userType !== EUserType.ALL) {
      options.type = body.userType
      if (body.classIds?.length) {
        options.classId = body.classIds
      } else if (body.gradeIds?.length) {
        options.grade = body.gradeIds
      }
    }

    const users = await this.userRepository.searchUserIds(admin.schoolId, options)

    // const users = await this.userRepository.getUsers(admin.schoolId)
    const playerIds = await users
      .filter((user) => !R.isNil(user.playerId))
      .map((item) => item.playerId)

    if (playerIds.length)
    {await this.oneSignalService.sendNotification('common.oneSignal', {
      playerIds: playerIds,
      headings: {
        en: message.title.en_uk,
        'zh-Hant': message.title.zh_HK,
        'zh-Hans': message.title.zh_cn,
      },
      contents: {
        en: message.message.en_uk,
        'zh-Hant': message.message.zh_HK,
        'zh-Hans': message.message.zh_cn,
      },
      data: {
        id: message.id,
      },
    })}

    return message
  }

  @Get()
  @ApiOperation({ summary: 'list message' })
  @ApiPageResult(Message, 200)
  @CurrentLocaleHeader()
  @SchoolAdminAuth()
  async listMessage(
    @Query() query: QueryLogDto,
    @CurrentSchoolAdmin() admin: any,
    @CurrentLocale() local = ELocaleType.ZH_HK
  ) {
    return this.messageService.listMessage(admin.schoolId, query)
  }
}
