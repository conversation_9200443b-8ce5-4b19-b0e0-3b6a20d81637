import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  AuthSchema,
  BooleanResponse,
  CurrentUser,
} from '@/common'
import { Notification } from '@/entities'
import { ListNotificationRequest, MarkNotificationReadRequest } from '../dto'
import { NotificationService } from '../services'

@ApiTags('Notifications')
@Controller('v1/admin/notifications')
@ApiExtraModels(Notification, BooleanResponse)
export class NotificationAdminController {
  constructor(private readonly notificationService: NotificationService) {}

  @ApiOperation({ summary: 'List notifications' })
  @ApiPageResult(Notification, 200)
  @AdminAuth()
  @Get()
  async ListNotifications(
    @Query() query: ListNotificationRequest,
    @CurrentUser('userId') userId: number
  ) {
    return this.notificationService.listNotifications(
      Number(userId),
      AuthSchema.ADMIN,
      query
    )
  }

  @ApiOperation({ summary: 'Get notifications' })
  @ApiBaseResult(Notification, 200)
  @AdminAuth()
  @Get('/:id')
  async getNotification(@Param('id') id: number) {
    return this.notificationService.getNotification(id)
  }

  @ApiOperation({ summary: 'Mark notifications read' })
  @ApiListResult(Notification, 201)
  @AdminAuth()
  @Post('/mark-read')
  async markNotificationRead(@Body() body: MarkNotificationReadRequest) {
    return this.notificationService.markNotificationRead(body)
  }

  @ApiOperation({ summary: 'Manual send notification' })
  @ApiBaseResult(BooleanResponse, 201)
  @AdminAuth()
  @Post('/debug/:schoolId')
  async sendManualNotification(@Param('schoolId') schoolId: string) {
    return this.notificationService.sendManualNotification(Number(schoolId))
  }
}
