import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  AuthSchema,
  CurrentUser,
  SchoolAdminAuth,
} from '@/common'
import { Notification } from '@/entities'
import { ListNotificationRequest, MarkNotificationReadRequest } from '../dto'
import { NotificationService } from '../services'

@ApiTags('Notifications')
@Controller('v1/school-admin/notifications')
export class NotificationSchoolController {
  constructor(private readonly notificationService: NotificationService) {}

  @ApiOperation({ summary: 'List notifications' })
  @ApiPageResult(Notification, 200)
  @SchoolAdminAuth()
  @Get()
  async ListNotifications(
    @Query() query: ListNotificationRequest,
    @CurrentUser('userId') userId: number
  ) {
    return this.notificationService.listNotifications(
      Number(userId),
      AuthSchema.SCHOOL_ADMIN,
      query
    )
  }

  @ApiOperation({ summary: 'Get notifications' })
  @ApiBaseResult(Notification, 200)
  @SchoolAdminAuth()
  @Get('/:id')
  async getNotification(@Param('id') id: number) {
    return this.notificationService.getNotification(id)
  }

  @ApiOperation({ summary: 'Mark notifications read' })
  @ApiListResult(Notification, 201)
  @SchoolAdminAuth()
  @Post('/mark-read')
  async markNotificationRead(@Body() body: MarkNotificationReadRequest) {
    return this.notificationService.markNotificationRead(body)
  }
}
