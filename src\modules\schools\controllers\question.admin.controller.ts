import { Body, Controller, Param, ParseInt<PERSON>ipe, Patch, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AdminAuth, ApiBaseResult } from '@/common'
import { CreateQuestionDto, getQuestionDto, QuestionDto } from '../dto'
import { QuestionService } from '../services/question.service'

@ApiTags('Science Room')
@ApiExtraModels(QuestionDto)
@Controller('v1/admin/questions')
export class QuestionAdminController {
  constructor(private readonly questionService: QuestionService) {}

  @ApiOperation({ summary: '创建题目' })
  @ApiBaseResult(QuestionDto, 200)
  @AdminAuth()
  @Post()
  async createQuestion(@Body() data: CreateQuestionDto) {
    // const question = await this.questionService.create(data)
    // return getQuestionDto(question)
  }

  @ApiOperation({ summary: '更新题目' })
  @AdminAuth()
  @Patch(':id')
  async updateQuestion(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateQuestionDto
  ) {
    // await this.questionService.update(id, data)
  }
}
