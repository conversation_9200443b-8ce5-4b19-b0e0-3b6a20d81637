import { Body, Controller, Get, Patch, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ClientAuth,
  CurrentPlatform,
  CurrentUser,
  EPlatform,
} from '@/common'
import { EBookVersion } from '@/enums'
import { IReadRecordService } from '@/modules/shared/interfaces'
import {
  EndReadingDto,
  ReadingDto,
  StartReadingDto,
  UserBalanceDto,
  UseReadingTimeDto,
} from '../dto'
import { ReadingReflectionService, SchoolService, UserBalanceService } from '../services'
import { ReadingTimeService } from '../services/index1'
import { ReadingMessageService } from '../services/index2'

@ApiTags('Reading-time')
@ApiExtraModels(ReadingDto)
@Controller('v1/client/reading-time')
export class ReadingTimeClientController {
  constructor(
    private readonly readingTimeService: ReadingTimeService,
    private readonly userBalanceService: UserBalanceService,
    private readonly readRecordService: IReadRecordService,
    private readonly schoolService: SchoolService,
    private readonly message: ReadingMessageService,
    private readonly readingReflectionService: ReadingReflectionService
  ) {}

  @ApiOperation({ summary: 'start reading' })
  @ApiBaseResult(ReadingDto, 200)
  @Post('start')
  @ClientAuth()
  async startReading(
    @CurrentUser('userId') userId: number,
    @Body() data: StartReadingDto
  ) {
    return this.readingTimeService.startReading(userId, data.bookId)
  }

  @ApiOperation({ summary: 'report reading time ' })
  @ApiBaseResult(ReadingDto, 200)
  @Patch()
  @ClientAuth()
  async consumeReadingTime(
    @CurrentUser() user: any,
    @CurrentPlatform() platform: EPlatform,
    @Body() data: UseReadingTimeDto
  ) {
    const res = await this.readingTimeService.consumeReadingTime(
      user.userId,
      data.time,
      data.sessionId
    )

    await this.message.littleReadingTime({
      ...res,
      platform,
      userId: user.userId,
      schoolId: user.schoolId,
    })
    return res
  }

  @ApiOperation({ summary: 'end reading' })
  @Patch('end')
  @ClientAuth()
  async endReading(
    @CurrentUser('userId') userId: number,
    @CurrentUser('schoolId') schoolId: number,
    @Body() data: EndReadingDto
  ) {
    const school = await this.schoolService.getSchoolWithCache(schoolId)
    return this.readingTimeService.endReading(
      school.isSharingTime,
      userId,
      data.sessionId
    )
  }

  @ApiOperation({ summary: 'get user reading time' })
  @Get()
  @ClientAuth()
  @ApiBaseResult(UserBalanceDto, 200)
  async getReadingTime(@CurrentUser() user: any) {
    const reading = await this.userBalanceService.getUserBalance(user.userId)
    const count = await this.readRecordService.getUserReadingBookCount(user.userId)
    const readingReflectionCount =
      await this.readingReflectionService.getReadingReflectionBookCount(
        user.userId,
        EBookVersion.SUBSCRIPTION
      )

    const school = await this.schoolService.findOne({
      where: { id: user.schoolId },
      relations: ['balance'],
    })
    return {
      isSharingTime: school.isSharingTime ?? false,
      schoolTotalBoughtQuota: school.balance?.totalBoughtQuota ?? 0,
      schoolUsedQuota: school.balance?.usedQuota ?? 0,
      totalQuota: Number(reading?.totalQuota ?? 0),
      usedQuota: Number(reading?.usedQuota ?? 0),
      totalUsedQuota: Number(reading?.totalUsedQuota ?? 0),
      count,
      readingReflectionCount,
    }
  }
}
