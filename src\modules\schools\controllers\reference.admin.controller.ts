import { <PERSON>, Get, Param, ParseIntPipe, Query, Res } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { Response } from 'express'
import { AdminAuth, CurrentLocale, CurrentLocaleHeader, ELocaleType } from '@/common'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { ReferenceService } from '../services'

@Controller('v1/admin/reference')
export class ReferenceAdminController {
  constructor(private readonly referenceService: ReferenceService) {}

  @AdminAuth()
  @Get('/all-publishers')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'Export for all publishers' })
  async exportForAllPublishers(
    @Query() query: QueryReadingTimeDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response
  ) {
    const file = await this.referenceService.exportForPublishers(query, local)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader('Content-Disposition', 'attachment; filename=publishers.xlsx')

    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/publishers/:id')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'Export for a publisher' })
  async exportForPublisher(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: QueryReadingTimeDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response
  ) {
    const file = await this.referenceService.exportForPublishers(query, local, id)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader('Content-Disposition', 'attachment; filename=publishers.xlsx')

    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('/publishers/:id/schools')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'Export school data for a publisher' })
  async exportSchoolsForPublisher(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: QueryReadingTimeDto,
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response
  ) {
    const file = await this.referenceService.exportForSchool(id, query, local)
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader('Content-Disposition', 'attachment; filename=schools.xlsx')

    res.send(Buffer.from(file))
  }
}
