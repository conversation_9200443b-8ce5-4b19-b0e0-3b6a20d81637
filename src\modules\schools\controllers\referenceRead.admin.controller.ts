import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import R from 'ramda'
import { AdminAuth, ApiListResult } from '@/common'
import { PAGE_SIZE } from '@/modules/constants'
import { IReferenceReadService } from '@/modules/shared/interfaces'
import { LimitDto, QueryReadingTimeDto, ReferenceTopBookDto } from '../../books/dto'
import { QuerySchoolReferenceBookStatisticDto } from '../../books/dto/referenceBook.dto'
import { GradeService } from '../services'

@Controller('v1/admin/reference-read/:schoolId')
export class ReferenceReadAdminController {
  constructor(
    private readonly referenceReadService: IReferenceReadService,
    private readonly gradeService: GradeService
  ) {}

  @ApiOperation({ summary: 'get top n books in reference' })
  @Get('top-n-books')
  @ApiListResult(ReferenceTopBookDto, 200)
  @AdminAuth()
  async getTopNBooks(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: LimitDto
  ) {
    const { limit = PAGE_SIZE } = query
    return this.referenceReadService.getTopBooks(schoolId, limit)
  }

  @ApiOperation({ summary: 'distribution of readers in reference' })
  @Get('users-count')
  @AdminAuth()
  async getUserCount(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryReadingTimeDto
  ) {
    return this.referenceReadService.groupByClass(schoolId, query)
  }

  @ApiOperation({ summary: '阅读详细-用户' })
  @Get('users')
  @AdminAuth()
  async getStatistic(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QuerySchoolReferenceBookStatisticDto
  ) {
    const data = await this.referenceReadService.statistic(schoolId, query)
    const grades = await this.gradeService.listGrades(
      data.items.map((item) => item.gradeId)
    )

    return {
      ...data,
      items: data.items.map((item) => ({
        ...R.pick(['displayName', 'class', 'grade', 'bookName'], item),
        totalReadCount: Number(item.totalReadCount),
        total: Number(item.total),
        totalBookCount: Number(item.totalBookCount || 0),
        grade: grades.find((grade) => grade.id === item.gradeId)?.grade,
      })),
    }
  }
}
