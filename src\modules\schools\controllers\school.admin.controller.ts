import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {Body, Controller, Delete, Get, Header, Inject, Param, ParseIntPipe, Patch, Post, Query, Res,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { InjectDataSource } from '@nestjs/typeorm'
import { Cache } from 'cache-manager'
import { Response } from 'express'
import R from 'ramda'
import { DataSource } from 'typeorm'
import {AdminAuth, ApiBaseResult, ApiPageResult, BooleanResponse, CurrentAdmin, CurrentLocale,
  CurrentLocaleHeader, ELocaleType, PageRequest, RedisService, RedlockService,
} from '@/common'
import { ETaskType, TaskService } from '@/common/components/task'
import { School, SchoolBalance, ScienceContracts } from '@/entities'
import {
  EBookVersion,
  EGrade,
  EListStatus,
  ESchoolStatus,
  EUserType,
  SchoolType,
} from '@/enums'
import { CreateSchoolHomepageDto } from '@/modules/books/dto'
import {
  CREATE_SCHOOL_KEY,
  getSubscriptionHomepageKey,
  getSubscriptionNewestKey,
  getSubscriptionRecommendKey,
  LOCK_BALANCE_REDIS_SCHOOL_KEY,
} from '@/modules/constants'
import { IAssistantContractsService, IBookLevelService, IBookService,IInitLeaderBoardService, IUserRepo } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system'
import { GRADES_LOCALE } from '../constants'
import {
  CreateScienceContractDto,
  DeleteSchoolDto,
  getSchoolDto,
  ListSchoolDto,
  ModifySchoolDto,
  ModifySchoolStatusDto,
  SchoolDto,
  SchoolGradeDto,
  SchoolResponse,
  SetShareReadingDto,
} from '../dto'
import {
  ContractService,
  SchoolBalanceService,
  SchoolHomepageService,
  SchoolService,
  UserBalanceService,
} from '../services'
import { UserClassService } from '../services/userClass.service'

@ApiTags('School')
@ApiExtraModels(SchoolResponse, School, SchoolGradeDto)
@Controller('v1/admin/schools')
export class SchoolAdminController {
  constructor(
    private readonly schoolService: SchoolService,
    private readonly schoolBalanceService: SchoolBalanceService,
    private readonly logService: OperationLogService,
    private readonly redisService: RedisService,
    private readonly redlockService: RedlockService,
    private readonly userRepository: IUserRepo,
    private readonly userBalanceService: UserBalanceService,
    private readonly bookLevelService: IBookLevelService,
    private readonly schoolHomepageService: SchoolHomepageService,
    private readonly leaderBoardService: IInitLeaderBoardService,
    private readonly contractService: ContractService,
    private readonly bookService: IBookService,
    private readonly userClassService: UserClassService,
    private readonly taskService: TaskService,
    private readonly assistantContractsService: IAssistantContractsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  @AdminAuth()
  @ApiBaseResult(SchoolGradeDto, 200)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'list all grades' })
  @Get('grades')
  getAllGrades(@CurrentLocale() local = ELocaleType.ZH_HK) {
    return {
      grades: Object.values(EGrade).map((v) => ({
        code: v,
        text: GRADES_LOCALE[local][v],
      })),
    }
  }

  @AdminAuth()
  @ApiBaseResult(School, 201, 'Create school')
  @ApiOperation({ summary: 'Create school' })
  @Post()
  async createSchool(@Body() body: ModifySchoolDto, @CurrentAdmin() user: any) {
    return this.redlockService.lockWrapper(
      `${CREATE_SCHOOL_KEY}${JSON.stringify(body.name)}`,
      10 * 1000,
      async () => {
        const school = await this.schoolService.createSchool(body)

        const {
          staffLevelIds = [],
          isAllLevelForStaff = false,
          isAllLevelForStudent = false,
          studentLevelIds = [],
          id,
        } = school

        const bookIds = await this.bookService.getBookIdsForStudent({
          isAllLevelForStudent,
          studentLevelIds,
          id,
        })

        const bookIdsForStaff = await this.bookService.getBookIdsForTeacher({
          isAllLevelForStaff,
          staffLevelIds,
        })

        await this.leaderBoardService.initReadingTimeWhenCreateSchool(
          school.id,
          bookIds,
          bookIdsForStaff
        )

        const schoolHomepage = {
          name: { zh_HK: '今日推薦', en_uk: 'Recommend', zh_cn: '今日推荐' },
          status: EListStatus.ONLINE,
        } as CreateSchoolHomepageDto
        await this.schoolHomepageService.createHomepage(
          { ...schoolHomepage, version: EBookVersion.SUBSCRIPTION },
          school
        )
        await this.schoolHomepageService.createHomepage(
          { ...schoolHomepage, version: EBookVersion.REFERENCE },
          school
        )
        await this.logService.createLog({
          operation: `添加學校“${school.name.zh_HK}”`,
          metaData: { schoolId: school.id },
          user,
        })
        return getSchoolDto(school)
      }
    )
  }

  @AdminAuth()
  // @ApiBaseResult(School, 200, 'Get school type')
  @ApiOperation({ summary: 'Get school type' })
  @Get('type')
  schoolType() {
    return Object.values(SchoolType)
  }

  @AdminAuth()
  @Get('export-schools')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Schools.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export schools' })
  async exportSchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() user: any
  ) {
    const file = await this.schoolService.exportSchoolV2(local, user)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @ApiPageResult(ScienceContracts, 200)
  @ApiOperation({ summary: '获取科学活动室合约列表' })
  @Get(':id/science-room-contracts')
  async getScienceContracts(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: PageRequest
  ) {
    return this.schoolService.getScienceContracts(id, query)
  }

  @AdminAuth()
  @ApiPageResult(School, 200, 'List schools')
  @ApiOperation({ summary: 'List schools' })
  @Get('simple')
  async listSchoolWithName(@Query() query: ListSchoolDto) {
    const data = await this.schoolService.listSchools(query)
    return { ...data, data: data.items.map((item) => ({ id: item.id, name: item.name })) }
  }

  @AdminAuth()
  @ApiBaseResult(School, 200, 'Get schools')
  @ApiOperation({ summary: 'Get schools' })
  @Get(':id')
  async getSchool(@Param('id', ParseIntPipe) id: number) {
    const school = (await this.schoolService.findOne({ where: { id  } })) as any

    const contract = await this.schoolService.getLastScienceContract(id)
    if (school.staffLevelIds?.length) {
      school.staffLevels = await this.bookLevelService.getByIds(school.staffLevelIds)
    }
    if (school.studentLevelIds?.length) {
      school.studentLevels = await this.bookLevelService.getByIds(school.studentLevelIds)
    }

    const hasDraftContract = await this.contractService.hasDraftContract(id)
    const hasContract = await this.contractService.hasContract(id)

    // assistant contract
    const assistantContract = await this.assistantContractsService.getLastContract(id)
    const hasAssistantContract = await this.assistantContractsService.hasContract(id)

    return {
      ...getSchoolDto(school),
      hasDraftContract,
      hasContract,
      gradeCodes: contract?.gradeCodes,
      scienceRoomContract: contract?.contractNo,
      hasAssistantContract,
      assistantContract: assistantContract?.contractNo,
      assistantId: assistantContract?.assistant?.assistantId,
    }
  }

  @AdminAuth()
  @ApiPageResult(School, 200, 'List schools')
  @ApiOperation({ summary: 'List schools' })
  @Get()
  async listSchool(@Query() query: ListSchoolDto) {
    return this.schoolService.listSchools(query)
  }

  @AdminAuth()
  @ApiOperation({ summary: '发布科学活动室合约' })
  @ApiBaseResult(BooleanResponse, 200)
  @Post(':id/science-room-contracts')
  async addContract(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateScienceContractDto,
    @CurrentAdmin() admin: any
  ) {
    await this.schoolService.createScienceContracts(id, data, admin)
    return { status: true }
  }

  @AdminAuth()
  @ApiBaseResult(SchoolDto, 200)
  @ApiOperation({ summary: 'update school balance' })
  @Patch(':id/balance/:balance')
  async updateSchoolBalance(
    @Param('id', ParseIntPipe) id: number,
    @Param('balance', ParseIntPipe) balance: number,
    @CurrentAdmin() admin: any
  ) {
    const school = await this.schoolService.findOne({ where: { id  } })
    const res = await this.schoolBalanceService.updateSchoolBalance(id, balance * 3600)

    await this.logService.createLog({
      operation: `${school.name.zh_HK} 
      学校更新前的余额${res.beforeBalance}
      学校更新后的余额${res.afterBalance}`,
      metaData: { schoolId: school.id },
      user: admin,
    })
  }

  @AdminAuth()
  @ApiBaseResult(SchoolDto, 200)
  @ApiOperation({ summary: 'reading time setting' })
  @Patch(':id/sharing')
  async setShareReading(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: SetShareReadingDto,
    @CurrentAdmin() admin: any
  ) {
    const school = await this.schoolService.findOne({ where: { id  } })
    if (school.isSharingTime === data.isSharingTime) {return getSchoolDto(school)}

    await this.redlockService.lockWrapper(
      `${LOCK_BALANCE_REDIS_SCHOOL_KEY}${id}`,
      5 * 60,
      async () => {
        await this.dataSource.transaction(async (manager) => {
          await manager.update(
            School,
            { id },
            {
              isSharingTime: data.isSharingTime,
            }
          )

          await this.schoolService.clearSchoolCache(id)
          const balance = await this.schoolBalanceService.getSchoolBalance(id)

          await manager.update(
            SchoolBalance,
            { id: balance.id },
            {
              totalDistributionQuota: data.isSharingTime
                ? 0
                : balance.totalBoughtQuota - balance.usedQuota,
              distributionQuota: 0,
            }
          )

          const users = await this.userRepository.searchUserIds(id)
          const userIds = users.map((item) => item.id)
          for (const userId of userIds) {
            // 回收用戶自行分配的時數
            await this.userBalanceService.revokeLeftReadingTime(
              data.isSharingTime,
              userId,
              manager
            )
          }
        })
      }
    )

    await this.logService.createLog({
      operation: `${school.name.zh_HK}切換至${
        data.isSharingTime ? '共享時數' : '自行分配時數'
      }`,
      metaData: { schoolId: school.id },
      user: admin,
    })
  }

  @AdminAuth()
  @ApiBaseResult(School, 200, 'Patch schools')
  @ApiOperation({ summary: 'Patch schools' })
  @Patch(':id')
  async patchSchool(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: ModifySchoolDto
  ) {
    const s = await this.schoolService.findOne({ where: { id  } })
    const school = await this.schoolService.updateSchool(id, body)

    const isTeachLevelChanged =
      s.isAllLevelForStaff !== school.isAllLevelForStaff ||
      R.difference(s.staffLevelIds ?? [], school.staffLevelIds ?? []).length > 0 ||
      R.difference(school.staffLevelIds ?? [], s.staffLevelIds ?? []).length > 0

    const isStudentLevelChanged =
      s.isAllLevelForStudent !== school.isAllLevelForStudent ||
      R.difference(s.studentLevelIds ?? [], school.studentLevelIds ?? []).length > 0 ||
      R.difference(school.studentLevelIds ?? [], s.studentLevelIds ?? []).length > 0

    if (isTeachLevelChanged)
    {await this.schoolHomepageService.deleteHomepageForLevel(id, EUserType.TEACHER)}

    if (isStudentLevelChanged)
    {await this.schoolHomepageService.deleteHomepageForLevel(id, EUserType.STUDENT)}

    if (isTeachLevelChanged) {
      await this.cacheManager.del(getSubscriptionHomepageKey(id, EUserType.TEACHER))
      await this.cacheManager.del(getSubscriptionNewestKey(id, EUserType.TEACHER))
      await this.cacheManager.del(getSubscriptionRecommendKey(id, EUserType.TEACHER))
    }

    if (isStudentLevelChanged) {
      await this.cacheManager.del(getSubscriptionHomepageKey(id, EUserType.STUDENT))
      await this.cacheManager.del(getSubscriptionNewestKey(id, EUserType.STUDENT))
      await this.clearStudentCache(id)
    }

    if (s.type !== school.type) {
      await this.cacheManager.del(getSubscriptionRecommendKey(id, EUserType.TEACHER))
      await this.clearStudentCache(id)
    }

    if (isTeachLevelChanged || isStudentLevelChanged) {
      await this.taskService.deliver(ETaskType.UPDATE_SCHOOL_LEVEL, { schoolId: id })
    }
    return getSchoolDto(school)
  }

  @AdminAuth()
  @ApiBaseResult(BooleanResponse, 200, 'Patch school status')
  @ApiOperation({ summary: 'Patch school status' })
  @Patch()
  async patchSchoolStatus(
    @Body() data: ModifySchoolStatusDto,
    @CurrentAdmin() user: any
  ) {
    let schoolIds = data.schoolIds?.length ? data.schoolIds : []
    if (schoolIds.length <= 0) {
      const schools = await this.schoolService.searchSchoolWithFilters({
        name: data.name,
        filterStatus: data.filterStatus,
        buyContracts: data.buyContracts,
        region: data.region,
      })
      schoolIds = schools.map((item) => item.id)
      if (data.exceptions?.length) {
        schoolIds = schoolIds.filter((id) => !data.exceptions.includes(id))
      }
    }
    const schools = await this.schoolService.findSchools(schoolIds)
    await this.schoolService.updateSchoolStatus(schoolIds, data.status)
    const operation =
      data.status === ESchoolStatus.INACTIVE
        ? schools.length > 3
          ? `批量失效`
          : '失效學校'
        : schools.length > 3
          ? `批量恢復`
          : '恢复學校'
    await this.logService.createLog({
      operation: `${operation}${schools
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${schools.length > 3 ? `等${schools.length}所學校` : ''}`,
      metaData: { schoolIds },
      user,
    })
    return { status: true }
  }

  @AdminAuth()
  @ApiBaseResult(School, 200, 'Delete school')
  @ApiOperation({ summary: 'Delete school' })
  @Delete()
  async delete(@Body() data: DeleteSchoolDto, @CurrentAdmin() user: any) {
    let schoolIds = data.ids?.length ? data.ids : []
    if (schoolIds.length <= 0) {
      const schools = await this.schoolService.searchSchoolWithFilters({
        name: data.name,
        filterStatus: data.filterStatus,
        buyContracts: data.buyContracts,
        region: data.region,
      })
      schoolIds = schools.map((item) => item.id)
      if (data.exceptions?.length) {
        schoolIds = schoolIds.filter((id) => !data.exceptions.includes(id))
      }
    }

    const homepages = await Promise.all(
      schoolIds.map((id) => this.schoolHomepageService.getDefaultHomepage(id))
    )

    await this.schoolHomepageService.deleteDefaultHomepage(
      homepages.map((item) => item.id)
    )

    const schools = await this.schoolService.removeSchool(schoolIds)

    await this.logService.createLog({
      operation: `${schools.length > 3 ? `批量删除` : '删除學校'}${schools
        .slice(0, 3)
        .map((item) => `“${item.name.zh_HK}”`)
        .join(',')} ${schools.length > 3 ? `等${schools.length}所學校` : ''}`,
      metaData: { schoolIds },
      user,
    })
  }

  private async clearStudentCache(schoolId: number) {
    const userClass = await this.userClassService.listAllClass(schoolId)
    await Promise.all(
      userClass.map((item) =>
        this.cacheManager.del(
          getSubscriptionRecommendKey(schoolId, EUserType.STUDENT, item.gradeId, item.id)
        )
      )
    )
  }
}
