import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  CurrentSchoolAdmin,
} from '@/common'
import { PAGE_SIZE } from '@/modules/constants'
import {
  AdminQueryReadingTimeDto,
  AdminQuerySchoolDto,
  AdminQueryStudentReadingTimeDto,
  BookReadingTimeDto,
  ListAdminGradeDto,
  SchoolReadingTimeCountDto,
  SchoolReadingUserCountDto,
  SchoolUserCountByDateDto,
  StudentReadingTimeDto,
  Top10BookOfReadingTime,
} from '../../books/dto'
import { GradeService } from '../services'
import { HomepageService } from '../services/homepage.service'
import { SchoolDashboardService, UserClassService } from '../services/index1'

@ApiTags('school-dashboard')
@Controller('v1/admin/school-reading-time')
export class SchoolReadingTimeAdminController {
  constructor(
    private readonly userClassService: UserClassService,
    private readonly gradeService: GradeService,
    private readonly homepageService: HomepageService,
    private readonly schoolDashboardService: SchoolDashboardService
  ) {}

  @ApiOperation({ summary: 'get reading user count by day' })
  @ApiListResult(SchoolUserCountByDateDto, 200)
  @AdminAuth()
  @Get('user-count-in-day')
  async readingUserCountByDay(
    @CurrentSchoolAdmin() user: any,
    @Query() query: AdminQueryReadingTimeDto
  ) {
    return this.schoolDashboardService.readingUserCountAndTimeByDay(
      { schoolId: query.schoolId },
      query
    )
  }

  @ApiOperation({ summary: 'get reading user count by grade and class' })
  @ApiBaseResult(SchoolReadingUserCountDto, 200)
  @AdminAuth()
  @Get('user-count-in-class')
  async readingUserCount(
    @CurrentSchoolAdmin() user: any,
    @Query() query: AdminQueryReadingTimeDto
  ) {
    return this.schoolDashboardService.getReadingUserCountByClass(
      { schoolId: query.schoolId },
      query
    )
  }

  @ApiOperation({ summary: 'reading time statistic' })
  @ApiBaseResult(SchoolReadingTimeCountDto, 200)
  @AdminAuth()
  @Get('reading-time-in-class')
  async statisticReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Query() query: AdminQueryReadingTimeDto
  ) {
    return this.schoolDashboardService.getReadingTimeByClass(
      { schoolId: query.schoolId },
      query
    )
  }

  @ApiOperation({ summary: `get detail of student's reading time` })
  @ApiPageResult(StudentReadingTimeDto, 200)
  @AdminAuth()
  @Get('students')
  async studentReadingTime(
    @CurrentSchoolAdmin() user: any,
    @Query() query: AdminQueryStudentReadingTimeDto
  ) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    return this.schoolDashboardService.getReadingOfStudent(
      { ...query, pageIndex, pageSize },
      query.schoolId
    )
  }

  @ApiOperation({ summary: `get detail of book's reading time` })
  @ApiPageResult(BookReadingTimeDto, 200)
  @AdminAuth()
  @Get('books')
  async bookReadingTime(@CurrentSchoolAdmin() user: any, @Query() query: any) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query

    return this.schoolDashboardService.listStudentReadingByBook(
      {
        ...query,
        pageIndex,
        pageSize,
      },
      query.schoolId
    )
  }

  @ApiOperation({ summary: 'get top 10 books' })
  @ApiListResult(Top10BookOfReadingTime, 200)
  @AdminAuth()
  @Get('top-10-books')
  async top10Books(@Query() user: AdminQuerySchoolDto) {
    return this.homepageService.getTop10Books(user.schoolId)
  }

  @ApiOperation({ summary: 'list grade' })
  // @ApiPageResult(GradeDto, 200)
  @AdminAuth()
  @Get('grades')
  async listGrades(@Query() query: ListAdminGradeDto) {
    const data = await this.gradeService.listGrade(query.schoolId, query)
    const items = await Promise.all(
      data.items.map(async (item) => {
        const classes = await this.userClassService.searchClass(query.schoolId, {
          gradeId: item.id,
        })
        return { ...item, classes }
      })
    )
    return { ...data, items }
  }
}
