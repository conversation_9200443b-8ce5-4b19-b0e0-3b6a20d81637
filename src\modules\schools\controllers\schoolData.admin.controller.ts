import { <PERSON>, <PERSON>, Header, <PERSON><PERSON>, ParseIntPipe, Query, Res } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { Response } from 'express'
import moment from 'moment-timezone'
import R from 'ramda'
import {
  AdminAuth,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
  ExcelService,
} from '@/common'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { QuerySchoolReferenceBookStatisticDto } from '@/modules/books/dto/referenceBook.dto'
import { IReferenceReadService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { getISBN, getName } from '@/utils/book.utitl'
import { HomepageService } from '../services'

@Controller('v1/admin/school-data/:schoolId')
export class SchoolDataAdminController {
  constructor(
    private readonly homepageService: HomepageService,
    private readonly excelService: ExcelService,
    private readonly referenceReadService: IReferenceReadService,
    private readonly logService: LogService
  ) {}

  @AdminAuth()
  @Get('export/top-10-books')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top 10 books.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10Books(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Res() res: Response,
    @CurrentAdmin() admin: any
  ) {
    const file = await this.homepageService.exportReferenceHot(schoolId, local, admin)
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('users-count/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=reading user count.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user count' })
  async exportUserCount(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() user: any,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryReadingTimeDto
  ) {
    const data = await this.referenceReadService.groupByClass(schoolId, query)
    const students = data.students.filter((item) => item.classes.length != 0)

    const studentTotalCount = R.flatten(
      students.map((item) => item.classes.map((c) => Number(c.userCount ?? 0)))
    ).reduce((pre, time) => pre + time, 0)

    const totalCount = studentTotalCount + Number(data.teachers.userCount ?? 0)

    const excelData: any = R.flatten(
      students.map((item) => {
        const gradeCount = item.classes.reduce(
          (pre, curr) => pre + Number(curr.userCount ?? 0),
          0
        )

        return item.classes.map((c) => {
          const classCount = Number(c.userCount ?? 0)
          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            type: local === ELocaleType.ZH_HK ? '學生' : 'STUDENT',
            grade: item.grade ?? '',
            class: c.class ?? '',
            gradeReaderCount: gradeCount,
            gradeReaderCountRatio:
              totalCount > 0 ? `${(gradeCount / totalCount) * 100}` : 0,
            classReaderCount: classCount,
            classReaderCountRatio:
              gradeCount > 0 ? `${(classCount / gradeCount) * 100}` : 0,
          }
        })
      })
    )
    const teacherCount = Number(data.teachers.userCount ?? 0)
    excelData.push({
      startTime: moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      type: local === ELocaleType.ZH_HK ? '教職員' : 'TEACHER',
      gradeReaderCount: teacherCount,
      gradeReaderCountRatio:
        totalCount > 0 ? `${(Number(teacherCount) / totalCount) * 100}` : 0,
      grade: '-',
      class: '-',
      classReaderCount: '-',
      classReaderCountRatio: '-',
    })
    let row = 2
    const merges = R.flatten(
      students.map((item) => {
        const length = item.classes.length === 0 ? 1 : item.classes.length
        const merge = [
          {
            start: { row: row, column: 4 },
            end: { row: row + length - 1, column: 4 },
          },
          {
            start: { row: row, column: 5 },
            end: { row: row + length - 1, column: 5 },
          },
          {
            start: { row: row, column: 6 },
            end: { row: row + length - 1, column: 6 },
          },
        ]
        row += length
        return merge
      })
    )
    merges.push({
      start: { row: 2, column: 1 },
      end: { row: excelData.length + 1, column: 1 },
    })
    merges.push({
      start: { row: 2, column: 2 },
      end: { row: excelData.length + 1, column: 2 },
    })
    merges.push({
      start: { row: 2, column: 3 },
      end: { row: excelData.length, column: 3 },
    })

    await this.logService.save('下载学校閱讀人數分佈', user, { ...query, schoolId })
    const file = await this.excelService.buildExcel({
      name: `schoolReferenceUserCount.${local}`,
      data: excelData,
      merges,
    })
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('users/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=users.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export reading user' })
  async exportUsers(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @CurrentAdmin() admin: any,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QuerySchoolReferenceBookStatisticDto
  ) {
    const data = await this.referenceReadService.exportUsers(schoolId, query)
    const file = await this.excelService.buildExcel({
      name: `referenceReadingOfUsers.${local}`,
      data: data.map((item) => ({
        startTime: moment
          .tz(query.startTime * 1000, 'Asia/Hong_Kong')
          .format('YYYY-MM-DD'),
        endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
        name: item.displayName,
        isbn: getISBN(item.isbn),
        grade: item.grade,
        class: item.class,
        bookName: getName(item.bookName),
        serialNo: item.serialNo,
        email: item.email,
        publisher: getName(item.publisherName),
        counts: Number(item.total),
        bookCounts: Number(item.totalBookCount),
        totalCounts: Number(item.totalReadCount),
        ratio: Number(item.totalBookByGrade || 0)
          ? `${(Number(item.totalBookCount) / Number(item.totalBookByGrade)) * 100}`
          : 0,
      })),
    })
    await this.logService.save('下载学校閱讀詳情（用戶）', admin, { ...query, schoolId })
    res.send(Buffer.from(file))
  }
}
