import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  AdminAuth,
  ApiBaseResult,
  ApiPageResult,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
} from '@/common'
import { EQuestionType, ESubjectNo } from '@/enums'
import { GRADES_LOCALE } from '../constants'
import {
  CreateSubjectDraftDto,
  CreateSubjectDto,
  FilterSubjectDto,
  getSubjectDto,
  QueryPlatformSubjectDto,
  SubjectDto,
  SubjectNoDto,
  UpdateSubjectDraftDto,
  UpdateSubjectDto,
  UpdateSubjectStatusDto,
} from '../dto'
import { SubjectService } from '../services/subject.service'

@ApiTags('Science Room')
@ApiExtraModels(SubjectDto, SubjectNoDto)
@Controller('v1/admin/subjects')
export class SubjectAdminController {
  constructor(private readonly subjectService: SubjectService) {}

  @ApiOperation({ summary: '课题编号' })
  @AdminAuth()
  @ApiPageResult(SubjectNoDto, 200)
  @Get('serial-no')
  async listSubjectNo() {
    return {
      serialNo: Object.values(ESubjectNo),
    }
  }

  @ApiOperation({ summary: '创建课题' })
  @ApiBaseResult(SubjectDto, 200)
  @AdminAuth()
  @Post()
  async createSubject(
    @Body() data: CreateSubjectDto | CreateSubjectDraftDto,
    @CurrentAdmin() admin: any
  ) {
    const subject = await this.subjectService.createSubject(data, admin)
    return getSubjectDto(subject)
  }

  @ApiOperation({ summary: '上下架课题' })
  @AdminAuth()
  @Patch('status')
  async updateSubjectStatus(@Body() body: UpdateSubjectStatusDto) {
    await this.subjectService.updateStatus(body, body.toStatus)
  }

  @ApiOperation({ summary: '更新课题' })
  @AdminAuth()
  @Patch(':id')
  async updateSubject(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateSubjectDto | UpdateSubjectDraftDto
  ) {
    const subject = await this.subjectService.updateSubject(id, data)
    return getSubjectDto(subject)
  }

  @ApiOperation({ summary: '课题详情' })
  @AdminAuth()
  @ApiBaseResult(SubjectDto, 200)
  @Get(':id')
  async getSubject(@Param('id', ParseIntPipe) id: number, @CurrentLocale() local) {
    const subject = await this.subjectService.getSubject(id)
    return {
      ...getSubjectDto(subject),
      questionCount: subject.questions.filter(
        (v) => v.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS
      ).length,
      gradeName: GRADES_LOCALE[local][subject.grade],
    }
  }

  @ApiOperation({ summary: '课题列表' })
  @AdminAuth()
  @CurrentLocaleHeader()
  @ApiPageResult(SubjectDto, 200)
  @Get()
  async getSubjects(@Query() query: QueryPlatformSubjectDto, @CurrentLocale() local) {
    const data = await this.subjectService.listSubjects(query)
    return {
      ...data,
      items: data.items.map((v) => {
        return {
          ...getSubjectDto(v),
          questionCount: v.questions.length, // listSubjects已经过滤介绍
          gradeName: GRADES_LOCALE[local][v.grade],
        }
      }),
    }
  }

  @ApiOperation({ summary: '删除课题' })
  @AdminAuth()
  @Delete()
  async deleteSubject(@Query() query: FilterSubjectDto) {
    await this.subjectService.deleteSubject(query)
  }
}
