import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import moment from 'moment'
import R from 'ramda'
import {
  ApiBaseResult,
  ApiListResult,
  ApiPageResult,
  ClientAuth,
  CurrentLocale,
  CurrentUser,
} from '@/common'
import { EQuestionType } from '../../../enums'
import {
  getSubjectDto,
  getSubjectWithSchoolDto,
  getThemeDto,
  LatestAnswerDto,
  QueryClientSubjectDto,
  SubjectCategoryDto,
  SubjectDto,
  SubjectWithAnswer,
  ThemeDto,
} from '../dto'
import { SubjectService, UserAnswerService } from '../services'

@ApiTags('Science Room')
@ApiExtraModels(SubjectDto, ThemeDto, SubjectWithAnswer)
@Controller('v1/client/subjects')
export class SubjectClientController {
  constructor(
    private readonly subjectService: SubjectService,
    private readonly answerService: UserAnswerService
  ) {}

  @ApiOperation({ summary: '课题列表' })
  @ApiPageResult(SubjectDto, 200)
  @Get()
  @ClientAuth()
  async getThemes(
    @Query() query: QueryClientSubjectDto,
    @CurrentUser() user: any,
    @CurrentLocale() local
  ) {
    const data = await this.subjectService.listOnlineSubjects(
      user.isTeacher,
      user.gradeId,
      user.schoolId,
      query?.subjectCategoryId
    )
    const userSubjectData = await this.answerService.getAnswerResultBySubjectIds(
      data.map((v) => v.id),
      user.userId
    )
    return data
      .sort((a, b) => {
        //online排在前面
        const statusA = a.schoolSubjects?.[0]?.status || ''
        const statusB = b.schoolSubjects?.[0]?.status || ''
        return statusB.localeCompare(statusA)
      })
      .map((item) => {
        const onlineAt = item.schoolSubjects?.[0]?.onlineAt
        return {
          completed: userSubjectData.some((v) => item.id == v.subject.id),
          ...getSubjectDto(item),
          questionCount: item.questions.filter(
            (v) => v.questionType == EQuestionType.MULTIPLE_CHOICE_QUESTIONS
          ).length,
          onlineAt: onlineAt
            ? moment.tz(onlineAt * 1000, 'Asia/Hong_Kong').format('DD/MM/YYYY HH:mm')
            : '-',
          theme: {
            ...item.theme,
            nameLocale: item.theme.name[local],
          },
          subjectCategory: {
            ...item.subjectCategory,
            nameLocale: item.subjectCategory.name[local],
          },
          ...getSubjectWithSchoolDto(item),
          nameLocale: item.name[local],
        }
      })
  }
  @ApiOperation({ summary: '范畴列表' })
  @ApiListResult(SubjectCategoryDto, 200)
  @Get('categories')
  @ClientAuth()
  async getCategories(@CurrentLocale() local, @CurrentUser() user: any) {
    const list = await this.subjectService.listAllSubjectCategories()
    const cidList = await this.subjectService.listOnlineSubjects2(
      user.isTeacher,
      user.gradeId,
      user.schoolId
    )
    return list
      .filter((v) => cidList.includes(v.id))
      .map((v) => {
        return {
          ...v,
          nameLocale: v.name[local],
        }
      })
  }

  @ApiOperation({ summary: '主题列表' })
  @ApiListResult(ThemeDto, 200)
  @Get('themes')
  @ClientAuth()
  async getSubjects(@CurrentUser() user: any, @CurrentLocale() local) {
    const data = await this.subjectService.listOnlineThemes(user.schoolId)
    return data.map((item) => {
      return {
        ...getThemeDto(item),
        nameLocale: item.name[local],
      }
    })
  }

  @ApiOperation({ summary: '课题详情' })
  @Get(':id')
  @ClientAuth()
  @ApiBaseResult(SubjectWithAnswer, 200)
  async getSubject(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
    @CurrentLocale() local
  ) {
    const data = await this.subjectService.getSubject(id)
    const subject = getSubjectDto(data)
    if (subject.book?.authors) {
      subject.book.authorNames = subject.book?.authors.map((v) => v.name[local])
      delete subject.book.authors
    }
    // let questionCount = 0
    subject.questions = subject.questions.map((item) => {
      if (item.questionType !== EQuestionType.MULTIPLE_CHOICE_QUESTIONS) {
        return item
      }
      // questionCount++
      const options = item.options.map((option) =>
        R.omit(['isCorrect', 'reason'], option)
      )
      return {
        ...item,
        options,
      }
    }) as any
    const questionCount = subject.questions.filter(
      (v) => v.questionType == EQuestionType.MULTIPLE_CHOICE_QUESTIONS
    ).length
    const answer = await this.answerService.maxAnswer(id, user.userId)
    return { ...subject, nameLocale: subject.name[local], answer, questionCount }
  }

  @Get('latest/:id')
  @ApiBaseResult(LatestAnswerDto, 200)
  @ApiOperation({ summary: '获取课题的最近一次答题' })
  @ClientAuth()
  async get(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: any) {
    return await this.answerService.latestAnswer(id, user.userId)
  }
}
