import { Body, Controller, Get, Param, ParseIntPipe, Patch, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import R from 'ramda'
import {
  ApiBaseResult,
  ApiPageResult,
  CurrentLocale,
  CurrentSchoolAdmin,
  CurrentUser,
  SchoolAdminAuth,
} from '@/common'
import { EQuestionType, ESchoolSubjectStatus, ESubjectNo, ESubjectStatus } from '@/enums'
import { GRADES_LOCALE } from '../constants'
import {
  getSubjectDto,
  getSubjectWithSchoolDto,
  PatchSchoolSubjectDto,
  QuerySchoolSubjectDto,
  SubjectDto,
  SubjectNoDto,
  SubjectWithSchoolDto,
} from '../dto'
import { SchoolService } from '../services'
import { SchoolSubjectService } from '../services/index1'
import { SubjectService } from '../services/subject.service'

@ApiTags('Science Room')
@ApiExtraModels(SubjectDto, SubjectWithSchoolDto, SubjectNoDto)
@Controller('v1/school/subjects')
export class SubjectSchoolController {
  constructor(
    private readonly schoolService: SchoolService,
    private readonly subjectService: SubjectService,
    private readonly schoolSubjectService: SchoolSubjectService
  ) {}

  @ApiOperation({ summary: '课题编号' })
  @SchoolAdminAuth()
  @ApiPageResult(SubjectNoDto, 200)
  @Get('serial-no')
  async listSubjectNo() {
    return {
      serialNo: Object.values(ESubjectNo),
    }
  }

  @ApiOperation({ summary: '课题详情' })
  @SchoolAdminAuth()
  @ApiBaseResult(SubjectWithSchoolDto, 200)
  @Get(':id')
  async getSubject(
    @Param('id', ParseIntPipe) id: number,
    @CurrentSchoolAdmin('schoolId') schoolId: number,
    @CurrentLocale() local
  ) {
    const subject = await this.subjectService.getSubject(id)
    const schoolSubject = await this.subjectService.getSchoolSubject(id, schoolId)
    return Object.assign(
      getSubjectWithSchoolDto({
        ...subject,
        schoolSubjects: schoolSubject ? [schoolSubject] : undefined,
      }),
      {
        questionCount: subject.questions.filter(
          (v) => v.questionType === EQuestionType.MULTIPLE_CHOICE_QUESTIONS
        ).length,
        gradeName: GRADES_LOCALE[local][subject.grade],
      }
    )
  }

  @ApiOperation({ summary: '课题列表' })
  @SchoolAdminAuth()
  @ApiPageResult(SubjectWithSchoolDto, 200)
  @Get()
  async getSubjects(
    @Query() query: QuerySchoolSubjectDto,
    @CurrentSchoolAdmin() admin: any,
    @CurrentLocale() local
  ) {
    const data = await this.subjectService.listSubjects({
      ...R.omit(['status'], query),
      schoolSubjectStatus: query.status,
      status: ESubjectStatus.ONLINE,
      schoolId: admin.schoolId,
      schoolContract: true,
    })
    const items = data.items.map((v) => {
      return {
        ...getSubjectWithSchoolDto(v),
        questionCount: v.questions.length, // listSubjects已经过滤介绍
        gradeName: GRADES_LOCALE[local][v.grade],
      }
    })

    return {
      ...data,
      items,
    }
  }

  @ApiOperation({ summary: '上下架课题' })
  @SchoolAdminAuth()
  @Patch('status')
  async updateSubjectStatus(
    @Body() body: PatchSchoolSubjectDto,
    @CurrentUser() admin: any
  ) {
    await this.schoolSubjectService.schoolUpdateStatus(body, admin.schoolId)
  }
}
