import { <PERSON>, <PERSON>, Header, <PERSON><PERSON>, ParseInt<PERSON>ipe, Query, <PERSON>s } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import R from 'ramda'
import {
  AdminAuth,
  CurrentAdmin,
  CurrentLocale,
  CurrentLocaleHeader,
  CurrentSchoolAdmin,
  ELocaleType,
  ExcelService,
  SchoolAdminAuth,
} from '@/common'
import { ELessonDocumentType, ESchoolSubjectStatus, ESubjectStatus } from '../../../enums'
import { QueryReadingTimeDto } from '../../books/dto'
import { LogService } from '../../system'
import {
  ExportAdminDocumentsSubjectDto,
  ExportBatchAdminDocumentsSubjectDto,
  ExportDetailBySubjectDto,
  ExportGroupBySchoolDto,
  ExportSchoolDocumentsSubjectDto,
  FilterSubjectDto,
  getSubjectWithSchoolDto,
  QuerySchoolSubjectDto,
  QuerySchoolUserAnswerExportDto,
  QueryTimeDto,
  Top10SubjectsDto,
} from '../dto'
import { SubjectService, SubjectStatsService, UserAnswerService } from '../services'

@ApiTags('Science Room')
@ApiExtraModels(Top10SubjectsDto)
@Controller('v1/science-room/stats')
export class SubjectStatsController {
  constructor(
    private readonly subjectStatsService: SubjectStatsService,
    private readonly excelService: ExcelService,
    private readonly subjectService: SubjectService,
    private readonly logService: LogService,
    private readonly userAnswerService: UserAnswerService
  ) {}

  @SchoolAdminAuth()
  @Get('school-admin/top-10-subjects/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top-10-subjects.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10Subjects(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryTimeDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportTop10Subjects(user, query, user.schoolId, local, res)
  }

  @AdminAuth()
  @Get('admin/:schoolId/top-10-subjects/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=top-10-subjects.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export top 10 books' })
  async exportTop10SubjectsAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QueryTimeDto,
    @CurrentAdmin() user: any,
    @Res() res: Response,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    this.subjectStatsService.exportTop10Subjects(user, query, schoolId, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/grade-uv/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=distribution-of-engagement-user.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '年级互动UV' })
  async gradeUVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto
  ) {
    this.subjectStatsService.exportGradeUV(user, user.schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('admin/:schoolId/grade-uv/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=distribution-of-engagement-user.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '年级互动UV' })
  async gradeUVExportAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Res() res: Response,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryReadingTimeDto
  ) {
    this.subjectStatsService.exportGradeUV(user, schoolId, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/grade-pv/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=distribution-of-engagement-count.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '年级互动PV' })
  async gradePVExport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto
  ) {
    this.subjectStatsService.exportGradePV(user, user.schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('admin/:schoolId/grade-pv/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header(
    'Content-Disposition',
    'attachment; filename=distribution-of-engagement-user.xlsx'
  )
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '年级互动PV' })
  async gradePVExportAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Res() res: Response,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryReadingTimeDto
  ) {
    this.subjectStatsService.exportGradePV(user, schoolId, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/detail-by-user/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=engagement-detail-user.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '互動詳情(用戶)' })
  async detailByUser(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QuerySchoolUserAnswerExportDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportDetailByUser(user, user.schoolId, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/study-report/export')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', `attachment; filename=study-report.zip`)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '学习报告' })
  async studyReport(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QuerySchoolUserAnswerExportDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportStudyReport(user, user.schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('admin/:schoolId/detail-by-user/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=engagement-detail-user.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '互動詳情(用戶)' })
  async detailByUserAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @CurrentAdmin() user: any,
    @Res() res: Response,
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QuerySchoolUserAnswerExportDto
  ) {
    this.subjectStatsService.exportDetailByUser(user, schoolId, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/detail-by-subject/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=engagement-detail-subject.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '互动详细(课题)' })
  async detailBySubject(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportDetailBySubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportDetailBySubject(user, user.schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('admin/:schoolId/detail-by-subject/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=engagement-detail-subject.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '互动详细(课题)' })
  async detailBySubjectAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportDetailBySubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response,
    @Param('schoolId') schoolId: number | string
  ) {
    if (schoolId == 'all') {
      schoolId = undefined
    }
    this.subjectStatsService.exportDetailBySubject(user, schoolId, query, local, res)
  }

  @AdminAuth()
  @Get('admin/group-by-school/export')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Number-of-engagement(school).xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '課題參與數量(學校)' })
  async uvpvGroupBySchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportGroupBySchoolDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportUVPVBySchool(user, query, local, res)
  }

  @AdminAuth()
  @Get('admin/batch-documents/export')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', `attachment; filename=admin-documents.zip`)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '批量下载教案 admin' })
  async exportBatchDocuments(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportBatchAdminDocumentsSubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportBatchDocuments(user, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/batch-documents/export')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', `attachment; filename=school-documents.zip`)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '批量下载教案 school' })
  async exportBatchDocumentsSchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: QuerySchoolSubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportBatchDocuments(
      user,
      {
        ...R.omit(['status'], query),
        schoolSubjectStatus: query.status,
        status: ESubjectStatus.ONLINE,
        schoolId: user.schoolId,
        schoolContract: true,
      },
      local,
      res
    )
  }

  @AdminAuth()
  @Get('admin/documents/export')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', `attachment; filename=admin-documents.zip`)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '下载单个课题教案 admin' })
  async exportDocuments(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportAdminDocumentsSubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportDocuments(user, query, local, res)
  }

  @SchoolAdminAuth()
  @Get('school-admin/documents/export')
  @Header('Content-Type', 'application/zip')
  @Header('Content-Disposition', `attachment; filename=school-documents.zip`)
  @CurrentLocaleHeader()
  @ApiOperation({ summary: '下载单个课题教案 school' })
  async exportDocumentsSchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Query() query: ExportSchoolDocumentsSubjectDto,
    @CurrentSchoolAdmin() user: any,
    @Res() res: Response
  ) {
    this.subjectStatsService.exportDocuments(
      user,
      {
        ...R.omit(['status'], query),
        schoolSubjectStatus: query.status,
        status: ESubjectStatus.ONLINE,
        schoolId: user.schoolId,
        schoolContract: true,
      },
      local,
      res
    )
  }

  @ApiOperation({ summary: '课题列表CSV school' })
  @SchoolAdminAuth()
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Subjects.xlsx')
  @CurrentLocaleHeader()
  @Get('school-admin/subject-list/export')
  async getSubjectsCVS(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QuerySchoolSubjectDto,
    @CurrentSchoolAdmin() admin: any
  ) {
    this.subjectStatsService.exportSchoolSubjects(
      admin,
      {
        ...R.omit(['status'], query),
        schoolSubjectStatus: query.status,
        status: ESubjectStatus.ONLINE,
        schoolId: admin.schoolId,
        schoolContract: true,
      },
      local,
      res
    )
  }

  @ApiOperation({ summary: '课题列表CSV admin' })
  @AdminAuth()
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Subjects.xlsx')
  @CurrentLocaleHeader()
  @Get('admin/subject-list/export')
  async getSubjectsCVSAdmin(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: FilterSubjectDto,
    @CurrentSchoolAdmin() admin: any
  ) {
    this.subjectStatsService.exportAdminSubjects(admin, query, local, res)
  }
}
