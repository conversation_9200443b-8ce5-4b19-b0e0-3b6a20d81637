import { Controller, Get } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AdminAuth, ApiListResult } from '@/common'
import { ThemeDto } from '../dto'
import { SubjectService } from '../services'

@ApiTags('Science Room')
@ApiExtraModels(ThemeDto)
@Controller('v1/admin/themes')
export class ThemeAdminController {
  constructor(private readonly subjectService: SubjectService) {}

  // @ApiListResult(ThemeDto, 200)
  // @ApiOperation({ summary: '获取所有主题' })
  // @Get()
  // @AdminAuth()
  // async getThemes() {
  //   return this.subjectService.listAllThemes()
  // }
}
