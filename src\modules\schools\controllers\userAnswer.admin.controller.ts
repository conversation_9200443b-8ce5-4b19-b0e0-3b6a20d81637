import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import moment from 'moment'
import { AdminAuth, ApiListResult, ApiPageResult } from '@/common'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { getDays } from '@/utils'
import {
  QueryAdminAnswerDto,
  QueryAdminTimesUsersGroupByDateAndSubjectDto,
  QueryAnswerStatisticsDto,
  QuerySchoolUserAnswerDto,
  QueryTimeDto,
  TimesUserAnswerDto,
  TimesUserByDateDto,
  Top10SubjectsDto,
  UsersAnswerDto,
} from '../dto'
import { UserAnswerService } from '../services'

@ApiTags('Science Room')
@Controller('v1/admin/user-answers')
export class UserAnswerAdminController {
  constructor(private readonly userAnswerService: UserAnswerService) {}

  @Get(':schoolId/top-10-subjects')
  @ApiListResult(Top10SubjectsDto, 200)
  @ApiOperation({ summary: '最受欢迎课题top 10 (学校数据详情)' })
  @AdminAuth()
  async top10Subject(
    @Query() query: QueryTimeDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return await this.userAnswerService.top10Subject(schoolId, query)
  }

  @Get(':schoolId/times-users-by-date')
  @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动数据概览 (学校数据详情)' })
  @AdminAuth()
  async timesUsersByDate(
    @Query() query: QueryTimeDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    const data = await this.userAnswerService.groupByDate({
      ...query,
      schoolId,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      times: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.times ?? 0
      ),
      users: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.users ?? 0
      ),
    }))
  }

  @Get(':schoolId/users-by-grade')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动人数分布 (学校数据详情)' })
  @AdminAuth()
  async usersByGrade(
    @Query() query: QueryReadingTimeDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return this.userAnswerService.usersGroupByGrade(query, schoolId)
  }

  @Get(':schoolId/times-by-grade')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动人次分布 (学校数据详情)' })
  @AdminAuth()
  async timesByGrade(
    @Query() query: QueryReadingTimeDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return this.userAnswerService.timesGroupByGrade(query, schoolId)
  }

  @Get(':schoolId/detail-by-user')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动详细-用户 (学校数据详情)' })
  @AdminAuth()
  async detailByUser(
    @Query() query: QuerySchoolUserAnswerDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return this.userAnswerService.groupByUserAndSubject(query, schoolId)
  }

  @Get(':schoolId/detail-by-subject')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动详细-课题 (学校数据详情)' })
  @AdminAuth()
  async detailBySubject(
    @Query() query: QuerySchoolUserAnswerDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return this.userAnswerService.groupBySubject(query, schoolId)
  }

  @ApiOperation({ summary: '互动详细-列表 (学校数据详情)' })
  @AdminAuth()
  @Get(':schoolId/list')
  async getUserAnswer(
    @Query() query: QueryAdminAnswerDto,
    @Param('schoolId', ParseIntPipe) schoolId: number
  ) {
    return this.userAnswerService.answers(query, schoolId)
  }

  @ApiOperation({ summary: '互动详细-XXX (学校数据详情)' })
  @AdminAuth()
  @Get(':schoolId/statistic-by-user-and-subject')
  async exportUserAnswer(
    @Param('schoolId', ParseIntPipe) schoolId: number,
    @Query() query: QueryAnswerStatisticsDto
  ) {
    query.schoolId = schoolId
    return this.userAnswerService.answerStatisticsByUserAndSubject(query)
  }

  @Get('times-users-by-date-subject')
  @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动数据概览(课题详情）' })
  @AdminAuth()
  async timesUsersByDateAndSubject(
    @Query() query: QueryAdminTimesUsersGroupByDateAndSubjectDto
  ) {
    const data = await this.userAnswerService.groupByDate({
      ...query,
      schoolId: query.schoolId,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      times: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.times ?? 0
      ),
      users: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.users ?? 0
      ),
    }))
  }

  @AdminAuth()
  @Get('times-group-by-subject')
  @ApiOperation({ summary: '課題參與數量 (人次)' })
  @ApiPageResult(TimesUserAnswerDto, 200)
  async timesGroupBySubject(@Query() query: QueryAdminAnswerDto) {
    return this.userAnswerService.timesGroupBySubject(query)
  }

  @AdminAuth()
  @Get('times-group-by-school')
  @ApiOperation({ summary: '課題參與人數 - 學校' })
  @ApiPageResult(TimesUserAnswerDto, 200)
  async timesGroupBySchool(@Query() query: QueryAdminAnswerDto) {
    return this.userAnswerService.timesGroupBySchool(query)
  }

  @AdminAuth()
  @Get('users-group-by-subject')
  @ApiOperation({ summary: '課題參與人數' })
  @ApiPageResult(UsersAnswerDto, 200)
  async usersGroupBySubject(@Query() query: QueryAdminAnswerDto) {
    return this.userAnswerService.usersGroupBySubject(query)
  }

  @AdminAuth()
  @Get('users-group-by-school')
  @ApiOperation({ summary: '課題參與人數 - 學校' })
  @ApiPageResult(UsersAnswerDto, 200)
  async usersGroupBySchool(@Query() query: QueryAdminAnswerDto) {
    return this.userAnswerService.usersGroupBySchool(query)
  }
}
