import { Body, Controller, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiBaseResult, ClientAuth, CurrentUser } from '@/common'
import { AnswerCountDto, AnswerDto, AnswerQuestionDto } from '../dto'
import { UserAnswerService } from '../services'

@ApiTags('Science Room')
@ApiExtraModels(AnswerDto)
@Controller('v1/client/user-answers')
export class UserAnswerClientController {
  constructor(private readonly userAnswerService: UserAnswerService) {}

  @Post()
  @ApiBaseResult(AnswerDto, 200)
  @ApiOperation({ summary: '用户 完成答题上报' })
  @ClientAuth()
  async createUserAnswer(@Body() body: AnswerQuestionDto, @CurrentUser() user: any) {
    return await this.userAnswerService.answerQuestion(
      body,
      user.userId,
      user.schoolId,
      user.classId,
      user.gradeId
    )
  }

  @Post('/start')
  @ApiBaseResult(AnswerCountDto, 200)
  @ApiOperation({ summary: '用户 开始答题上报' })
  @ClientAuth()
  async createUserAnswerCount(@Body() body: AnswerCountDto, @CurrentUser() user: any) {
    return await this.userAnswerService.answerCount(
      body,
      user.userId,
      user.schoolId,
      user.classId,
      user.gradeId,
      user.isTeacher
    )
  }
}
