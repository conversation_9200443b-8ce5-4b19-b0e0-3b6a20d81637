import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import moment from 'moment'
import { ApiListResult, CurrentUser, SchoolAdminAuth } from '@/common'
import { getDays } from '@/utils'
import {
  QueryAnswersDto,
  QueryAnswerStatisticsDto,
  QuerySchoolUserAnswerDto,
  QueryTimeDto,
  QueryTimesUsersGroupByDateAndSubjectDto,
  TimesUserByDateDto,
  Top10SubjectsDto,
} from '../dto'
import { UserAnswerService } from '../services'

@ApiTags('Science Room')
@ApiExtraModels(Top10SubjectsDto)
@Controller('v1/school-admin/user-answers')
export class UserAnswerSchoolController {
  constructor(private readonly userAnswerService: UserAnswerService) {}

  @Get('top-10-subjects')
  @ApiListResult(Top10SubjectsDto, 200)
  @ApiOperation({ summary: '最受欢迎课题top 10' })
  @SchoolAdminAuth()
  async top10Subject(@Query() query: QueryTimeDto, @CurrentUser() user: any) {
    return await this.userAnswerService.top10Subject(user.schoolId, query)
  }

  @Get('times-users-by-date')
  @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动数据概览' })
  @SchoolAdminAuth()
  async timesUsersByDate(@Query() query: QueryTimeDto, @CurrentUser() user: any) {
    const data = await this.userAnswerService.groupByDate({
      ...query,
      schoolId: user.schoolId,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      times: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.times ?? 0
      ),
      users: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.users ?? 0
      ),
    }))
  }

  @Get('times-users-by-date-subject')
  @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动数据概览(课题详情）' })
  @SchoolAdminAuth()
  async timesUsersByDateAndSubject(
    @Query() query: QueryTimesUsersGroupByDateAndSubjectDto,
    @CurrentUser() user: any
  ) {
    const data = await this.userAnswerService.groupByDate({
      ...query,
      schoolId: user.schoolId,
    })

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      times: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.times ?? 0
      ),
      users: Number(
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.users ?? 0
      ),
    }))
  }

  @Get('users-by-grade')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动人数分布' })
  @SchoolAdminAuth()
  async usersByGrade(@Query() query: QueryTimeDto, @CurrentUser() user: any) {
    return this.userAnswerService.usersGroupByGrade(query, user.schoolId)
  }

  @Get('times-by-grade')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动人次分布' })
  @SchoolAdminAuth()
  async timesByGrade(@Query() query: QueryTimeDto, @CurrentUser() user: any) {
    return this.userAnswerService.timesGroupByGrade(query, user.schoolId)
  }

  @Get('detail-by-user')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动详细-用户' })
  @SchoolAdminAuth()
  async detailByUser(@Query() query: QuerySchoolUserAnswerDto, @CurrentUser() user: any) {
    return this.userAnswerService.groupByUserAndSubject(query, user.schoolId)
  }

  @Get('detail-by-subject')
  // @ApiListResult(TimesUserByDateDto, 200)
  @ApiOperation({ summary: '互动详细-课题' })
  @SchoolAdminAuth()
  async detailBySubject(
    @Query() query: QuerySchoolUserAnswerDto,
    @CurrentUser() user: any
  ) {
    return this.userAnswerService.groupBySubject(query, user.schoolId)
  }

  @ApiOperation({ summary: '互动详细-列表' })
  @SchoolAdminAuth()
  @Get('list')
  async getUserAnswer(@Query() query: QueryAnswersDto, @CurrentUser() user: any) {
    return this.userAnswerService.answers(query, user.schoolId)
  }

  @ApiOperation({ summary: '互动详细-XXX' })
  @SchoolAdminAuth()
  @Get('statistic-by-user-and-subject')
  async exportUserAnswer(
    @Query() query: QueryAnswerStatisticsDto,
    @CurrentUser() user: any
  ) {
    query.schoolId = user.schoolId
    return this.userAnswerService.answerStatisticsByUserAndSubject(query)
  }
}
