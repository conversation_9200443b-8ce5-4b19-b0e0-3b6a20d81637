import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  ApiListResult,
  CurrentSchoolAdmin,
  SchoolAdminAuth,
} from '@/common'
import {
  CreateUserClassDto,
  DeleteUserClassDto,
  getUserClassDto,
  QueryUserClassDto,
  UserClassDto,
} from '../dto'
import { UserClassService } from '../services/index1'

@ApiTags('Class')
@ApiExtraModels(UserClassDto)
@Controller('v1/school-admin/user-class')
export class UserClassSchoolController {
  constructor(private readonly userClassService: UserClassService) {}

  @ApiOperation({ summary: 'create class' })
  @ApiBaseResult(UserClassDto, 200)
  @Post()
  @SchoolAdminAuth()
  async createClass(@Body() data: CreateUserClassDto, @CurrentSchoolAdmin() user: any) {
    const userClass = await this.userClassService.createUserClass(user.schoolId, data)
    return getUserClassDto(userClass)
  }

  @ApiOperation({ summary: 'update class' })
  @ApiBaseResult(UserClassDto, 200)
  @Patch(':id')
  @SchoolAdminAuth()
  async updateClass(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CreateUserClassDto,
    @CurrentSchoolAdmin() user: any
  ) {
    const userClass = await this.userClassService.updateClass(id, user.schoolId, data)
    return getUserClassDto(userClass)
  }

  // @ApiOperation({ summary: 'list class' })
  // @ApiListResult(UserClassDto, 200)
  // @SchoolAdminAuth()
  // @Get('list')
  // async listClass(@CurrentSchoolAdmin() user: any, @Query() query: PageRequest) {
  //   const data = await this.userClassService.listClass(user.schoolId, query)
  //   return {
  //     ...data,
  //     total: GRADE_VALUES.length,
  //     items: GRADE_VALUES.map((grade) => ({
  //       grade,
  //       classes: data.items.find((item) => item.grade === grade)?.classes ?? [],
  //     })),
  //   }
  // }

  @ApiOperation({ summary: 'search class' })
  @ApiListResult(UserClassDto, 200)
  @SchoolAdminAuth()
  @Get()
  async searchClass(@CurrentSchoolAdmin() user: any, @Query() query: QueryUserClassDto) {
    const data = await this.userClassService.searchClass(user.schoolId, query)
    return data.map((item) => getUserClassDto(item))
  }

  @ApiOperation({ summary: 'delete class' })
  // @ApiListResult(UserClassDto, 200)
  @SchoolAdminAuth()
  @Delete()
  async deleteClass(@Body() data: DeleteUserClassDto) {
    await this.userClassService.deleteClass(data.classIds)
  }
}
