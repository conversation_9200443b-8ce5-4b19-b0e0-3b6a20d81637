import { <PERSON>, Get, Header, Query, Re<PERSON> } from '@nestjs/common'
import { ApiOperation } from '@nestjs/swagger'
import { Response } from 'express'
import {
  AdminAuth,
  ApiPageResult,
  CurrentLocale,
  CurrentLocaleHeader,
  ELocaleType,
} from '@/common'
import { EViewBookGroupType } from '@/enums'
import { QueryReadingTimeDto } from '@/modules/books/dto'
import { IAuthorService, IPublisherService, IViewBookDetailService } from '@/modules/shared/interfaces'
import {
  PlatformStatisticViewBookDto,
  QueryPlatformStatisticViewBookDto,
} from '../../books/dto/viewBook.dto'
import { SchoolService } from '../services'

@Controller('v1/admin/book-detail')
export class ViewBookDetailAdminController {
  constructor(
    private readonly viewBookDetailService: IViewBookDetailService,
    private readonly schoolService: SchoolService,
    private readonly publisherService: IPublisherService,
    private readonly authorService: IAuthorService
  ) {}

  @ApiOperation({ summary: 'view counts' })
  @ApiPageResult(PlatformStatisticViewBookDto, 200)
  @AdminAuth()
  @Get('view-counts')
  async viewCounts(@Query() query: QueryPlatformStatisticViewBookDto) {
    const { group = EViewBookGroupType.SCHOOL } = query
    const data = await this.viewBookDetailService.statisticsForPlatform(query)

    let authors = [],
      schools = [],
      publishers = []
    switch (group) {
      case EViewBookGroupType.SCHOOL:
        schools = await this.schoolService.findSchools(
          data.items.map((item) => item.groupId)
        )

        break
      case EViewBookGroupType.PUBLISHER:
        publishers = await this.publisherService.findPublishers(
          data.items.map((item) => item.groupId)
        )
        break
      case EViewBookGroupType.AUTHOR:
        authors = await this.authorService.getAuthors(
          data.items.map((item) => item.groupId)
        )
        break
    }
    return {
      ...data,
      items: data.items.map((item) => ({
        count: Number(item.viewCount),
        authors:
          group === EViewBookGroupType.AUTHOR
            ? authors.find((author) => author.id === item.groupId)?.name
            : undefined,
        schools:
          group === EViewBookGroupType.SCHOOL
            ? schools.find((school) => school.id === item.groupId)?.name
            : undefined,
        publishers:
          group === EViewBookGroupType.PUBLISHER
            ? publishers.find((publisher) => publisher.id === item.groupId)?.name
            : undefined,
      })),
    }
  }

  @AdminAuth()
  @Get('export-schools')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Schools.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export book detail - school' })
  async exportSchool(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto
  ) {
    const file = await this.viewBookDetailService.export(
      { ...query, group: EViewBookGroupType.SCHOOL },
      local
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export-publishers')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Schools.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export schools' })
  async exportPublisher(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto
  ) {
    const file = await this.viewBookDetailService.export(
      { ...query, group: EViewBookGroupType.PUBLISHER },
      local
    )
    res.send(Buffer.from(file))
  }

  @AdminAuth()
  @Get('export-authors')
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )
  @Header('Content-Disposition', 'attachment; filename=Schools.xlsx')
  @CurrentLocaleHeader()
  @ApiOperation({ summary: 'export schools' })
  async exportAuthors(
    @CurrentLocale() local: ELocaleType = ELocaleType.EN_UK,
    @Res() res: Response,
    @Query() query: QueryReadingTimeDto
  ) {
    const file = await this.viewBookDetailService.exportByAuthorBooks(query, local)
    res.send(Buffer.from(file))
  }
}
