import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { EBookVersion, EOrderDirection } from '@/enums'
import { SelectDtoRequest } from './base'

export class PageDto {
  @ApiPropertyOptional()
  total?: number

  @ApiPropertyOptional()
  pageIndex?: number

  @ApiPropertyOptional()
  pageSize?: number
}

export class QueryTimeDto {
  @ApiPropertyOptional()
  startTime: number

  @ApiPropertyOptional()
  endTime: number
}

export class SubmitReadingReflectionDto {
  @ApiProperty()
  @IsNumber()
  @Transform(({ value }) => (value ? Number(value) : value))
  bookId: number

  @ApiProperty()
  @IsString()
  @IsOptional()
  content?: string

  @ApiProperty()
  @IsString()
  @IsOptional()
  audioUrl?: string

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => (value ? Number(value) : value))
  audioTime?: number

  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class QueryReadBookDto extends IntersectionType(QueryTimeDto, PageDto) {
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsEnum(EOrderDirection)
  @IsOptional()
  orderDirection?: EOrderDirection

  @ApiPropertyOptional()
  orderField?: string
}

export class QueryReadingReflectionListDto extends IntersectionType(
  QueryTimeDto,
  PageDto
) {
  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion

  @ApiPropertyOptional()
  @IsEnum(EOrderDirection)
  @IsOptional()
  orderDirection?: EOrderDirection

  @ApiPropertyOptional()
  orderField?: string

  @ApiPropertyOptional()
  grade?: number

  @ApiPropertyOptional()
  keyword?: string

  @ApiPropertyOptional()
  reviewState?: number

  @ApiPropertyOptional()
  flagState?: number

  @ApiPropertyOptional()
  class?: number

  @ApiPropertyOptional()
  bookId?: number

  @ApiPropertyOptional()
  userId?: number
}

export class QueryAdminReadingReflectionListDto extends QueryReadingReflectionListDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  schoolId: number
}

export class QueryReadingReflectionDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  bookId: number

  @ApiPropertyOptional({ enum: EBookVersion })
  @IsEnum(EBookVersion)
  @IsOptional()
  version?: EBookVersion
}

export class QueryReadingReflectionInfoDto extends IntersectionType(
  QueryReadingReflectionDto,
  QueryTimeDto
) {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  userId: number
}

export class QueryReadingReflectionInfoClientDto extends IntersectionType(
  QueryReadingReflectionDto,
  QueryTimeDto
) {}

export class BathSelectReadBookDto extends IntersectionType(
  QueryReadingReflectionListDto,
  SelectDtoRequest
) {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  setState: number
}
