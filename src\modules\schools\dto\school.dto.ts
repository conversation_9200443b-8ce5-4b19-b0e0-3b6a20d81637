import {
  ApiProperty,
  ApiPropertyOptional,
  OmitType,
  PartialType,
  PickType,
} from '@nestjs/swagger'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator'
import R from 'ramda'
import { PageRequest } from '@/common/dto'
import { School, ScienceContracts } from '@/entities'
import { countries, EGrade, EOrderDirection } from '@/enums'
import { MultiLanguage } from '@/interfaces'
type SchoolField = keyof School
const fields: SchoolField[] = [
  'schoolId',
  'name',
  'logo',
  'principalName',
  'principalEmail',
  'principalNo',
  'webUrl',
  'adminPanelUrl',
  'teacherEmailSuffix',
  'studentEmailSuffix',
  'joinedAt',
  'address',
  'description',
  'status',
  'hasOTP',
  'staffLevelIds',
  'isAllLevelForStaff',
  'isAllLevelForStudent',
  'studentLevelIds',
  'type',
  'region',
  'version',
  'hasScienceRoom',
  'hasAssistant',
]
export class SchoolBase extends PickType(School, fields) {}

export class PublicSchoolDto extends PickType(School, [
  'name',
  'id',
  'teacherEmailSuffix',
  'studentEmailSuffix',
]) {
  constructor(data: School) {
    super()
    this.name = data.name
    this.id = data.id
    this.teacherEmailSuffix = data.teacherEmailSuffix
    this.studentEmailSuffix = data.studentEmailSuffix
  }
}

export const getPublicSchoolDto = (data: School) => new PublicSchoolDto(data)

export class SchoolResponse extends SchoolBase {}

export class ModifySchoolDto extends OmitType(SchoolBase, [
  'joinedAt',
  'status',
  'schoolId',
  'version',
]) {}

export class ModifySchoolStatusDto extends PickType(School, ['status']) {
  @ApiPropertyOptional({ type: [Number] })
  @IsNumber(undefined, { each: true })
  @IsArray()
  @IsOptional()
  schoolIds?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  buyContracts?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  region?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  filterStatus?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber(undefined, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]
}

export class DeleteSchoolDto {
  @ApiPropertyOptional({ type: [Number] })
  @IsNumber(undefined, { each: true })
  @IsArray()
  @IsOptional()
  ids?: number[]

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  buyContracts?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  region?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  filterStatus?: string

  @ApiPropertyOptional({ type: [Number] })
  @IsNumber(undefined, { each: true })
  @IsArray()
  @IsOptional()
  exceptions?: number[]
}

// export class PatchSchoolRequest extends OmitType(SchoolBase, [
//   'joinedAt',
//   'status',
//   'schoolId',
// ]) {}

class GradeWithText {
  @ApiProperty({ enum: EGrade })
  code: EGrade

  @ApiProperty()
  text: string
}
export class SchoolGradeDto {
  @ApiProperty({ type: [GradeWithText] })
  grades: {
    code: EGrade
    text: string
  }[]
}
export class ListSchoolDto extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  schoolId?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  status?: string

  @ApiPropertyOptional({
    description: '购买服务',
    example: 'SUBSCRIPTION, hasAssistant',
  })
  @IsString()
  @IsOptional()
  buyContracts?: string

  @ApiPropertyOptional({
    description: '地区',
    example: 'HK,TW',
  })
  @IsString()
  @IsOptional()
  region?: string

  @ApiPropertyOptional({
    description: '学校加入时间 升降序',
    example: 'DESC,ASC',
  })
  @IsString()
  @IsOptional()
  orderDirection?: EOrderDirection
}

export function getSchoolResponse(school: School) {
  return school
}

export class QueryLogDto extends PageRequest {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}

export class PatchSchoolSchoolAdminDto extends PickType(School, ['hasOTP']) {}

export class SchoolDto extends OmitType(School, ['deletedAt']) {
  @ApiProperty()
  regionName: MultiLanguage

  constructor(data: School) {
    super()
    Object.assign(this, R.pick(fields, data))
    this.hasOTP = data.hasOTP ?? true
    this.isSharingTime = data.isSharingTime ?? false
    this.id = data.id
    if (data.region) {
      this.regionName = countries[data.region]
    }
  }
}

export const getSchoolDto = (data: School) => new SchoolDto(data)

export class SetShareReadingDto extends PickType(School, ['isSharingTime']) {}

export class CreateScienceContractDto extends PartialType(
  PickType(ScienceContracts, ['contractNo', 'gradeCodes'])
) {
  @ApiProperty()
  @IsBoolean()
  hasScienceRoom: boolean
}
