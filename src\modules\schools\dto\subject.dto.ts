import {
  ApiProperty,
  ApiPropertyOptional,
  IntersectionType,
  OmitType,
  PartialType,
  PickType,
} from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator'
import moment from 'moment'
import R from 'ramda'
import { PageRequest } from '@/common'
import { SchoolSubject } from '@/entities/schoolSubject.entity'
import { SubjectExtendBook } from '@/entities/subjectExtendBook'
import {
  EBadge,
  EGrade,
  EOrderDirection,
  ESchoolSubjectStatus,
  ESubjectNo,
  ESubjectStatus,
} from '@/enums'
import { BookDto } from '@/modules/books/dto'
import { SelectDto } from '@/utils'
import { MultiLanguage } from '@/interfaces'
import {
  AnswerItem,
  Options,
  Question,
  QuestionAbility,
  Subject,
  SubjectCategory,
  Theme,
  UserAnswer,
} from '../../../entities'

export class QueryTimeDto {
  @ApiProperty()
  // @IsNumber()
  @Type(() => Number)
  startTime?: number

  @ApiProperty()
  // @IsNumber()
  @Type(() => Number)
  endTime?: number
}

export const getSerialNo = (data: string) => data?.split(';').filter((item) => !!item)

export class CreateQuestionDto extends PickType(Question, [
  'name',
  'questionType',
  'answerType',
  'ability',
  'image',
  'options',
  'sequence',
  'description',
]) {}

export class CreateSubjectDto extends PickType(Subject, [
  'name',
  'serialNo',
  'sequence',
  'image',
  'countdown',
  'bgImage',
  'bgMusic',
  'grade',
  'fromBookPos',
  'toBookPos',
  'status',
  'video',
  'videos',
  'documents',
]) {
  @ApiProperty({ description: '主题ID' })
  @IsNumber()
  themeId: number

  @ApiProperty({ description: '范畴ID' })
  @IsNumber()
  subjectCategoryId: number

  @ApiPropertyOptional({ description: '书籍ID' })
  @IsOptional()
  @IsNumber()
  bookId?: number

  @ApiProperty({ description: '题目', type: [CreateQuestionDto] })
  @Type(() => CreateQuestionDto)
  @ValidateNested({ each: true })
  @IsOptional()
  questions?: CreateQuestionDto[]

  @ApiProperty({ description: '延伸书籍列表', type: [SubjectExtendBook] })
  @IsOptional()
  subjectExtendBooks: SubjectExtendBook[]
}

export class CreateSubjectDraftDto extends PartialType(CreateSubjectDto) {
  @ApiProperty({ description: '课题状态', enum: ESubjectStatus })
  status: ESubjectStatus.DRAFT
}

export class UpdateQuestionDto extends IntersectionType(
  CreateQuestionDto,
  PickType(Question, ['id'])
) {}

export class QuestionDto extends PickType(Question, [
  'id',
  'name',
  'questionType',
  'answerType',
  'ability',
  'image',
  'options',
  'sequence',
  'description',
]) {
  constructor(question: Question) {
    super(question)
    Object.assign(
      this,
      R.pick(
        [
          'id',
          'name',
          'questionType',
          'answerType',
          'image',
          'options',
          'sequence',
          'description',
          'ability',
        ],
        question
      )
    )
    // this.ability = question.ability ? getAbility(question.ability) : null
  }
}

export const getQuestionDto = (question: Question) => new QuestionDto(question)

export class UpdateSubjectDto extends OmitType(CreateSubjectDto, ['questions']) {
  @ApiProperty({ description: '题目', type: [UpdateQuestionDto] })
  @Type(() => UpdateQuestionDto)
  @ValidateNested({ each: true })
  @IsOptional()
  questions?: UpdateQuestionDto[]
}

export class UpdateSubjectDraftDto extends PartialType(UpdateSubjectDto) {
  @ApiProperty({ description: '课题状态', enum: ESubjectStatus })
  status: ESubjectStatus.DRAFT
}

export class ThemeDto extends PickType(Theme, ['id', 'name', 'image']) {
  @ApiPropertyOptional({ type: String })
  nameLocale?: string

  constructor(theme: Theme) {
    super(theme)
    Object.assign(this, R.pick(['id', 'name', 'image'], theme))
  }
}

export const getThemeDto = (theme: Theme) => new ThemeDto(theme)
export class SubjectCategoryDto extends PickType(SubjectCategory, ['id', 'name']) {
  @ApiProperty({ type: [ThemeDto] })
  themes: ThemeDto[]

  @ApiPropertyOptional({ type: String })
  nameLocale?: string

  constructor(subjectCategory: SubjectCategory) {
    super(subjectCategory)
    Object.assign(this, R.pick(['id', 'name', 'colors', 'image'], subjectCategory))
    if (subjectCategory.themes?.length) {
      this.themes = subjectCategory.themes.map((item) => getThemeDto(item))
    }
  }
}

export class SubjectSchoolDto extends PickType(SchoolSubject, [
  'id',
  'status',
  'onlineAt',
]) {}

export class SubjectDto extends PickType(Subject, [
  'id',
  'name',
  'serialNo',
  'sequence',
  'image',
  'countdown',
  'bgImage',
  'bgMusic',
  'video',
  'videos',
  'grade',
  'fromBookPos',
  'toBookPos',
  'status',
  'createdAt',
  'createdBy',
  'documents',
  'onlineAt',
  'offlineAt',
]) {
  @ApiPropertyOptional({ description: '用户已完成答题', type: Boolean })
  completed?: boolean

  @ApiPropertyOptional({ description: '题目数' })
  questionCount?: number

  @ApiPropertyOptional({ type: String })
  nameLocale?: string

  @ApiProperty({ description: '主题', type: ThemeDto })
  theme: ThemeDto

  @ApiProperty({ description: '范畴', type: SubjectCategoryDto })
  subjectCategory: SubjectCategoryDto

  @ApiPropertyOptional({ description: '书籍', type: BookDto })
  book?: BookDto

  @ApiProperty({ description: '题目', type: [QuestionDto] })
  questions: QuestionDto[]

  @ApiProperty({ description: '延伸书籍列表' })
  subjectExtendBooks: SubjectExtendBook[]

  @ApiProperty({ description: '年级名称', type: String })
  gradeName: string

  constructor(subject: Subject) {
    super(subject)
    Object.assign(
      this,
      R.pick(
        [
          'id',
          'name',
          // 'serialNo',
          'sequence',
          'image',
          'countdown',
          'bgImage',
          'bgMusic',
          'grade',
          'fromBookPos',
          'toBookPos',
          'status',
          'createdBy',
          'video',
          'videos',
          'documents',
          'createdAt',
          'onlineAt',
          'offlineAt',
        ],
        subject
      )
    )
    this.grade = subject.grade
    this.serialNo = getSerialNo(subject.serialNo as any) as any
    if (subject.theme) {this.theme = new ThemeDto(subject.theme)}
    if (subject.subjectCategory)
    {this.subjectCategory = new SubjectCategoryDto(subject.subjectCategory)}
    if (subject.book) {this.book = new BookDto(subject.book)}
    if (subject.subjectExtendBooks?.length) {
      this.subjectExtendBooks = subject.subjectExtendBooks
    }
    if (subject.questions?.length)
    {this.questions = subject.questions
      .sort((a, b) => a.sequence - b.sequence)
      .map((question) => new QuestionDto(question))}
    // this.createdAt = subject.createdAt
    //   ? (Math.floor(subject.createdAt.getTime() / 1000) as any)
    //   : null
  }
}

export const getSubjectDto = (subject: Subject) => new SubjectDto(subject)

export class SubjectWithSchoolDto extends SubjectDto {
  @ApiProperty({ type: SubjectSchoolDto })
  schoolSubject: SubjectSchoolDto

  constructor(subject: Subject) {
    super(subject)
    this.schoolSubject = subject.schoolSubjects?.[0]
      ? {
          id: subject.schoolSubjects[0].id,
          status: getSchoolSubjectStatus(subject.schoolSubjects[0]),
          onlineAt: subject.schoolSubjects[0].onlineAt,
        }
      : ({ status: ESchoolSubjectStatus.PRE_ONLINE } as any)
    // console.log(subject)
  }
}

export const getSubjectWithSchoolDto = (subject: Subject) =>
  new SubjectWithSchoolDto(subject)

export class QuerySubjectDto {
  @ApiPropertyOptional({
    description: '主题ID',
    type: () => [Number],
    example: 'themeId=1&themeId=2',
  })
  @IsOptional()
  @Transform((params) => [].concat(params.value))
  @Type(() => Number)
  @IsNumber({ allowNaN: false }, { each: true })
  themeId?: number[]

  @ApiPropertyOptional({
    description: '范畴ID',
    type: () => [Number],
    example: 'subjectCategoryId=1&subjectCategoryId=2',
  })
  @IsOptional()
  @Transform((params) => [].concat(params.value))
  @Type(() => Number)
  @IsNumber({ allowNaN: false }, { each: true })
  subjectCategoryId?: number[]

  @ApiPropertyOptional({
    description: '适用年级',
    enum: EGrade,
    isArray: true,
    example: 'grade=KINDERGARTEN&grade=FIRST_PRIMARY_GRADE',
  })
  @IsOptional()
  @Transform((params) => [].concat(params.value))
  @IsEnum(EGrade, { each: true })
  grade?: EGrade[]

  @ApiPropertyOptional({
    description: '课程编号',
    enum: ESubjectNo,
    isArray: true,
    example: 'serialNo=XXX&serialNo=XXX',
  })
  @IsOptional()
  @Transform((params) => [].concat(params.value))
  @IsEnum(ESubjectNo, { each: true })
  serialNo?: ESubjectNo[]
}

export class ListSubjectDto extends IntersectionType(QuerySubjectDto, PageRequest) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  schoolSubjectStatus?: ESchoolSubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  schoolId?: number

  schoolContract?: boolean
}

export class QueryPlatformSubjectDto extends IntersectionType(
  QuerySubjectDto,
  PageRequest
) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus
}

export class QuerySchoolSubjectDto extends IntersectionType(
  QuerySubjectDto,
  PageRequest
) {
  @ApiPropertyOptional({ enum: ESchoolSubjectStatus })
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  status?: ESchoolSubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string
}

export class ExportSchoolDocumentsSubjectDto extends IntersectionType(
  QuerySubjectDto,
  PageRequest
) {
  @ApiProperty({
    description: '课题ID',
    type: () => Number,
    example: '',
  })
  @IsNumber()
  @Type(() => Number)
  subjectId: number

  @ApiPropertyOptional({ enum: ESchoolSubjectStatus })
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  status?: ESchoolSubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string
}

export class ExportBatchAdminDocumentsSubjectDto extends IntersectionType(
  QuerySubjectDto,
  PageRequest
) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  schoolSubjectStatus?: ESchoolSubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string
}

export class ExportAdminDocumentsSubjectDto extends IntersectionType(
  QuerySubjectDto,
  PageRequest
) {
  @ApiProperty({
    description: '课题ID',
    type: () => Number,
    example: '',
  })
  @IsNumber()
  @Type(() => Number)
  subjectId: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  schoolSubjectStatus?: ESchoolSubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string
}

export class SequenceDto extends PickType(Question, ['id', 'sequence']) {}

export class UpdateSequenceDto {
  @ApiProperty({ type: [SequenceDto] })
  @Type(() => SequenceDto)
  @ValidateNested({ each: true })
  items: SequenceDto[]
}

export class FilterSubjectDto extends IntersectionType(QuerySubjectDto, SelectDto) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  schoolSubjectStatus?: ESchoolSubjectStatus

  schoolId?: number
  onlineAt?: number
  keyword?: string
}

export class FilterPlatformSubjectDto extends IntersectionType(
  QuerySubjectDto,
  SelectDto
) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESubjectStatus)
  status?: ESubjectStatus
}

export class FilterSchoolSubjectDto extends IntersectionType(QuerySubjectDto, SelectDto) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(ESchoolSubjectStatus)
  status?: ESchoolSubjectStatus
}

export class UpdateSubjectStatusDto extends PartialType(FilterPlatformSubjectDto) {
  @ApiProperty({ description: '修改的状态', enum: ESubjectStatus })
  @IsEnum(ESubjectStatus)
  toStatus: ESubjectStatus
}

export class UpdateSchoolSubject extends PickType(SchoolSubject, [
  'status',
  'onlineAt',
  'onlineType',
]) {}

export class UpdateAnswerTimeDto extends PickType(UserAnswer, [
  'id',
  'time',
  'scores',
  'answers',
  'badge',
  // 'maxBadge',
]) {}

export class QueryAdminAnswerDto extends IntersectionType(QueryTimeDto, PageRequest) {
  @ApiPropertyOptional({})
  @IsOptional()
  sortBy?:
  | 'className'
  | 'gradeName'
  | 'users'
  | 'times'
  | 'maxScores'
  | 'minScores'
  | 'gradeScores'
  | 'classScores'

  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}

export class TimesUserAnswerDto {
  @ApiProperty({ description: '学校ID或课题ID' })
  id: number

  @ApiProperty({ description: '人次' })
  times: number

  @ApiProperty({ description: '课题名称或学校名称' })
  name: MultiLanguage
}

export class UsersAnswerDto {
  @ApiProperty({ description: '学校ID或课题ID' })
  id: number

  @ApiProperty({ description: '人数' })
  users: number

  @ApiProperty({ description: '课题名称或学校名称' })
  name: MultiLanguage
}

export class TimesUserByDateDto {
  @ApiProperty()
  date: string

  @ApiProperty({ description: '人数' })
  users: number

  @ApiProperty({ description: '人次' })
  times: number
}

export class Top10SubjectsDto {
  @ApiProperty({ description: '课题ID' })
  id: number

  @ApiProperty({ description: '人次' })
  times: number

  @ApiProperty({ description: '课题名称' })
  name: MultiLanguage

  @ApiProperty({ description: '课题图片' })
  image: string
}

export class QuerySchoolUserAnswerDto extends IntersectionType(
  QueryTimeDto,
  PageRequest
) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  gradeId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  classId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional({})
  @IsOptional()
  sortBy?:
  | 'className'
  | 'gradeName'
  | 'users'
  | 'times'
  | 'maxScores'
  | 'minScores'
  | 'gradeScores'
  | 'classScores'

  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}

export class ExportDetailBySubjectDto extends QueryTimeDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  gradeId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  classId?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiPropertyOptional({})
  @IsOptional()
  sortBy?: 'users' | 'times' | 'maxScores' | 'minScores' | 'gradeScores'

  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}

export class ExportGroupBySchoolDto extends QueryTimeDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  sortBy?: 'times' | 'users'
}

function OmitPagination(c) {
  return OmitType(c, ['pageSize', 'pageIndex'] as any)
}

export class QuerySchoolUserAnswerExportDto extends OmitPagination(
  QuerySchoolUserAnswerDto
) {}
export class PatchSchoolSubjectDto extends IntersectionType(
  PickType(SchoolSubject, ['onlineAt', 'onlineType']),
  FilterSchoolSubjectDto
) {
  @ApiProperty({ description: '修改的状态', enum: ESchoolSubjectStatus })
  @IsEnum(ESchoolSubjectStatus)
  toStatus: ESchoolSubjectStatus
}

export class QueryClientSubjectDto {
  @ApiPropertyOptional({ description: '范畴ID' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  subjectCategoryId?: number
}

export class SubjectNoDto {
  @ApiProperty({ enum: ESubjectNo, isArray: true })
  serialNo: ESubjectNo[]
}

export class AnswerQuestionDto extends AnswerItem {
  @ApiProperty()
  @IsNumber()
  subjectId: number

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  questionIdx?: number
}

export class AnswerCountDto {
  @ApiProperty()
  @IsNumber()
  subjectId: number
}

export class MaxAnswerDto extends PickType(UserAnswer, ['badge', 'time', 'scores']) {
  @ApiProperty({ type: [QuestionAbility] })
  ability: QuestionAbility[]

  // @ApiProperty()
  // totalScores: number

  @ApiPropertyOptional()
  totalTime: number

  @ApiProperty()
  correctCount: number

  @ApiProperty()
  questionCount: number
}

export class LatestAnswerDto extends PickType(UserAnswer, ['badge', 'time']) {
  @ApiProperty()
  correctCount: number

  @ApiProperty()
  questionCount: number
}

export class SubjectWithAnswer extends SubjectDto {
  @ApiProperty({ type: MaxAnswerDto })
  answer: MaxAnswerDto

  @ApiProperty()
  questionCount: number
}

export class AnswerDto {
  @ApiProperty()
  isCorrect: boolean

  @ApiProperty({ type: [Options] })
  correctOptions: Options[]

  @ApiProperty()
  questionId: number

  @ApiProperty({ enum: EBadge, isArray: true })
  badge: EBadge[]

  @ApiProperty({ enum: EBadge, isArray: true })
  newBadge: EBadge[]

  @ApiProperty({ enum: EBadge })
  @IsOptional()
  comingBadge?: EBadge
}

export class QueryAnswersDto extends IntersectionType(QueryTimeDto, PageRequest) {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  subjectId?: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  userId?: number

  @ApiPropertyOptional({ examples: ['createdAt', 'scores'] })
  @IsOptional()
  sortBy?: string

  @ApiPropertyOptional({})
  @IsOptional()
  @IsEnum(EOrderDirection)
  orderDirection?: EOrderDirection
}

export class QueryAnswerStatisticsDto extends QueryTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  subjectId?: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  userId?: number

  @ApiProperty()
  @Type(() => Number)
  schoolId?: number
}

export class TimesAndUsersGroupByDateDto extends QueryTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  subjectId?: number

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  schoolId?: number
}

export class QueryTimesUsersGroupByDateAndSubjectDto extends QueryTimeDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  subjectId?: number
}

export class QueryAdminTimesUsersGroupByDateAndSubjectDto extends QueryTimesUsersGroupByDateAndSubjectDto {
  @ApiPropertyOptional()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  schoolId?: number
}

export const getSchoolSubjectStatus = (schoolSubject: SchoolSubject) => {
  switch (schoolSubject.status) {
    case ESchoolSubjectStatus.HIDDEN:
      return ESchoolSubjectStatus.HIDDEN
    case ESchoolSubjectStatus.ONLINE:
    // 根据上线时间显示上线或者待上线状态
    case ESchoolSubjectStatus.PRE_ONLINE:
      return schoolSubject.onlineAt && schoolSubject.onlineAt <= moment().unix()
        ? ESchoolSubjectStatus.ONLINE
        : ESchoolSubjectStatus.PRE_ONLINE
  }
}
