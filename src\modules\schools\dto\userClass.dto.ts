import { ApiProperty, ApiPropertyOptional, PartialType, PickType } from '@nestjs/swagger'
import { IsArray, IsNumber, ValidateNested } from 'class-validator'
import R from 'ramda'
import { Grade, UserClass } from '@/entities'

export class UserClassDto extends PickType(UserClass, ['id', 'class', 'gradeId']) {
  constructor(data: UserClass) {
    super()
    Object.assign(this, R.pick(['id', 'class', 'gradeId'], data))
  }
}

export const getUserClassDto = (data: UserClass) => new UserClassDto(data)

export class QueryUserClassDto extends PartialType(
  PickType(UserClass, ['gradeId', 'class'])
) {
  gradeIds?: number[]
}

export class CreateUserClassDto extends PickType(UserClass, [
  'class',
  'gradeId',
  'sequence',
]) {}

export class DeleteUserClassDto {
  @ApiProperty()
  @IsArray()
  @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
  classIds: number[]
}

export class CreateGradeDto extends PickType(Grade, ['grade', 'gradeCode']) {}

export class UpdateGradeDto extends PickType(Grade, ['grade', 'gradeCode']) {}

export class SortGradeItem extends PickType(Grade, ['id', 'sequence']) {}

export class SortGradeDto {
  @ApiProperty({ type: [SortGradeItem] })
  @ValidateNested()
  grades: SortGradeItem[]
}
