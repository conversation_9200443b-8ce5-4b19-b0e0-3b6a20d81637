export enum EOperationType {
  INVITE_ADMIN = 'INVITE_ADMIN',
  ACCEPT_INVITE = 'ACCEPT_INVITE',
  DELETE_ADMIN = 'DELETE_ADMIN',
  HIDE_BOOK = 'HIDE_BOOK',
  SHOW_BOOK = 'SHOW_BOOK',
  ADD_USER = 'ADD_USER',
  DELETE_USER = 'DELETE_USER',
  DISABLE_USER = 'DISABLE_USER',
  DISTRIBUTE_TIME = 'DISTRIBUTE_TIME',
  REVOKE_TIME = 'REVOKE_TIME',
  CREATE_PUBLISHER = 'CREATE_PUBLISHER',
  DELETE_PUBLISHER = 'DELETE_PUBLISHER',
  DISABLE_PUBLISHER = 'DISABLE_PUBLISHER',
  ENABLE_PUBLISHER = 'ENABLE_PUBLISHER',
  CREATE_AUTHOR = 'CREATE_AUTHOR',
  DELETE_AUTHOR = 'DELETE_AUTHOR',
  CREATE_SCHOOL = 'CREATE_SCHOOL',
  DELETE_SCHOOL = 'DELETE_SCHOOL',
}
