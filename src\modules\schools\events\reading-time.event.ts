export class ReadingTimeWarningEvent {
  constructor(
    public readonly userId: number,
    public readonly schoolId: number,
    public readonly platform: string,
    public readonly isSharingTime: boolean,
    public readonly totalQuota: number,
    public readonly usedQuota: number
  ) {}
}

export class MessageCreatedEvent {
  constructor(
    public readonly messageData: any,
    public readonly schoolId: number,
    public readonly createdBy: any
  ) {}
}
