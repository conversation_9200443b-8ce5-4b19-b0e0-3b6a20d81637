import { getExceptionGroup } from '@/common/decorators'
import { ExceptionGroup } from '@/common/enums'
import { BaseException } from '@/common/exceptions'

const SchoolExceptionMeta = getExceptionGroup(ExceptionGroup.SCHOOL)

@SchoolExceptionMeta(1, {
  en_us: 'school not exist',
  zh_HK: '學校不存在',
  zh_cn: '学校不存在',
})
export class SchoolNotExistException extends BaseException {}

@SchoolExceptionMeta(2, {
  en_us: 'class not exist',
  zh_HK: '班級不存在',
  zh_cn: '班级不存在',
})
export class UserClassNotExistException extends BaseException {}

@SchoolExceptionMeta(3, {
  en_us: 'class duplicated',
  zh_HK: '班級重複',
  zh_cn: '班级重复',
})
export class UserClassDuplicatedException extends BaseException {}

@SchoolExceptionMeta(4, {
  en_us: 'There are students in the class and cannot be deleted',
  zh_HK: '班級下有學生，無法進行刪除',
  zh_cn: '班级下有学生，无法进行删除',
})
export class ClassCanNotDeleteException extends BaseException {}

@SchoolExceptionMeta(5, {
  en_us: 'No reading hours at school, please purchase first',
  zh_HK: '學校無閱讀時數，請先購買',
  zh_cn: '学校无阅读时数，请先购买',
})
export class SchoolBalanceNotExistException extends BaseException {}

@SchoolExceptionMeta(6, {
  en_us: 'school reading time not enought',
  zh_HK: '閱讀時間不夠了',
  zh_cn: '阅读时间不够了',
})
export class SchoolTimeNotEnoughtException extends BaseException {}

@SchoolExceptionMeta(7, {
  en_us: 'no permission',
  zh_HK: '無權限',
  zh_cn: '无权限',
})
export class NoPermissionException extends BaseException {}

@SchoolExceptionMeta(8, {
  en_us: 'Your reading hours is insufficient, please contact your school administrator.',
  zh_HK: '您的閱讀時數餘額不足，請聯繫您學校的管理員。',
  zh_cn: '您的阅读时数余额不足，请联系您学校的管理员。',
})
export class NoReadingTimeLeftException extends BaseException {}

@SchoolExceptionMeta(9, {
  en_us: 'reading session not exist',
  zh_HK: '未開始閱讀',
  zh_cn: '未开始阅读',
})
export class ReadingSessionNotExistException extends BaseException {}

@SchoolExceptionMeta(10, {
  en_us: 'read record not exist',
  zh_HK: '閱讀記錄不存在',
  zh_cn: '阅读记录不存在',
})
export class ReadRecordNotExistException extends BaseException {}

@SchoolExceptionMeta(11, {
  en_us: 'no reading time',
  zh_HK: '無閱讀時間',
  zh_cn: '无阅读时间',
})
export class UserBalanceNotExistException extends BaseException {}

@SchoolExceptionMeta(12, {
  en_us: 'reading session error',
  zh_HK: '閱讀數據錯誤',
  zh_cn: '阅读数据错误',
})
export class ReadingSessionException extends BaseException {}

@SchoolExceptionMeta(13, {
  en_us: 'The deduction hours have exceeded the remaining allocable hours for the school',
  zh_HK: '扣減時間已超出該學校剩餘可分配時數',
  zh_cn: '扣减时间已超出该学校剩余可分配时数',
})
export class ReadingTimeNotEnoughtException extends BaseException {}

@SchoolExceptionMeta(14, {
  en_us: 'duplicate customize grade',
  zh_HK: '自定義年級重複',
  zh_cn: '自定义年级重复',
})
export class DuplicateGradeException extends BaseException {}

@SchoolExceptionMeta(15, {
  en_us: 'can not delete grade',
  zh_HK: '不能刪除年級',
  zh_cn: '不能删除年级',
})
export class DeleteGradeException extends BaseException {}

@SchoolExceptionMeta(16, {
  en_us: 'grade not exits',
  zh_HK: '班級不存在',
  zh_cn: '班级不存在',
})
export class GradeNotException extends BaseException {}

@SchoolExceptionMeta(17, {
  en_us: 'Reading hours are shared and cannot be assigned',
  zh_HK: '閱讀時數是共享的，不可分配',
  zh_cn: '阅读时数是共享的，不可分配',
})
export class ShareReadingTimeException extends BaseException {}

@SchoolExceptionMeta(18, {
  en_us: 'Reading hours are shared and cannot be revoke',
  zh_HK: '閱讀時數是共享的，不可回收',
  zh_cn: '阅读时数是共享的，不可回收',
})
export class ReadingTimeRevokeException extends BaseException {}

@SchoolExceptionMeta(19, {
  en_us: 'Message not exist',
  zh_HK: '通知不存在',
  zh_cn: '通知不存在',
})
export class MessageNotExistException extends BaseException {}

@SchoolExceptionMeta(20, {
  en_us: 'You have requested reading hours',
  zh_HK: '您已申請閱讀時數',
  zh_cn: '您已申请阅读时数',
})
export class DuplicateApplicationException extends BaseException {}

@SchoolExceptionMeta(21, {
  en_us: 'Reading hours request does not exist',
  zh_HK: '閱讀時數申請不存在',
  zh_cn: '阅读时数申请不存在',
})
export class ApplicationNotExistException extends BaseException {}

@SchoolExceptionMeta(22, {
  en_us: 'Reading hours request has finished',
  zh_HK: '閱讀時數申請已結束',
  zh_cn: '阅读时数申请已结束',
})
export class ApplicationFinishedException extends BaseException {}

@SchoolExceptionMeta(23, {
  en_us: 'Minimum allocation is 1 hour',
  zh_HK: '最少分配為1小时',
  zh_cn: '最少分配为1小时',
})
export class MiniumDistributeReadingTimeLimit extends BaseException {}

@SchoolExceptionMeta(24, {
  en_us: 'invalid reading time record',
  zh_HK: '無效閱讀時數記錄',
  zh_cn: '无效阅读时数记录',
})
export class InvalidReadingTimeReportException extends BaseException {}

@SchoolExceptionMeta(25, {
  en_us: 'This serial number has been used',
  zh_HK: '該合約编号已经存在',
  zh_cn: '该合同编号已经存在',
})
export class DuplicateContractException extends BaseException {}

@SchoolExceptionMeta(26, {
  en_us: 'The contract not exist',
  zh_HK: '該合約不存在',
  zh_cn: '该合同不存在',
})
export class ContractNotExistException extends BaseException {}

@SchoolExceptionMeta(27, {
  en_us: 'The number of copies of contract books must be greater than 0',
  zh_HK: '合約書籍副本數量需大於0',
  zh_cn: '合约书籍副本数量需大于0',
})
export class CopiesCountException extends BaseException {}

@SchoolExceptionMeta(28, {
  en_us: 'duplicate subject',
  zh_HK: '重複課題',
  zh_cn: '重复课题',
})
export class DuplicateSubjectException extends BaseException {}

@SchoolExceptionMeta(29, {
  en_us: 'duplicate question',
  zh_HK: '重複題目',
  zh_cn: '重复题目',
})
export class DuplicateQuestionException extends BaseException {}

@SchoolExceptionMeta(30, {
  en_us: 'subject not exist',
  zh_HK: '課題不存在',
  zh_cn: '课题不存在',
})
export class SubjectNotExistException extends BaseException {}

@SchoolExceptionMeta(31, {
  en_us: 'question not exist',
  zh_HK: '題目不存在',
  zh_cn: '题目不存在',
})
export class QuestionNotExistException extends BaseException {}

@SchoolExceptionMeta(32, {
  en_us: 'answer not exist',
  zh_HK: '答题不存在',
  zh_cn: '答案不存在',
})
export class AnswerNotExistException extends BaseException {}

@SchoolExceptionMeta(33, {
  en_us: 'duplicate question sequence',
  zh_HK: '重複題目順序',
  zh_cn: '重复题目顺序',
})
export class DuplicateQuestionSequenceException extends BaseException {}

@SchoolExceptionMeta(34, {
  en_us: 'duplicate system grade',
  zh_HK: '系统年級重複',
  zh_cn: '系统年级重复',
})
export class DuplicateGradeCodeException extends BaseException {}

@SchoolExceptionMeta(35, {
  en_us: 'Each book can have a maximum of 5 submitted reading reports',
  zh_HK: '閱讀感想已達上限',
  zh_cn: '阅读感想已达上限',
})
export class ReadingReflectionCountLimitException extends BaseException {}

@SchoolExceptionMeta(36, {
  en_us: 'The maximum number of characters is 2500',
  zh_HK: '字數已达上限2500',
  zh_cn: '字数已达上限2500',
})
export class ReadingReflectionMaxLimitException extends BaseException {}

@SchoolExceptionMeta(37, {
  en_us: 'Multiple-choice questions must have at least 3 options',
  zh_HK: '多選題必須設定至少3個選項',
  zh_cn: '多选题必须设置至少3个选项',
})
export class SubjectQuestionsMultipleException extends BaseException {}

@SchoolExceptionMeta(38, {
  en_us: 'Multiple-choice questions must have at least 2 correct answers',
  zh_HK: '多重選擇題必須設定至少2個正確答案',
  zh_cn: '多选题必须设置至少2个正确答案',
})
export class SubjectAnswerMultipleException extends BaseException {}
