import {  Module }from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import {
  Application,   Contract, ContractHistories, Grade, Message, Notification, ReadRecord,
  ReferenceBook, ReferenceReadRecord,
  School, SchoolBalance, ScienceContracts, Subject, SubjectCategory,
  Theme,   UserAnswer, UserAnswerCount, UserClass,
  ViewBookDetail,
} from '@/entities'
import { QuestionHistory } from '@/entities/questionHistory'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import { SchoolSubject } from '@/entities/schoolSubject.entity'
import { SubjectExtendBook } from '@/entities/subjectExtendBook'
import {
  IContractService, IGradeService,   IHomepageService, IReadingReflectionService, IReadingTimeManagerService, ISchoolDashboardService,
  ISchoolService,   IUserBalanceService, IUserClassService,
} from '@/modules/shared/interfaces'
import {ApplicationSchoolAdminController, BalanceAdminController, BalanceSchoolController, BookListAdminController,
  BookListClientController, ContractAdminController, ContractSchoolAdminController, GradeSchoolController, HomepageAdminController,
  HomepageClientController, MessageClientController, MessageSchoolController, NotificationAdminController, NotificationSchoolController,
  ReadingPosClientController, ReadingReflectionAdminController, ReadingReflectionClientController, ReadingReflectionSchoolController,
  ReadingTimeAdminController, ReadingTimeClientController, ReadingTimeSchoolController, ReferenceAdminController,
  ReferenceReadAdminController, ReferenceReadClientController, ReferenceReadSchoolController, SchoolAdminController,
  SchoolDataAdminController, SchoolHomepageSchoolController, SchoolPublicController, SchoolSchoolController, SubjectAdminController,
  SubjectCategoryAdminController, SubjectCategorySchoolController, SubjectClientController, SubjectSchoolController, SubjectStatsController,
  ThemeAdminController, UserAnswerAdminController, UserAnswerClientController, UserAnswerSchoolController, UserClassSchoolController,
  ViewBookDetailAdminController, ViewBookDetailClientController,
} from './controllers'
import { ReadRecordRepository } from './repositories'
import {
  ApplicationService,
  BookListService, ClearCacheService, ContractService,
  GradeService, HomepageService, NotificationService, QuestionService, ReadingReflectionBatchService,
  ReadingReflectionExportService,   ReadingReflectionQueryService, ReadingReflectionService,
  ReferenceReadStatisticService, ReferenceService, SchoolBalanceService, SchoolHomepageService,
  SchoolService, SubjectService, SubjectStatsService, UserAnswerService, UserBalanceService} from './services'
import {
  ReadingTimeManagerService, ReadingTimeService,
  SchoolDashboardService, SchoolSubjectService, UserClassService,
} from './services/index1'
import { MessageService, ReadingMessageService } from './services/index2'

@Module({
  exports: [
    ReadRecordRepository,
    SchoolBalanceService,
    UserBalanceService,
    SchoolService,
  ],
  providers: [
    // Application Services
    ApplicationService,

    // Book Services
    BookListService,

    // Cache Services
    ClearCacheService,

    // Contract Services
    ContractService,

    // Grade Services
    GradeService,

    // Homepage Services
    HomepageService,

    // Message Services
    MessageService,
    ReadingMessageService,

    // Notification Services
    NotificationService,

    // Question Services
    QuestionService,

    // Reading Reflection Services
    ReadingReflectionService,
    ReadingReflectionQueryService,
    ReadingReflectionExportService,
    ReadingReflectionBatchService,

    // Reading Time Services
    ReadingTimeService,
    ReadingTimeManagerService,

    // Reference Services
    ReferenceService,
    ReferenceReadStatisticService,

    // School Services
    SchoolService,
    SchoolBalanceService,
    SchoolDashboardService,
    SchoolHomepageService,
    SchoolSubjectService,

    // Subject Services
    SubjectService,
    SubjectStatsService,

    // User Services
    UserAnswerService,
    UserBalanceService,
    UserClassService,
    ReadingReflectionService,

    // Bridge Services
    SchoolService,
    HomepageService,

    // Repositories
    ReadRecordRepository,

    // Interface implementations
    { provide: IContractService, useClass: ContractService },
    { provide: IGradeService, useClass: GradeService },
    { provide: ISchoolService, useClass: SchoolService },
    { provide: IHomepageService, useClass: HomepageService },
    { provide: IUserBalanceService, useClass: UserBalanceService },
    { provide: IUserClassService, useClass: UserClassService },
    { provide: IReadingReflectionService, useClass: ReadingReflectionService },
    { provide: IReadingTimeManagerService, useClass: ReadingTimeManagerService },
    { provide: ISchoolDashboardService, useClass: SchoolDashboardService },
  ],



  imports: [
    TypeOrmModule.forFeature([
      Application, Message, School, SchoolBalance, ScienceContracts, ReadRecord,
      ReferenceBook, Subject, Theme, SubjectCategory, SchoolSubject,
      UserAnswer, UserAnswerCount, Notification, Grade, UserClass,
      Contract, ContractHistories, ReferenceReadRecord, ViewBookDetail,
      SubjectExtendBook, QuestionHistory, ReadingReflection, ReferenceReadingReflection,
    ]),
  ],
  controllers: [
    // Application Controllers
    ApplicationSchoolAdminController,

    // Balance Controllers
    BalanceAdminController,
    BalanceSchoolController,

    // BookList Controllers
    BookListAdminController,
    BookListClientController,

    // Contract Controllers
    ContractAdminController,
    ContractSchoolAdminController,

    // Grade Controllers
    GradeSchoolController,

    // Homepage Controllers
    HomepageAdminController,
    HomepageClientController,

    // Message Controllers
    MessageClientController,
    MessageSchoolController,

    // Notification Controllers
    NotificationAdminController,
    NotificationSchoolController,

    // Reading Position Controllers
    ReadingPosClientController,

    // Reading Reflection Controllers
    ReadingReflectionAdminController,
    ReadingReflectionClientController,
    ReadingReflectionSchoolController,

    // Reading Time Controllers
    ReadingTimeAdminController,
    ReadingTimeClientController,
    ReadingTimeSchoolController,

    // Reference Controllers
    ReferenceAdminController,
    ReferenceReadAdminController,
    ReferenceReadClientController,
    ReferenceReadSchoolController,

    // School Controllers
    SchoolAdminController,
    SchoolDataAdminController,
    SchoolHomepageSchoolController,
    SchoolPublicController,
    SchoolSchoolController,

    // Subject Controllers
    SubjectAdminController,
    SubjectCategoryAdminController,
    SubjectCategorySchoolController,
    SubjectClientController,
    SubjectSchoolController,
    SubjectStatsController,

    // Theme Controllers
    ThemeAdminController,

    // User Answer Controllers
    UserAnswerAdminController,
    UserAnswerClientController,
    UserAnswerSchoolController,

    // User Class Controllers
    UserClassSchoolController,

    // View Book Detail Controllers
    ViewBookDetailAdminController,
    ViewBookDetailClientController,
  ],

})
export class SchoolsModule {}
