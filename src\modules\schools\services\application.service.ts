import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import { Repository } from 'typeorm'
import { EmailService, isDev, isProduction, isStaging, PageRequest } from '@/common'
import { Application, Message } from '@/entities'
import { ApplicationStatus } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { ISchoolAdminRepo, IUserRepo } from '@/modules/shared/interfaces'
import { CreateApplicationDto, UpdateApplicationDto } from '../dto'
import {
  ApplicationFinishedException,
  ApplicationNotExistException,
  DuplicateApplicationException,
} from '../exception'
import { UserBalanceService } from './userBalance.service'

@Injectable()
export class ApplicationService {
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(Message) private readonly messageRepository: Repository<Message>,
    private readonly userBalanceService: UserBalanceService,
    private readonly emailService: EmailService,
    private readonly userService: IUserRepo,
    private readonly schoolAdminService: ISchoolAdminRepo
  ) {}

  async getApplicationByMessageId(messageId: number) {
    return this.applicationRepository.findOne({
      where: { messageId },
    })
  }

  async createApplication(data: CreateApplicationDto, user: any) {
    const duplicateApplication = await this.applicationRepository.findOne({ where: { messageId: data.messageId,
    } })
    if (duplicateApplication) {
      throw new DuplicateApplicationException()
    }

    const balance = await this.userBalanceService.getUserBalance(user.userId)

    const application = await this.applicationRepository.save({
      messageId: data.messageId,
      ctime: balance.totalQuota - balance.usedQuota,
      time: data.time,
      approvalTime: 0,
      userId: user.userId,
      schoolId: user.schoolId,
      status: ApplicationStatus.PENDING,
      createdBy: user,
    })

    const client = await this.userService.findOne({ where: { id: user.userId  } })
    const { items } = await this.schoolAdminService.listSchoolAdmins(user.schoolId, {
      pageIndex: 1,
      pageSize: 100,
      isRoot: true,
    })
    for (const admin of items) {
      await this.emailService.sendPrepared(admin.email, 'createReadingTimeAppliction', {
        name: `${client.givenName ?? ''}${client.familyName ?? ''}`,
        time: `${(application.time / 3600).toFixed(2)}`,
        email: client.email,
        applicationTime: moment
          .tz(application.createdAt, 'Asia/Hong_Kong')
          .format('DD/MM/YYYY HH:mm:ss A'),
        url: isProduction()
          ? 'https://school-admin.sjrc.club'
          : `https://school-admin-panel${
            isStaging() ? '-stg' : isDev() ? '-dev' : '-uat'
          }.trusive.hk`,
      })
    }

    await this.messageRepository.update(
      { id: data.messageId },
      {
        applicationId: application.id,
      }
    )
    return application
  }

  async getApplications(schoolId: number, query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const builder = this.applicationRepository
      .createQueryBuilder('application')
      .where({ schoolId })
    const total = await builder.getCount()
    const items = await builder
      .orderBy({ 'application.createdAt': 'DESC' })
      .take(pageSize)
      .skip((pageIndex - 1) * pageSize)
      .getMany()

    return { pageIndex, pageSize, items, total }
  }

  async updateApplication(id: number, data: UpdateApplicationDto, updatedBy: any) {
    const application = await this.applicationRepository.findOne({ where: { id  } })
    if (!application) {
      throw new ApplicationNotExistException()
    }

    if (application.status !== ApplicationStatus.PENDING) {
      throw new ApplicationFinishedException()
    }

    await this.applicationRepository.update(
      { id },
      {
        approvalTime: data.approvalTime,
        status: data.status,
        updatedBy,
        remarks: data.remarks ?? null,
      }
    )
    return { ...application, ...data, updatedBy }
  }
}
