import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import { Repository } from 'typeorm'
import { RedisService } from '@/common'
import { REDIS_KEY } from '@/common/constants'
import { ReadRecord } from '@/entities'
import {
  getReadingRecordCacheKey,
  getReadingUsersSetName,
  getReadRecordSetName,
  getRedisKey,
  getUserBalanceCacheKey,
} from '../../constants'

@Injectable()
export class ClearCacheService {
  constructor(
    @InjectRepository(ReadRecord) private readonly model: Repository<ReadRecord>,
    private readonly redisService: RedisService
  ) {}

  async count() {
    const records = await this.model.query(
      `select id, user_id, unix_timestamp(created_at) as created_at, unix_timestamp(updated_at) as updated_at, reading_time, school_id as schoolId from read_record where unix_timestamp(updated_at) > ${
        moment().unix() - 20 * 60
      } order by created_at desc`
    )

    console.log('count records:', records.length)

    console.log(records[0])

    const expireRecords = records.filter(
      (item) =>
        Number(item.created_at) + item.reading_time + 600 < Number(item.updated_at)
    )

    const pipe = this.redisService.pipeline()
    expireRecords.forEach((item) => {
      pipe.del(`${REDIS_KEY}.${getReadingRecordCacheKey(item.user_id, item.id)}`)
      pipe.srem(
        getReadRecordSetName(item.schoolId),
        getReadingRecordCacheKey(item.user_id, item.id)
      )
    })

    const users = await this.model.query(`
      select
        user_id,
        school_id
      from
        read_record
      where
        user_id in (
          select
            user_id
          from
            user_balances
          where
            unix_timestamp(updated_at) > ${moment().unix() - 20 * 60}
        )
        and unix_timestamp(updated_at) < ${moment().unix() - 20 * 60}
  `)

    const userIds = [...new Set(users.map((item) => item.user_id))]

    const expireUserBalances = userIds.map((userId) =>
      users.find((item) => item.user_id === userId)
    )

    expireUserBalances.forEach((item) =>
      pipe.srem(getReadingUsersSetName(item.school_id), item.user_id)
    )

    const res = await pipe.exec()
    // console.log(res)
    console.log('expire records:', expireRecords.length)
    console.log('expire user balance', expireUserBalances.length)
    // console.log(expireRecords[0])
  }

  async balance() {
    const data = await this.model.query(
      `select * from logs where operation = 'user balance error'`
    )
    console.log(data)
    if (data.length) {
      const users = [...new Set(data.map((item) => item.operator.userId))]
      console.log(users)
      const pipe = this.redisService.pipeline()
      users.map((userId) =>
        pipe.del(`${getRedisKey(getUserBalanceCacheKey(userId as number))}`)
      )
      await pipe.exec()
    }
    await this.model.query(`delete from logs where operation = 'user balance error'`)
  }
}
