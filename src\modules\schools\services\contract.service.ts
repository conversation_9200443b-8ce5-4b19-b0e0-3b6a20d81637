import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { isInt } from 'class-validator'
import Excel from 'exceljs'
import R from 'ramda'
import { Readable } from 'stream'
import { In, Like, Repository } from 'typeorm'
import { ELocaleType, ExcelService, PageRequest, SessionService } from '@/common'
import {
  Author,
  Book,
  Contract,
  ContractBook,
  ContractHistories,
  ReferenceBook,
  School,
} from '@/entities'
import { EBookVersion, EContractStatus, EOrderDirection, ESchoolVersion } from '@/enums'
import { FileException } from '@/modules/account/exception'
import { PAGE_SIZE } from '@/modules/constants'
import { IBookRepo,IBookService, IContractService, IInitLeaderBoardService, ILeaderBoardService } from '@/modules/shared/interfaces'
import { OperationLogService } from '@/modules/system'
import { getCellValue } from '@/utils'
import { getISBN, getName } from '@/utils/book.utitl'
import {
  CreateContractDto,
  ListContractsRequest,
  PublishContractDto,
  UpdateContractDto,
} from '../dto'
import {
  ContractNotExistException,
  CopiesCountException,
  DuplicateContractException,
  SchoolNotExistException,
} from '../exception'

@Injectable()
export class ContractService implements IContractService {
  constructor(
    @InjectRepository(Book)
    private readonly bookModel: Repository<Book>,
    @InjectRepository(Author)
    private readonly authorRepositories: Repository<Author>,
    @InjectRepository(Contract)
    private readonly contractRepositories: Repository<Contract>,
    @InjectRepository(ReferenceBook)
    private readonly referenceBookRepositories: Repository<ReferenceBook>,
    @InjectRepository(School)
    private readonly schoolRepositories: Repository<School>,
    @InjectRepository(ContractBook)
    private readonly contractBookRepositories: Repository<ContractBook>,
    @InjectRepository(ContractHistories)
    private readonly contractHistoriesRepositories: Repository<ContractHistories>,
    private readonly excelService: ExcelService,
    private readonly sessionService: SessionService,
    private readonly initLeaderBoardService: IInitLeaderBoardService,
    private readonly logService: OperationLogService,
    private readonly bookRepositories: IBookRepo,
    private readonly bookService: IBookService,
    private readonly leaderBoardService: ILeaderBoardService
  ) {}

  async getPublishedBooksAndCopiesCount(schoolId: number) {
    const result = await this.referenceBookRepositories.query(`
    select  count(*) as bookCount, ifnull(sum(copies_count), 0) as copiesCount from reference_books where school_id = ${schoolId};
    `)
    const bookCountBeforeUpdated = Number(result[0].bookCount)
    const copiesCountBeforeUpdated = Number(result[0].copiesCount)

    return {
      bookCountBeforeUpdated,
      copiesCountBeforeUpdated,
    }
  }

  // async getDraftContract(schoolId: number, query: PageRequest) {
  //   const contract = await this.contractRepositories.findOne({
  //     where: {
  //       school: { id: schoolId },
  //       status: EContractStatus.DRAFT,
  //     },
  //     order: { createdAt: 'DESC' },
  //   })
  //   if (!contract)
  //     throw new NotFoundException(`Draft contract not found, schoolId: ${schoolId}`)

  //   const { pageIndex = 1, pageSize = PAGE_SIZE } = query
  //   const contractBooks = await this.contractBookRepositories.find({
  //     where: {
  //       contract: { id: contract.id },
  //     },
  //     relations: ['book', 'book.authors'],
  //     skip: (pageIndex - 1) * pageSize,
  //     take: pageSize,
  //     withDeleted: true,
  //   })

  //   return {
  //     ...contract,
  //     contractBooks: contractBooks.map((item) => ({
  //       ...R.pick(['id', 'name', 'coverUrl'], item.book),
  //       isbn: getISBN(item.book.isbn),
  //       authors: item.book.authors?.map((author) => R.pick(['id', 'name'], author)),
  //       copiesCount: item.copiesCount,
  //     })),
  //   }
  // }

  async hasDraftContract(schoolId: number) {
    const contract = await this.contractRepositories.findOne({
      where: {
        school: { id: schoolId },
        status: EContractStatus.DRAFT,
      },
    })
    return !!contract
  }

  async hasContract(schoolId: number) {
    const contract = await this.contractRepositories.findOne({
      where: {
        school: { id: schoolId },
      },
    })
    return !!contract
  }

  async getContract(id: number, keyword?: string) {
    const contract = await this.contractRepositories.findOne({
      where: {
        id,
      },
      relations: ['contractBooks', 'contractBooks.book'],
      withDeleted: true,
    })

    if (!contract) {throw new ContractNotExistException()}

    let ids = contract.contractBooks.map((item) => item.book.id)
    if (keyword) {
      const data = await this.bookService.searchBookIds(
        { ids, keyword },
        { withDeleted: true }
      )
      ids = data
    }

    const books = await this.bookRepositories.listBooks(
      {
        ids,
      },
      {
        withDeleted: true,
        relations: ['authors'],
        fields: ['id', 'isbn', 'coverUrl', 'name'],
      }
    )

    return {
      ...contract,
      contractBooks: contract.contractBooks
        .map((contractBook) => {
          const book = books.find((b) => b.id === contractBook.book.id)

          return book
            ? {
                ...R.pick(['id', 'name', 'authors', 'coverUrl'], book),
                isbn: getISBN(book.isbn),
                copiesCount: contractBook.copiesCount,
              }
            : undefined
        })
        .filter((x) => !R.isNil(x)),
    }
  }

  async createContract(
    data: CreateContractDto,
    options: {
      admin: Record<string, any>
    }
  ) {
    const { admin = {} } = options || {}

    const { serialNumber, bookCount, copiesCount, books, schoolId } = data

    if (new Set(books.map((item) => item.bookId)).size !== books.length) {
      throw new BadRequestException('书籍重复')
    }

    const exist = await this.contractRepositories.findOne({
      where: { serialNumber: data.serialNumber },
    })
    if (exist) {throw new DuplicateContractException()}

    const school = await this.schoolRepositories.findOne({
      where: {
        id: schoolId,
      },
      select: ['id'],
    })
    if (!school) {throw new BadRequestException('学校不存在')}

    const contract = new Contract({
      serialNumber,
      bookCount,
      copiesCount,
      status: EContractStatus.DRAFT,
      school,
      createdBy: R.pick(
        [
          'id',
          'userId',
          'familyName',
          'givenName',
          'displayName',
          'profileImage',
          'email',
        ],
        admin
      ),
    })

    contract.bookCountBeforeUpdated = new Set(data.books.map((item) => item.bookId)).size
    contract.copiesCountBeforeUpdated = data.books.reduce(
      (acc, item) => acc + item.copiesCount,
      0
    )

    if (books && books.length > 0) {
      contract.contractBooks = books.map(
        (item) =>
          new ContractBook({
            book: { id: item.bookId } as Book,
            copiesCount: item.copiesCount,
          })
      )
    }

    // await this.createUpdateContractOperationLog(undefined, contract, options.admin)

    return this.contractRepositories.save(contract)
  }

  // 每次发布前，会调用update 更新合约，只有已发布的合约需要记录操作日志
  async createUpdateContractOperationLog(
    originalContract: Contract,
    contract: Contract,
    bookIds: number[],
    admin: any
  ) {
    if (contract.status !== EContractStatus.PUBLISHED) {return}

    const deleted = (originalContract?.contractBooks || []).filter(
      (x) => !(contract?.contractBooks || []).some((y) => y.book.id === x.book.id)
    )
    const added = (contract?.contractBooks || []).filter(
      (x) =>
        !(originalContract?.contractBooks || []).some((y) => y.book.id === x.book.id)
    )
    const updated = (contract?.contractBooks || []).filter((x) => {
      const original = (originalContract?.contractBooks || []).find(
        (y) => y.book.id === x.book.id
      )
      return original && original.copiesCount !== x.copiesCount
    })

    let histories = []
    if (added.length) {
      histories = added.map((item) => ({
        serialNumber: contract.serialNumber,
        book: { id: item.book.id },
        bookCount: 1,
        copiesCount: item.copiesCount,
        school: { id: contract.school.id },
        contract: { id: contract.id },
        createdBy: R.pick(
          [
            'id',
            'userId',
            'familyName',
            'givenName',
            'displayName',
            'profileImage',
            'email',
          ],
          admin
        ),
      }))
      const books = await this.bookRepositories.listBooks(
        {
          ids: added.slice(0, 2).map((x) => x.book.id),
        },
        {
          fields: ['id', 'name'],
        }
      )
      await this.logService.createLog({
        user: admin,
        operation: `添加了${contract.serialNumber}合約的${books
          .map((item) => `《${getName(item.name)}》`)
          .join(' ')}${
          added.length > 2 ? `等${added.length}本` : ''
        }書籍，複本數量${R.sum(added.map((x) => x.copiesCount))}本。`,
        metaData: {
          serialNumber: contract.serialNumber,
          books: added.map((item) => ({
            bookId: item.book.id,
            copiesCount: item.copiesCount,
          })),
        },
      })
    }

    if (deleted.length) {
      histories = histories.concat(
        deleted.map((item) => ({
          serialNumber: contract.serialNumber,
          book: { id: item.book.id },
          bookCount: -1,
          copiesCount: -item.copiesCount,
          school: { id: contract.school.id },
          contract: { id: contract.id },
          createdBy: R.pick(
            [
              'id',
              'userId',
              'familyName',
              'givenName',
              'displayName',
              'profileImage',
              'email',
            ],
            admin
          ),
        }))
      )

      const removeBooks = R.difference(
        deleted.map((item) => item.book.id),
        bookIds
      )
      if (removeBooks.length)
      {await this.removeBookFromPublishedContract(contract.school.id, removeBooks)}

      const books = await this.bookRepositories.listBooks(
        {
          ids: deleted.slice(0, 2).map((x) => x.book.id),
        },
        { fields: ['id', 'name'] }
      )
      await this.logService.createLog({
        user: admin,
        operation: `删除了${contract.serialNumber}合約的${books
          .map((item) => `《${getName(item.name)}》`)
          .join(' ')}${deleted.length > 2 ? `等${deleted.length}本` : ''}書籍。`,
        metaData: {
          serialNumber: contract.serialNumber,
          books: deleted.map((item) => item.book.id),
        },
      })
    }

    if (updated.length) {
      histories = histories.concat(
        updated.map((item) => {
          const original = originalContract.contractBooks.find(
            (o) => o.book.id === item.book.id
          )
          return {
            serialNumber: contract.serialNumber,
            bookCount: 0,
            book: { id: item.book.id },
            copiesCount: item.copiesCount - original.copiesCount,
            school: { id: contract.school.id },
            contract: { id: contract.id },
            createdBy: R.pick(
              [
                'id',
                'userId',
                'familyName',
                'givenName',
                'displayName',
                'profileImage',
                'email',
              ],
              admin
            ),
          }
        })
      )

      const books = await this.bookRepositories.listBooks(
        {
          ids: updated.slice(0, 2).map((x) => x.book.id),
        },
        { fields: ['id', 'name'] }
      )

      await this.logService.createLog({
        user: admin,
        operation: `修改了${contract.serialNumber}合約的${books
          .sort((a, b) => a.id - b.id)
          .map((item) => `《${getName(item.name)}》`)
          .join(' ')}${updated.length > 2 ? `等` : ''}書籍的副本數量，為${updated
          .slice(0, 2)
          .sort((a, b) => a.book.id - b.book.id)
          .map((item) => `${item.copiesCount}本`)
          .join('、')}${updated.length > 2 ? `等` : ''}`,
        metaData: {
          serialNumber: contract.serialNumber,
          books: updated.map((item) => ({
            bookId: item.book.id,
            copiesCount: item.copiesCount,
          })),
        },
      })
    }

    if (histories.length) {await this.contractHistoriesRepositories.save(histories)}
  }

  async updateContract(id: string, data: UpdateContractDto, options?: { admin: any }) {
    const { bookCount, copiesCount, books } = data

    if (new Set(books.map((item) => item.bookId)).size !== books.length) {
      throw new BadRequestException('书籍重复')
    }

    const original = await this.contractRepositories.findOne({
      where: { id: Number(id) },
      // relations: ['school', 'contractBooks', 'contractBooks.book'],
    })
    if (!original) {throw new ContractNotExistException()}

    const contract = await this.contractRepositories.findOne({
      where: { id: Number(id) },
      relations: ['school', 'contractBooks', 'contractBooks.book'],
      withDeleted: true,
    })

    const originalContract = JSON.parse(JSON.stringify(contract))

    if (contract.status !== EContractStatus.PUBLISHED) {
      if (bookCount) {contract.bookCount = bookCount}
      if (copiesCount) {contract.copiesCount = copiesCount}
      if (data.serialNumber) {contract.serialNumber = data.serialNumber}
    }

    // 合约现有书籍数量及副本数量
    contract.bookCountBeforeUpdated = new Set(data.books.map((item) => item.bookId)).size
    contract.copiesCountBeforeUpdated = data.books.reduce(
      (acc, item) => acc + item.copiesCount,
      0
    )

    if (books && books.length > 0) {
      contract.contractBooks = books.map(
        (item) =>
          new ContractBook({
            book: { id: item.bookId } as Book,
            copiesCount: item.copiesCount,
          })
      )
    }

    await this.contractRepositories.save(contract)

    await this.createUpdateContractOperationLog(
      originalContract,
      contract,
      books.map((item) => item.bookId),
      options.admin
    )

    // 若是发布后再次更新，更新完后直接发布
    // if (contract.status === EContractStatus.PUBLISHED) {
    //   await this.publishContract(contract.id, options)
    // }

    return this.getContract(contract.id)
  }

  async publishVersion(data: PublishContractDto, options: { admin: any }) {
    const school = await this.schoolRepositories.findOne({ where: { id: data.schoolId } })
    if (!school) {throw new SchoolNotExistException()}

    if (
      data.contractId &&
      [ESchoolVersion.REFERENCE, ESchoolVersion.SUBSCRIPTION_REFERENCE].includes(
        data.version
      )
    ) {
      await this.publishContract(data.contractId, options)
    }

    if (school.version !== data.version) {
      school.version = data.version
      switch (data.version) {
        case ESchoolVersion.SUBSCRIPTION:
          // await this.contractRepositories.query(
          //   `update contracts set status = '${EContractStatus.INVALID}' where school_id = ${school.id}`,
          // )
          school.referenceBooks = null
          break
        case ESchoolVersion.REFERENCE:
          break
        case ESchoolVersion.SUBSCRIPTION_REFERENCE:
          break
      }
      await this.schoolRepositories.save(school)
      const users = await this.schoolRepositories.query(
        `select user_id as userId from users where school_id = ${school.id}`
      )
      const userIds = users.map((user) => user.userId)
      await this.sessionService.deleteSessions(userIds)
    }
  }

  async publishContract(id: number, options: { admin: any }) {
    const { admin = {} } = options || {}
    const contract = await this.contractRepositories.findOne({
      where: {
        id,
      },
      relations: ['school'],
    })
    if (!contract) {throw new ContractNotExistException()}

    const isFirstPublish = contract.status !== EContractStatus.PUBLISHED

    const books = await this.contractBookRepositories.find({
      where: {
        contract: { id },
        copiesCount: 0,
      },
    })

    if (books.length) {throw new CopiesCountException()}

    const school = await this.schoolRepositories.findOne({
      where: {
        id: contract.school.id,
      },
    })
    if (!school) {throw new SchoolNotExistException()}

    contract.status = EContractStatus.PUBLISHED
    contract.publishedAt = new Date()
    contract.publishedBy = R.pick(
      ['id', 'userId', 'familyName', 'givenName', 'displayName', 'profileImage', 'email'],
      admin
    )

    await this.contractRepositories.save(contract)

    const items = await this.contractRepositories.query(`
    select t1.book_id as bookId, ifnull(sum(t1.copies_count),0) as copiesCount 
    from contract_books t1 left join contracts t2 on t1.contract_id = t2.id 
    where t2.school_id = ${school.id} and t2.status = '${EContractStatus.PUBLISHED}' group by book_id;`)

    console.log('items: ', items) 
    if (items && items.length > 0)
    {school.referenceBooks = items.map(
      (item: { bookId: string; copiesCount: string }) =>
        new ReferenceBook({
          book: {
            id: Number(item.bookId),
          } as Book,
          copiesCount: Number(item.copiesCount),
        })
    )}

    await this.schoolRepositories.save(school)

    await this.initLeaderBoardService.syncReferenceRanking(school.id)

    if (isFirstPublish) {
      const contract = await this.contractRepositories.findOne({
        where: {
          id,
        },
        relations: ['school', 'contractBooks', 'contractBooks.book'],
      })
      await this.createUpdateContractOperationLog(
        undefined,
        contract,
        undefined,
        options.admin
      )
    }

    return contract
  }

  async listContracts(query: ListContractsRequest) {
    const {
      pageIndex = 1,
      pageSize = PAGE_SIZE,
      schoolId,
      orderDirection = EOrderDirection.DESC,
    } = query
    const [items, total] = await this.contractRepositories.findAndCount({
      where: { school: { id: Number(schoolId) } },
      order: { createdAt: orderDirection },
      relations: ['school'],

      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    })

    return { items, total, pageIndex, pageSize }
  }

  async fillContractBooks(
    items: {
      isbn: string
      bookName: string
      rowNumber: number
      copiesCount: number
    }[],
    local: ELocaleType = ELocaleType.EN_UK
  ) {
    const errors = []
    const filledItems = []

    const books = await this.bookModel.find({
      where: {
        isbn: In(items.map((item) => String(item.isbn))),
        version: Like(`%${EBookVersion.REFERENCE}%`),
      },
      select: ['id', 'name', 'authorIds', 'isbn', 'coverUrl'],
    })

    const authors = await this.authorRepositories.find({
      where: {
        id: In(R.flatten(books.map((x) => x.authorIds))),
      },
      select: ['id', 'name'],
    })

    for (const item of items) {
      if (R.isNil(item.isbn) || item.isbn.length <= 0)
      {errors.push({
        rowNumber: item.rowNumber,
        message: 'ISBN不能為空',
      })}

      if (R.isNil(item.bookName) || item.bookName.length <= 0)
      {errors.push({
        rowNumber: item.rowNumber,
        message: '書籍名稱不能為空',
      })}

      if (item.copiesCount <= 0)
      {errors.push({
        rowNumber: item.rowNumber,
        message: '副本數量必須大於0',
      })}

      if (isInt(item.copiesCount) === false) {
        errors.push({
          rowNumber: item.rowNumber,
          message: '副本數量必須是正整數',
        })
      }

      const book = books.find((x) => x.isbn === item.isbn)
      if (!book) {
        errors.push({
          rowNumber: item.rowNumber,
          message: `找不到ISBN为${item.isbn}的书籍`,
        })
        continue
      }

      //todo zh_cn cindy
      if (book.name.en_uk !== item.bookName && book.name.zh_HK !== item.bookName) {
        errors.push({
          rowNumber: item.rowNumber,
          message: `书籍名称不匹配`,
        })
        continue
      }
      filledItems.push({
        ...item,
        bookId: book.id,
        isbn: book.isbn,
        name: book.name,
        coverUrl: book.coverUrl,
        authorNames: authors.map((author) => author.name[local]),
      })
    }
    return {
      errors,
      filledBooks: filledItems,
    }
  }

  async downloadTemplateFile(local: ELocaleType = ELocaleType.EN_UK) {
    return this.excelService.buildExcel([
      {
        name: `bulkUploadContractBookInformation.${local}`,
        data: [
          {
            isbn: '例: 9789888599356',
            bookName: '例: 自遊人生,旅居藍圖',
            copiesCount: '100',
          },
        ],
      },
    ])
  }

  async uploadBookTemplateFile(file: Express.Multer.File, local: ELocaleType) {
    const workbook = new Excel.Workbook()

    try {
      await workbook.xlsx.load(file.buffer)
    } catch (error) {
      try {
        const stream = Readable.from(file.buffer)
        await workbook.csv.read(stream)
      } catch (err) {
        throw new FileException()
      }
    }

    const items = []
    workbook.getWorksheet(1).eachRow((row, rowNumber) => {
      if (rowNumber !== 1)
      {items.push({
        rowNumber,
        isbn: getCellValue(row.getCell(1)),
        bookName: getCellValue(row.getCell(2)),
        copiesCount: getCellValue(row.getCell(3)),
      })}
    })

    const { errors, filledBooks } = await this.fillContractBooks(
      items.map((item) => ({
        ...item,
        isbn: !R.isNil(item.isbn) ? String(item.isbn) : null,
        copiesCount: !R.isNil(item.copiesCount) ? Number(item.copiesCount) : 0,
      })),
      local
    )

    if (errors.length) {return { errors, books: undefined }}

    return { errors: undefined, books: filledBooks }
  }

  async count(schoolId: number) {
    const [contractData, bookData] = await Promise.all([
      this.contractRepositories.query(
        `select sum(book_count) as count, sum(copies_count) as books from contracts where school_id = ${schoolId} and status = '${EContractStatus.PUBLISHED}'`
      ),
      this.contractBookRepositories.query(
        `select count(distinct(book_id)) as count, sum(copies_count) as books from reference_books where school_id = ${schoolId}`
      ),
    ])
    return {
      contractBookCount: contractData[0].count,
      contractBookCopiesCount: contractData[0].books,
      referenceBookCount: bookData[0].count,
      referenceBookCopiesCount: bookData[0].books,
    }
  }

  // private async updateSchoolContractBooks(schoolId: number) {
  //   const contracts = await this.contractRepositories.query(`
  //     select
  //       contract_books.book_id as id,
  //       sum(contract_books.copies_count) as copiesCount
  //     from
  //       contract
  //       inner join contract_books on contract.id = contract_books.contract_id
  //     where
  //       contract.school_id = ${schoolId}
  //       and status = '${EContractStatus.PUBLISHED}'
  //     group by
  //       contract_books.book_id
  //   `)

  //   const limit = pLimit(100)
  //   await Promise.all(
  //     contracts.map((item) =>
  //       limit(() =>
  //         this.contractRepositories.query(`
  //           INSERT INTO
  //             reference_books (school_id, book_id, copies_count)
  //           VALUES
  //             (${schoolId}, ${item.id}, ${item.copiesCount}) ON DUPLICATE KEY
  //           UPDATE
  //             copies_count = ${item.copiesCount}
  //       `),
  //       ),
  //     ),
  //   )

  //   await this.contractRepositories.query(`
  //     delete from
  //       reference_books
  //     where
  //       school_id = ${schoolId}
  //       and book_id not in (${contracts.map((item) => item.id).join(',')})
  //   `)
  // }

  private async removeBookFromPublishedContract(schoolId: number, bookIds: number[]) {
    const homepages = await this.contractBookRepositories.query(
      `select id from school_homepage where school_id = ${schoolId} and version = '${ESchoolVersion.REFERENCE}'`
    )
    if (homepages.length && bookIds.length) {
      await this.contractRepositories.query(`
        delete from
          books_school_homepages_school_homepage
        where
          school_homepage_id in (${homepages.map((item) => item.id).join(',')})
          and books_id in (${bookIds.join(',')})
      `)
    }

    await this.contractBookRepositories.query(`
      delete from
        school_homepage
      where
        id In(
          select
            id
          from
            (
              select
                school_homepage.id,
                IFNULL(t.books, 0) as books
              from
                school_homepage
                left join (
                  select
                    count(*) as books,
                    school_homepage_id as id
                  from
                    books_school_homepages_school_homepage
                  group by
                    school_homepage_id
                ) as t on t.id = school_homepage.id
              where
                school_homepage.type is not null
            ) as list
          where
            books = 0
        );
    `)

    if (bookIds.length) {
      await this.contractBookRepositories.query(
        `delete from bookshelves where book_id IN (${bookIds.join(',')}) and version = '${
          EBookVersion.REFERENCE
        }'`
      )
      await this.leaderBoardService.removeBooksFromReference(bookIds, [schoolId])
    }
  }
}
