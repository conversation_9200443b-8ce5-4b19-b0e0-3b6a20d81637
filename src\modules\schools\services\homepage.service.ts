import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Inject, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Cache } from 'cache-manager'
import moment from 'moment' 
import R from 'ramda'
import { DataSource, In, Repository } from 'typeorm'
import { ELocaleType, ExcelService, PageRequest } from '@/common'
import { Homepage } from '@/entities'
import { EListStatus, EUserType, SchoolType } from '@/enums'
import {
  getSubscriptionHomepageKey,
  getSubscriptionRecommendKey,
  PAGE_SIZE,
} from '@/modules/constants'
import { IBookRepo, IHomepageService,ILeaderBoardService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { getISBN, getName } from '@/utils/book.utitl'
import { CreateHomepageDto, getBookDto, modifyHomepageField } from '../../books/dto'
import { filterHomageBooks } from '../../books/utils/hompage.util'
import { homepageValidator } from '../../books/validators'
import { BookListService } from './bookList.service'
import { ReferenceReadStatisticService } from './referenceRead.statistic.service'

@Injectable()
export class HomepageService implements IHomepageService{
  constructor(
    @InjectRepository(Homepage) private readonly homepageRepository: Repository<Homepage>,
    private readonly bookRepositories: IBookRepo,
    private readonly bookListService: BookListService,
    private readonly leaderBoardService: ILeaderBoardService, // private readonly initLeaderBoardService: InitLeaderBoardService,
    private readonly excelService: ExcelService,
    private readonly referenceStatisticService: ReferenceReadStatisticService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly logService: LogService,
    private readonly dataSource: DataSource
  ) {}

  async sortHomepageBooks(homepage: Homepage) {
    const map = await this.getHomepageBookSorts(homepage.id)
    return {
      ...homepage,
      books: homepage.books.sort((a, b) => map[a.id] - map[b.id] || 0),
    }
  }

  async getHomepageBookSorts(id: number): Promise<{ [x: number]: number }> {
    const sorts = await this.homepageRepository
      .query(`select t1.books_id as book_id, @rownum:=@rownum+1 as sort from books_homepages_homepage t1, (SELECT (@rowNum :=0) ) t2 where t1.homepage_id = ${id} order by created_at desc;
    `)
    return (sorts || []).reduce((response, item) => {
      response[Number(item.book_id)] = Number(item.sort)
      return response
    }, {})
  }

  async createHomepage(data: CreateHomepageDto): Promise<Homepage> {
    const homepage = R.pick(modifyHomepageField, data)
    if (data.bookIds?.length) {
      const books = await this.bookRepositories.searchBooks({
        ids: [...new Set(data.bookIds)],
      })
      homepage.books = books
    }
    if (data.bookListId) {
      const booklist = await this.bookListService.getBookList({ id: data.bookListId })
      homepage.booklist = booklist
    }

    return this.homepageRepository.save(homepage)
  }

  async listClientHomepage(levels?: number[]): Promise<Homepage[]> {
    const homepages = await this.homepageRepository.find({
      where: { status: EListStatus.ONLINE, isPermanent: false },
      relations: [
        'books',
        'books.labels',
        'books.authors',
        'books.bookLevels',
        'booklist',
        'booklist.books',
        'booklist.books.labels',
        'booklist.books.authors',
        'booklist.books.bookLevels',
      ],
      order: { sort: 'ASC' },
    })
    return homepages
      .map((item) => filterHomageBooks(item, levels))
      .filter((item) => !R.isNil(item)) as Homepage[]
  }
  async listClientHomepageById(id: number, levels?: number[]) {
    const homepage = await this.homepageRepository.findOne({
      where: { id },
      relations: [
        'books',
        'books.labels',
        'books.authors',
        'books.bookLevels',
        'booklist',
        'booklist.books',
        'booklist.books.labels',
        'booklist.books.authors',
        'booklist.books.bookLevels',
      ],
      order: { sort: 'ASC' },
    })

    if (homepage) {
      const filteredHomepage = filterHomageBooks(homepage, levels)
      if (!R.isNil(filteredHomepage)) {
        return filteredHomepage as Homepage
      }
    }
    return null
  }
  async getHomepageWithRelation(id: number) {
    const homepage = await this.homepageRepository.findOne({
      where: { id },
      relations: [
        'books',
        'books.labels',
        'books.authors',
        'books.bookLevels',
        'booklist',
        'booklist.books',
        'booklist.books.labels',
        'booklist.books.authors',
        'booklist.books.bookLevels',
      ],
    })
    homepageValidator(homepage).exist()
    return homepage
  }

  async getTodayHomepageWithRelation() {
    const defaultHomepage = await this.homepageRepository.findOne({
      where: { id: 1 },
      relations: [
        'books',
        'books.labels',
        'books.authors',
        'books.bookLevels',
        'booklist',
        'booklist.books',
        'booklist.books.labels',
        'booklist.books.authors',
        'booklist.books.bookLevels',
      ],
    })
    homepageValidator(defaultHomepage).exist()
    const otherHomepage = await this.homepageRepository.find({
      where: { parentId: 1 },

      relations: [
        'books',
        'books.labels',
        'books.authors',
        'books.bookLevels',
        'booklist',
        'booklist.books',
        'booklist.books.labels',
        'booklist.books.authors',
        'booklist.books.bookLevels',
      ],
      order: { sort: 'ASC' },
    })
    return [defaultHomepage, ...otherHomepage]
  }

  async getHomepage(id: number) {
    const homepage = await this.homepageRepository.findOne({ where: { id } })
    homepageValidator(homepage).exist()
    return homepage
  }

  async getHomepages(ids: number[], relations?: string[]) {
    return this.homepageRepository.find({ where: { id: In(ids) }, relations })
  }

  async updateHomepage(id: number, data: CreateHomepageDto) {
    const homepage = await this.getHomepage(id)
    const hasStatus = data.status !== homepage.status
    if (!homepage.isFixedSort && !homepage.isPermanent)
    {Object.assign(homepage, R.pick(modifyHomepageField, data))}

    if (data.bookIds?.length) {
      const books = await this.bookRepositories.searchBooks({
        ids: [...new Set(data.bookIds)],
      })
      homepage.books = []
      await this.homepageRepository.save(homepage)
      homepage.books = books
      homepage.booklist = null
    } else {
      homepage.books = null
    }

    if (data.bookListId) {
      const booklist = await this.bookListService.getBookList({ id: data.bookListId })
      homepage.booklist = null
      await this.homepageRepository.save(homepage)
      homepage.booklist = booklist
      homepage.books = null
    } else {
      homepage.booklist = null
    }

    if (data.status && hasStatus) {
      if (data.status === EListStatus.ONLINE) {
        homepage.offlineAt = null
        homepage.onlineAt = new Date()
      } else if (data.status === EListStatus.OFFLINE) {
        homepage.offlineAt = new Date()
      }
    }

    return this.homepageRepository.save(homepage)
  }

  async update(id: number, data: Partial<Homepage>) {
    const homepage = await this.getHomepage(id)
    if (data.status && data.status !== homepage.status) {
      if (data.status === EListStatus.ONLINE) {
        homepage.offlineAt = null
        homepage.onlineAt = new Date()
      } else if (data.status === EListStatus.OFFLINE) {
        homepage.offlineAt = new Date()
      }
    }
    Object.assign(homepage, R.pick(modifyHomepageField, data))
    return this.homepageRepository.save(homepage)
  }

  async listHomepage(query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const total = await this.homepageRepository
      .createQueryBuilder('homepage')
      .where('homepage.parent_id IS NULL')
      .getCount()
    let items = await this.homepageRepository
      .createQueryBuilder('homepage')
      .leftJoinAndSelect('homepage.books', 'books')
      .leftJoinAndSelect('homepage.booklist', 'booklist')
      .leftJoinAndSelect('booklist.books', 'booklistBooks')
      .where('homepage.parent_id IS NULL')
      .skip((pageIndex - 1) * pageSize)
      .take(pageSize)
      .orderBy('homepage.sort', 'ASC')
      .getMany()

    items = await Promise.all(
      items.map(async (x) =>
        x?.books
          ? this.sortHomepageBooks(x)
          : x?.booklist?.books
            ? {
                ...x,
                booklist: await this.bookListService.sortBooklistBooks(x.booklist),
              }
            : x
      )
    )

    return { total, items, pageIndex, pageSize }
  }

  async deleteHomepage(ids: number[]) {
    await this.dataSource.transaction(async (manager) => {
      await manager.query(
        `delete from books_homepages_homepage where homepage_id In (${ids.join(',')})`
      )
      await manager.query(
        `update book_lists set homepage_id = null where homepage_id In (${ids.join(
          ','
        )})`
      )
      await manager.delete(Homepage, { id: In(ids) })
    })
  }

  async listAllHomepageIds() {
    const data = await this.homepageRepository
      .createQueryBuilder('homepage')
      .select(['homepage.id'])
      .where('homepage.parent_id IS NULL')
      .getMany()
    return data.map((item) => item.id).filter((id) => id !== 1)
  }

  async getTeacherRecommend(type: SchoolType, levelIds: number[], schoolId?: number) {
    if (type) {
      const typeHomeage = await this.homepageRepository
        .createQueryBuilder('homepage')
        .select(['homepage.id'])
        .where(`homepage.name->'$.zh_HK' = '${this.convertSchoolTypeToName(type)}' and homepage.parent_id = 1`)
        .getOne()

      if (typeHomeage) {
        const homepage = await this.getHomepageWithRelation(typeHomeage.id)
        const h = filterHomageBooks(homepage, levelIds, schoolId) as Homepage

        if (h?.booklist?.books?.length || h?.books?.length) {
          const defaultHomepage = await this.homepageRepository.findOne({ where: { id: 1 } })
          return { ...h, name: defaultHomepage.name }
        }
      }
    }
    const homepage = await this.getHomepageWithRelation(1)
    return filterHomageBooks(homepage, levelIds, schoolId)
  }

  async getHotBooks(
    schoolId: number,
    pageSize: number,
    skip: number,
    isTeacher: boolean,
    options?: { fields?: string[]; relations?: string[] },
    level?: number[]
  ) {
    const total = await this.leaderBoardService.getTotalOfReadingRanking(
      schoolId,
      EUserType.TEACHER
    )

    const leader = await this.leaderBoardService.getTopReadingRanking(
      schoolId,
      EUserType.TEACHER,
      0,
      -1
    )
    if (!leader || leader.length === 0) {
      return { total, items: [] }
    }

    const ids = leader.map((item) => Number(item.bookId))
    const hotBookIds = await this.bookRepositories.getLevelHotBookIds(
      level,
      ids,
      skip,
      pageSize,
      schoolId,
      isTeacher
    )
    const hotBookIdSet = new Set(hotBookIds)
    const reading = leader
      .filter((item) => hotBookIdSet.has(Number(item.bookId)))
      .map((item) => ({
        id: item.bookId,
        readingTime: item.readingTime,
      }))

    const {
      fields = [
        'id',
        'name',
        'isbn',
        'coverUrl',
        'description',
        'url',
        'level',
        'hiddeSchoolIds',
      ],
      relations = ['authors', 'categories', 'labels'],
    } = options || {}
    const data = await this.bookRepositories.listBooks(
      {
        ids: hotBookIds,
      },
      {
        withDeleted: false,
        fields,
        relations,
      }
    )

    const items = reading.map((item) => ({
      ...data.find((book) => book.id === item.id),
      readingTime: item.readingTime,
    }))

    return { total, items }
  }

  async getReferenceHot(
    schoolId: number,
    query: PageRequest,
    options: { fields?: string[]; relations?: string[] } = {}
  ) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const total = await this.leaderBoardService.getTotalOfReferenceRanking(schoolId)
    const data = await this.leaderBoardService.getTopReferenceRanking(
      schoolId,
      (pageIndex - 1) * pageSize,
      pageIndex * pageSize - 1
    )
    const books = await this.bookRepositories.listBooks(
      {
        ids: data.map((item) => item.bookId),
      },
      { withDeleted: true, ...options }
    )
    return {
      pageIndex,
      pageSize,
      total,
      items: data.map((item) => ({
        ...item,
        ...(books.find((book) => book.id === item.bookId) || {}),
      })),
    }
  }

  async exportReferenceHot(
    schoolId: number,
    local: ELocaleType = ELocaleType.ZH_HK,
    admin: any
  ) {
    const data = await this.getReferenceHot(
      schoolId,
      { pageIndex: 1, pageSize: 10 },
      {
        fields: ['id', 'name', 'isbn'],
        relations: ['authors', 'publisher'],
      }
    )

    const totalData = await this.referenceStatisticService.getTotalReadCounts(
      schoolId,
      data.items.map((item) => item.id)
    )

    await this.logService.save('下载学校top 10 书籍', admin, { schoolId })
    return this.excelService.buildExcel({
      name: `top10ReferenceBooks.${local}`,
      data: data.items.map((item) => ({
        startTime: '2024/1/1',
        endTime: moment.tz(Date.now(), 'Asia/Hong_Kong').format('YYYY/MM/DD'),
        isbn: getISBN(item.isbn),
        bookName: getName(item.name, local),
        author: item.authors?.map((author) => getName(author.name, local)).join(','),
        publisher: getName(item.publisher?.name, local),
        readingUsers: Number(totalData.find((x) => x.bookId === item.id)?.total ?? 0),
        readingCount: Number(item.viewCount),
      })),
    })
  }

  async getTop10Books(
    schoolId: number,
    options?: { fields?: string[]; relations?: string[] }
  ) {
    const data = await this.getHotBooks(schoolId, 10, 0, true, options)

    if (data.items.length === 0) {
      return data.items
    }

    return data.items
      .map((item) => ({
        totalReadingTime: item.readingTime ?? 0,
        book: getBookDto(item),
        bookId: item.id,
      }))
      .filter((item) => !!item)
  }

  async clearRecommendCache() {
    const classes = await this.homepageRepository.query(
      `select id, grade_id as gradeId, school_id as schoolId from user_class where deleted_at is null`
    )
    await Promise.all(
      classes.map((item) =>
        this.cacheManager.del(
          getSubscriptionRecommendKey(
            item.schoolId,
            EUserType.STUDENT,
            item.gradeId,
            item.id
          )
        )
      )
    )
    await Promise.all(
      [...new Set(classes.map((item) => item.schoolId))].map((schoolId) =>
        this.cacheManager.del(
          getSubscriptionRecommendKey(schoolId as any, EUserType.TEACHER)
        )
      )
    )
  }

  async clearCustomCache() {
    const schools = await this.homepageRepository.query(
      `select id from schools where deleted_at is null`
    )
    await Promise.all(
      schools.map(async (item) => {
        await this.cacheManager.del(
          getSubscriptionHomepageKey(item.id, EUserType.STUDENT)
        )
        await this.cacheManager.del(
          getSubscriptionHomepageKey(item.id, EUserType.TEACHER)
        )
      })
    )
  }

  private convertSchoolTypeToName(type: SchoolType) {
    switch (type) {
      case SchoolType.COLLEGES:
        return '大專院校'
      case SchoolType.KINDERGARTEN:
        return '幼稚園'
      case SchoolType.MIDDLE:
        return '中學'
      case SchoolType.PRIMARY:
        return '小學'
    }
  }
}
