import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ELocaleType } from '@/common'
import { Message, User } from '@/entities'
import { EOrderDirection, EUserType } from '@/enums'
import { PAGE_SIZE } from '@/modules/constants'
import { QueryLogDto } from '../dto'
import { CreateMessageDto, MessageDto } from '../dto/message.dto'
import { MessageNotExistException } from '../exception'
import { ApplicationService } from './application.service'
import { SchoolService } from './school.service'

@Injectable()
export class MessageService {
  constructor(
    @InjectRepository(Message) private readonly messageRepository: Repository<Message>,
    private readonly schoolService: SchoolService,
    private readonly applicationService: ApplicationService
  ) {}

  async createMessage(data: CreateMessageDto, schoolId: number, createdBy: any) {
    const school = await this.schoolService.findOne({ where: { id: schoolId  } })
    return this.messageRepository.save({
      ...data,
      school,
      readUserIds: [],
      toUserIds: data.toUserIds ?? null,
      type: data.type ?? null,
      createdBy,
    })
  }

  async searchMessage(userId: number, type: string) {
    return this.messageRepository
      .createQueryBuilder('message')
      .where(`message.to_user_ids is null or ${userId} MEMBER OF(\`message\`.\`to_user_ids\`) = 1 and type = ${type}`)
      .orderBy('message.createdAt', 'DESC')
      .getOne()
  }

  async listMessage(schoolId: number, query: QueryLogDto, local?: ELocaleType) {
    const { orderDirection = EOrderDirection.DESC } = query
    const builder = this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.school', 'school', 'school.id = :schoolId', { schoolId })
      .where('message.type is null')

    const total = await builder.getCount()
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const items: any[] = await builder
      .orderBy('message.createdAt', orderDirection)
      .skip((pageIndex - 1) * pageSize)
      .take(pageSize)
      .getMany()
    return {
      total,
      items: items.map((item) => ({
        id: item.id,
        title: local ? item.title[local] : item.title,
        message: local ? item.message[local] : item.message,
        createdAt: item.createdAt,
        createdBy: item.createdBy,
        // hasRead: item.readUserIds?.includes(userId) ?? false,
        type: item.type,
        meta: item.meta,
        remarks: item.remarks,
        // application: item?.application,
      })),
      pageIndex,
      pageSize,
    }
  }

  async listClientMessage(
    schoolId: number,
    user: User,
    query: MessageDto,
    local?: ELocaleType
  ) {
    const { orderDirection = EOrderDirection.DESC, hasRead } = query
    const builder = this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.school', 'school', 'school.id = :schoolId', { schoolId })
      .where(this.getClientMessageFilter(user, hasRead))
    const total = await builder.getCount()
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const items: any[] = await builder
      .orderBy('message.createdAt', orderDirection)
      .skip((pageIndex - 1) * pageSize)
      .take(pageSize)
      .getMany()
    return {
      total,
      items: items.map((item) => ({
        id: item.id,
        title: local ? item.title[local] : item.title,
        message: local ? item.message[local] : item.message,
        createdAt: item.createdAt,
        createdBy: item.createdBy,
        hasRead: item.readUserIds?.includes(user.id) ?? false,
        type: item.type,
        meta: item.meta,
        remarks: item.remarks,
        // application: item?.application,
      })),
      pageIndex,
      pageSize,
    }
  }

  async getMessage(id: number, local?: ELocaleType) {
    const message = await this.messageRepository.findOne({ where: { id } })
    if (!message) {
      throw new MessageNotExistException()
    }
    const application = await this.applicationService.getApplicationByMessageId(
      message.id
    )
    return {
      id: message.id,
      title: local ? message.title[local] : message.title,
      message: local ? message.message[local] : message.message,
      type: message.type,
      meta: message.meta,
      application,
      createdAt: message.createdAt,
      createdBy: message.createdBy,
      toUserIds: message.toUserIds,
    }
  }

  async getUnReadCount(schoolId: number, user: User) {
    return this.messageRepository
      .createQueryBuilder('message')
      .innerJoin('message.school', 'school', 'school.id = :schoolId', { schoolId })
      .where(
        `${this.getClientMessageFilter(user)} and ${
          user.id
        } MEMBER OF(\`message\`.\`read_user_ids\`) = 0 `
      )
      .getCount()
  }

  async readMessage(schoolId: number, userId: number, id: number) {
    const messages = await this.messageRepository.query(
      `select id, read_user_ids as readUserIds from messages where school_id = ${schoolId} and ${userId} MEMBER OF(\`read_user_ids\`) = 0 and id = ${id}`
    )

    if (messages.length) {
      for (const message of messages) {
        await this.messageRepository.update(
          { id: message.id },
          { readUserIds: message.readUserIds.concat([userId]) }
        )
      }
    }
  }

  private getClientMessageFilter(user: User, hasRead?: boolean) {
    let where
    const hasReadWhere =
      hasRead === undefined
        ? ''
        : `and ${
          hasRead ? '' : 'NOT'
        } JSON_CONTAINS(\`message\`.\`read_user_ids\`, CAST(${user.id} AS JSON), '$')`

    if (user.type === EUserType.TEACHER) {
      where = `(message.user_type in ('${EUserType.ALL}', '${EUserType.TEACHER}') `
    } else {
      where = `((message.user_type = '${EUserType.ALL}' 
      or (message.user_type = '${EUserType.STUDENT}' 
      and (\`message\`.\`class_ids\` is null 
      or ${user.userClass.id} MEMBER OF(\`message\`.\`class_ids\`) = 1) 
      and ${user.userClass.gradeId} MEMBER OF(\`message\`.\`grade_ids\`) = 1)) `
    }

    where =
      where +
      `or ((message.to_user_ids is null and message.user_type is null ) or ${
        user.id
      } MEMBER OF(\`message\`.\`to_user_ids\`) = 1) ) 
       ${hasReadWhere}
       and message.created_at >= "${user.createdAt.toISOString()}" `

    return where
  }
}
