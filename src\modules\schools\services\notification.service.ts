import { Injectable, NotFoundException } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { FindOptionsWhere, In, Repository } from 'typeorm'
import { AuthSchema, EmailService, RedlockService } from '@/common'
import { Notification } from '@/entities'
import { getTaskLockKey } from '@/modules/constants'
import { ListNotificationRequest, MarkNotificationReadRequest } from '../dto'

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly redlockService: RedlockService,
    private readonly emailService: EmailService
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async sendInsufficientReadTimeNotification() {
    await this.redlockService.lockWrapper(
      getTaskLockKey('sendInsufficientNotification'),
      20000,
      async () => {
        return this.redlockService.lockWrapper(
          'sendInsufficientNotification',
          20000,
          async () => {
            const result = await this.notificationRepository.query(
              `select t1.school_id as schoolId, t2.name->'$.zh_HK' as schoolName, t2.name->'$.en_uk' as schoolEnName from (
            select t1.school_id, t1.total_bought_quota, t2.used_quota
            from school_balances t1, (select SUM(reading_time) as used_quota, school_id from read_record group by school_id) t2
            where t1.school_id = t2.school_id
          ) t1, schools t2 where t1.school_id = t2.id and t2.deleted_at is null and t2.status = 'active' and t2.has_notified = false and t1.used_quota >= t1.total_bought_quota * 0.8;`
            )
            if (!result || result.length <= 0) {return}

            // Send notification to platform admin
            await this.sendInsufficientReadTimeNotificationToPlatformAdmin(
              result.map((x: any) => ({
                schoolName: x.schoolName,
                schoolEnName: x.schoolEnName,
              }))
            )

            // Send notification to school super admin
            for (const item of result) {
              await this.sendInsufficientReadTimeNotificationToSchoolAdmin(item.schoolId)
            }
          }
        )
      }
    )
  }

  async sendInsufficientReadTimeNotificationToPlatformAdmin(
    data: { schoolName: string; schoolEnName: string }[]
  ) {
    const result = await this.notificationRepository.query(
      `select t1.email, t1.id
      from administrators t1,admin_user_roles t3, roles t4, role_permissions t5, permissions t6
      where t1.deleted_at is null and t1.status = 'active' 
      and t1.id = t3.admin_user_id
      and t3.role_id = t4.id and t4.deleted_at is null
      and t4.id = t5.role_id
      and t5.permission_id = t6.id and t6.deleted_at is null and t6.code = 'noticeManage:schoolInsufficientReadTimeEmail';`
    )

    if (!result || result.length <= 0) {return}

    for (const item of data) {
      await this.emailService.sendPrepared(
        Array.from(new Set(result.map((x) => x.email))),
        'insufficientReadTimeForPlatform',
        {
          schoolName: item.schoolName,
        }
      )
      await this.notificationRepository.save(
        result.map((x) => ({
          userId: x.id,
          authSchema: AuthSchema.ADMIN,
          title: {
            zh_HK: `[${item.schoolName}] 帳戶閱讀時數餘額不足`,
            zh_cn: `[${item.schoolName}] 帐户阅读时数余额不足`,
            en_uk: `[${item.schoolEnName}] insufficient purchased hours reminder`,
          },
          content: {
            zh_HK: `[${item.schoolName}] 帳戶閱讀時數餘額不足，學校用戶即將無法繼續閱讀書籍。請及時聯繫該學校購買閱讀時數，以免影響學校用戶閱讀書籍。`,
            zh_cn: `[${item.schoolName}] 帐户阅读时数余额不足，学校用户即将无法继续阅读书籍。请及时联系该学校购买阅读时数，以免影响学校用户阅读书籍。`,
            en_uk: `[${item.schoolEnName}] purchased hours are insufficient. User will be unable to read soon.Please contact school to purchase reading hours ASAP.`,
          },
          isRead: false,
        }))
      )
    }
  }

  async sendInsufficientReadTimeNotificationToSchoolAdmin(schoolId: number) {
    const result = await this.notificationRepository.query(
      `select t1.email, t1.school_id, t1.id
      from school_administrators t1, schools t2 
      where t1.deleted_at is null and (t1.status = 'active' or t1.status = 'pending') and t1.is_root = true
      and t1.school_id = t2.id and t2.deleted_at is null and t2.status = 'active'
      and t2.id = ${schoolId};`
    )

    if (!result || result.length <= 0) {return}

    console.log('school admin:', result)

    await this.emailService.sendPrepared(
      Array.from(new Set(result.map((x) => x.email))),
      'insufficientReadTime',
      {}
    )

    await this.notificationRepository.save(
      result.map((x) => ({
        userId: x.id,
        authSchema: AuthSchema.SCHOOL_ADMIN,
        title: {
          zh_HK: '學校帳戶閱讀時數餘額不足',
          zh_cn: '学校帐户阅读时数余额不足',
          en_uk: `Insufficient purchased hours reminder`,
        },
        content: {
          zh_HK:
            '學校帳戶閱讀時數餘額不足，學校用戶即將無法繼續閱讀書籍。請及時聯繫 SJRC 公司 購買閱讀時數，以免影響學校用戶閱讀書籍。',
          zh_cn:
            '学校帐户阅读时数余额不足，学校用户即将无法继续阅读书籍。请及时联系 SJRC 公司 购买阅读时数，以免影响学校用户阅读书籍。',
          en_uk: `Purchased hours are insufficient. User will be unable to read soon.Please contact SJRC to purchase reading hours ASAP.`,
        },
        isRead: false,
      }))
    )
    await this.notificationRepository.query(
      `update schools set has_notified = true where id = ${schoolId}`
    )
  }

  async sendManualNotification(schoolId: number) {
    const result = await this.notificationRepository.query(
      `select t1.id as schoolId, t1.name->'$.zh_HK' as schoolName, t1.name->'$.en_uk' as schoolEnName from schools t1 where id = ${schoolId};`
    )
    if (!result || result.length <= 0) {return}

    // Send notification to platform admin
    await this.sendInsufficientReadTimeNotificationToPlatformAdmin(
      result.map((x: any) => ({
        schoolName: x.schoolName,
        schoolEnName: x.schoolEnName,
      }))
    )

    // Send notification to school super admin
    for (const item of result) {
      await this.sendInsufficientReadTimeNotificationToSchoolAdmin(item.schoolId)
    }
  }

  async createNotification(data: Partial<Notification>) {
    return this.notificationRepository.save(data)
  }

  async listNotifications(
    id: number,
    authSchema: AuthSchema,
    query: ListNotificationRequest
  ) {
    const { pageIndex = 1, pageSize = 10, isRead } = query || {}

    let where: FindOptionsWhere<Notification> = {
      userId: id,
      authSchema,
    }

    if (!R.isNil(isRead))
    {where = {
      ...where,
      isRead,
    }}
    const [items, total] = await this.notificationRepository.findAndCount({
      where,
      order: { createdAt: 'DESC' },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    })
    return {
      items,
      total,
      pageIndex,
      pageSize,
    }
  }

  async getNotification(id: number) {
    const notification = await this.notificationRepository.findOne({ where: { id } })

    if (!notification) {
      throw new NotFoundException(`Notification not found`)
    }

    return notification
  }

  async markNotificationRead(body: MarkNotificationReadRequest) {
    const { ids } = body
    await this.notificationRepository.update(
      {
        id: In(ids),
      },
      { isRead: true }
    )

    return this.notificationRepository.find({
      where: {
        id: In(ids),
      },
      select: ['id', 'isRead'],
    })
  }
}
