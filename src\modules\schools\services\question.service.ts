import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { Repository } from 'typeorm'
import { Question } from '@/entities'
import { allNameCondition } from '@/utils'
import { CreateQuestionDto, UpdateSequenceDto } from '../dto'
import { DuplicateQuestionException, QuestionNotExistException } from '../exception'

export class QuestionService {
  constructor(
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>
  ) {}

  // async create(data: CreateQuestionDto): Promise<Question> {
  //   const duplicate = await this.questionRepository
  //     .createQueryBuilder('question')
  //     .where(`${allNameCondition(data.name)} AND subject_id = ${data.subjectId}`)
  //     .getOne()
  //   if (duplicate) {
  //     throw new DuplicateQuestionException()
  //   }

  //   const max = await this.questionRepository.findOne({
  //     where: { subject: { id: data.subjectId } },
  //     order: { sequence: 'DESC' },
  //   })

  //   return this.questionRepository.save({
  //     ...R.pick(['name', 'image', 'answers'], data),
  //     sequence: max?.sequence + 1 || 1,
  //     subject: { id: data.subjectId },
  //   })
  // }

  // async update(id: number, data: CreateQuestionDto) {
  //   const question = await this.questionRepository.findOne({ where: { id  }, select: ['id']  })
  //   if (!question) {
  //     throw new QuestionNotExistException()
  //   }

  //   const duplicate = await this.questionRepository
  //     .createQueryBuilder('question')
  //     .where(`${allNameCondition(data.name)} AND subject_id = ${data.subjectId} AND id != ${id}`)
  //     .getOne()

  //   if (duplicate) {
  //     throw new DuplicateQuestionException()
  //   }

  //   await this.questionRepository.update(id, {
  //     ...R.pick(['name', 'image', 'answers'], data),
  //     subject: { id: data.subjectId },
  //   })
  // }

  async updateSequence(data: UpdateSequenceDto) {
    await Promise.all(
      data.items.map((item) =>
        this.questionRepository.update(item.id, { sequence: item.sequence })
      )
    )
  }

  async remove(id: number): Promise<void> {
    await this.questionRepository.softDelete(id)
  }
}
