import { Injectable } from '@nestjs/common'
import { WebsocketGateway } from 'src/modules/websocket'
import { AuthSchema, EPlatform } from '@/common'
import { OneSignalService } from '@/common/services/oneSignal.service'
import { IUserRepo } from '@/modules/shared/interfaces'
import {
  REMAINING_READING_TIME_REMINDER_MESSAGE,
  REMAINING_READING_TIME_REMINDER_TITLE,
} from '../constants'
import { MessageService } from './message.service'

@Injectable()
export class ReadingMessageService {
  constructor(
    private readonly messageService: MessageService,
    private readonly webSocketGateway: WebsocketGateway,
    private readonly userRepositories: IUserRepo,
    private readonly oneSignalService: OneSignalService
  ) {}

  async littleReadingTime(data: {
    userId: number
    schoolId: number
    platform: EPlatform
    isSharingTime: boolean
    totalQuota: number
    usedQuota: number
  }) {
    if (!data.isSharingTime && data.totalQuota * 0.8 <= data.usedQuota) {
      const userData = await this.userRepositories.findOne({ where: { id: data.userId } })
      if (userData.hasNotifed) {return}

      const message = await this.messageService.createMessage(
        {
          title: REMAINING_READING_TIME_REMINDER_TITLE,
          message: REMAINING_READING_TIME_REMINDER_MESSAGE, 
          toUserIds: [data.userId],
          type: 'APPLICATION',
        },
        data.schoolId,
        { type: 'SYSTEM', total: data.totalQuota }
      )
      await this.webSocketGateway.sendNotifications(data.userId, AuthSchema.CLIENT, {
        id: message.id,
        title: message.title,
        message: message.message,
        createdAt: message.createdAt,
        type: message.type,
      })
      await this.userRepositories.updateUser(data.userId, { hasNotifed: true })

      if ([EPlatform.ANDROID, EPlatform.IOS].includes(data.platform)) {
        if (userData.playerId)
        {await this.oneSignalService.sendNotification('common.oneSignal', {
          playerIds: [userData.playerId],
          headings: {
            en: REMAINING_READING_TIME_REMINDER_TITLE.en_uk,
            'zh-Hant': REMAINING_READING_TIME_REMINDER_TITLE.zh_HK,
            'zh-Hans': REMAINING_READING_TIME_REMINDER_TITLE.zh_cn,
          },
          contents: {
            en: REMAINING_READING_TIME_REMINDER_MESSAGE.en_uk,
            'zh-Hant': REMAINING_READING_TIME_REMINDER_MESSAGE.zh_HK,
            'zh-Hans': REMAINING_READING_TIME_REMINDER_MESSAGE.zh_cn,
          },
          data: {
            id: message.id,
          },
        })}
      }
    }
  }
}
