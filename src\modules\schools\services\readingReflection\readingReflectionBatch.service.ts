import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment'
import { Brackets, Repository } from 'typeorm'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import { EBookVersion } from '@/enums'
import { BathSelectReadBookDto } from '../../dto/readingReflection.dto'

@Injectable()
export class ReadingReflectionBatchService {
  constructor(
    @InjectRepository(ReadingReflection)
    private readonly readingReflectionRepository: Repository<ReadingReflection>,

    @InjectRepository(ReferenceReadingReflection)
    private readonly referenceReadingReflectionRepository: Repository<ReferenceReadingReflection>
  ) {}

  /**
   * 批量设置为已读 镖旗
   * @param schoolId
   * @param setStateFiled
   * @param bathSelectReadBookDto
   */
  async StatebatchSetSchool(
    setStateFiled: any,
    schoolId: number,
    bathSelectReadBookDto: BathSelectReadBookDto
  ) {
    const {
      isFullSelected,
      excludedIds,
      specifiedIds,
      grade,
      class: userClass,
      keyword,
      startTime,
      endTime,
      flagState,
      reviewState,
      version = EBookVersion.SUBSCRIPTION,
    } = bathSelectReadBookDto

    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    const queryBuilder = repository
      .createQueryBuilder(alias)
      .leftJoin(`${alias}.book`, 'book')
      .leftJoin('users', 'user', `user.id = ${alias}.user_id`)

    // 处理全选的情况
    if (isFullSelected) {
      if (excludedIds && excludedIds.length > 0) {
        queryBuilder.where(`${alias}.id NOT IN (:...excludedIds)`, { excludedIds })
      }
    } else {
      // 处理反选和指定 IDs
      if (specifiedIds && specifiedIds.length > 0) {
        queryBuilder.where(`${alias}.id IN (:...specifiedIds)`, { specifiedIds })
      } else {
        throw new Error('No specified IDs provided for update.')
      }
    }

    if (startTime && endTime) {
      queryBuilder.andWhere(`${alias}.created_at BETWEEN :startTime AND :endTime`, {
        startTime: moment.unix(startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment.unix(endTime).format('YYYY-MM-DD HH:mm:ss'),
      })
    }
    if (keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('book.name LIKE :keyword', { keyword: `%${keyword}%` }).orWhere(
            'user.given_name LIKE :keyword',
            { keyword: `%${keyword}%` }
          )
        })
      )
    }
    queryBuilder.andWhere(`${alias}.school_id = :schoolId`, { schoolId })

    if (grade) {
      queryBuilder.andWhere(`${alias}.grade_id = :grade`, { grade })
    }
    if (userClass) {
      queryBuilder.andWhere(`${alias}.user_class_id = :userClass`, { userClass })
    }
    if (flagState) {
      queryBuilder.andWhere(`${alias}.flag_state = :flagState`, { flagState })
    }
    if (reviewState) {
      queryBuilder.andWhere(`${alias}.review_state = :reviewState`, { reviewState })
    }

    const ids = await queryBuilder.select([`${alias}.id as id`]).getRawMany()
    const idList = ids.map((item) => item.id)

    if (idList.length > 0) {
      await repository
        .createQueryBuilder(alias)
        .update()
        .set(setStateFiled)
        .whereInIds(idList)
        .execute()
    }

    return {}
  }
}
