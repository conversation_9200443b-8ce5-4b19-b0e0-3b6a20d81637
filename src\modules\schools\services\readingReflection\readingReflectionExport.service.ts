import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import AdmZip from 'adm-zip'
import {
  AlignmentType,
  Document,
  Footer,
  ImageRun,
  Packer,
  PageNumber,
  Paragraph,
  Table,
  TableCell,
  TableRow,
  TextRun,
} from 'docx'
import moment from 'moment'
import fetch from 'node-fetch'
import sharp from 'sharp'
import { Repository } from 'typeorm'
import { ExcelService } from '@/common'
import { Book, User } from '@/entities'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import { EBookVersion } from '@/enums'
import {
  ReadingReflectionFlagState,
  ReadingReflectionReviewState,
} from '@/enums/readingReflection.enum'
import { SJRC_LOGO_JPG } from '@/modules/constants'
import { LogService } from '@/modules/system'
import { QueryReadingReflectionListDto, QueryTimeDto } from '../../dto/readingReflection.dto'
import { SchoolService } from '../school.service'
import { ReadingReflectionQueryService } from './readingReflectionQuery.service'

import 'moment-timezone'

@Injectable()
export class ReadingReflectionExportService {
  constructor(
    private readonly schoolService: SchoolService,
    private readonly excelService: ExcelService,
    private readonly logService: LogService,
    private readonly readingReflectionQueryService: ReadingReflectionQueryService,

    @InjectRepository(ReadingReflection)
    private readonly readingReflectionRepository: Repository<ReadingReflection>,

    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,

    @InjectRepository(ReferenceReadingReflection)
    private readonly referenceReadingReflectionRepository: Repository<ReferenceReadingReflection>
  ) {}

  /**
   * 获取日期范围
   * @param query
   * @returns
   */
  private getDateRange(query: QueryTimeDto) {
    const startTime = query.startTime
      ? moment.tz(query.startTime * 1e3, 'Asia/Hong_Kong').format('YYYY/MM/DD')
      : '-'
    const endTime = query.endTime
      ? moment.tz(query.endTime * 1e3, 'Asia/Hong_Kong').format('YYYY/MM/DD')
      : '-'
    return {
      startTime,
      endTime,
    }
  }

  /**
   * 格式化日期
   * @param dateString
   * @returns
   */
  private formatDate = (dateString: string): string => {
    return moment(dateString).add(8, 'hours').format('YYYY-MM-DD HH:mm:ss')
  }

  /**
   * 导出閱讀感想詳情
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportReadRefectionDetail(user, schoolId, query, local, res) {
    const dateRange = this.getDateRange(query)
    let total = 0
    let list = []
    query.pageIndex = 1
    query.pageSize = 100
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const excelName =
      version === EBookVersion.SUBSCRIPTION
        ? 'readRefectionDetailSubscription'
        : 'readRefectionDetailReference'
    do {
      const data = await this.getSchoolUserReadingReflectionDetail(schoolId, query)
      const schoolUserReadingReflectionList = data.items.map((v) => {
        const sortedReflections = (v.allReflectionContents || []).sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateB - dateA // 按照时间降序排序
        })
        const reflections = Array(15)
          .fill({
            content: '語音閱讀感想',
            createdAt: '',
          })
          .map((_, index) => {
            const reflection = sortedReflections[index] || {}
            return {
              [`reflections${index + 1}`]:
                reflection.content !== undefined
                  ? reflection.content !== null
                    ? reflection.content
                    : '語音閱讀感想'
                  : '',
              [`reflections${index + 1}_created_at`]: reflection.createdAt
                ? this.formatDate(reflection.createdAt)
                : '',
            }
          })
          .reduce((acc, curr) => ({ ...acc, ...curr }), {})
        return {
          ...v,
          bookName: local == 'zh_HK' ? v.name?.zh_HK : v.name?.en_uk || '',
          ...reflections,
          latest_created_at: this.formatDate(v.latest_created_at) ?? ' ',
          totalReadingTime: v?.totalReadingTime ? parseInt(v.totalReadingTime) / 3600 : 0,
          ...dateRange,
        }
      })

      query.pageIndex += 1
      total = data.total
      list = list.concat(schoolUserReadingReflectionList)
    } while ((query.pageIndex - 1) * query.pageSize < total)

    const file = await this.excelService.buildExcel(
      {
        name: `${excelName}.${local}`,
        data: list,
      },
      schoolId == undefined ? '閱讀感想詳情' : undefined
    )

    await this.logService.save('下载-阅读感想-閱讀感想詳情 ', user)
    res.send(Buffer.from(file))
  }

  /**
   * 导出老师检阅阅读感想
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportTeacherCheckRefection(user, schoolId, query, local, res) {
    const dateRange = this.getDateRange(query)
    let total = 0
    let list = []
    query.pageIndex = 1
    query.pageSize = 100
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const excelName =
      version === EBookVersion.SUBSCRIPTION
        ? 'readRefectionTeacherCheckSubscription'
        : 'readRefectionTeacherCheckReference'
    do {
      const data = await this.readingReflectionQueryService.getSchoolUserReadingReflectionList(schoolId, query)

      const schoolUserReadingReflectionList = data.items.map((v) => {
        return {
          ...v,
          refectionContent: v.refectionContent || '語音閱讀感想',
          bookName: local == 'zh_HK' ? v.name?.zh_HK : v.name?.en_uk || '',
          review: v.reviewState == ReadingReflectionReviewState.READ ? '已讀' : '未讀',
          flag: v.flagState == ReadingReflectionFlagState.MARKED ? 'Y' : 'N',
          totalReadingTime: v?.totalReadingTime ? parseInt(v.totalReadingTime) / 3600 : 0,
          created_at: this.formatDate(v.created_at) ?? ' ',
          ...dateRange,
        }
      })
      query.pageIndex += 1
      total = data.total
      list = list.concat(schoolUserReadingReflectionList)
    } while ((query.pageIndex - 1) * query.pageSize < total)

    const file = await this.excelService.buildExcel(
      {
        name: `${excelName}.${local}`,
        data: list,
      },
      schoolId == undefined ? '老師檢閱狀態' : undefined
    )

    await this.logService.save('下载-阅读感想-老師檢閱狀態 ', user)
    res.send(Buffer.from(file))
  }

  /**
   * 获取学校用户阅读感想书籍详情
   * @param schoolId
   * @param query
   * @returns
   */
  async getSchoolUserReadingReflectionDetail(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    return this.readingReflectionQueryService.getSchoolUserReadingReflectionDetail(schoolId, query)
  }

  /**
   * 获取学校用户阅读感想书籍ID列表
   * @param schoolId
   * @param query
   * @returns
   */
  async getSchoolUserReadingReflectionBookIds(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    return this.readingReflectionQueryService.getSchoolUserReadingReflectionBookIds(schoolId, query)
  }

  /**
   * 个人阅读感想篇章
   * @param user
   * @param schoolId
   * @param query
   * @param local
   * @param res
   */
  async exportPersonalReflectionDocx(user, schoolId, query, local, res) {
    const school = await this.schoolService.findOne({ where: { id: schoolId } })

    // 获取Logo图片
    const logoImage = await fetch(SJRC_LOGO_JPG)
    const logoBuffer = Buffer.from(await logoImage.arrayBuffer())
    const logoResize = await sharp(logoBuffer).resize(300).png({ quality: 90 })
    const logoResizeBuffer = await logoResize.toBuffer()
    let schoolLogoResizeBuffer: Buffer
    if (school.logo) {
      const schoolLogoImage = await fetch(school.logo)
      const schoolLogoBuffer = Buffer.from(await schoolLogoImage.arrayBuffer())
      const schoolLogoResize = await sharp(schoolLogoBuffer)
        .resize(300)
        .png({ quality: 90 })
      schoolLogoResizeBuffer = await schoolLogoResize.toBuffer()
    } else {
      schoolLogoResizeBuffer = logoResizeBuffer
    }
    const userBookReflections = await this.getSchoolUserReadingReflectionBookIds(
      schoolId,
      query
    )
    if (!userBookReflections || userBookReflections?.length === 0) {
      return res.send('')
    }
    // 获取日期范围和阅读感想数据
    const zip = new AdmZip()

    const dateRange = this.getDateRange(query)
    for (const userBookReflection of userBookReflections) {
      query.userId = userBookReflection.userId
      query.bookId = userBookReflection.bookId
      const reflections = await this.getSchoolUserReadingReflectionBookDetail(
        schoolId,
        query
      )

      const { items } = reflections
      const reflection = items[0]
      const allReflectionContents = reflection.allReflectionContents
      try {
        const doc = await this.generateDocx(
          query.version ?? EBookVersion.SUBSCRIPTION,
          school,
          reflection,
          allReflectionContents,
          schoolLogoResizeBuffer,
          logoResizeBuffer,
          dateRange
        )
        const file = await Packer.toBuffer(doc)
        zip.addFile(`${reflection.userName}--${reflection.name.zh_HK}.docx`, file)
      } catch (error) {
        console.log('error', error)
      }
    }
    res.send(zip.toBuffer())
  }

  /**
   * 获取学校用户阅读感想书籍详情
   * @param schoolId
   * @param query
   * @returns
   */
  async getSchoolUserReadingReflectionBookDetail(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    return this.readingReflectionQueryService.getSchoolUserReadingReflectionBookDetail(schoolId, query)
  }

  /**
   * 生成Word文档
   * @param version
   * @param school
   * @param reflection
   * @param allReflectionContents
   * @param schoolLogoResizeBuffer
   * @param logoResizeBuffer
   * @param dateRange
   * @returns
   */
  async generateDocx(
    version,
    school,
    reflection,
    allReflectionContents,
    schoolLogoResizeBuffer,
    logoResizeBuffer,
    dateRange
  ) {
    allReflectionContents.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime()
      const dateB = new Date(b.createdAt).getTime()
      return dateB - dateA
    })
    return new Document({
      sections: allReflectionContents.map((content, index) => ({
        properties: {},
        children: [
          // 第一部分：Logo
          new Paragraph({
            children: [
              new ImageRun({
                data: schoolLogoResizeBuffer,
                type: 'png',
                transformation: { width: 100, height: 100 },
              }),
              new TextRun(' '.repeat(92)), // 添加间隔
              new ImageRun({
                data: logoResizeBuffer,
                type: 'png',
                transformation: { width: 120, height: 75 },
              }),
            ],
          }),

          // 空一行
          new Paragraph({
            children: [new TextRun('')],
          }),

          // 第二部分：表格信息
          new Table({
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('學校: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(school.name?.zh_HK ?? '')],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('學生姓名: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(reflection?.userName ?? '')],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('目前年級/班別: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(reflection?.currentGradeClassName ?? '')],
                      }),
                    ],
                  }),
                ],
              }),

              // 提交感想时年级/班别
              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('提交感想時年級/班別: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(reflection?.submitGradeClassName ?? ' ')],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('學號: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(reflection.studentSerialNo ?? '')],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('時段: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [
                          new TextRun(dateRange.startTime + ' 至 ' + dateRange.endTime),
                        ],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('閱讀書籍: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(`《${reflection.name?.zh_HK}》`)],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [
                          new TextRun(
                            version === EBookVersion.SUBSCRIPTION
                              ? '總閱讀時數(小時): '
                              : '總閱讀次數'
                          ),
                        ],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [
                          new TextRun(
                            version === EBookVersion.SUBSCRIPTION
                              ? reflection.totalReadingTime / 3600 + ' 小時'
                              : reflection.readingCount + ' 次'
                          ),
                        ],
                      }),
                    ],
                  }),
                ],
              }),

              new TableRow({
                children: [
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun('總閱讀感想數量: ')],
                      }),
                    ],
                  }),
                  new TableCell({
                    width: { size: 5000, type: 'dxa' },
                    children: [
                      new Paragraph({
                        children: [new TextRun(reflection.readingReflectionCount ?? 0)],
                      }),
                    ],
                  }),
                ],
              }),
            ],
          }),

          // 空一行
          new Paragraph({
            children: [new TextRun('')],
          }),
          // 第三部分：阅读感想内容
          new Paragraph({
            children: [
              new TextRun({
                text: `閱讀感想：`,
              }),
              new TextRun(' '.repeat(50)),
              new TextRun({
                text: `第 ${index + 1} 篇/共 ${allReflectionContents.length ?? 0} 篇`,
              }),
            ],
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `提交時間：${this.formatDate(content.createdAt)}`,
              }),
            ],
          }),
          // 空一行
          new Paragraph({
            children: [new TextRun('')],
          }),
          new Table({
            rows: [
              new TableRow({
                height: { value: 8000, rule: 'atLeast' },
                children: [
                  new TableCell({
                    width: { size: 90000, type: 'dxa' },
                    shading: { fill: 'FFFFFF' }, // 设置背景色为白色
                    borders: {
                      top: { style: 'single', size: 4, color: '000000' },
                      left: { style: 'single', size: 4, color: '000000' },
                      bottom: { style: 'single', size: 4, color: '000000' },
                      right: { style: 'single', size: 4, color: '000000' },
                    },
                    children: (content.content && typeof content.content === 'string'
                      ? content.content.split('\n')
                      : ['語音閱讀感想']
                    ).map((line) => {
                      return new Paragraph({
                        children: [
                          new TextRun({
                            text: line,
                          }),
                        ],
                      })
                    }),
                  }),
                ],
              }),
            ],
          }),
        ],
      })),
    })
  }
}
