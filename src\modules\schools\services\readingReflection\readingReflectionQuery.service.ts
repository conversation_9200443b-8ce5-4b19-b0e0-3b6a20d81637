import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Brackets, Repository } from 'typeorm'
import { Book, ReadRecord, ReferenceReadRecord, User } from '@/entities'
import { ReadingReflection } from '@/entities/readingReflection.entity'
import { ReferenceReadingReflection } from '@/entities/referenceReadingReflection.entity'
import { EBookVersion, EOrderDirection, EUserType } from '@/enums'
import { QueryReadingReflectionListDto } from '../../dto/readingReflection.dto'

@Injectable()
export class ReadingReflectionQueryService {
  constructor(
    @InjectRepository(ReadingReflection)
    private readonly readingReflectionRepository: Repository<ReadingReflection>,

    @InjectRepository(Book)
    private readonly bookRepository: Repository<Book>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,

    @InjectRepository(ReferenceReadingReflection)
    private readonly referenceReadingReflectionRepository: Repository<ReferenceReadingReflection>,

    @InjectRepository(ReadRecord)
    private readonly readRecordRepository: Repository<ReadRecord>,

    @InjectRepository(ReferenceReadRecord)
    private readonly referenceReadRecordRepository: Repository<ReferenceReadRecord>
  ) {}

  /**
   * 获取学校 阅读感想记录 参考馆 ｜ 订阅版
   * @param schoolId
   * @param query
   * @returns
   */
  async getSchoolUserReadingReflectionList(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    const orderField = query.orderField ?? 'created_at'
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    // 子查询：统计每本书用户的阅读感想次数
    const thoughtsSubQuery = repository
      .createQueryBuilder('thoughts')
      .select([
        `thoughts.book_id AS bookId`,
        `thoughts.user_id AS userId`,
        `COUNT(*) AS thoughtCount`,
        `MAX(CASE WHEN thoughts.content IS NOT NULL AND thoughts.content != '' THEN 1 ELSE 0 END) AS has_content`,
        `MAX(CASE WHEN thoughts.audio_url IS NOT NULL AND thoughts.audio_url != '' THEN 1 ELSE 0 END) AS has_audio`,
      ])
      .where('thoughts.school_id = :schoolId', { schoolId })
      .andWhere('thoughts.user_type = :userType', { userType: EUserType.STUDENT })
      .andWhere('thoughts.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('thoughts.book_id, thoughts.user_id')
      .getQuery()

    // 子查询：阅读统计
    const statsSubQuery =
      version === EBookVersion.SUBSCRIPTION
        ? `
        SELECT 
          reading.book_id AS bookId,
          reading.user_id AS userId,
          SUM(reading.reading_time) AS totalReadingTime
        FROM read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `
        : `
        SELECT 
          reading.book_id AS bookId,
          reading.user_id AS userId,
          COUNT(*) AS readingCount
        FROM reference_read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .withDeleted()
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    const userSubQueryBuilder = this.userRepository
      .createQueryBuilder('users')
      .withDeleted()
    const userSubQuery = userSubQueryBuilder.getQuery()
    const userSubQueryParams = userSubQueryBuilder.getParameters()

    // 主查询
    const queryBuilder = repository
      .createQueryBuilder(alias)
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `${alias}.book_id = book.book_id`,
        bookSubQueryParams
      )
      .leftJoin(
        `(${userSubQuery})`,
        'users',
        `${alias}.user_id = users.users_id`,
        userSubQueryParams
      )
      .leftJoin(
        'user_class',
        'current_user_class',
        'users.users_user_class_id = current_user_class.id'
      )
      .leftJoin(
        'user_class',
        'submit_user_class',
        `${alias}.user_class_id = submit_user_class.id`
      )
      .leftJoin(
        'grades',
        'current_grades',
        'current_user_class.grade_id = current_grades.id'
      )
      .leftJoin('grades', 'submit_grades', `${alias}.grade_id = submit_grades.id`)
      .leftJoin(
        `(${thoughtsSubQuery})`,
        'thoughtStats',
        'thoughtStats.bookId = book.book_id AND thoughtStats.userId = users.users_id'
      )
      .leftJoin(
        `(${statsSubQuery})`,
        'readStats',
        'readStats.bookId = book.book_id AND readStats.userId = users.users_id'
      )
      .select([
        `${alias}.id as id`,
        `${alias}.user_id as userId`,
        'book.book_id AS bookId',
        'book.book_name AS name',
        'users.users_given_name AS userName',
        'users.users_serial_no AS studentSerialNo',
        'users.users_email AS userEmail',
        'current_user_class.class AS currentClassName',
        'submit_user_class.class AS submitClassName',
        'current_grades.grade AS currentGradeName',
        'submit_grades.grade AS submitGradeName',
        'thoughtStats.thoughtCount AS readingReflectionCount',
        `${alias}.review_state AS reviewState`,
        `${alias}.flag_state AS flagState`,
        `${alias}.content AS refectionContent`,
        `${alias}.created_at`,
        version === EBookVersion.SUBSCRIPTION
          ? 'readStats.totalReadingTime AS totalReadingTime'
          : 'readStats.readingCount AS readingCount',
        'thoughtStats.has_content AS hasContent',
        'thoughtStats.has_audio AS hasAudio',
      ])
      .where(`${alias}.created_at BETWEEN :startTime AND :endTime`, {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .andWhere(`${alias}.user_type = :userType`, { userType: EUserType.STUDENT })
      .andWhere(`${alias}.school_id = :schoolId`, { schoolId })

    if (query.grade) {
      queryBuilder.andWhere(`${alias}.grade_id = :grade`, { grade: query.grade })
    }
    if (query.class) {
      queryBuilder.andWhere(`${alias}.user_class_id = :class`, { class: query.class })
    }
    if (query.flagState) {
      queryBuilder.andWhere(`${alias}.flag_state = :flagState`, {
        flagState: query.flagState,
      })
    }
    if (query.reviewState) {
      queryBuilder.andWhere(`${alias}.review_state = :reviewState`, {
        reviewState: query.reviewState,
      })
    }
    if (query.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('users.users_given_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          }).orWhere('book.book_name LIKE :keyword', { keyword: `%${query.keyword}%` })
        })
      )
    }

    const totalQueryBuilder = queryBuilder.clone()
    totalQueryBuilder
      .select('COUNT(*)', 'total')
      .offset(undefined)
      .limit(undefined)
      .orderBy(undefined)

    const totalRecords = await totalQueryBuilder.getRawOne()
    // 排序、分页
    queryBuilder.orderBy(orderField, orderDirection).offset(offset).limit(pageSize)
    const books = await queryBuilder.getRawMany()
    return {
      items: books.map(
        ({
          currentClassName,
          submitClassName,
          currentGradeName,
          submitGradeName,
          ...rest
        }) => ({
          ...rest,
          currentGradeClassName: `${currentGradeName} / ${currentClassName}`,
          submitGradeClassName: `${submitGradeName} / ${submitClassName}`,
          hasContent: Number(rest.hasContent),
          hasAudio: Number(rest.hasAudio),
        })
      ),
      total: parseInt(totalRecords?.total || 0, 10) || 0,
      pageIndex,
      pageSize,
    }
  }

  /**
   * 获取未提交阅读感想的记录
   * @param schoolId
   * @param query
   * @returns
   */
  async getUnsubmittedReadingReflectionList(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC

    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const defaultField =
      version === EBookVersion.SUBSCRIPTION
        ? 'SUM(record.reading_time)'
        : 'COUNT(record.id)'
    const orderField = query.orderField ?? defaultField

    const startTime = query.startTime
      ? new Date(query.startTime * 1000).toISOString()
      : null
    const endTime = query.endTime ? new Date(query.endTime * 1000).toISOString() : null

    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readRecordRepository
        : this.referenceReadRecordRepository

    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .withDeleted()
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    const queryBuilder = repository
      .createQueryBuilder('record')
      .select([
        'record.user_id AS userId',
        'record.book_id AS bookId',
        'COUNT(record.id) AS readingCount',
        version === EBookVersion.SUBSCRIPTION
          ? 'SUM(record.reading_time) AS totalReadingTime'
          : 'COUNT(record.id) AS totalReadingTime',
        'user.given_name AS userName',
        'book.book_name AS bookName',
        'user.serial_no AS studentSerialNo',
        'current_user_class.class AS currentClassName',
        'current_grades.grade AS currentGradeName',
      ])
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `record.book_id = book.book_id`,
        bookSubQueryParams
      )
      .leftJoin('users', 'user', 'user.id = record.user_id')
      .leftJoin(
        'user_class',
        'current_user_class',
        'user.user_class_id = current_user_class.id'
      )
      .leftJoin(
        'grades',
        'current_grades',
        'current_user_class.grade_id = current_grades.id'
      )
      .leftJoin(`${alias}`, 'reflection', 'record.user_id = reflection.user_id')
      .where('record.school_id = :schoolId', { schoolId })
      .andWhere('user.type = :userType', { userType: EUserType.STUDENT })
      .andWhere('reflection.user_id IS NULL')

    if (startTime && endTime) {
      queryBuilder.andWhere('record.created_at BETWEEN :startTime AND :endTime', {
        startTime,
        endTime,
      })
    }
    if (query.grade) {
      queryBuilder.andWhere('current_user_class.grade_id = :grade', {
        grade: query.grade,
      })
    }
    if (query.class) {
      queryBuilder.andWhere('current_user_class.id = :class', { class: query.class })
    }
    if (query.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('book.book_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          }).orWhere('user.given_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          })
        })
      )
    }
    // 统计总数
    const totalQueryBuilder = queryBuilder.clone()
    totalQueryBuilder
      .select('COUNT(DISTINCT record.user_id, record.book_id)', 'total')
      .offset(undefined)
      .limit(undefined)
      .orderBy(undefined)

    const totalResult = await totalQueryBuilder.getRawOne()
    const total = parseInt(totalResult?.total || 0, 10) || 0

    queryBuilder.groupBy('record.user_id, record.book_id')
    queryBuilder.orderBy(orderField, orderDirection).offset(offset).limit(pageSize)

    const res = await queryBuilder.getRawMany()

    return {
      items: res.map(({ currentClassName, currentGradeName, ...rest }) => ({
        ...rest,
        currentGradeClassName: `${currentGradeName} / ${currentClassName}`,
      })),
      total: total,
      pageIndex,
      pageSize,
    }
  }

  /**
   * 获取阅读感想记录 参考馆 ｜ 订阅版
   * @param userId
   * @param query
   * @returns
   */
  async getReadingReflectionList(userId: number, query: QueryReadingReflectionListDto) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    const orderField = query.orderField ?? 'count'
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    await repository.query('SET SESSION group_concat_max_len = 10000')

    // 构建子查询
    const subQuery = repository
      .createQueryBuilder('readingReflection')
      .select([
        'readingReflection.book_id AS bookId',
        'COUNT(readingReflection.book_id) AS count',
      ])
      .where('readingReflection.user_id = :userId', { userId })
      .andWhere('readingReflection.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('readingReflection.book_id')

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .leftJoin('book.authors', 'authors')
      .select([
        'book.id',
        'book.name',
        'book.cover_url',
        'GROUP_CONCAT(DISTINCT authors.name) AS authorsList',
      ])
      .withDeleted()
      .groupBy('book.id, book.name, book.cover_url')
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    // 主查询
    const queryBuilder = repository
      .createQueryBuilder('readingReflection')
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `readingReflection.book_id = book.book_id`,
        bookSubQueryParams
      )
      .innerJoin(
        `(${subQuery.getQuery()})`,
        'readCount',
        'readCount.bookId = readingReflection.book_id'
      )
      .select([
        'book.book_id AS id',
        'book.book_name AS name',
        'book.cover_url AS cover_url',
        'book.authorsList AS authors',
        'readCount.count AS count',
        'MAX(readingReflection.created_at) AS latest_created_at',
      ])
      .groupBy('book.book_id, book.book_name, readCount.count')
      .setParameters(subQuery.getParameters())

    const books = await queryBuilder
      .orderBy(orderField, orderDirection)
      .offset(offset)
      .limit(pageSize)
      .getRawMany()

    const total = await repository
      .createQueryBuilder('readingReflection')
      .select('COUNT(DISTINCT book.book_id)', 'count')
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `readingReflection.book_id = book.book_id`,
        bookSubQueryParams
      )
      .innerJoin(
        `(${subQuery.getQuery()})`,
        'readCount',
        'readCount.bookId = readingReflection.book_id'
      )
      .setParameters(subQuery.getParameters())
      .getRawOne()

    return {
      items: books.map((book) => ({
        ...book,
        authors: book.authors ? JSON.parse('[' + book.authors + ']') : [],
      })),
      total: total.count,
      pageIndex,
      pageSize,
    }
  }

  async getSchoolUserReadingReflectionBookDetail(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    // 子查询：统计每本书用户的阅读感想次数
    const thoughtsSubQuery = repository
      .createQueryBuilder('thoughts')
      .select([
        `thoughts.book_id AS bookId`,
        `thoughts.user_id AS userId`,
        `COUNT(*) AS thoughtCount`,
      ])
      .where('thoughts.school_id = :schoolId', { schoolId })
      .andWhere('thoughts.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('thoughts.book_id, thoughts.user_id')
      .getQuery()

    // 子查询：阅读统计
    const statsSubQuery =
      version === EBookVersion.SUBSCRIPTION
        ? `
        SELECT
          reading.book_id AS bookId,
          reading.user_id AS userId,
          SUM(reading.reading_time) AS totalReadingTime
        FROM read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `
        : `
        SELECT
          reading.book_id AS bookId,
          reading.user_id AS userId,
          COUNT(*) AS readingCount
        FROM reference_read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `
    const contentSubQuery = repository
      .createQueryBuilder('reflections')
      .select([
        `reflections.user_id AS userId`,
        `reflections.book_id AS bookId`,
        `JSON_ARRAYAGG(JSON_OBJECT('content', reflections.content, 'createdAt', reflections.created_at)) AS contents`,
      ])
      .groupBy('reflections.user_id, reflections.book_id')
      .getQuery()

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .select(['book.id', 'book.name'])
      .withDeleted()
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    // 主查询
    const queryBuilder = repository
      .createQueryBuilder(alias)
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `${alias}.book_id = book.book_id`,
        bookSubQueryParams
      )
      .leftJoin('users', 'users', `${alias}.user_id = users.id`)
      .leftJoin(
        'user_class',
        'current_user_class',
        'users.user_class_id = current_user_class.id'
      )
      .leftJoin(
        'user_class',
        'submit_user_class',
        `${alias}.user_class_id = submit_user_class.id`
      )
      .leftJoin(
        'grades',
        'current_grades',
        'current_user_class.grade_id = current_grades.id'
      )
      .leftJoin('grades', 'submit_grades', `${alias}.grade_id = submit_grades.id`)
      .leftJoin(
        `(${thoughtsSubQuery})`,
        'thoughtStats',
        'thoughtStats.bookId = book.book_id AND thoughtStats.userId = users.id'
      )
      .leftJoin(
        `(${statsSubQuery})`,
        'readStats',
        'readStats.bookId = book.book_id AND readStats.userId = users.id'
      )
      .leftJoin(
        `(${contentSubQuery})`,
        'refectionContent',
        'refectionContent.bookId = book.book_id AND refectionContent.userId = users.id'
      )
      .select([
        `${alias}.user_id as userId`,
        'book.book_id AS id',
        'book.book_name AS name',
        'users.given_name AS userName',
        'users.serial_no AS studentSerialNo',
        'users.email AS userEmail',
        'max(refectionContent.contents) AS allReflectionContents',
        'max(current_user_class.class) AS currentClassName',
        'max(submit_user_class.class) AS submitClassName',
        'max(current_grades.grade) AS currentGradeName',
        'max(submit_grades.grade) AS submitGradeName',
        'max(thoughtStats.thoughtCount) AS readingReflectionCount',
        `max(${alias}.created_at) AS latest_created_at`,
        version === EBookVersion.SUBSCRIPTION
          ? 'max(readStats.totalReadingTime) AS totalReadingTime'
          : 'max(readStats.readingCount) AS readingCount',
      ])
      .groupBy(`book.book_id, ${alias}.user_id`)
      .where(`${alias}.created_at BETWEEN :startTime AND :endTime`, {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .andWhere(`${alias}.school_id = :schoolId`, { schoolId })
      .andWhere(`${alias}.book_id = :bookId`, { bookId: query.bookId })
      .andWhere(`${alias}.user_id = :userId`, { userId: query.userId })

    if (query.grade) {
      queryBuilder.andWhere(`${alias}.grade_id = :grade`, { grade: query.grade })
    }
    if (query.class) {
      queryBuilder.andWhere(`${alias}.user_class_id = :class`, { class: query.class })
    }
    if (query.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('users.given_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          }).orWhere('book.book_name LIKE :keyword', { keyword: `%${query.keyword}%` })
        })
      )
    }

    const totalQueryBuilder = queryBuilder.clone()
    totalQueryBuilder
      .select('COUNT(*)', 'total')
      .offset(undefined)
      .limit(undefined)
      .orderBy(undefined)

    const totalRecords = await totalQueryBuilder.getRawOne()
    // 排序、分页
    queryBuilder.orderBy('users.serial_no', orderDirection).offset(offset).limit(pageSize)
    const books = await queryBuilder.getRawMany()

    return {
      items: books.map(
        ({
          currentClassName,
          submitClassName,
          currentGradeName,
          submitGradeName,
          ...rest
        }) => ({
          ...rest,
          currentGradeClassName: `${currentGradeName} / ${currentClassName}`,
          submitGradeClassName: `${submitGradeName} / ${submitClassName}`,
        })
      ),
      total: parseInt(totalRecords?.total || 0, 10) || 0,
      pageIndex,
      pageSize,
    }
  }

  async getSchoolUserReadingReflectionBookIds(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .select(['book.id', 'book.name'])
      .withDeleted()
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    // 主查询
    const queryBuilder = repository
      .createQueryBuilder(alias)
      .select([`${alias}.book_id AS bookId`, `${alias}.user_id AS userId`])
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `${alias}.book_id = book.book_id`,
        bookSubQueryParams
      )
      .leftJoin('users', 'users', `${alias}.user_id = users.id`)
      .groupBy(`${alias}.book_id, ${alias}.user_id`)
      .where(`${alias}.created_at BETWEEN :startTime AND :endTime`, {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .andWhere(`${alias}.school_id = :schoolId`, { schoolId })
      .andWhere(`${alias}.user_type = :userType`, { userType: EUserType.STUDENT })

    if (query.bookId) {
      queryBuilder.andWhere(`${alias}.book_id = :bookId`, { bookId: query.bookId })
    }

    if (query.grade) {
      queryBuilder.andWhere(`${alias}.grade_id = :grade`, { grade: query.grade })
    }

    if (query.class) {
      queryBuilder.andWhere(`${alias}.user_class_id = :class`, { class: query.class })
    }

    if (query.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('users.given_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          }).orWhere('book.book_name LIKE :keyword', { keyword: `%${query.keyword}%` })
        })
      )
    }

    // 使用 `getRawMany` 获取原始结果
    const books = await queryBuilder.getRawMany()

    return books.map((row) => ({
      bookId: row.bookId,
      userId: row.userId,
    }))
  }

  async getSchoolUserReadingReflectionDetail(
    schoolId: number,
    query: QueryReadingReflectionListDto
  ) {
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 10
    const offset = (pageIndex - 1) * pageSize
    const orderDirection = query.orderDirection ?? EOrderDirection.DESC
    const version = query.version ?? EBookVersion.SUBSCRIPTION
    const repository =
      version === EBookVersion.SUBSCRIPTION
        ? this.readingReflectionRepository
        : this.referenceReadingReflectionRepository
    const alias =
      version === EBookVersion.SUBSCRIPTION
        ? 'reading_reflection'
        : 'reference_reading_reflection'

    // 子查询：统计每本书用户的阅读感想次数
    const thoughtsSubQuery = repository
      .createQueryBuilder('thoughts')
      .select([
        `thoughts.book_id AS bookId`,
        `thoughts.user_id AS userId`,
        `COUNT(*) AS thoughtCount`,
      ])
      .where('thoughts.school_id = :schoolId', { schoolId })
      .andWhere('thoughts.created_at BETWEEN :startTime AND :endTime', {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('thoughts.book_id, thoughts.user_id')
      .getQuery()

    // 子查询：阅读统计
    const statsSubQuery =
      version === EBookVersion.SUBSCRIPTION
        ? `
        SELECT
          reading.book_id AS bookId,
          reading.user_id AS userId,
          SUM(reading.reading_time) AS totalReadingTime
        FROM read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `
        : `
        SELECT
          reading.book_id AS bookId,
          reading.user_id AS userId,
          COUNT(*) AS readingCount
        FROM reference_read_record AS reading
        WHERE reading.created_at BETWEEN :startTime AND :endTime
          AND reading.school_id = :schoolId
        GROUP BY reading.book_id, reading.user_id
      `
    const contentSubQuery = repository
      .createQueryBuilder('reflections')
      .select([
        `reflections.user_id AS userId`,
        `reflections.book_id AS bookId`,
        `JSON_ARRAYAGG(JSON_OBJECT('content', reflections.content, 'createdAt', reflections.created_at)) AS contents`,
      ])
      .where(`reflections.created_at BETWEEN :startTime AND :endTime`, {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .groupBy('reflections.user_id, reflections.book_id')
      .getQuery()

    const bookSubQueryBuilder = this.bookRepository
      .createQueryBuilder('book')
      .select(['book.id', 'book.name'])
      .withDeleted()
      .groupBy('book.id, book.name')
    const bookSubQuery = bookSubQueryBuilder.getQuery()
    const bookSubQueryParams = bookSubQueryBuilder.getParameters()

    // 主查询
    const queryBuilder = repository
      .createQueryBuilder(alias)
      .leftJoin(
        `(${bookSubQuery})`,
        'book',
        `${alias}.book_id = book.book_id`,
        bookSubQueryParams
      )
      .leftJoin('users', 'users', `${alias}.user_id = users.id`)
      .leftJoin(
        'user_class',
        'current_user_class',
        'users.user_class_id = current_user_class.id'
      )
      .leftJoin(
        'user_class',
        'submit_user_class',
        `${alias}.user_class_id = submit_user_class.id`
      )
      .leftJoin(
        'grades',
        'current_grades',
        'current_user_class.grade_id = current_grades.id'
      )
      .leftJoin('grades', 'submit_grades', `${alias}.grade_id = submit_grades.id`)
      .leftJoin(
        `(${thoughtsSubQuery})`,
        'thoughtStats',
        'thoughtStats.bookId = book.book_id AND thoughtStats.userId = users.id'
      )
      .leftJoin(
        `(${statsSubQuery})`,
        'readStats',
        'readStats.bookId = book.book_id AND readStats.userId = users.id'
      )
      .leftJoin(
        `(${contentSubQuery})`,
        'refectionContent',
        'refectionContent.bookId = book.book_id AND refectionContent.userId = users.id'
      )
      .select([
        `${alias}.user_id as userId`,
        'book.book_id AS bookId',
        'book.book_name AS name',
        'users.given_name AS userName',
        'users.serial_no AS studentSerialNo',
        'users.email AS userEmail',
        'max(refectionContent.contents) AS allReflectionContents',
        'max(current_user_class.class) AS currentClassName',
        'max(submit_user_class.class) AS submitClassName',
        'max(current_grades.grade) AS currentGradeName',
        'max(submit_grades.grade) AS submitGradeName',
        'max(thoughtStats.thoughtCount) AS readingReflectionCount',
        `max(${alias}.created_at) AS latest_created_at`,
        version === EBookVersion.SUBSCRIPTION
          ? 'max(readStats.totalReadingTime) AS totalReadingTime'
          : 'max(readStats.readingCount) AS readingCount',
      ])
      .groupBy(`book.book_id, ${alias}.user_id`)
      .where(`${alias}.created_at BETWEEN :startTime AND :endTime`, {
        startTime: new Date(query.startTime * 1000).toISOString(),
        endTime: new Date(query.endTime * 1000).toISOString(),
      })
      .andWhere(`${alias}.school_id = :schoolId`, { schoolId })
      .andWhere(`${alias}.user_type = :userType`, { userType: EUserType.STUDENT })

    if (query.grade) {
      queryBuilder.andWhere(`${alias}.grade_id = :grade`, { grade: query.grade })
    }
    if (query.class) {
      queryBuilder.andWhere(`${alias}.user_class_id = :class`, { class: query.class })
    }
    if (query.keyword) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('users.given_name LIKE :keyword', {
            keyword: `%${query.keyword}%`,
          }).orWhere('book.book_name LIKE :keyword', { keyword: `%${query.keyword}%` })
        })
      )
    }

    const totalQueryBuilder = queryBuilder.clone()
    totalQueryBuilder
      .select('COUNT(*)', 'total')
      .offset(undefined)
      .limit(undefined)
      .orderBy(undefined)

    const totalRecords = await totalQueryBuilder.getRawOne()
    // 排序、分页
    queryBuilder.orderBy('users.serial_no', orderDirection).offset(offset).limit(pageSize)
    const books = await queryBuilder.getRawMany()

    return {
      items: books.map(
        ({
          currentClassName,
          submitClassName,
          currentGradeName,
          submitGradeName,
          ...rest
        }) => ({
          ...rest,
          currentGradeClassName: `${currentGradeName} / ${currentClassName}`,
          submitGradeClassName: `${submitGradeName} / ${submitClassName}`,
        })
      ),
      total: parseInt(totalRecords?.total || 0, 10) || 0,
      pageIndex,
      pageSize,
    }
  }
}
