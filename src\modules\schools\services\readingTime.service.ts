import { Injectable } from '@nestjs/common'
import moment from 'moment' 
import R from 'ramda'
import { generateUniqueId, RedisService, RedlockService } from '@/common'
import { SchoolBalance } from '@/entities'
import {  IBookRepo, ILeaderBoardService, IUserRepo,
} from '@/modules/shared/interfaces'
import {
  getReadingRecordCacheKey,
  getReadingSchoolsSetName,
  getReadingUsersSetName,
  getStudentReadingTimeLockKey,
  getUserBalanceCacheKey,
  getUserCacheKey,
  READING_SESSION_SET,
  USER_READING_SESSION_KEY,
} from '../../constants'
import {
  InvalidReadingTimeReportException,
  ReadingSessionNotExistException,
} from '../exception'
import { ReadRecordRepository } from '../repositories'
import { balanceValidator } from '../validators'
import { SchoolService } from './school.service'
import { SchoolBalanceService } from './schoolBalance.service'
import { UserBalanceService } from './userBalance.service'

@Injectable()
export class ReadingTimeService {
  private readonly sessionExpiredIn = 30 * 60

  constructor(
    private readonly readRecordRepository: ReadRecordRepository,
    private readonly redisService: RedisService,
    private readonly userRepository: IUserRepo,
    private readonly bookRepositories: IBookRepo,
    private readonly userBalanceService: UserBalanceService,
    private readonly leaderBoardService: ILeaderBoardService,
    private readonly schoolBalanceService: SchoolBalanceService,
    private readonly schoolService: SchoolService,
    private readonly redlockService: RedlockService
  ) {}

  async startReading(userId: number, bookId: number) {
    let balance: SchoolBalance
    const [user, book] = await Promise.all([
      this.userRepository.findUserWithCache(userId),
      this.bookRepositories.getBook(
        { id: bookId },
        { relations: ['publisher', 'authors'] }
      ),
    ])

    const [school, userBalance] = await Promise.all([
      this.schoolService.getSchoolWithCache(user.school.id),
      this.userBalanceService.getBalanceWithCache(userId),
    ])

    if (school.isSharingTime) {
      balance = (await this.schoolBalanceService.getSchoolBalanceWithCache(
        school.id
      )) as any
      balanceValidator().hasSharingTime(balance)
    } else {
      balanceValidator().hasReadingTime(userBalance as any)
    }

    const payload = {
      userId,
      bookId,
      class: user.userClass?.id ?? null,
      grade: user.userClass?.gradeId ?? null,
      userType: user.type,
      schoolId: user.school.id,
      publisherId: book.publisher?.id,
      authorId: book.authors.map((item) => item.id),
      region: school.region,
      isHidden: book.hiddeSchoolIds?.includes(school.id),
    }

    const preSessionId = await this.redisService.get(this.getUserKey(school.id, userId))
    if (preSessionId) {
      // end last reading
      try {
        await this.endReading(school.isSharingTime, userId, preSessionId)
      } catch (error) {
        console.log('end last reading error', error)
      }
    }

    const record = await this.readRecordRepository.createRecord({
      ...payload,
      readingTime: 0,
      region: school.region,
    })

    const { sessionId } = await this.createReadingSession(userId, {
      ...payload,
      id: record.id,
    })

    // console.log(
    //   `******startReading******${payload.schoolId}:${payload.userId}`,
    //   sessionId,
    //   record.id,
    // )

    await Promise.all([
      this.redisService.sadd(getReadingSchoolsSetName(), school.id),
      this.redisService.sadd(getReadingUsersSetName(school.id), userId as any),
    ])

    return {
      sessionId,
      total: school.isSharingTime ? balance.totalBoughtQuota : userBalance.totalQuota,
      usedQuota: school.isSharingTime ? balance.usedQuota : userBalance.usedQuota,
    }
  }

  async consumeReadingTime(userId: number, readingTime: number, sessionId: string) {
    await this.redisService.expire(
      this.getSessionKey(userId, sessionId),
      this.sessionExpiredIn
    )

    const payload = await this.getSession(userId, sessionId)
    const readRecord = await this.readRecordRepository.getRecordWithCache(
      userId,
      payload.id
    )
    if (readRecord.readingTime + readingTime > moment().unix() - readRecord.createdAt + 8)
    {throw new InvalidReadingTimeReportException()}

    const res = { sessionId }

    return this.redlockService.lockWrapper(
      getStudentReadingTimeLockKey(payload.schoolId, userId),
      10 * 1000,
      async () => {
        const school = await this.schoolService.getSchoolWithCache(payload.schoolId)

        await this.leaderBoardService.increaseReadingTime(
          payload.schoolId,
          readingTime,
          payload.bookId,
          payload.isHidden
        )

        const record = await this.readRecordRepository.getRecordWithCache(
          payload.userId,
          payload.id
        )

        if (new Date(record.createdAt * 1000).getHours() !== new Date().getHours()) {
          await this.splitReadingTime(
            school.isSharingTime,
            userId,
            sessionId,
            readingTime
          )
        } else {
          await this.readRecordRepository.updateReadRecord(
            payload.userId,
            payload.id,
            readingTime
          )
        }

        const balance = await this.userBalanceService.useReadingTime(
          school.isSharingTime,
          userId,
          readingTime
        )

        // 将学生的时数加入统计队列
        await this.schoolBalanceService.enqueueReadingTime(payload.schoolId, readingTime)

        // 此处获取学校余额
        const schoolBalance = await this.schoolBalanceService.getSchoolBalanceWithCache(
          payload.schoolId
        )

        return {
          totalQuota: school.isSharingTime
            ? schoolBalance.totalBoughtQuota
            : balance.totalQuota,
          usedQuota: school.isSharingTime ? schoolBalance.usedQuota : balance.usedQuota,
          isSharingTime: school.isSharingTime,
          ...res,
        }
      }
    )
  }

  async endReading(isSharingTime = false, userId: number, sessionId: string) {
    const payload = await this.getSession(userId, sessionId)

    await this.readRecordRepository.clearReadingRecord(
      userId,
      payload.id,
      payload.schoolId
    )
    await this.userBalanceService.clearUserBalance(isSharingTime, userId)
    await this.schoolBalanceService.flushSchoolReadingTime(payload.schoolId)
    await this.removeSession(userId, payload.schoolId, sessionId)
    await this.redisService.srem(getReadingUsersSetName(payload.schoolId), userId as any)
    await this.redisService.del(getUserCacheKey(userId))
  }

  async splitReadingTime(
    isSharingTime = false,
    userId: number,
    sessionId: string,
    readingTime: number
  ) {
    const payload = await this.getSession(userId, sessionId)
    await this.readRecordRepository.clearReadingRecord(
      userId,
      payload.id,
      payload.schoolId
    )
    await this.userBalanceService.flushUserReadingTime(isSharingTime, userId)
    await this.schoolBalanceService.flushSchoolReadingTime(payload.schoolId)

    const newRecord = await this.readRecordRepository.createRecord({
      ...R.pick(
        [
          'userId',
          'userType',
          'bookId',
          'authorId',
          'schoolId',
          'class',
          'publisherId',
          'grade',
          'region',
        ],
        payload
      ),
      readingTime,
    })

    await this.redisService.set(
      this.getSessionKey(userId, sessionId),
      JSON.stringify({ ...payload, id: newRecord.id }),
      this.sessionExpiredIn
    )
  }

  private async getSession(userId: number, sessionId: string) {
    const session = await this.redisService.get(this.getSessionKey(userId, sessionId))

    if (!session) {
      throw new ReadingSessionNotExistException()
    }
    try {
      return JSON.parse(session)
    } catch (error) {
      throw new ReadingSessionNotExistException()
    }
  }

  private async removeSession(userId: number, schoolId: number, sessionId: string) {
    await this.redisService.del(this.getSessionKey(userId, sessionId))
    await this.redisService.del(this.getUserKey(schoolId, userId))
    await this.redisService.srem(
      READING_SESSION_SET,
      this.getSessionKey(userId, sessionId)
    )
    await this.redisService.del(sessionId)
  }

  private async createReadingSession(userId: number, payload: any) {
    const sessionId = generateUniqueId('reading')
    await this.redisService.set(
      this.getSessionKey(userId, sessionId),
      JSON.stringify(payload),
      this.sessionExpiredIn
    )

    await this.redisService.set(this.getUserKey(payload.schoolId, userId), sessionId)

    await this.redisService.sadd(
      READING_SESSION_SET,
      this.getSessionKey(userId, sessionId)
    )

    await this.redisService.hmset(sessionId, {
      recordKey: getReadingRecordCacheKey(userId, payload.id),
      userBalanceKey: getUserBalanceCacheKey(userId),
      userId,
      schoolId: payload.schoolId,
    })

    return { sessionId }
  }

  private getSessionKey(userId: number, sessionId: string) {
    return `${USER_READING_SESSION_KEY}${userId}:${sessionId}`
  }

  private getUserKey(schoolId: number, userId: number) {
    return `school:${schoolId}:user:${userId}:sessionId`
  }
}
