import { Injectable } from '@nestjs/common'
import R from 'ramda'
import { DataSource } from 'typeorm'
import { ELocaleType } from '@/common'
import { IUserRepo } from '@/modules/shared/interfaces'
import { OperationLogService } from '../../system/services'
import { DistributionTimeDto } from '../dto'
import { ShareReadingTimeException } from '../exception'
import { SchoolService } from './school.service'
import { SchoolBalanceService } from './schoolBalance.service'
import { UserBalanceService } from './userBalance.service'

@Injectable()
export class ReadingTimeManagerService {
  constructor(
    private readonly userRepository: IUserRepo,
    private readonly schoolBalanceService: SchoolBalanceService,
    private readonly userBalanceService: UserBalanceService,
    private readonly logService: OperationLogService,
    private readonly schoolService: SchoolService,
    private readonly dataSource: DataSource
  ) {}
  async distributionTimeToUsers(user: any, data: DistributionTimeDto) {
    const school = await this.schoolService.findOne({ where: { id: user.schoolId } })
    if (school.isSharingTime) {
      throw new ShareReadingTimeException()
    }
    await this.dataSource.transaction(async (entityManager) => {
      const users = await this.userRepository.findUsers({ ids: data.userIds })
      const userIds = users.map((item) => item.id)

      await this.schoolBalanceService.distributeStudentReadingTime(
        user.schoolId,
        data.time * users.length,
        entityManager
      )
      await this.userBalanceService.distributeReadingTime(
        data.time,
        userIds,
        entityManager
      )

      const hour = data.time / 60 / 60
      await this.logService.createLog({
        user,
        metaData: { userIds: data.userIds },
        operation: `分配${hour.toFixed(2)}小時給${users
          .slice(0, 3)
          .map((item) => `“${item.email}”`)
          .join(',')} ${users.length > 3 ? `等${users.length}个帳戶` : '帳戶'}`,
      })
    })
    await this.userRepository.updateUsers(data.userIds, { hasNotifed: false })
  }

  async revokeTimeFromUsers(user: any, data: DistributionTimeDto, isAutomatic = false) {
    const school = await this.schoolService.findOne({ where: { id: user.schoolId } })
    if (school.isSharingTime) {
      throw new ShareReadingTimeException()
    }

    return this.dataSource.transaction(async (entityManager) => {
      const res = await this.userBalanceService.revokeReadingTime(
        data.time,
        data.userIds,
        entityManager,
        user
      )

      const totalTime = R.sum(res.map((x) => x.revokedTime))
      await this.schoolBalanceService.revokeStudentReadingTime(
        user.schoolId,
        totalTime,
        entityManager
      )

      if (!isAutomatic) {
        const users = await this.userRepository.findUsers({
          ids: data.userIds.slice(0, 3),
        })
        const hour = data.time / 60 / 60
        await this.logService.createLog({
          user,
          metaData: { userIds: data.userIds },
          operation: `扣減${users
            .slice(0, 3)
            .map((item) => `“${item.email}”`)
            .join(',')}${
            data.userIds.length > 3 ? `等${data.userIds.length}个帳戶` : '帳戶'
          }${hour.toFixed(2)}小時`,
        })

        const successCount = res.filter((item) => item.result).length

        return {
          totalTime,
          result:
            res.length - successCount > 0
              ? {
                  [`${ELocaleType.EN_UK}`]: `${
                    res.length - successCount
                  } users don't have enough reading hours to be deducted, other users' reading hours have been deducted successfully.`,
                  [`${ELocaleType.ZH_HK}`]: `${
                    res.length - successCount
                  }個用戶時數不夠，無法順利扣減時數，其他用戶已經完成扣減時數`,
                }
              : undefined,
        }
      }
    })
  }
}
