import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { ReferenceReadRecord } from '@/entities'

@Injectable()
export class ReferenceReadStatisticService {
  constructor(
    @InjectRepository(ReferenceReadRecord)
    private readonly model: Repository<ReferenceReadRecord>
  ) {}

  async getTotalReadCounts(schoolId: number, bookIds: number[]) {
    if (bookIds.length === 0) {return []}
    return this.model.query(
      `select book_id as bookId, count(distinct(user_id)) as total from reference_read_record where school_id = ${schoolId} and book_id in (${bookIds.join(
        ','
      )}) group by book_id`
    )
  }
}
