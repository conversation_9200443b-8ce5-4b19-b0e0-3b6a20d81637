import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import R from 'ramda'
import {
  EntityManager,
  FindOneOptions,
  FindOptionsWhere,
  In,
  Like,
  Not,
  Raw,
  Repository,
} from 'typeorm'
import { PAGE_SIZE } from '@/modules/constants'
import { ISchoolService } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import {
  And,
  ELocaleType,
  ExcelService,
  generateUniqueId,
  ListingQueryBuilder,
  Or,
  PageRequest,
  RedisService,
  SessionService,
} from '../../../common'
import { School, SchoolBalance, ScienceContracts } from '../../../entities'
import { EBookVersion, ESchoolStatus, ESchoolVersion } from '../../../enums'
import { nameLikeCondition, schoolExcelData, schoolExcelDataV2 } from '../../../utils'
import { DuplicatedSchoolName } from '../../account/exception'
import { SCHOOL_CONTRACTS } from '../constants'
import {
  CreateScienceContractDto,
  getSchoolResponse,
  ListSchoolDto,
  ModifySchoolDto,
} from '../dto'
import { SchoolNotExistException } from '../exception'

@Injectable()
export class SchoolService implements ISchoolService{
  constructor(
    @InjectRepository(School)
    private readonly schoolRepository: Repository<School>,
    @InjectRepository(ScienceContracts)
    private readonly scienceContractsRepository: Repository<ScienceContracts>,
    private readonly excelService: ExcelService,
    private readonly redisService: RedisService,
    private readonly sessionService: SessionService,
    private readonly logService: LogService
  ) {}

  async createScienceContracts(
    schoolId: number,
    data: CreateScienceContractDto,
    admin: any,
    manager?: EntityManager
  ) {
    // 开启并且有合约编码
    if (data.hasScienceRoom && data.contractNo) {
      // 查找是否已存在相同合约号的合约
      let contract = await manager.findOne(ScienceContracts, {
        where: { contractNo: data.contractNo, school: { id: schoolId } },
      })

      // 合约不存在则创建
      if (!contract) {
        contract = await manager.save(ScienceContracts, {
          contractNo: data.contractNo,
          gradeCodes: data.gradeCodes,
          school: { id: schoolId },
          createdBy: R.pick(
            ['id', 'name', 'email', 'userId', 'profileImage', 'familyName', 'givenName'],
            admin
          ),
        })
      }

      // 获取最新的合约信息
      const latestContract = await manager
        .createQueryBuilder(ScienceContracts, 'science_contracts')
        .select(['science_contracts.contractNo', 'science_contracts.createdAt'])
        .where('science_contracts.school_id = :schoolId', { schoolId })
        .orderBy('science_contracts.createdAt', 'DESC')
        .getOne()

      // 如果存在最新合约，并且合约号相同，则更新年级信息
      if (latestContract && latestContract.contractNo === contract.contractNo) {
        await manager.update(
          ScienceContracts,
          { contractNo: data.contractNo },
          {
            gradeCodes: data.gradeCodes,
            updatedBy: R.pick(
              [
                'id',
                'name',
                'email',
                'userId',
                'profileImage',
                'familyName',
                'givenName',
              ],
              admin
            ),
          }
        )
      }
    }

    // 更新学校的科学室状态
    await manager.update(
      School,
      { id: schoolId },
      { hasScienceRoom: data.hasScienceRoom }
    )
  }
  async findOne(options?: FindOneOptions<School>) {
    const school = await this.schoolRepository.findOne(options)
    if (!school) {throw new SchoolNotExistException()}
    return school
  }

  async getSchoolWithCache(id: number) {
    const key = this.getSchoolKey(id)
    let school = await this.redisService.getByJson(key)

    if (!school) {
      const _school = await this.schoolRepository.findOne({ where: { id } })
      if (!_school) {throw new SchoolNotExistException()}

      school = {
        id: _school.id,
        isSharingTime: _school.isSharingTime,
        region: _school.region,
      }

      await this.redisService.set(key, JSON.stringify(school))
    }
    return school
  }

  listSchools(options?: ListSchoolDto) {
    const {
      keyword,
      schoolId,
      name,
      status,
      buyContracts,
      region,
      orderDirection = 'DESC',
      pageIndex = 1,
      pageSize = 10,
    } = options || {}

    const condition = []

    const keyworkCondition = []
    if (keyword) {
      keyworkCondition.push([{ name: Like(`%${keyword}%`) }])
      condition.push(Or(keyworkCondition))
    }

    if (schoolId) {condition.push({ schoolId })}
    if (name) {condition.push({ name })}
    if (status) {condition.push({ status })}
    if (region) {
      const regionList = typeof region === 'string' ? region.split(',') : region
      condition.push({ region: In(regionList) })
    }

    if (buyContracts?.length) {
      const contractCondition = []
      const contractTypes = ['SUBSCRIPTION', 'REFERENCE']
      contractTypes.forEach((type) => {
        if (buyContracts.includes(type)) {
          contractCondition.push({ version: Like(`%${type}%`) })
        }
      })

      if (buyContracts.includes('hasScienceRoom')) {
        contractCondition.push({ hasScienceRoom: SCHOOL_CONTRACTS.hasScienceRoom })
      }

      if (buyContracts.includes('hasAssistant')) {
        contractCondition.push({ hasAssistant: SCHOOL_CONTRACTS.hasAssistant })
      }

      if (contractCondition.length) {
        condition.push(Or(contractCondition))
      }
    }
    return ListingQueryBuilder.new<School, School>()
      .repository(this.schoolRepository)
      .where(And(condition))
      .pageIndex(pageIndex)
      .pageSize(pageSize)
      .order({ joinedAt: orderDirection })
      .query(getSchoolResponse)
  }

  searchSchool(name: string) {
    const alias = 's'

    const builder = this.schoolRepository.createQueryBuilder(alias)
    if (name) {
      const condition = nameLikeCondition(name, alias)
      builder.where(condition.where, condition.parameter)
    }
    return builder.getMany()
  }

  searchSchoolWithFilters(filters: {
    name?: string
    filterStatus?: string
    region?: string
    buyContracts?: string
  }) {
    const alias = 's'
    const builder = this.schoolRepository.createQueryBuilder(alias)

    if (filters.name) {
      const condition = nameLikeCondition(filters.name, alias)
      builder.where(condition.where, condition.parameter)
    }

    if (filters.filterStatus) {
      builder.andWhere(`${alias}.status = :status`, { status: filters.filterStatus })
    }

    if (filters.region?.length) {
      const regionList =
        typeof filters.region === 'string' ? filters.region.split(',') : filters.region
      builder.andWhere(`${alias}.region IN (:...regionList)`, {
        regionList,
      })
    }
    if (filters.buyContracts?.length) {
      const contractTypes = ['SUBSCRIPTION', 'REFERENCE']
      const contractConditions: string[] = []
      const contractParams: Record<string, any> = {}
      contractTypes.forEach((type) => {
        if (filters.buyContracts.includes(type)) {
          contractConditions.push(`${alias}.version LIKE :version_${type}`)
          contractParams[`version_${type}`] = `%${type}%`
        }
      })

      if (filters.buyContracts.includes('hasScienceRoom')) {
        contractConditions.push(`${alias}.hasScienceRoom = :hasScienceRoom`)
        contractParams['hasScienceRoom'] = SCHOOL_CONTRACTS.hasScienceRoom
      }

      if (filters.buyContracts.includes('hasAssistant')) {
        contractConditions.push(`${alias}.hasAssistant = :hasAssistant`)
        contractParams['hasAssistant'] = SCHOOL_CONTRACTS.hasAssistant
      }

      if (contractConditions.length) {
        builder.andWhere(`(${contractConditions.join(' OR ')})`, contractParams)
      }
    }

    return builder.getMany()
  }

  async createSchool(data: ModifySchoolDto) {
    const { name } = data

    const where = []
    if (name.zh_HK)
    {where.push({
      name: Raw(() => ` name->'$.zh_HK' = '${name.zh_HK.replace('\'', '\'\'')}'`),
    })}
    if (name.en_uk)
    {where.push({
      name: Raw(() => ` name->'$.en_uk' = '${name.en_uk.replace('\'', '\'\'')}'`),
    })}
    if (name.zh_cn)
    {where.push({
      name: Raw(() => ` name->'$.zh_cn' = '${name.zh_cn.replace('\'', '\'\'')}'`),
    })}
    const _school = await this.schoolRepository.findOne({ where })
    if (_school) {throw new DuplicatedSchoolName()}

    const balance = new SchoolBalance({
      totalBoughtQuota: 0,
      distributionQuota: 0,
      totalDistributionQuota: 0,
      usedQuota: 0,
    })
    const school = await this.schoolRepository.save({
      ...data,
      schoolId: generateUniqueId(),
      joinedAt: moment().unix(),
      status: ESchoolStatus.ACTIVE,
      balance,
    })

    return school
  }

  async updateSchool(id: number, data: Partial<School>) {
    const school = await this.schoolRepository.findOne({ where: { id } })
    if (!school) {throw new NotFoundException()}

    Object.assign(school, data)
    if (school.isSharingTime !== data.isSharingTime || data.region !== school.region) {
      await this.clearSchoolCache(id)
    }

    if (data.status === ESchoolStatus.INACTIVE) {
      const users = await this.schoolRepository.query(
        `select user_id as userId from users where school_id = ${id}`
      )
      const userIds = users.map((user) => user.userId)
      await this.sessionService.deleteSessions(userIds)
    }

    return this.schoolRepository.save(school)
  }

  async updateSchoolStatus(ids: number[], status: ESchoolStatus) {
    await this.schoolRepository.update({ id: In(ids) }, { status })
    if (status === ESchoolStatus.INACTIVE) {
      const users = await this.schoolRepository.query(
        `select user_id as userId from users where school_id in (${ids.join(',')})`
      )
      const userIds = users.map((user) => user.userId)
      await this.sessionService.deleteSessions(userIds)
    }
  }

  async removeSchool(ids: number[]) {
    const schools = await this.schoolRepository.find({ where: { id: In(ids) } })
    if (schools.length !== ids.length) {throw new NotFoundException(' school not found')}
    await this.schoolRepository.softDelete({ id: In(ids) })
    await Promise.all(ids.map((id) => this.clearSchoolCache(id)))
    return schools
  }

  async findSchools(ids: number[]) {
    return this.schoolRepository.find({ where: { id: In(ids) }, withDeleted: true })
  }

  async findSchoolIds(options: { version?: EBookVersion; hasScienceRoom?: boolean }) {
    const filter: any = {}
    if (!R.isNil(options.hasScienceRoom)) {
      filter.hasScienceRoom = options.hasScienceRoom
    }
    if (options.version) {
      filter.version =
        options.version === EBookVersion.SUBSCRIPTION
          ? In([ESchoolVersion.SUBSCRIPTION, ESchoolVersion.SUBSCRIPTION_REFERENCE])
          : In([ESchoolVersion.REFERENCE, ESchoolVersion.SUBSCRIPTION_REFERENCE])
    }
    const data = await this.schoolRepository.find({
      where: {
        ...filter,
      },
      select: ['id'],
    })
    return data.map((item) => item.id)
  }

  async findSchoolsWithExcludedIds(ids: number[]) {
    if (ids.length > 0)
    {return this.schoolRepository.find({
      where: { id: Not(In(ids)) },
    })}
    else {return this.schoolRepository.find()}
  }

  async listAllSchool() {
    return this.schoolRepository.find()
  }

  async count(options: { version?: EBookVersion; hasScienceRoom?: boolean } = {}) {
    const filter: any = {}
    if (options.version) {
      filter.version =
        options.version === EBookVersion.SUBSCRIPTION
          ? In([ESchoolVersion.SUBSCRIPTION, ESchoolVersion.SUBSCRIPTION_REFERENCE])
          : In([ESchoolVersion.REFERENCE, ESchoolVersion.SUBSCRIPTION_REFERENCE])
    }

    if (!R.isNil(options.hasScienceRoom)) {
      filter.hasScienceRoom = options.hasScienceRoom
    }
    return this.schoolRepository.count({
      where: {
        ...filter,
      },
    })
  }

  async exportSchool(local: ELocaleType = ELocaleType.ZH_HK, user: any) {
    const schools = await this.schoolRepository.find({
      version: Like(`%${EBookVersion.SUBSCRIPTION}%`),
    } as any)
    const data = schools.map((school) => schoolExcelData(school, local))
    await this.logService.save('下载学校总数量', user)
    return this.excelService.buildExcel({ name: `schools.${local}`, data })
  }

  async exportSchoolV2(local: ELocaleType = ELocaleType.ZH_HK, user: any) {
    // 综合所有类型学校
    const schools = await this.schoolRepository.find({})
    const data = schools.map((school) => schoolExcelDataV2(school, local))
    await this.logService.save('下载学校总数量', user)
    return this.excelService.buildExcel({ name: `schools.${local}`, data })
  }

  async clearSchoolCache(id: number) {
    await this.redisService.del(this.getSchoolKey(id))
  }

  async getScienceContracts(schoolId: number, query: PageRequest) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const [items, total] = await this.scienceContractsRepository.findAndCount({
      where: { school: { id: schoolId } },
      order: { id: 'DESC' },
      take: pageSize,
      skip: (pageIndex - 1) * pageSize,
    })
    return { items, total, pageIndex, pageSize }
  }

  async getLastScienceContract(schoolId: number) {
    return this.scienceContractsRepository.findOne({
      where: { school: { id: schoolId } },
      order: { id: 'DESC' },
    })
  }

  private getSchoolKey(id: number) {
    return `reading:school:${id}`
  }
}
