import { Injectable } from '@nestjs/common'
import moment from 'moment-timezone'
import R from 'ramda'
import { ELocaleType, EmailService, ExcelService, getPageResponse } from '@/common'
import {
  QueryReadingTimeDto,
  QueryReadingTimeOfStudentByBookDto,
  QueryStudentReadingTimeDto,
} from '@/modules/books/dto'
import { IBookRepo, IPublisherService, IReadRecordService, ISchoolDashboardService, IUserRepo } from '@/modules/shared/interfaces'
import { LogService } from '@/modules/system'
import { getDays } from '@/utils'
import { getISBN } from '@/utils/book.utitl'
import { convertSchoolTypeToName } from '../constants'
import { GradeService } from './grade.service'
import { UserClassService } from './userClass.service'

@Injectable()
export class SchoolDashboardService implements ISchoolDashboardService {
  constructor(
    private readonly userRepository: IUserRepo,
    private readonly bookRepositories: IBookRepo,
    private readonly readRecordService: IReadRecordService,
    private readonly gradeService: GradeService,
    private readonly excelService: ExcelService, 
    private readonly mailService: EmailService,
    private readonly logService: LogService,
    private readonly classService: UserClassService,
    private readonly publisherService: IPublisherService
  ) {}

  async getReadingOfStudent(query: QueryStudentReadingTimeDto, schoolId: number) {
    let userIds = []
    let bookIds = []
    if (query.keyword) {
      const accounts = await this.userRepository.getUsers(schoolId, query.keyword)
      userIds = accounts.map((item) => item.id)
      const books = await this.bookRepositories.searchBookByName(query.keyword)
      bookIds = books.map((item) => item.id)

      if (userIds.length === 0 && bookIds.length === 0) {
        return {
          items: [],
          total: 0,
          pageIndex: query.pageIndex || 1,
          pageSize: query.pageSize || 10,
        }
      }
    }

    const data = await this.readRecordService.listStudentReading(
      schoolId,
      query,
      userIds,
      bookIds
    )
    const books: any = data.items.length
      ? await this.bookRepositories.searchBooks(
        {
          ids: data.items.map((item: any) => item.bookId),
        },
        { withDeleted: true },
        {
          fields: ['id', 'name', 'isbn'],
          relations: ['publisher'],
        }
      )
      : []

    const users = data.items.length
      ? await this.userRepository.findUsersWithDelete({
        ids: data.items.map((item: any) => item.userId),
      })
      : []

    const grades = data.items.length
      ? await this.gradeService.listGradesWithDelete(
        users.map((item) => item?.userClass?.gradeId)
      )
      : []

    const schoolClasses = (await this.classService.listAllClass(schoolId)).map((item) => {
      return {
        id: item.id,
        class: item.class,
      }
    })
    const schoolGrades = (await this.gradeService.listAllGrade(schoolId)).map((item) => {
      return {
        id: item.id,
        grade: item.grade,
      }
    })

    return {
      ...data,
      items: data.items.map((item: any) => {
        const user = users.find((u) => u.id === item.userId)
        const name =
          user?.givenName || user?.familyName
            ? `${user?.givenName ? user?.givenName : ''} ${
              user?.familyName ? user?.familyName : ''
            }`
            : null
        const book = books.find((b: any) => b.id === item.bookId)
        const readRecordGradeId = item.gradeId
        const readRecordClassId = item.classId

        return {
          ...item,
          name,
          gradeId: grades?.find((item) => item.id === user?.userClass?.gradeId)?.id,
          grade: grades?.find((item) => item.id === user?.userClass?.gradeId)?.grade,
          class: user?.userClass?.class,
          classId: user?.userClass?.id,
          readRecordGrade:
            schoolGrades?.find((grade) => grade.id == readRecordGradeId)?.grade || '',
          readRecordClass:
            schoolClasses?.find((classItem) => classItem.id == readRecordClassId)
              ?.class || '',
          serialNo: user?.serialNo,
          email: user?.email,
          bookName: book?.name,
          publisher: book.publisher?.name,
          isbn: getISBN(book.isbn),
        }
      }),
    }
  }

  async exportReadingOfStudent(options: {
    local: ELocaleType
    schoolId: number
    user: any
    query: any
  }) {
    console.log('start job exportReadingTimeInSchool...')
    const { local, user, query } = options
    const data = await this.getReadingOfStudent(
      R.omit(['pageIndex', 'pageSize'], query) as any,
      options.schoolId
    )
    const gradeIds = [...new Set(data.items.map((item) => item.gradeId))]
    const classIds = [...new Set(data.items.map((item) => item.classId))]
    const userIds = [...new Set(data.items.map((item) => item.userId))]

    const gradeTimes = gradeIds.map((gradeId) => ({
      time: data.items
        .filter((item) => item.gradeId === gradeId)
        .map((item) => Number(item.totalReadingTime ?? 0))
        .reduce((pre, curr) => pre + curr, 0),
      gradeId,
    }))

    const classTimes = classIds.map((classId) => ({
      time: data.items
        .filter((item) => item.classId === classId)
        .map((item) => Number(item.totalReadingTime ?? 0))
        .reduce((pre, curr) => pre + curr, 0),
      classId: classId,
    }))

    const userTimes = userIds.map((userId) => ({
      userId,
      time: data.items
        .filter((item) => item.userId === userId)
        .map((item) => Number(item.totalReadingTime ?? 0))
        .reduce((pre, curr) => pre + curr, 0),
    }))

    const file = await this.excelService.buildExcel({
      name: `readingOfStudents.${local}`,
      data: data.items.map((item) => {
        const userTime = userTimes.find((user) => user.userId === item.userId)?.time ?? 0
        const classTime = classTimes.find((c) => c.classId === item.classId)?.time ?? 0
        const gradeTime =
          gradeTimes.find((grade) => grade.gradeId === item.gradeId)?.time ?? 0
        const currentGradeClass = `${item.grade}/${item.class}`
        const readRecordGradeClass = `${item.readRecordGrade}/${item.readRecordClass}`
        return {
          startTime: moment
            .tz(query.startTime * 1000, 'Asia/Hong_Kong')
            .format('YYYY-MM-DD'),
          endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
          name: item.name,
          serialNo: item.serialNo ?? '',
          email: item.email ?? '',
          isbn: getISBN(item.isbn) ?? '',
          publisher: item.publisher?.zh_HK,
          bookName: item.bookName.zh_HK || item.bookName.en_uk || '',
          readingTime: Number(item.totalReadingTime) / 3600 ?? 0,
          readingBooks:
            data.items.filter((user) => item.userId === user.userId)?.length ?? 0,
          totalReadingTime: Number(item.totalReadingTime || 0) / 3600,
          allBooksTotalReadingTime: Number(item.allBooksTotalReadingTime || 0) / 3600,
          readingTimeGradeRatio: `${(userTime / gradeTime) * 100}`,
          readingTimeClassRatio: `${(userTime / classTime) * 100}`,
          currentGradeClass: currentGradeClass ?? '',
          readRecordGradeClass: readRecordGradeClass ?? '',
        }
      }),
    })

    await this.mailService.sendPrepared(user.email, 'dataExport', {
      attachments: [
        {
          content: file,
          filename: `readingTimeInStudents_${moment()
            .tz('Asia/Hong_Kong')
            .format('YYYYMMDDHHmmss')}.csv`,
        },
      ],
      subject: '（閱讀時數）閱讀詳情（用戶）',
    })

    await this.logService.save('下载订阅版閱讀詳情（用戶）csv 档案', user, {
      ...query,
      schoolId: options.schoolId,
    })

    return {
      status: true,
    }
  }

  async exportReadingOfStudentAllSchool(options: {
    local: ELocaleType
    schools: any
    user: any
    query: any
  }) {
    console.log('start job exportReadingTimeInSchool...')
    const { local, user, query, schools } = options

    const allSchoolData = []

    for (const school of schools) {
      const data = await this.getReadingOfStudent(
        R.omit(['pageIndex', 'pageSize'], query) as any,
        school.id
      )

      const gradeIds = [...new Set(data.items.map((item) => item.gradeId))]
      const classIds = [...new Set(data.items.map((item) => item.classId))]
      const userIds = [...new Set(data.items.map((item) => item.userId))]

      const gradeTimes = gradeIds.map((gradeId) => ({
        time: data.items
          .filter((item) => item.gradeId === gradeId)
          .map((item) => Number(item.totalReadingTime ?? 0))
          .reduce((pre, curr) => pre + curr, 0),
        gradeId,
      }))

      const classTimes = classIds.map((classId) => ({
        time: data.items
          .filter((item) => item.classId === classId)
          .map((item) => Number(item.totalReadingTime ?? 0))
          .reduce((pre, curr) => pre + curr, 0),
        classId: classId,
      }))

      const userTimes = userIds.map((userId) => ({
        userId,
        time: data.items
          .filter((item) => item.userId === userId)
          .map((item) => Number(item.totalReadingTime ?? 0))
          .reduce((pre, curr) => pre + curr, 0),
      }))

      allSchoolData.push(
        ...data.items.map((item) => {
          const userTime =
            userTimes.find((user) => user.userId === item.userId)?.time ?? 0
          const classTime = classTimes.find((c) => c.classId === item.classId)?.time ?? 0
          const gradeTime =
            gradeTimes.find((grade) => grade.gradeId === item.gradeId)?.time ?? 0
          const currentGradeClass = `${item.grade}/${item.class}`
          const readRecordGradeClass = `${item.readRecordGrade}/${item.readRecordClass}`
          return {
            schoolType: convertSchoolTypeToName(school.type),
            schoolName: school.name.zh_HK,
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            name: item.name,
            serialNo: item.serialNo ?? '',
            email: item.email ?? '',
            isbn: getISBN(item.isbn) ?? '',
            publisher: item.publisher?.zh_HK,
            bookName: item.bookName.zh_HK || item.bookName.en_uk || '',
            readingTime: Number(item.totalReadingTime) / 3600 ?? 0,
            readingBooks:
              data.items.filter((user) => item.userId === user.userId)?.length ?? 0,
            totalReadingTime: Number(item.totalReadingTime || 0) / 3600,
            allBooksTotalReadingTime: Number(item.allBooksTotalReadingTime || 0) / 3600,
            readingTimeGradeRatio: `${(userTime / gradeTime) * 100}`,
            readingTimeClassRatio: `${(userTime / classTime) * 100}`,
            currentGradeClass: currentGradeClass ?? '',
            readRecordGradeClass: readRecordGradeClass ?? '',
          }
        })
      )
    }

    const file = await this.excelService.buildExcel({
      name: `readingOfStudentsAllSchool.${local}`,
      data: allSchoolData,
    })

    return file
  }

  async listStudentReadingByBook(
    query: QueryReadingTimeOfStudentByBookDto,
    schoolId: number
  ) {
    const data = await this.readRecordService.listStudentReadingByBook(query, schoolId)
    if (data.items?.length) {
      const books = await this.bookRepositories.searchBooks(
        { ids: data.items.map((item) => item.bookId) },
        { withDeleted: true },
        {
          fields: ['id', 'name', 'isbn', 'publishedAt'],
          relations: ['authors', 'publisher'],
        }
      )

      data.items = data.items.map((item) => {
        const book = books.find((b) => b.id === item.bookId)
        return {
          ...item,
          isbn: getISBN(book?.isbn),
          name: book?.name,
          publisherGroup: item?.publisherGroupName,
          publisher: item?.publisherName,
          authorName: book?.authors?.map((author) => author.name),
          publishedAt: book?.publishedAt,
        }
      })
    }

    return getPageResponse(data)
  }

  async getReadingUserCountByClass(user: any, query: QueryReadingTimeDto) {
    const data = await this.readRecordService.readingUserCountByClass(
      user.schoolId,
      query
    )
    const classes = await this.classService.listAllClass(user.schoolId)
    const grades = await this.gradeService.listAllGrade(user.schoolId)
    return {
      teacher: data.teacher,
      student: grades.map((item) => ({
        grade: item.grade,
        gradeSeq: item?.sequence || 0,
        classes: classes
          .filter((c) => c.gradeId === item.id)
          .map((c) => ({
            class: c.class,
            classSeq: c?.sequence || 0,
            totalUser:
              data.student.find((s) => s.grade === item.id && s.class === c.id)
                ?.totalUser ?? 0,
          })),
      })),
    }
  }

  async exportReadingUserCountByClass(
    user: any,
    query: QueryReadingTimeDto,
    locale: ELocaleType
  ) {
    const readingData = await this.getReadingUserCountByClass(user, query)

    const data = {
      ...readingData,
      student: readingData.student
        .map((x) => ({
          ...x,
          classes: x.classes.sort((a, b) => a.classSeq - b.classSeq),
        }))
        .sort((a, b) => a.gradeSeq - b.gradeSeq),
    }

    const students = data.student.filter((item) => item.classes.length != 0)

    const studentTotalReaderCount = R.flatten(
      students.map((item) => item.classes.map((c) => Number(c.totalUser ?? 0)))
    ).reduce((pre, time) => pre + time, 0)
    const totalReaderCount = studentTotalReaderCount + Number(data.teacher.totalUser ?? 0)
    const excelData: any = R.flatten(
      students.map((item) => {
        const gradeReaderCount = item.classes.reduce(
          (pre, curr) => pre + Number(curr.totalUser ?? 0),
          0
        )

        return item.classes.map((c) => {
          const classReaderCount = Number(c.totalUser ?? 0)
          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            type: locale === ELocaleType.ZH_HK ? '學生' : 'STUDENT',
            grade: item.grade ?? '',
            class: c.class ?? '',
            gradeReaderCount,
            gradeReaderCountRatio:
              totalReaderCount > 0 ? `${(gradeReaderCount / totalReaderCount) * 100}` : 0,
            classReaderCount: classReaderCount,
            classReaderCountRatio:
              gradeReaderCount > 0 ? `${(classReaderCount / gradeReaderCount) * 100}` : 0,
          }
        })
      })
    )
    const teacherReadingTime = Number(data.teacher.totalUser ?? 0)
    excelData.push({
      startTime: moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      type: locale === ELocaleType.ZH_HK ? '教職員' : 'TEACHER',
      gradeReaderCount: teacherReadingTime,
      gradeReaderCountRatio:
        totalReaderCount > 0
          ? `${(Number(teacherReadingTime) / totalReaderCount) * 100}`
          : 0,
      grade: '-',
      class: '-',
      classReaderCount: '-',
      classReaderCountRatio: '-',
    })
    let row = 2
    const merges = R.flatten(
      students.map((item) => {
        const length = item.classes.length === 0 ? 1 : item.classes.length
        const merge = [
          {
            start: { row: row, column: 4 },
            end: { row: row + length - 1, column: 4 },
          },
          {
            start: { row: row, column: 5 },
            end: { row: row + length - 1, column: 5 },
          },
          {
            start: { row: row, column: 6 },
            end: { row: row + length - 1, column: 6 },
          },
        ]
        row += length
        return merge
      })
    )
    merges.push({
      start: { row: 2, column: 1 },
      end: { row: excelData.length + 1, column: 1 },
    })
    merges.push({
      start: { row: 2, column: 2 },
      end: { row: excelData.length + 1, column: 2 },
    })
    merges.push({
      start: { row: 2, column: 3 },
      end: { row: excelData.length, column: 3 },
    })

    await this.logService.save('下载订阅版閱讀人數分佈', user, query)

    return await this.excelService.buildExcel({
      name: `readingUserCountInClass.${locale}`,
      data: excelData,
      merges,
    })
  }

  async getReadingTimeByClass(user: any, query: QueryReadingTimeDto) {
    const data = await this.readRecordService.readingTimeByClass(user.schoolId, query)
    const classes = await this.classService.listAllClass(user.schoolId)
    const grades = await this.gradeService.listAllGrade(user.schoolId)

    return {
      teacher: data.teacher,
      student: grades.map((item) => ({
        grade: item.grade,
        classes: classes
          .filter((c) => c.gradeId === item.id)
          .map((c) => ({
            class: c.class,
            totalReadingTime:
              data.student.find((s) => s.grade === item.id && s.class === c.id)
                ?.totalReadingTime ?? 0,
          })),
      })),
    }
  }

  async exportReadingTimeByClass(
    user: any,
    query: QueryReadingTimeDto,
    locale: ELocaleType
  ) {
    const data = await this.getReadingTimeByClass(user, query)

    const students = data.student.filter((item) => item.classes.length != 0)

    const studentTotalReadingTime = R.flatten(
      students.map((item) => item.classes.map((c) => Number(c.totalReadingTime ?? 0)))
    ).reduce((pre, time) => pre + time, 0)
    const totalReadingTime = Number(
      (studentTotalReadingTime + Number(data.teacher.totalReadingTime ?? 0)) / 3600
    )
    const excelData: any = R.flatten(
      students.map((item) => {
        const gradeReadingTimes = item.classes.reduce(
          (pre, curr) => pre + Number(curr.totalReadingTime ?? 0),
          0
        )
        const gradeReadingTime = Number(gradeReadingTimes) / 3600

        return item.classes.map((c) => {
          const classReadingTime = Number(Number(c.totalReadingTime ?? 0) / 3600 ?? 0)
          return {
            startTime: moment
              .tz(query.startTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            endTime: moment
              .tz(query.endTime * 1000, 'Asia/Hong_Kong')
              .format('YYYY-MM-DD'),
            type: locale === ELocaleType.ZH_HK ? '學生' : 'STUDENT',
            grade: item.grade ?? '',
            class: c.class ?? '',
            gradeReadingTimes,
            gradeReadingTime,
            gradeReadingTimeRatio:
              totalReadingTime > 0 ? `${(gradeReadingTime / totalReadingTime) * 100}` : 0,
            classReadingTime: classReadingTime,
            classReadingTimes: c.totalReadingTime ?? 0,
            classReadingTimeRatio:
              gradeReadingTimes > 0
                ? `${(classReadingTime / gradeReadingTime) * 100}`
                : 0,
          }
        })
      })
    )
    const teacherReadingTime = Number(data.teacher.totalReadingTime ?? 0) / 3600
    excelData.push({
      startTime: moment.tz(query.startTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      endTime: moment.tz(query.endTime * 1000, 'Asia/Hong_Kong').format('YYYY-MM-DD'),
      type: locale === ELocaleType.ZH_HK ? '教職員' : 'TEACHER',
      gradeReadingTime: teacherReadingTime,
      gradeReadingTimeRatio:
        totalReadingTime > 0
          ? `${(Number(teacherReadingTime) / totalReadingTime) * 100}`
          : 0,
      grade: '-',
      class: '-',
      classReadingTime: '-',
      classReadingTimeRatio: '-',
    })
    let row = 2
    const merges = R.flatten(
      students.map((item) => {
        const length = item.classes.length === 0 ? 1 : item.classes.length
        const merge = [
          {
            start: { row: row, column: 4 },
            end: { row: row + length - 1, column: 4 },
          },
          {
            start: { row: row, column: 5 },
            end: { row: row + length - 1, column: 5 },
          },
          {
            start: { row: row, column: 6 },
            end: { row: row + length - 1, column: 6 },
          },
          {
            start: { row: row, column: 7 },
            end: { row: row + length - 1, column: 7 },
          },
        ]
        row += length
        return merge
      })
    )
    merges.push({
      start: { row: 2, column: 1 },
      end: { row: excelData.length + 1, column: 1 },
    })
    merges.push({
      start: { row: 2, column: 2 },
      end: { row: excelData.length + 1, column: 2 },
    })
    merges.push({
      start: { row: 2, column: 3 },
      end: { row: excelData.length, column: 3 },
    })

    await this.logService.save('下载订阅版閱讀時數分佈', user, query)

    return this.excelService.buildExcel({
      name: `readingTimeInClass.${locale}`,
      data: excelData,
      merges,
    })
  }

  async readingUserCountAndTimeByDay(user: any, query: QueryReadingTimeDto) {
    const data = await this.readRecordService.readingUserCountByHour(user.schoolId, query)

    return getDays(query.startTime, query.endTime).map((date) => ({
      date,
      count:
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.count ?? 0,
      totalReadingTime:
        data.find(
          (item) => moment.tz(item.date, 'Asia/Hong_Kong').format('YYYY-MM-DD') == date
        )?.totalReadingTime ?? 0,
    }))
  }
}
