import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { DataSource, In, Repository } from 'typeorm'
import { ELocaleType } from '@/common' 
import { School, SchoolHomepage } from '@/entities'
import { EBookVersion, EListStatus, EUserType } from '@/enums'
import { StudentHomepageDuplicatedException } from '@/modules/exception'
import { IBookRepo } from '@/modules/shared/interfaces'
import { CreateSchoolHomepageDto, modifySchoolHomepageField } from '../../books/dto'
import { filterHomageBooks } from '../../books/utils/hompage.util'
import { schoolHomepageValidator } from '../../books/validators'
import { GradeService } from '../services'
import { UserClassService } from './index1'

@Injectable()
export class SchoolHomepageService {
  constructor(
    @InjectRepository(SchoolHomepage)
    private readonly homepageRepository: Repository<SchoolHomepage>,
    private readonly bookRepositories: IBookRepo,
    private readonly userClassService: UserClassService,
    private readonly gradeService: GradeService,
    private readonly dataSource: DataSource
  ) {}

  async sortSchoolHomepageBooks(schoolHomepage: SchoolHomepage) {
    const map = await this.getSchoolHomepageBookSorts(schoolHomepage.id)
    return {
      ...schoolHomepage,
      books: schoolHomepage.books.sort((a, b) => map[a.id] - map[b.id] || 0),
    }
  }

  async getSchoolHomepageBookSorts(id: number): Promise<{ [x: number]: number }> {
    const sorts = await this.homepageRepository
      .query(`select t1.books_id as book_id, @rownum:=@rownum+1 as sort 
      from books_school_homepages_school_homepage t1, (SELECT (@rowNum :=0) ) t2 
      where t1.school_homepage_id = ${id} order by t1.created_at desc;
    `)

    return (sorts || []).reduce(
      (response: { [x: number]: number }, item: { book_id: string; sort: string }) => {
        response[Number(item.book_id)] = Number(item.sort)
        return response
      },
      {}
    )
  }

  async createHomepage(
    data: CreateSchoolHomepageDto,
    school: Partial<School>,
    local: ELocaleType = ELocaleType.ZH_HK
  ): Promise<SchoolHomepage> {
    const { version = EBookVersion.SUBSCRIPTION } = data

    if (data.type === EUserType.TEACHER) {
      const duplicated = await this.homepageRepository
        .createQueryBuilder('homepage')
        .innerJoin('homepage.school', 'school', 'school.id = :schoolId', {
          schoolId: school.id,
        })
        .where(`homepage.type ='${EUserType.TEACHER}' and homepage.version ='${version}'`)
        .getOne()
      schoolHomepageValidator(duplicated).duplicated()
    } else {
      const homepages = await this.homepageRepository
        .createQueryBuilder('homepage')
        .innerJoin('homepage.school', 'school', 'school.id = :schoolId', {
          schoolId: school.id,
        })
        .where(`homepage.type ='${EUserType.STUDENT}' and homepage.version ='${version}'`)
        .getMany()

      if (data.classIds?.length) {
        const classIds = R.intersection(
          R.flatten(
            homepages
              .filter((item) => item.classIds?.length)
              .map((item) => item.classIds)
          ),
          data.classIds
        )

        if (classIds.length) {
          const userClass = await this.userClassService.getClassByIds(classIds)

          throw new StudentHomepageDuplicatedException(
            local === ELocaleType.ZH_HK
              ? `班級${userClass
                .map((item) => item.class)
                .join(',')}推薦書籍已存在，請勿重複推薦`
              : `The recommended books for Class ${userClass
                .map((item) => item.class)
                .join(',')} already exist, please do not repeat the recommendation`
          )
        }
      } else if (data.gradeIds?.length) {
        const gradeIds = R.intersection(
          R.flatten(
            homepages
              .filter(
                (item) =>
                  (!item.classIds || item.classIds?.length === 0) &&
                  item.gradeIds?.length
              )
              .map((item) => item.gradeIds)
          ),
          data.gradeIds
        )

        if (gradeIds.length) {
          const grades = await this.gradeService.listGrades(gradeIds)

          throw new StudentHomepageDuplicatedException(
            local === ELocaleType.ZH_HK
              ? `年級${grades
                .map((item) => item.grade)
                .join(',')}推薦書籍已存在，請勿重複推薦`
              : `The recommended books for Grade ${grades
                .map((item) => item.grade)
                .join(',')} already exist, please do not repeat the recommendation`
          )
        }
      }
    }

    const homepage = R.pick(modifySchoolHomepageField, data) as SchoolHomepage
    if (data.bookIds?.length) {
      const books = await this.bookRepositories.searchBooks(
        {
          ids: [...new Set(data.bookIds)],
        },
        { withDeleted: version === EBookVersion.REFERENCE }
      )
      homepage.books = books
    }

    if (school) {
      homepage.school = school as any
    }

    return this.homepageRepository.save(homepage)
  }

  async getDefaultHomepage(schoolId: number) {
    return this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .where(
        `homepage.type is null and homepage.version = '${EBookVersion.SUBSCRIPTION}'`
      )
      .getOne()
  }

  async deleteHomepageForLevel(schoolId: number, type: EUserType) {
    const homepages = await this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .where(`homepage.type = '${type}'`)
      .andWhere(`homepage.version = '${EBookVersion.SUBSCRIPTION}'`)
      .getMany()

    if (homepages.length) {
      await this.dataSource.transaction(async (manager) => {
        await manager.query(
          `delete from books_school_homepages_school_homepage where school_homepage_id in (${homepages
            .map((item) => item.id)
            .join(',')})`
        )
        await manager.delete(SchoolHomepage, { id: In(homepages.map((item) => item.id)) })
      })
    }
  }

  async getHomepageWithRelationById(id: number, schoolId: number) {
    const homepage = await this.homepageRepository.findOne({ where: { id  } })
    if (homepage) {
      const homepageWithBooks = await this.homepageRepository.findOne({
        where: { id },
        relations: ['books'],
        withDeleted: homepage.version === EBookVersion.REFERENCE,
      })
      if (homepageWithBooks?.books.length) {
        const relations =
          homepage.version === EBookVersion.REFERENCE
            ? ['authors', 'referenceBooks', 'categories', 'labels']
            : ['authors', 'categories', 'labels', 'bookLevels']
        const ids = homepageWithBooks?.books?.map((item) => item.id)
        const books = await this.bookRepositories.listBooks(
          { ids },
          {
            withDeleted: homepage.version === EBookVersion.REFERENCE,
            relations,
            referenceSchoolId: schoolId,
            fields: ['id', 'name', 'coverUrl', 'isbn', 'description', 'status', 'level'],
          }
        )

        homepage.books = books
      }
    }
    return homepage
  }

  async listHomepageWithRelation(schoolId: number, version: EBookVersion) {
    const homepages = await this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .leftJoinAndSelect('homepage.books', 'books')
      .where(`homepage.version = :version`, { version })
      .getMany()

    return Promise.all(
      homepages.map(async (homepage) => {
        if (homepage?.books.length) {
          const ids = homepage?.books?.map((item) => item.id)
          const books = await this.bookRepositories.searchBooks({ ids })
          homepage.books = books
        }
        return homepage
      })
    )
  }

  async getHomepage(id: number) {
    const homepage = await this.homepageRepository.findOne({ where: { id  } })
    schoolHomepageValidator(homepage).exist()
    return homepage
  }

  async getReferenceHomepage(schoolId: number) {
    const homepage = await this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .where('homepage.version = :version', { version: EBookVersion.REFERENCE })
      .getOne()

    if (homepage) {
      return this.homepageRepository
        .createQueryBuilder('homepage')
        .withDeleted()
        .leftJoinAndSelect('homepage.books', 'books')
        .where('homepage.id = :id', { id: homepage.id })
        .getOne()
    }
    return homepage
  }

  async updateHomepage(id: number, data: CreateSchoolHomepageDto) {
    const homepage = await this.getHomepage(id)
    const hasStatus = data.status !== homepage.status

    Object.assign(homepage, R.pick(modifySchoolHomepageField, data))

    await this.homepageRepository.query(
      `delete from books_school_homepages_school_homepage where school_homepage_id = ${id}`
    )
    if (data.bookIds?.length) {
      const books = await this.bookRepositories.searchBooks(
        {
          ids: [...new Set(data.bookIds)],
        },
        { withDeleted: homepage.version === EBookVersion.REFERENCE }
      )
      homepage.books = books
    } else {homepage.books = null}

    if (data.status && hasStatus) {
      if (data.status === EListStatus.ONLINE) {
        homepage.offlineAt = null
        homepage.onlineAt = new Date()
      } else if (data.status === EListStatus.OFFLINE) {
        homepage.offlineAt = new Date()
      }
    }

    return this.homepageRepository.save(homepage)
  }

  async deleteHomepage(id: number) {
    const homepage = await this.homepageRepository.findOne({ where: { id } })
    if (homepage) {
      if (!R.isNil(homepage.type))
      {await this.dataSource.transaction(async (manager) => {
        await manager.query(
          `delete from books_school_homepages_school_homepage where school_homepage_id = ${id}`
        )
        await manager.delete(SchoolHomepage, { id })
      })}
    }
    return homepage
  }

  async deleteDefaultHomepage(ids: number[]) {
    await this.dataSource.manager.transaction(async (manager) => {
      await manager.query(
        `delete from books_school_homepages_school_homepage where school_homepage_id in (${ids.join(
          ','
        )})`
      )
      await manager.delete(SchoolHomepage, { id: In(ids) })
    })
  }

  async getReferenceRecommend(schoolId: number, user: any) {
    const homepages = await this.homepageRepository.query(`
      select
        id,
        name,
        type,
        class_ids as classIds,
        grade_ids as gradeIds,
        option_name as optionName
      from
        school_homepage
        inner join (
          select
            school_homepage_id,
            ifnull(count(books_id), 0) as total
          from
            books_school_homepages_school_homepage
          group by
            school_homepage_id
        ) as books on books.school_homepage_id = school_homepage.id
      where
        school_id = ${schoolId}
        and version = '${EBookVersion.REFERENCE}'
        and (
          type is null
          or type = ${
  user.isTeacher ? `'${EUserType.TEACHER}'` : `'${EUserType.STUDENT}'`
}
        )
        and books.total > 0
    `)

    let homepage
    if (user.isTeacher) {
      homepage = homepages.find((item) => item.type === EUserType.TEACHER)
    } else {
      homepage = homepages.find((item) => item.classIds?.includes(user.classId))
      if (!homepage) {
        homepage = homepages.find((item) => item.gradeIds?.includes(user.gradeId))
      }
    }

    if (!homepage) {
      homepage = homepages.find((item) => R.isNil(item.type))
    }

    if (!homepage) {
      const data = await this.bookRepositories.listReferenceBooks(
        schoolId,
        {
          pageIndex: 1,
          pageSize: 7,
        },
        {
          fields: ['id', 'name', 'coverUrl', 'isbn', 'description', 'url'],
          relations: ['authors', 'categories', 'labels'],
        }
      )
      return {
        name: {
          [ELocaleType.ZH_HK]: '今日推薦',
          [ELocaleType.ZH_CN]: '今日推荐',
          [ELocaleType.EN_UK]: `Today's Pick`,
        },
        books: data.items,
      } as any as SchoolHomepage
    }

    const data = await this.homepageRepository
      .createQueryBuilder('homepage')
      .withDeleted()
      .leftJoinAndSelect('homepage.books', 'books')
      .leftJoinAndSelect('books.authors', 'authors')
      .leftJoinAndSelect('books.categories', 'categories')
      .leftJoinAndSelect('books.labels', 'labels')
      .andWhere('homepage.id = :id', { id: homepage.id })
      .getOne()

    return data ? this.sortSchoolHomepageBooks(data) : null
  }

  async getTeacherRecommend(schoolId: number, levelIds: number[]) {
    const schoolHomepages = await this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .leftJoinAndSelect('homepage.books', 'books')
      .where('homepage.version = :version', { version: EBookVersion.SUBSCRIPTION })
      .getMany()

    const [teacherHomepage] = schoolHomepages.filter(
      (item) => item.type === EUserType.TEACHER
    )

    const defaultHomepage = schoolHomepages.find((item) => R.isNil(item.type))

    if (teacherHomepage && teacherHomepage.books?.length) {
      const homepage = await this.getHomepageWithRelationById(
        teacherHomepage.id,
        schoolId
      )
      const h = filterHomageBooks(homepage, levelIds)
      if (h?.books?.length) {return h}
    }

    if (R.isNil(defaultHomepage)) {return undefined}

    const homepage = await this.getHomepageWithRelationById(defaultHomepage.id, schoolId)
    return filterHomageBooks(homepage, levelIds)
  }

  async getStudentRecommend(
    schoolId: number,
    classId: number,
    gradeId: number,
    levels: number[]
  ) {
    const schoolHomepages = await this.homepageRepository
      .createQueryBuilder('homepage')
      .innerJoin('homepage.school', 'school', 'school.id = :schoolId', { schoolId })
      .leftJoinAndSelect('homepage.books', 'books')
      .where('homepage.version = :version', { version: EBookVersion.SUBSCRIPTION })
      .getMany()

    const [classRecommend] = schoolHomepages.filter((item) =>
      item.classIds?.includes(classId)
    )

    if (classRecommend) {
      const h = filterHomageBooks(classRecommend, levels, schoolId)
      if (h?.books?.length) {return h}
    }

    const [gradeRecommend] = schoolHomepages.filter(
      (item) =>
        item.gradeIds?.includes(gradeId) &&
        (R.isNil(item.classIds) || item.classIds?.length === 0)
    )

    if (gradeRecommend) {
      const h = filterHomageBooks(gradeRecommend, levels, schoolId)
      if (h?.books?.length) {return h}
    }

    const [defaultRecommend] = schoolHomepages.filter((item) => R.isNil(item.type))

    if (defaultRecommend?.books?.length)
    {return filterHomageBooks(defaultRecommend, levels, schoolId)}
    return undefined
  }

  async createReferenceHomepage() {
    const schools = await this.homepageRepository.query(`
      select
        schools.id,
        t.id as homepage
      from
        schools
        left join(
          select
            school_homepage.school_id,
            school_homepage.id
          from
            school_homepage
          where
            (
              school_homepage.type is null
              and school_homepage.version = '${EBookVersion.SUBSCRIPTION}'
            )
        ) as t on schools.id = t.school_id
    `)

    const homepages = schools.filter((item) => !item.homepage)

    for (const school of homepages) {
      await this.homepageRepository.query(`
        insert into
          school_homepage (
            school_id,
            status,
            name,
            version
          )
        values
          (
            ${school.id},
            'online',
            '{\"en_uk\": \"Today\\'s Pick\", \"zh_HK\": \"今日推薦\", \"zh_cn\": \"今日推荐\"}',
            '${EBookVersion.SUBSCRIPTION}'
          );
      `)
    }
  }
}
