import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import moment from 'moment-timezone'
import R from 'ramda'
import { In, MoreThan, Repository } from 'typeorm'
import { SchoolSubject } from '@/entities/schoolSubject.entity'
import { EOnlineType, ESchoolSubjectStatus, ESubjectStatus } from '@/enums'
import { FilterSubjectDto, PatchSchoolSubjectDto } from '../dto'
import { SubjectService } from './subject.service'

@Injectable()
export class SchoolSubjectService {
  constructor(
    @InjectRepository(SchoolSubject)
    private readonly schoolSubjectRepository: Repository<SchoolSubject>,
    private readonly subjectService: SubjectService
  ) {}

  async schoolUpdateStatus(body: PatchSchoolSubjectDto, schoolId: number) {
    const subjects = await this.subjectService.findSubjects(
      {
        ...R.omit(['status'], body),
        schoolSubjectStatus:
          body.onlineAt === 0 ? ESchoolSubjectStatus.PRE_ONLINE : body.status,
        schoolId,
        status: ESubjectStatus.ONLINE,
      },
      ['subject.id', 'schoolSubjects.id'],
      ['schoolSubjects']
    )

    if (!subjects.length) {return}
    // 取消上线时间
    if (body.onlineAt === 0) {
      await this.schoolSubjectRepository.save(
        subjects.map((item) => ({
          id: item.schoolSubjects[0]?.id,
          status: ESchoolSubjectStatus.PRE_ONLINE,
          onlineAt: null,
          onlineType: null,
          school: { id: schoolId },
          subject: { id: item.id },
        }))
      )
    } else if (body.toStatus === ESchoolSubjectStatus.ONLINE) {
      await this.schoolSubjectRepository.save(
        subjects.map((item) => ({
          ...R.pick(['id'], { id: item.schoolSubjects[0]?.id }),
          onlineAt:
            body.onlineType === EOnlineType.IMMEDIATELY ? moment().unix() : body.onlineAt,
          status:
            body.onlineType === EOnlineType.IMMEDIATELY
              ? ESchoolSubjectStatus.ONLINE
              : ESchoolSubjectStatus.PRE_ONLINE,
          onlineType: body.onlineType,
          school: { id: schoolId },
          subject: { id: item.id },
        }))
      )
    } else if (body.toStatus === ESchoolSubjectStatus.HIDDEN) {
      const schoolSubjects = subjects.filter((item) => item.schoolSubjects[0]?.id)

      if (schoolSubjects?.length) {
        await this.schoolSubjectRepository.save(
          schoolSubjects.map((item) => ({
            id: item.schoolSubjects[0]?.id,
            status: ESchoolSubjectStatus.HIDDEN,
            onlineAt: null,
            onlineType: null,
            school: { id: schoolId },
            subject: { id: item.id },
          }))
        )
      }
    }
  }
}
