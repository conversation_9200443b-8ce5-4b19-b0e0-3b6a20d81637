import { Process, Processor } from '@nestjs/bull'
import { Job } from 'bull'
import { ETaskType, TASK_QUEUE_NAME, TaskService } from '@/common/components/task'
import { EUserType } from '@/enums'
import { IBookLevelService, IBookRepo, ILeaderBoardService } from '@/modules/shared/interfaces'
import { SchoolService } from '../services'

@Processor(TASK_QUEUE_NAME)
export class UpdateSchoolLevelTask {
  constructor(
    private readonly bookRepositories: IBookRepo,
    private readonly bookLevelService: IBookLevelService,
    private readonly taskService: TaskService,
    private readonly schoolService: SchoolService,
    private readonly leadBoardService: ILeaderBoardService
  ) {}

  @Process(ETaskType.UPDATE_SCHOOL_LEVEL)
  async handleBook(job: Job<any>) {
    if (process.env.APP_ENV === 'local') {return}
    console.log({ parseBookTask: job?.id ?? 0 })
    await this.taskService.runTask(job?.data?.taskId, async () => {
      const school = await this.schoolService.findOne({ where: { id: job.data.schoolId } })
      const { isAllLevelForStaff, isAllLevelForStudent, staffLevelIds, studentLevelIds } =
        school

      if (!isAllLevelForStaff && staffLevelIds.length) {
        const removeLevels = await this.bookLevelService.findLevels(staffLevelIds)
        const books = await this.bookRepositories.searchBooks(
          {
            level: removeLevels.map((item) => item.id),
          },
          undefined,
          { fields: ['id'] }
        )

        await this.bookRepositories.removeBookRelationsForSchool(
          job.data.schoolId,
          books.map((item) => item.id),
          EUserType.TEACHER
        )

        await this.leadBoardService.removeBooksForReadingRanking(
          books.map((item) => item.id),
          [job.data.schoolId],
          EUserType.TEACHER
        )
      }

      if (!isAllLevelForStudent && studentLevelIds.length) {
        const removeLevels = await this.bookLevelService.findLevels(studentLevelIds)
        const books = await this.bookRepositories.searchBooks(
          {
            level: removeLevels.map((item) => item.id),
          },
          undefined,
          { fields: ['id'] }
        )

        await this.bookRepositories.removeBookRelationsForSchool(
          job.data.schoolId,
          books.map((item) => item.id),
          EUserType.STUDENT
        )

        await this.leadBoardService.removeBooksForReadingRanking(
          books.map((item) => item.id),
          [job.data.schoolId],
          EUserType.STUDENT
        )
      }
    })
  }
}
