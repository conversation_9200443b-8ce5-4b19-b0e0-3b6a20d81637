import { SchoolBalance } from '@/entities'
import { SchoolBalanceNotExistException } from '../exception'

export class SchoolBalanceValidator {
  constructor(private readonly model: SchoolBalance) {}

  exist(): SchoolBalanceValidator {
    if (!this.model) {
      throw new SchoolBalanceNotExistException()
    }
    return this
  }
}

export const schoolBalanceValidator = (model: SchoolBalance) =>
  new SchoolBalanceValidator(model)
