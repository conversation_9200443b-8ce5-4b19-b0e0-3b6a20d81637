import { SchoolBalance, UserBalance } from '@/entities'
import { NoReadingTimeLeftException } from '../exception'

export class BalanceValidator {
  hasReadingTime(balance?: UserBalance): BalanceValidator {
    if (balance.totalQuota <= balance.usedQuota) {
      throw new NoReadingTimeLeftException()
    }
    return this
  }

  hasSharingTime(balance: SchoolBalance): BalanceValidator {
    if (balance.totalBoughtQuota <= balance.usedQuota) {
      throw new NoReadingTimeLeftException()
    }
    return this
  }
}

export const balanceValidator = () => new BalanceValidator()
