import {FindManyOptions} from 'typeorm'
import {SchoolAdministrator} from '@/entities'
import {ListSchoolAdminDto} from '@/modules/account'


interface ISchoolAdmin {
  findOne(options: any): Promise<any>
  find(options?: any): Promise<any[]>

  listSchoolAdmins(schoolId?: number, options?: ListSchoolAdminDto)

}

export abstract class ISchoolAdminRepo implements ISchoolAdmin {
  abstract findOne(options: any): Promise<any>
  abstract find(options?: any): Promise<any[]>
  abstract listSchoolAdmins(schoolId?: number, options?: ListSchoolAdminDto)

  abstract count(options?: FindManyOptions<SchoolAdministrator>)

  abstract countByDay(query: { startTime: number; endTime: number })
}