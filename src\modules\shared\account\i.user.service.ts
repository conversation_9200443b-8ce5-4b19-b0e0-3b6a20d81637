import { School } from '@/entities'
import { CreateUserDto } from '@/modules/account'

interface IUser {
  createUser(school: School, data: CreateUserDto, admin: any): Promise<any>
  updateUser(id: number, data: any): Promise<any>
  exportStudents(options: any, user: any): Promise<any>
  exportAccounts(options: any, user: any): Promise<any>
}

export abstract class IUserService implements IUser {
  abstract createUser(school: School, data: CreateUserDto, admin: any): Promise<any>
  abstract updateUser(id: number, data: any): Promise<any>
  abstract exportStudents(options: any, user: any): Promise<any>
  abstract exportAccounts(options: any, user: any): Promise<any>
}