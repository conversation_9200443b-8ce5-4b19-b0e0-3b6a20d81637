import {PageRequest} from '@/common'
import {School} from '@/entities'
import { UpdateBookDto } from '@/modules/books/dto'

interface IBook {

}

export abstract class IBookService implements IBook {
  abstract updateBook(id: number, data: UpdateBookDto, admin: any): Promise<any>
  abstract searchBookIds(query: any, filter: any): Promise<any>
  abstract getLevels(levels: number[], user: any)

  abstract getNewestBooks(schoolId: number, data: PageRequest, levels?: number[])

  abstract getBookIdsForTeacher(school: Partial<School>)

  abstract getBookIdsForStudent(school: Partial<School>)
}