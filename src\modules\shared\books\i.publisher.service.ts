interface IPublisher {
  searchPublisher(keyword: string): Promise<any>
  getPublisher(options: { publisherId?: string; id?: number }): Promise<any>
  createPublisher(data: any): Promise<any>
  updatePublisher(id: number, data: any): Promise<any>
}

export abstract class IPublisherService implements IPublisher {
  abstract searchPublisher(keyword: string): Promise<any>
  abstract getPublisher(options: { publisherId?: string; id?: number }): Promise<any>
  abstract createPublisher(data: any): Promise<any>
  abstract updatePublisher(id: number, data: any): Promise<any>


  abstract findPublishers(ids: number[], relations?: string[])
}