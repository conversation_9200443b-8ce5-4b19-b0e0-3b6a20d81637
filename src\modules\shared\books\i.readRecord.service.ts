import {GetGlobalSummaryRequest, QuerySchoolPublisherReadingDto} from '@/modules/books/dto'
import {QueryReadingTime} from '@/modules/books/interfaces'

interface  IReadRecord {

}

export abstract class IReadRecordService implements IReadRecord {
  abstract patchUsersReadRecord(schoolId: number, items: any): Promise<any>
  abstract exportPublisherReadingTimeCSV(query: any): Promise<any>
  abstract timeDistribution(schoolId: number, query: any): Promise<any>
  abstract countBookReadingUsers(schoolId: number, ids: number[]): Promise<any>
  abstract getReadingUserIdsBySchool(schoolId: number, query: any): Promise<any>
  abstract listReadBooks(userId: number, schoolId: number, query: any): Promise<any>
  abstract listStudentReading(schoolId: number, query: any, userIds?: number[], bookIds?: number[]): Promise<any>
  abstract readingUserCountByClass(schoolId: number, query: any): Promise<any>
  abstract readingUserCountByHour(schoolId: number, query: any): Promise<any>
  abstract listStudentReadingByBook(query: any, schoolId: number): Promise<any>
  abstract readingTimeByClass(schoolId: number, query: any): Promise<any>

  abstract schoolPublisherReading(query: QuerySchoolPublisherReadingDto)

  abstract readingTimeAndUser(query)

  abstract readingCount(query: QueryReadingTime)

  abstract getGlobalSummary(query: GetGlobalSummaryRequest)

  abstract getSchoolUsedTime(schoolId: number)

  abstract getUserReadingBookCount(userId: number): Promise<number>
}