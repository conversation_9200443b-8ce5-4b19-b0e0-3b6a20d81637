import {EBookVersion} from '@/enums'

interface ISchool {

}
export abstract class ISchoolService implements ISchool {
  abstract listSchools(query: any): Promise<any>
  abstract createSchool(data: any): Promise<any>
  abstract updateSchool(id: number, data: any): Promise<any>
  abstract findOne(options: any): Promise<any>
  abstract listAllSchool(): Promise<any>

  abstract findSchoolIds(filter: any): Promise<any>
  abstract findSchoolIds(options: { version?: EBookVersion; hasScienceRoom?: boolean })

  abstract count(filter: any): Promise<number>

  abstract findSchools(ids: number[])
}