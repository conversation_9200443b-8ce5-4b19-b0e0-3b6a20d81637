interface IUserAnswer {
  latestAnswer(subjectId: number, userId: number): Promise<any>
  answerQuestion(
    data: any,
    userId: number,
    schoolId: number,
    classId: number,
    gradeId: number
  ): Promise<any>
  // 可根据需要继续添加其他方法声明
}

export abstract class IUserAnswerService implements IUserAnswer {
  abstract latestAnswer(subjectId: number, userId: number): Promise<any>
  abstract answerQuestion(
    data: any,
    userId: number,
    schoolId: number,
    classId: number,
    gradeId: number
  ): Promise<any>

  abstract answerCount(
    data: any,
    userId: number,
    schoolId: number,
    classId: number,
    gradeId: number,
    isTeacher: boolean
  ): Promise<any>

}