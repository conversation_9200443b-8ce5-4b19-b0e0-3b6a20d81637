import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger'
import { AdminAuth, ApiBaseResult, ApiPageResult, CurrentAdmin } from '@/common'
import {
  CreateAppVersionRequest,
  ListAppVersionsRequest,
  PatchAppVersionRequest,
} from '../dto'
import { AppVersion } from '../entities'
import { AppVersionService } from '../services/appVersion.service'

@ApiTags('System')
@ApiExtraModels(AppVersion)
@Controller('/v1/admin/system/app-versions')
export class AppVersionAdminContriller {
  constructor(private readonly appVersionService: AppVersionService) {}

  @AdminAuth()
  @ApiBaseResult(AppVersion, 201, 'Create app version')
  @ApiOperation({ summary: 'Create app version' })
  @Post()
  async createAppVersion(
    @Body() body: CreateAppVersionRequest,
    @CurrentAdmin() user: any,
  ) {
    return this.appVersionService.createAppVersion(body, { operator: user })
  }

  @AdminAuth()
  @ApiBaseResult(AppVersion, 200, 'Patch app version')
  @ApiOperation({ summary: 'Patch app version' })
  @Patch('/:id')
  async patchAppVersion(
    @Param('id') id: string,
    @Body() body: PatchAppVersionRequest,
    @CurrentAdmin() user: any,
  ) {
    return this.appVersionService.patchAppVersion(Number(id), body, { operator: user })
  }

  @AdminAuth()
  @ApiBaseResult(AppVersion, 200, 'Delete app version')
  @ApiOperation({ summary: 'Delete app version' })
  @Delete('/:id')
  async deleteAppVersion(@Param('id') id: string) {
    return this.appVersionService.deleteAppVersion(Number(id))
  }

  @AdminAuth()
  @ApiPageResult(AppVersion, 200, 'List app versions')
  @ApiOperation({ summary: 'List app versions' })
  @Get()
  async listAppVersions(@Query() query: ListAppVersionsRequest) {
    return this.appVersionService.listAppVersions(query)
  }

  @AdminAuth()
  @ApiBaseResult(AppVersion, 200, 'Get app versions')
  @ApiOperation({ summary: 'Get app versions' })
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
  })
  @Get('/:id')
  async getAppVersion(@Param('id') id: string) {
    return this.appVersionService.getAppVersion({ id: Number(id) })
  }
}
