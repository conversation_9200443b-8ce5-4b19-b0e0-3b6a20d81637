import { <PERSON>, Get, Param } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger'
import {
  ApiBaseResult,
  CurrentLocale,
  CurrentPlatform,
  ELocaleType,
  PublicAuth,
} from '@/common'
import { GetAppVersionResponse } from '../dto'
import { EAppPlatform } from '../enums'
import { AppVersionService } from '../services/appVersion.service'

@ApiTags('System')
@ApiExtraModels(GetAppVersionResponse)
@Controller('/v1/public/system/app-versions')
export class AppVersionPublicContriller {
  constructor(private readonly appVersionService: AppVersionService) {}

  @PublicAuth()
  @ApiBaseResult(GetAppVersionResponse, 200, 'Get upgrade app version')
  @ApiOperation({ summary: 'Get upgrade app version' })
  @Get('/:version')
  @ApiParam({
    name: 'version',
    required: true,
    type: String,
  })
  async getAppVersionsV2(
    @CurrentPlatform() platform: EAppPlatform,
    @Param('version') version: string,
    @CurrentLocale() locale: ELocaleType,
  ) {
    return this.appVersionService.getLatestAppVersion(platform, version, locale)
  }

  @Get('/android/apk')
  async getApkLink() {
    try {
      const android = await this.appVersionService.getAndroidLastVersion()
      return `https://s3.ap-southeast-1.amazonaws.com/images.sjrc.club/public/apk/SJRC-v${android.version}.apk`
    } catch (error) {
      return ''
    }
  }
}
