import { Body, Controller, Post } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiBaseResult, BooleanResponse, ClientAuth, CurrentUser } from '@/common'
import { CreateContactUsRequest } from '../dto'
import { ContactUs } from '../entities'
import { ContactUsService } from '../services/contact.service'

@ApiTags('System')
@ApiExtraModels(ContactUs, BooleanResponse)
@Controller('/v1/client/system/contact-us')
export class ContactUsClientContriller {
  constructor(private readonly contactUsService: ContactUsService) {}

  @ClientAuth()
  @ApiBaseResult(BooleanResponse, 201, 'Create contact us')
  @ApiOperation({ summary: 'Create contact us' })
  @Post()
  async createContactUs(
    @Body() body: CreateContactUsRequest,
    @CurrentUser('userId') userId: string,
  ) {
    await this.contactUsService.createContactUs({
      ...body,
      userId,
    })
    return {
      status: true,
    }
  }
}
