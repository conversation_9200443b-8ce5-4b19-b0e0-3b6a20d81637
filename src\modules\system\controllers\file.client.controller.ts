import { Controller } from '@nestjs/common'
import { ApiExtraModels, ApiTags } from '@nestjs/swagger'
import { S3Service } from '@/common'
import { FileResponse } from '../dto'

@ApiTags('Files')
@ApiExtraModels(FileResponse)
@Controller('v1/client/system/files')
export class FileClientController {
  constructor(private readonly S3Service: S3Service) {}

  // @ClientAuth()
  // @ApiBaseResult(FileResponse, 201, 'File upload ')
  // @Post('s3/upload')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     required: ['type', 'file'],
  //     properties: {
  //       type: { type: 'string', enum: Object.values(EFileType), description: '文件类型' },
  //       file: {
  //         type: 'string',
  //         format: 'binary',
  //       },
  //     },
  //   },
  // })
  // @ApiConsumes('multipart/form-data')
  // @UseInterceptors(FileInterceptor('file'))
  // async s3Upload(
  //   @Body() body: any,
  //   @UploadedFile() file: Express.Multer.File,
  // ): Promise<any> {
  //   const { type } = body
  //   const suffixName = path.extname(file.originalname)
  //   const url = await this.S3Service.upload({
  //     fileName: uuid() + suffixName,
  //     fileBuffer: file.buffer,
  //     path: `public/${type}`,
  //     contentType: suffixName,
  //   })
  //   return { url }
  // }
}
