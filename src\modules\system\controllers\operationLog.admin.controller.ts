import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { AdminAuth, ApiPageResult } from '@/common'
import { OperationLog } from '@/entities'
import { QueryLogDto } from '@/modules/schools/dto'
import { OperationLogService } from '../services'

@ApiTags('Log')
@ApiExtraModels(OperationLog)
@Controller('v1/admin/logs')
export class OperationLogAdminController {
  constructor(private readonly logService: OperationLogService) {}

  @ApiOperation({ summary: 'list logs' })
  @AdminAuth()
  @ApiPageResult(OperationLog, 200)
  @Get()
  async listLogs(@Query() data: QueryLogDto) {
    return this.logService.listAdminLog(data)
  }
}
