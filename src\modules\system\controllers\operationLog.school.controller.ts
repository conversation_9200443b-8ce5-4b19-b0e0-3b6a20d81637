import { Controller, Get, Query } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ApiPageResult, CurrentSchoolAdmin, SchoolAdminAuth } from '@/common'
import { OperationLog } from '@/entities'
import { QueryLogDto } from '../../schools/dto'
import { OperationLogService } from '../services'

@ApiTags('Log')
@ApiExtraModels(OperationLog)
@Controller('v1/school-admin/logs')
export class OperationLogSchoolController {
  constructor(private readonly logService: OperationLogService) {}

  @ApiOperation({ summary: 'list logs' })
  @SchoolAdminAuth()
  @ApiPageResult(OperationLog, 200)
  @Get()
  async listLogs(@Query() data: QueryLogDto, @CurrentSchoolAdmin() admin: any) {
    return this.logService.listLog(data, admin.schoolId)
  }
}
