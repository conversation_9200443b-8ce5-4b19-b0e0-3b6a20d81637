import { Controller, Get, Param } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger'
import { GetAppVersionResponse } from '../dto'
import { AppVersionService } from '../services/appVersion.service'

@ApiTags('System')
@ApiExtraModels(GetAppVersionResponse)
@Controller('/v1/public')
export class PublicContriller {
  constructor(private readonly appVersionService: AppVersionService) {}

  @Get('health')
  async getHealth() {
    return {
      status: 'ok',
    }
  }
}
