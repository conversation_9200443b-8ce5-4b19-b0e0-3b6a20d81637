import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator'
import { PageRequest } from '@/common'
import { AppVersion } from '../entities'
import { EAppPlatform } from '../enums'

export class CreateAppVersionRequest extends PickType(AppVersion, [
  'platform',
  'version',
  'forceUpgrade',
  'status',
  'title',
  'description',
  'multilingualTitle',
  'multilingualDescription',
]) {}

export class PatchAppVersionRequest extends PickType(AppVersion, [
  'forceUpgrade',
  'status',
  'title',
  'description',
  'multilingualTitle',
  'multilingualDescription',
]) {}

export class GetAppVersionResponse extends PickType(AppVersion, [
  'forceUpgrade',
  'platform',
  'title',
  'description',
]) {
  @ApiPropertyOptional({})
  @IsOptional()
  @IsString()
  version?: string

  @ApiProperty()
  @IsBoolean()
  hasNewVersion: boolean
}

export class ListAppVersionsRequest extends PageRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keyword?: string

  @ApiPropertyOptional({
    enum: EAppPlatform,
    example: EAppPlatform.ANDROID,
  })
  @IsEnum(EAppPlatform)
  @IsOptional()
  platform?: EAppPlatform
}
