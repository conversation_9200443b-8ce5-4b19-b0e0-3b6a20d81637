import { ApiProperty } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'app_settings' })
export class AppSetting extends BaseEntity<AppSetting> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({})
  @IsString()
  @Column({ nullable: true, default: null })
  key: string

  @ApiProperty({})
  @IsString()
  @Column({ nullable: true, default: null })
  value: string

  constructor(partial: Partial<AppSetting>) {
    super(partial)
    Object.assign(this, partial)
  }
}
