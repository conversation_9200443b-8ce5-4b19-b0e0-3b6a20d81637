import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { EAppPlatform, EAppVersionStatus } from '../enums'

@Entity({ name: 'app_versions' })
export class AppVersion extends BaseEntity<AppVersion> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({
    enum: EAppPlatform,
    example: EAppPlatform.ANDROID,
  })
  @IsEnum(EAppPlatform)
  @Column({ nullable: false })
  platform: EAppPlatform

  @ApiProperty({})
  @IsString()
  @MaxLength(64)
  @Column({ nullable: false })
  version: string

  @ApiPropertyOptional({})
  @IsOptional()
  @IsString()
  @MaxLength(255)
  @Column({ nullable: true })
  title?: string

  @ApiPropertyOptional({})
  @IsOptional()
  @Column({ type: 'json', nullable: true })
  multilingualTitle?: Record<string, any>

  @ApiPropertyOptional({})
  @IsOptional()
  @IsString()
  @MaxLength(1024)
  @Column({ length: 1024, nullable: true })
  description?: string

  @ApiPropertyOptional({})
  @IsOptional()
  @Column({ type: 'json', nullable: true })
  multilingualDescription?: Record<string, any>

  @ApiProperty({})
  @IsBoolean()
  @Column({ nullable: false, default: false })
  forceUpgrade: boolean

  @ApiProperty({
    enum: EAppVersionStatus,
    example: EAppVersionStatus.IN_REVIEW,
  })
  @IsEnum(EAppVersionStatus)
  @Column({ nullable: false })
  status: EAppVersionStatus

  @ApiPropertyOptional({})
  @IsOptional()
  @IsNumber()
  @Column({ nullable: true, default: 0 })
  weight?: number

  constructor(partial: Partial<AppVersion>) {
    super(partial)
    Object.assign(this, partial)
  }
}
