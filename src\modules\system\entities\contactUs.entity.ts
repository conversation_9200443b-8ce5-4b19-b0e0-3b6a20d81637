import { ApiProperty } from '@nestjs/swagger'
import { IsNumber, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'

@Entity({ name: 'contact_us' })
export class ContactUs extends BaseEntity<ContactUs> {
  @ApiProperty({
    example: 1,
  })
  @IsNumber()
  @PrimaryGeneratedColumn()
  id: number

  @ApiProperty({})
  @IsString()
  @Column({ nullable: true, default: null })
  userId: string

  @ApiProperty({})
  @IsString()
  @Column({ nullable: true, default: null, length: 2048 })
  content: string

  constructor(partial: Partial<ContactUs>) {
    super(partial)
    Object.assign(this, partial)
  }
}
