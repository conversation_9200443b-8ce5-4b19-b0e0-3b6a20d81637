import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEmail, IsString } from 'class-validator'
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from '@/common/entities'
import { ESchoolAdminType } from '@/enums'

@Entity({ name: 'operation_log' })
export class OperationLog extends BaseEntity<OperationLog> {
  @ApiProperty()
  @PrimaryGeneratedColumn()
  id: number

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsString()
  name?: string

  @Column({ nullable: true })
  @ApiPropertyOptional()
  @IsEmail()
  email?: string

  @Column()
  @ApiProperty()
  operation: string

  @Column()
  @ApiProperty()
  userId: number

  @Column()
  @ApiProperty()
  type: ESchoolAdminType

  @Column({ nullable: true })
  schoolId: number

  @Column({ nullable: true, type: 'json' })
  metaData: Record<string, any>

  constructor(partial: Partial<OperationLog>) {
    super(partial)
    Object.assign(this, partial)
  }
}
