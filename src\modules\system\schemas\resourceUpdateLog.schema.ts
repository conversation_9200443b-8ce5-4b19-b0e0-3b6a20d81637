import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { Document } from 'mongoose'

@Schema({})
export class ResourceUpdateLog {
  @ApiProperty()
  _id: string

  @IsString()
  @ApiProperty({
    example: 'Administrator id',
  })
  @Prop({ required: true, type: String })
  operatorUserId: string

  @IsString()
  @ApiProperty({
    example: 'Administrator full name',
  })
  @Prop({ required: true, type: String })
  operatorUserName: string

  @IsString()
  @ApiProperty({
    example: 'Administrator profile image',
  })
  @Prop({ required: true, type: String })
  operatorUserProfileImage: string

  @IsString()
  @ApiProperty({
    example: 'action name',
  })
  @Prop({ required: true, type: String })
  action: string

  @IsString()
  @ApiProperty({
    example: 'updated content type',
  })
  @Prop({ required: true, type: String })
  contentType: string

  @IsString()
  @ApiProperty({
    example: 'updated content id',
  })
  @Prop({ required: true, type: String })
  contentId: string

  @IsString()
  @ApiPropertyOptional({
    example: 'updated content description',
  })
  @IsOptional()
  @Prop({ required: false, type: String })
  description?: string

  @ApiProperty()
  @Prop({ required: true, type: Date, default: new Date() })
  createdAt: Date
}

export type ResourceUpdateLogDocument = ResourceUpdateLog & Document
export const ResourceUpdateLogSchema = SchemaFactory.createForClass(ResourceUpdateLog)
