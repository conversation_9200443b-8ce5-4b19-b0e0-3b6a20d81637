import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { Like, MoreThan, Repository } from 'typeorm'
import { And, ELocaleType, ListingQueryBuilder, Or } from '@/common'
import { ListAppVersionsRequest } from '../dto'
import { AppVersion } from '../entities'
import { EAppPlatform, EAppVersionStatus } from '../enums'
@Injectable()
export class AppVersionService {
  constructor(
    @InjectRepository(AppVersion)
    private readonly appVersionRepository: Repository<AppVersion>
  ) {}

  async createAppVersion(partial: Partial<AppVersion>, options: { operator: any }) {
    const { platform, version } = partial
    const entity = await this.appVersionRepository.findOne({ where: { platform,
      version,
    } })
    if (entity) {
      return this.patchAppVersion(
        entity.id,
        R.omit(['platform', 'version'], partial),
        options
      )
    }

    const subVersions = version.split('.')
    return this.appVersionRepository.save({
      ...partial,
      createdBy: options.operator,
      createdAt: new Date(),
      weight:
        Number(subVersions[0]) * 1000000 +
        Number(subVersions[1]) * 1000 +
        Number(subVersions[2]),
    })
  }
  async getAndroidLastVersion() {
    return await this.appVersionRepository.findOne({
      where: { platform: EAppPlatform.ANDROID, status: EAppVersionStatus.PUBLISHED },
      order: { id: 'DESC' },
    })
  }

  async getAppVersion(options: { version?: string; id?: number }) {
    const { version, id } = options
    const condition = {}
    if (id) {Object.assign(condition, { id })}
    if (version) {Object.assign(condition, { version })}
    return this.appVersionRepository.findOne({ where: condition })
  } 

  async getLatestAppVersion(
    platform: EAppPlatform,
    version: string,
    locale: ELocaleType = ELocaleType.EN_UK
  ) {
    const subVersions = version.split('.')
    const versions = await this.appVersionRepository.find({
      where: {
        platform,
        weight: MoreThan(
          Number(subVersions[0]) * 1000000 +
            Number(subVersions[1]) * 1000 +
            Number(subVersions[2])
        ),
        status: EAppVersionStatus.PUBLISHED,
      },
      order: {
        version: 'DESC',
      },
    })

    if (!versions || versions.length <= 0 || versions[0].version === version)
    {return {
      hasNewVersion: false,
      version,
      forceUpgrade: false,
      platform,
    }}
    const latestVersion = versions[0]
    return {
      hasNewVersion: versions && versions.length > 0 && latestVersion.version !== version,
      version: latestVersion.version,
      forceUpgrade: versions.some((x) => x.forceUpgrade),
      platform,
      title:
        latestVersion.multilingualTitle?.[locale] ||
        latestVersion.multilingualTitle?.[ELocaleType.EN_UK] ||
        latestVersion.title,
      description:
        latestVersion.multilingualDescription?.[locale] ||
        latestVersion.multilingualDescription?.[ELocaleType.EN_UK] ||
        latestVersion.description,
      multilingualTitle: latestVersion.multilingualTitle,
      multilingualDescription: latestVersion.multilingualDescription,
    }
  }

  async patchAppVersion(
    id: number,
    partial: Partial<AppVersion>,
    options: { operator: any }
  ) {
    const entity = await this.appVersionRepository.findOne({ where: { id,
    } })
    if (!entity) {throw new NotFoundException()}
    Object.assign(entity, R.omit(['platform', 'version'], partial))

    const subVersions = entity.version.split('.')
    entity.weight =
      Number(subVersions[0]) * 1000000 +
      Number(subVersions[1]) * 1000 +
      Number(subVersions[2])

    entity.updatedBy = options.operator
    entity.updatedAt = new Date()
    return this.appVersionRepository.save(entity)
  }

  async deleteAppVersion(id: number) {
    const entity = await this.appVersionRepository.findOne({ where: { id,
    } })
    if (!entity) {throw new NotFoundException()}
    await this.appVersionRepository.delete({ id: entity.id })
    return entity
  }

  async listAppVersions(options: ListAppVersionsRequest) {
    const { platform, keyword, pageIndex = 1, pageSize = 10 } = options || {}

    const condition = []

    const keyworkCondition = []
    if (keyword) {
      keyworkCondition.push([
        { version: Like(`%${keyword}%`) },
        // { title: Like(`%${keyword}%`) },
        // { platform: Like(`%${keyword}%`) },
      ])
      condition.push(Or(keyworkCondition))
    }
    if (platform) {condition.push({ platform })}

    return ListingQueryBuilder.new<AppVersion, any>()
      .repository(this.appVersionRepository)
      .where(And(condition))
      .pageIndex(pageIndex)
      .pageSize(pageSize)
      .order({ createdAt: 'DESC' })
      .query()
  }
}
