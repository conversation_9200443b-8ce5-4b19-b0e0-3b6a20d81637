import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { Repository } from 'typeorm'
import { Log } from '@/entities'

@Injectable()
export class LogService {
  constructor(@InjectRepository(Log) private readonly logRepository: Repository<Log>) {}

  async save(operation: string, user: any, params?: any) {
    await this.logRepository.save({
      operation,
      operator: R.pick(['userId', 'familyName', 'givenName', 'email'], user),
      params: params ? JSON.stringify(params) : null,
      schoolId: user.schoolId ? user.schoolId : null,
    })
  }
}
