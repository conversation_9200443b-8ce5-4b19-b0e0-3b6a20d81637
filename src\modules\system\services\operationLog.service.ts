import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import R from 'ramda'
import { Repository } from 'typeorm'
import { OperationLog } from '@/entities'
import { EOrderDirection, ESchoolAdminType } from '../../../enums'
import { PAGE_SIZE } from '../../constants'
import { QueryLogDto } from '../../schools/dto'
import { OperationLogData } from '../../schools/interfaces'

@Injectable()
export class OperationLogService {
  constructor(
    @InjectRepository(OperationLog)
    private readonly logRepository: Repository<OperationLog>
  ) {}

  async createLog(data: OperationLogData) {
    let type = data.user?.roles?.length ? data.user.roles : ''

    if (data.user?.schoolId) {
      if (R.isNil(data.user?.isTeacher))
      {type = data.user?.isRoot ? ESchoolAdminType.SUPER_ADMIN : ESchoolAdminType.ADMIN}
      else {type = ESchoolAdminType.USER}
    }

    return this.logRepository.save({
      name:
        data.user?.familyName || data.user?.givenName
          ? `${R.isNil(data.user?.givenName) ? '' : data.user?.givenName} ${
            R.isNil(data.user?.familyName) ? '' : data.user?.familyName
          }`
          : null,
      email: data.user?.email ?? null,
      userId: data.user?.userId,
      type,
      operation: data.operation,
      schoolId: data.user?.schoolId ?? null,
      metaData: data.metaData ?? null,
      createdBy: R.pick(['userId', 'roleName'], data?.user || {}),
    })
  }

  async listAdminLog(query: QueryLogDto) {
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const { orderDirection = EOrderDirection.DESC } = query

    const queryBuilder = this.logRepository.createQueryBuilder('log')
      .where('log.schoolId IS NULL')

    const total = await queryBuilder.getCount()

    const items = await queryBuilder
      .skip((pageIndex - 1) * pageSize)
      .take(pageSize)
      .orderBy('log.createdAt', orderDirection)
      .getMany()

    return { pageIndex, pageSize, total, items }
  }

  async listLog(query: QueryLogDto, schoolId?: number) {
    const where: any = {}
    if (schoolId) {
      where.schoolId = schoolId
    }
    const { orderDirection = EOrderDirection.DESC } = query
    const { pageIndex = 1, pageSize = PAGE_SIZE } = query
    const total = await this.logRepository.countBy(where)
    const items = await this.logRepository.find({
      where,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      order: { createdAt: orderDirection },
    })
    return { pageIndex, pageSize, total, items }
  }
}
