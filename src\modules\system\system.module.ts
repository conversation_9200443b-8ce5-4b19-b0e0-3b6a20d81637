import { Global,Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AppVersionAdminContriller, AppVersionPublicContriller, ContactUsClientContriller,
  FileAdminController, FileClientController, OperationLogAdminController, OperationLogSchoolController,
} from './controllers'
import { AppSetting, AppVersion, ContactUs, Log,OperationLog } from './entities'
import { AppVersionService, ContactUsService, LogService,OperationLogService} from './services'
 
@Global()
@Module({ 
  imports: [
    TypeOrmModule.forFeature([AppSetting, ContactUs, AppVersion, OperationLog, Log]),
    // MongooseModule.forFeature([
    //   { name: ResourceUpdateLog.name, schema: ResourceUpdateLogSchema },
    // ]),
  ],
  providers: [ContactUsService, AppVersionService, OperationLogService, LogService],
  controllers: [ContactUsClientContriller, AppVersionPublicContriller,
    AppVersionAdminContriller, FileAdminController, FileClientController,
    OperationLogAdminController, OperationLogSchoolController,
  ],


  exports: [TypeOrmModule, ContactUsService, AppVersionService, OperationLogService,  LogService],
})
export class SystemModule {}
