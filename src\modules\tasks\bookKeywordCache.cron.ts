import { Injectable } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { RedisService, RedlockService } from '@/common'
import { Book } from '@/entities'
import { getBooksKeywordKey, getTaskLockKey } from '../constants'

@Injectable()
export class BookKeywordCacheService {
  constructor(
    private readonly redlockService: RedlockService,
    private readonly redisService: RedisService,
    @InjectRepository(Book) private readonly bookRepositories: Repository<Book>,
  ) {}

  @Cron(CronExpression.EVERY_2_HOURS)
  async bookKeywordCache() {
    if (process.env.APP_ENV === 'local') return
    await this.redlockService.lockWrapper(
      getTaskLockKey('bookKeywordCache'),
      10 * 60 * 1000,
      async () => {
        const batchSize = 500
        const totalBooks = await this.bookRepositories
          .createQueryBuilder('book')
          .leftJoinAndSelect('book.authors', 'authors')
          .leftJoinAndSelect('book.publisher', 'publisher')
          .select([
            'book.id',
            'book.isbn',
            'book.description',
            'book.name',
            'book.status',
            'book.level',
            'book.version',
            'book.hiddeSchoolIds',
            'authors.name',
            'publisher.name',
          ])
          .getCount()
        for (let i = 0; i < totalBooks; i += batchSize) {
          const books = await this.bookRepositories
            .createQueryBuilder('book')
            .leftJoinAndSelect('book.authors', 'authors')
            .leftJoinAndSelect('book.publisher', 'publisher')
            .select([
              'book.id',
              'book.isbn',
              'book.description',
              'book.name',
              'book.status',
              'book.level',
              'book.version',
              'book.hiddeSchoolIds',
              'authors.name',
              'publisher.name',
            ])
            .skip(i)
            .take(batchSize)
            .getMany()
          const redisData = books.map((book) => {
            const bookData = {
              isbn: book.isbn,
              description: {
                en_uk: book.description?.en_uk || '',
                zh_HK: book.description?.zh_HK || '',
                zh_cn: book.description?.zh_cn || '',
              },
              name: {
                en_uk: book.name?.en_uk || '',
                zh_HK: book.name?.zh_HK || '',
                zh_cn: book.name?.zh_cn || '',
              },
              authors: {
                en_uk: book.authors?.map((author) => author.name?.en_uk).join(', ') || '',
                zh_HK: book.authors?.map((author) => author.name?.zh_HK).join(', ') || '',
                zh_cn: book.authors?.map((author) => author.name?.zh_cn).join(', ') || '',
              },
              publisher: {
                en_uk: book.publisher?.name?.en_uk || '',
                zh_HK: book.publisher?.name?.zh_HK || '',
                zh_cn: book.publisher?.name?.zh_cn || '',
              },
              status: book.status ?? '',
              level: Array.isArray(book.level) ? book.level : [],
              version: Array.isArray(book.version)
                ? book.version.join(',')
                : book.version || '',
              hidde_school_ids: Array.isArray(book.hiddeSchoolIds)
                ? book.hiddeSchoolIds
                : [],
            }

            return {
              key: getBooksKeywordKey(book.id),
              value: bookData,
            }
          })
          await Promise.all(
            redisData.map(({ key, value }) => this.redisService.jsonSet(key, '$', value)),
          )
        }
      },
    )
  }
}
