import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule'
import { RedisService, RedlockService } from '@/common'
import {
  getReadRecordSetName,
  getRedis<PERSON>ey,
  getTask<PERSON>ock<PERSON>ey,
  getUser<PERSON><PERSON><PERSON><PERSON>,
  READING_SESSION_SET,
} from '../constants'

@Injectable()
export class ExpireReadingCache {
  constructor(
    private readonly redisService: RedisService,
    private readonly redlockService: RedlockService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async expireReadingCache() {
    if (process.env.APP_ENV === 'local') return
    await this.redlockService.lockWrapper(
      getTaskLockKey('expireReadingCache'),
      10 * 60 * 1000,
      async () => {
        const sessionIds = await this.redisService.smembers(READING_SESSION_SET)
        const pipe = this.redisService.pipeline()
        sessionIds.forEach((sessionId) => pipe.exists(getRedisKey(sessionId)))
        const data = await pipe.exec()

        const expireSessionIds = data
          .map((item, index) => (!item[1] ? sessionIds[index] : null))
          .filter((item) => !!item)

        // console.log('******ExpireReadingCache******', expireSessionIds)
        if (expireSessionIds.length === 0) return

        const ids = expireSessionIds.map((item) => item.split(':').pop())
        // console.log('******ExpireReadingCache******', ids.length)

        const cachePipe = this.redisService.pipeline()
        ids.forEach((id) => cachePipe.hgetall(getRedisKey(id)))

        const cacheData = await cachePipe.exec()

        const delPipe = this.redisService.pipeline()
        // console.log('******ExpireReadingCache******', cacheData.length)
        cacheData
          .map((item) => item[1])
          .filter((item) => Object.keys(item).length)
          .forEach((item) => {
            // console.log('------ExpireReadingCache-----', item)
            delPipe.del(getRedisKey(item.recordKey))
            // delPipe.del(getRedisKey(item.userBalanceKey))

            delPipe.srem(getReadRecordSetName(item.schoolId), item.recordKey)
            delPipe.del(getRedisKey(getUserCacheKey(item.userId)))
          })

        ids.forEach((id) => delPipe.del(getRedisKey(id)))

        delPipe.srem(READING_SESSION_SET, ...expireSessionIds)
        const [error] = await delPipe.exec()
        if (error) {
          console.log('555555ExpireReadingCache555555', error)
        }
      },
    )
  }
}
