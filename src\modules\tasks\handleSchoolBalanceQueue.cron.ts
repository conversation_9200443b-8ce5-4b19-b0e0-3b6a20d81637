import { Injectable } from '@nestjs/common'
import { <PERSON>ron } from '@nestjs/schedule'
import pLimit from 'p-limit'
import { RedisService, RedlockService } from '@/common'
import {
  getReadingSchoolsSetName,
  getSchoolBalanceCacheKey,
  getSchoolBalanceQueueCacheKey,
  getTask<PERSON>ock<PERSON>ey,
} from '../constants'

@Injectable()
export class HandleSchoolBalanceQueueService {
  constructor(
    private readonly redlockService: RedlockService,
    private readonly redisService: RedisService,
  ) {}

  @Cron('*/6 * * * * *')
  async handleSchoolBalanceQueue() {
    const limit = pLimit(100)
    if (process.env.APP_ENV === 'local') return
    await this.redlockService.lockWrapper(
      getTaskLockKey('handleSchoolBalanceQueue'),
      1 * 60 * 1000,
      async () => {
        const schools = await this.redisService.smembers(getReadingSchoolsSetName())
        // console.log(
        //   `handleSchoolBalanceQueue----------------start----------get schools length: ${schools.length}`,
        // )
        const tasks = schools.map((schoolId) =>
          limit(async () => this.processSchoolBalance(Number(schoolId))),
        )
        await Promise.all(tasks)
        // console.log(`handleSchoolBalanceQueue----------------end----------processed schools: ${schools.length}`)
      },
    )
  }

  // 更新学校cache使用余额
  private async processSchoolBalance(schoolId: number) {
    const queueKey = getSchoolBalanceQueueCacheKey(schoolId)
    const usedQuotaKey = getSchoolBalanceCacheKey(schoolId)
    await this.redlockService.lockWrapper(
      getTaskLockKey(`handleSchoolBalanceQueue:${schoolId}`),
      1 * 10 * 1000,
      async () => {
        const schoolBalances = await this.redisService.lrange(queueKey, 0, -1)
        if (schoolBalances.length > 0) {
          const totalTime = schoolBalances.reduce(
            (sum, time) => sum + parseInt(time, 10),
            0,
          )
          // 获取更新前的余额
          const currentBalance = await this.redisService.hget(usedQuotaKey, 'usedQuota')
          console.log(
            `handleSchoolBalanceQueue --- school_id ${schoolId}  Current balance before update: ${currentBalance}`,
          )
          await this.redisService.hincrby(
            getSchoolBalanceCacheKey(schoolId),
            'usedQuota',
            totalTime,
          )
          // 获取更新后的余额
          const updatedBalance = await this.redisService.hget(usedQuotaKey, 'usedQuota')
          console.log(
            `handleSchoolBalanceQueue --- school_id ${schoolId}  Updated balance after increment: ${updatedBalance}`,
          )
          await this.redisService.ltrim(queueKey, schoolBalances.length, -1)
        }
      },
    )
  }
}
