import { Injectable } from '@nestjs/common'
import { <PERSON>ron } from '@nestjs/schedule'
import R from 'ramda'
import { RedisService, RedlockService } from '@/common'
import { chunkArray } from '@/utils/array.util'
import {
  getReadingSchoolsSetName,
  getReadingTimeLock<PERSON>ey,
  getReadingUsersSetName,
  getReadRecordSetName,
  getRedisKey, 
  getSchoolBalanceCacheKey,
  getTaskLockKey,
  getUserBalanceCache<PERSON>ey,
} from '../constants'
import { ReadRecordRepository } from '../schools/repositories'
import { SchoolBalanceService, UserBalanceService } from '../schools/services'

@Injectable()
export class SaveToDBService {
  constructor(
    private readonly redlockService: RedlockService,
    private readonly redisService: RedisService,
    private readonly userBalanceService: UserBalanceService,
    private readonly schoolBalanceService: SchoolBalanceService,
    private readonly recordRecordRepositories: ReadRecordRepository
  ) {}

  @Cron('*/30 * * * * *')
  async flushToDB() {
    if (process.env.APP_ENV === 'local') {return}
    await this.redlockService.lockWrapper(
      getTaskLockKey('flushToDB'),
      10 * 60 * 1000,
      async () => {
        const schools = await this.redisService.smembers(getReadingSchoolsSetName())
        console.log(
          `flushToDB----------------start----------get schools length: ${schools.length}`
        )
        for (const schoolId of schools) {
          await this.flushToDBForSchool(Number(schoolId))
        }
        console.log('flushToDB----------------end--------------------')
      }
    )
  }

  async flushToDBForSchool(schoolId: number) {
    let data
    let records

    await this.redlockService.lockWrapper(
      getReadingTimeLockKey(Number(schoolId)),
      10 * 60 * 1000,
      async () => {
        const pipe = this.redisService.pipeline()
        const recordPipe = this.redisService.pipeline()
        const users = await this.redisService.smembers(
          getReadingUsersSetName(Number(schoolId))
        )
        const recordKeys = await this.redisService.smembers(
          getReadRecordSetName(Number(schoolId))
        )
        if (recordKeys.length === 0) {return}
        console.log(
          `need flush to db | school_id: ${schoolId}  | user length: ${users.length} | record length: ${recordKeys.length}`
        )
        recordKeys.forEach((key) => recordPipe.hgetall(getRedisKey(key)))
        pipe.hgetall(getRedisKey(getSchoolBalanceCacheKey(Number(schoolId))))
        users.forEach((userId) =>
          pipe.hgetall(getRedisKey(getUserBalanceCacheKey(Number(userId))))
        )
        data = await pipe.exec()
        records = await recordPipe.exec()
      }
    )
    // 处理 records
    if (records?.length) {
      const recordChunks = chunkArray(
        records.map((item) => item[1]).filter((item) => item?.id && item?.readingTime)
      )

      for (const chunk of recordChunks) {
        await this.recordRecordRepositories.bathSaveReadingTimeToDB(chunk)
      }
    }

    // 处理 data
    if (data?.length) {
      const [schoolBalance, ...balances] = data.map((item) => item[1])

      if (schoolBalance?.id && schoolBalance?.usedQuota) {
        await this.schoolBalanceService.updateSchoolReadingTime(
          Number(schoolBalance.id),
          Number(schoolBalance.usedQuota)
        )
      }

      const validUserBalances = balances.filter(
        (item) => item?.id && !R.isNil(item.usedQuota) && !R.isNil(item.totalUsedQuota)
      )
      const userBalanceChunks = chunkArray(validUserBalances)
      for (const chunk of userBalanceChunks) {
        await this.userBalanceService.batchUpdateReadingTime(chunk)
      }
    }
  }
}
