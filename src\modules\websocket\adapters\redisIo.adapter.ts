import { INestApplicationContext } from '@nestjs/common'
import { IoAdapter } from '@nestjs/platform-socket.io'
import { createClient } from 'redis'
import { ServerOptions } from 'socket.io'
import { createAdapter } from '@socket.io/redis-adapter'
import { JwtService } from '@/common'

export class RedisIoAdapter extends IoAdapter {
  private adapterConstructor: ReturnType<typeof createAdapter>
  private jwtService: JwtService

  constructor(private app: INestApplicationContext) {
    super(app)
    this.jwtService = this.app.get(JwtService)
  }

  async connectToRedis(): Promise<void> {
    const pubClient = createClient({
      url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
      password: process.env.REDIS_PASSWORD,
    })
    const subClient = pubClient.duplicate()

    await Promise.all([pubClient.connect(), subClient.connect()])

    this.adapterConstructor = createAdapter(pubClient, subClient)
  }

  createIOServer(port: number, options?: ServerOptions): any {
    options.allowRequest = async (request, allowFunction) => {
      try {
        const urlParams = new URLSearchParams(request.url.split('?')[1])
        const authorization =
          request.headers.authorization ?? urlParams.get('authorization')
        options.transports = ['websocket']
        options.allowUpgrades = true
        options.cookie = true
        options.cors = {
          origin: '*',
          methods: ['GET', 'POST'],
        }
        await this.jwtService.verifyWssToken(authorization)
      } catch (e) {
        return allowFunction('Unauthorized', false)
      }
      return allowFunction(null, true)
    }
    // 配置心跳检查间隔和超时时间
    options.pingInterval = 3000
    options.pingTimeout = 60000
    const server = super.createIOServer(port, options)
    server.adapter(this.adapterConstructor)
    return server
  }
}
