import { forwardRef, Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { InjectModel } from '@nestjs/mongoose'
import { InjectRepository } from '@nestjs/typeorm'
import https from 'https'
import { HttpsProxyAgent } from 'https-proxy-agent'
import { Model } from 'mongoose'
import OpenAI from 'openai'
import { WebsocketGateway } from 'src/modules/websocket'
import { Repository } from 'typeorm'
import { RedisService } from '@/common'
import { IOpenAIConfig } from '@/common/interfaces/openai.interface'
import {
  Assistant,
  AssistantAdminErrorResponse,
  AssistantClientErrorResponse,
  AssistantThread,
  AssistantThreadMessageRuns,
  ReferenceBook,
} from '@/entities'
import { AssistantMessages } from '@/entities/assistantMessage.entity'
import { EUserType } from '@/enums'
import {
  AssistantErrorException,
  AssistantRateLimitException,
} from '@/modules/assistant/exception'
import {
  ASSISTANTS_BOOKS_KEY,
  ASSISTANTS_INFO_KEY,
  getAssistantInfoKey,
  getUserAssistantThreadKey,
} from '@/modules/constants'

@Injectable()
export class OpenAIService {
  private config: IOpenAIConfig
  private openai: OpenAI

  constructor(
    @Inject(forwardRef(() => WebsocketGateway)) //处理循环依赖
    private readonly websocketGateway: WebsocketGateway,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,

    @InjectRepository(Assistant)
    private readonly assistantRepository: Repository<Assistant>,
    @InjectRepository(AssistantThread)
    private readonly assistantThreadRepository: Repository<AssistantThread>,

    @InjectModel(AssistantMessages.name)
    private assistantMessagesModel: Model<AssistantMessages>,

    @InjectRepository(AssistantAdminErrorResponse)
    private readonly adminErrorRepository: Repository<AssistantAdminErrorResponse>,

    @InjectRepository(AssistantClientErrorResponse)
    private readonly clientErrorRepository: Repository<AssistantClientErrorResponse>,

    @InjectRepository(AssistantThreadMessageRuns)
    private readonly assistantThreadMessageRunsRepository: Repository<AssistantThreadMessageRuns>,

    @InjectRepository(ReferenceBook)
    private readonly referenceBookRepository: Repository<ReferenceBook>
  ) {
    this.config = this.configService.get('common.openai')
    this.openai = new OpenAI({
      apiKey: this.config.apikey,
      organization: 'org-LcLzT83YZuJjTB4zpnQATjo1',
      project: 'proj_kxJ1Lc1PuxwxO0uZEDTSLOR7',
      httpAgent:
        process.env.APP_ENV === 'local'
          ? new HttpsProxyAgent(this.config.proxy)
          : undefined, //大陆ip使用翻墙可以开启代理
    })
  }

  // 一，上传文件
  // 二，创建AI套餐
  // 1，创建助手
  // 2，创建存储向量数据库
  // 3，选择文件存储到向量数据库
  // 4，套餐删除  需要记录对应的文件isbn 与 openai的 file_id 进行修改删除
  // 三，会话
  // 1，创建会话绑定向量数据库
  // 2，执行run会话绑定assistant 流推送 | 文本处理 ｜ 对应套餐数据返回
  // 3，获取消息列表
  // 4，token限制 模型调用限制

  /**
   * 创建助手
   * @param name
   * @param vector_store_ids
   */
  async createAssistant(name: string, vector_store_ids: string) {
    return await this.openai.beta.assistants.create({
      name,
      model: 'gpt-4.1',
      description: 'assistant',
      top_p: 0.8,
      temperature: 0.6,
      tool_resources: {
        file_search: {
          vector_store_ids: [vector_store_ids],
        },
      },
    })
  }

  async updateAssistant(assistantId: string, name: string) {
    return await this.openai.beta.assistants.update(assistantId, {
      name,
    })
  }

  /**
   * 获取assistant信息
   * @returns
   */
  async getAssistant(assistantId: string) {
    const assistantString = await this.redisService.get(getAssistantInfoKey(assistantId))
    let assistant: Assistant | null = null
    if (!assistantString) {
      assistant = await this.cacheAssistant(assistantId)
    }
    assistant = JSON.parse(assistantString)
    return assistant
  }

  async cacheAssistant(assistantId: string) {
    const assistant = await this.assistantRepository.findOne({where: { assistantId }})
    if (!assistant) {return null}
    await this.redisService.set(
      getAssistantInfoKey(assistantId),
      JSON.stringify(assistant)
    )
    return assistant
  }

  /**
   * 获取助手列表
   * @returns
   */
  async getAssistantList() {
    const myAssistants = await this.openai.beta.assistants.list({
      order: 'desc',
      limit: 20,
    })

    return myAssistants.data
  }

  /**
   * 删除助手
   * @param assistantId
   */
  async deleteAssistant(assistantId: string) {
    await this.openai.beta.assistants.del(assistantId)
    // 删除redis缓存
    await this.redisService.del(ASSISTANTS_INFO_KEY)
  }

  /**
   * 上传文件
   * @param file
   * @returns
   */
  async uploadFile(link: any) {
    await this.openai.models.list({ httpAgent: new https.Agent({ keepAlive: true }) })
    return await this.openai.files.create({
      file: await fetch(link),
      purpose: 'assistants',
    })
  }

  /**
   * 删除文件
   * @param vectorStoreId
   * @param fileId
   * @returns
   */
  async deleteFile(fileId: string) {
    try {
      return await this.openai.files.del(fileId)
    } catch (error) {
      console.log('openai delete files error:', error)
    }
  }

  /**
   * @param openaiFileId
   */
  async getFile(openaiFileId: string) {
    return await this.openai.files.retrieve(openaiFileId)
  }

  /**
   * 获取文件列表
   */
  async getFileList(vectorStoreId: string, after: string) {
    return await this.openai.vectorStores.files.list(vectorStoreId, {
      limit: 3,
      after,
      filter: 'completed',
      order: 'desc',
    })
  }

  /**
   * 获取缓存书籍信息
   * @param opeanifileId
   * @returns
   */
  async getFileCache(opeanifileId: string) {
    const fileInfoString = await this.redisService.hget(
      ASSISTANTS_BOOKS_KEY,
      opeanifileId
    )
    return fileInfoString ? JSON.parse(fileInfoString) : null
  }
  /**
   * 创建向量数据库
   */
  async createVectorStore(name: string) {
    return await this.openai.vectorStores.create({ name })
  }

  /**
   * 获取向量数据库列表
   */
  async getVectorStoresList() {
    return await this.openai.vectorStores.list()
  }

  /**
   * 删除向量数据库
   * @param vectorStoreId
   * @returns
   */
  async deleteVectorStore(vectorStoreId: string) {
    return await this.openai.vectorStores.del(vectorStoreId)
  }

  async deleteVectorStoreFile(data: { vectorStoreId: string; openaiFileId: string }) {
    return this.openai.vectorStores.files.del(data.vectorStoreId, data.openaiFileId)
  }

  /**
   * 存储向量数据库文件
   */
  async createVectorStoreFile(data: { openaiFileId: string; vectorStoreId: string }) {
    return await this.openai.vectorStores.files.create(data.vectorStoreId, {
      file_id: data.openaiFileId,
    })
  }

  /**
   * 批量存储向量数据库文件
   * @param vectorStoreId
   * @param openaiFileIds
   * @returns
   */
  async createVectorStoreFileBatch(data: {
    vectorStoreId: string
    openaiFileIds: string[]
  }) {
    return await this.openai.vectorStores.fileBatches.create(data.vectorStoreId, {
      file_ids: data.openaiFileIds,
    })
  }

  /**
   * 批量删除向量数据库文件
   * @param vectorStoreId
   * @param vectorStoreFileBatchId
   * @returns
   */
  async deleteVectorStoreFileBatch(
    vectorStoreId: string,
    vectorStoreFileBatchId: string
  ) {
    return await this.openai.vectorStores.fileBatches.cancel(
      vectorStoreId,
      vectorStoreFileBatchId
    )
  }

  /**
   * 获取向量存储文件列表及状态（获取所有数据）
   * @param vectorStoreId 向量存储ID
   * @param batchId 批次ID
   * @returns 返回文件列表及状态信息
   */
  async getVectorStoresFileList(
    vectorStoreId: string,
    batchId: string,
    allFiles: any[] = [],
    after?: string
  ) {
    try {
      // 获取批次文件列表
      const batchFiles = await this.openai.vectorStores.fileBatches.listFiles(
        vectorStoreId,
        batchId,
        {
          limit: 100,
          after,
          order: 'desc',
        }
      )

      allFiles.push(...batchFiles.data)

      if (batchFiles.has_more) {
        return this.getVectorStoresFileList(
          vectorStoreId,
          batchId,
          allFiles,
          batchFiles.data[batchFiles.data.length - 1].id
        )
      }

      // 返回所有数据
      return {
        data: allFiles,
        total: allFiles.length,
      }
    } catch (error) {
      console.error('获取向量存储文件列表失败:', error)
      throw error
    }
  }
  /**
   * @param vectorStoreId
   * @param openaiFileId
   */
  async getVectorStoresFile(vectorStoreId: string, openaiFileId: string) {
    try {
      return await this.openai.vectorStores.files.retrieve(vectorStoreId, openaiFileId)
    } catch (error) {
      console.log(error)
      return null
    }
  }

  /**
   * 删除向量数据库文件
   * @param vectorStoreId
   * @param fileId
   */
  async deleteVectorStoresFile(vectorStoreId: string, fileId: string) {
    return await this.openai.vectorStores.files.del(vectorStoreId, fileId)
  }

  /**
   * @param threadId
   * @returns
   */
  async getThread(threadId: string) {
    try {
      console.log(threadId)
      const response = await this.openai.beta.threads.retrieve(threadId)
      return response
    } catch (error) {
      console.log(error)
      return error
    }
  }

  /**
   * 创建线程
   * @param vectorStoreId
   * @returns
   */
  async createThread(vectorStoreId: string) {
    return await this.openai.beta.threads.create({
      tool_resources: {
        file_search: {
          vector_store_ids: [vectorStoreId],
        },
      },
    })
  }

  /**
   * 用户创建新thread 并更新redis
   * @param user
   * @param schoolId
   */
  async newUserThreadId(user: any) {
    const { userId, schoolId = null } = user
    const assistants = await this.getAssistant(user.assistantId)
    const userThreadId = await this.createUserThread(
      user,
      assistants.assistantId,
      assistants.vectorStoreId
    )
    // 设置 Redis 数据
    await this.redisService.hset(
      getUserAssistantThreadKey(schoolId),
      userId.toString(),
      userThreadId.toString()
    )
  }

  /**
   * 用户创建线程
   * @param user
   * @param assistantId
   * @param threadId
   * @param content
   * @returns
   */
  async createUserThread(user: any, assistantId: string, vectorStoreId: string) {
    const { userId, schoolId = null, classId = null, gradeId = null } = user
    const thread = await this.createThread(vectorStoreId)
    // 记录thread
    await this.assistantThreadRepository.save({
      assistantId,
      threadId: thread.id,
      userType: user.isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      userId,
      schoolId,
      userClassId: classId,
      gradeId,
    })
    return thread.id
  }

  /**
   * 获取runList
   * @param threadId
   */
  async getRunsList(threadId: string) {
    return await this.openai.beta.threads.runs.list(threadId)
  }

  /**
   * 获取runs信息
   * @param threadId
   * @param runId
   * @returns
   */
  async getRuns(user: any, assistant: Assistant, threadId: string, runId: string) {
    try {
      const response = await this.openai.beta.threads.runs.retrieve(threadId, runId)
      if (
        response.status == 'failed' &&
        response.last_error.code == 'rate_limit_exceeded'
      ) {
        return {
          status: 'fail',
          msg: new AssistantRateLimitException().getResponse(),
          type: 'getRuns',
          obj: {
            threadId: response.thread_id,
            runId: response.id,
            status: response.status,
            msg: response.last_error,
          },
        }
      }
      if (response.status !== 'completed') {
        return {
          status: 'fail',
          msg: new AssistantErrorException().getResponse(),
          type: 'getRuns',
          obj: {
            threadId: response.thread_id,
            runId: response.id,
            status: response.status,
            msg: response.last_error,
          },
        }
      }

      const assistantMsg = await this.openai.beta.threads.messages.list(threadId, {
        order: 'desc',
        limit: 1,
      })
      const content = await this.handleContent(
        user,
        assistant,
        assistantMsg.data[0].content
      )

      // 记录assistant消息到数据库
      await this.saveConversationData({
        role: 'assistant',
        msgId: assistantMsg.data[0].id,
        runId: assistantMsg.data[0].run_id,
        user,
        assistantId: assistant.assistantId,
        threadId,
        content: content,
      })

      // 返回数据
      return {
        msgId: assistantMsg.data[0].id,
        runId: assistantMsg.data[0].run_id,
        threadId: assistantMsg.data[0].thread_id,
        role: assistantMsg.data[0].role,
        content: content,
      }
    } catch (error) {
      await this.clientErrorRepository.save({
        assistantId: assistant.assistantId,
        runId,
        threadId,
        errorMsg: error,
      })
      return {
        status: 'fail',
        msg: new AssistantErrorException().getResponse(),
        type: 'getRuns',
      }
    }
  }

  /**
   * 取消运行
   * @param threadId
   * @param runId
   */
  async cancelRun(threadId: string, runId: string) {
    return await this.openai.beta.threads.runs.cancel(threadId, runId)
  }

  /**
   *
   *
   * 创建消息并运行
   * queued 当Runs首次创建或者调用了retrieve获取状态后，就会变成queued等待运行。正常情况下，很快就会变成in_progress状态。
   * in_progress  说明run正在执行中，这时候可以调用run step来查看具体的执行过程
   * completed  执行完成，可以获取Assistant返回的消息了，也可以继续想Assistant提问了
   * requires_action  如果Assistant需要执行函数调用，就会转到这个状态，然后你必须按给定的参数调用指定的方法，之后run才可以继续运行
   * expired  当没有在expires_at之前提交函数调用输出，run将会过期。另外，如果在expires_at之前没获取输出，run也会变成expired状态
   * cancelling 当你调用client.beta.threads.runs.cancel(run_id=run.id, thread_id=thread.id)方法后，run就会变成cancelling，取消成功后就会变成cancelled状态
   * cancelled  Run已成功取消。
   * failed 运行失败，你可以通过查看Run中的last_error对象来查看失败的原因。
   *
   * 模型调用限制    "code": "rate_limit_exceeded"
   * @param user
   * @param assistantId
   * @param threadId
   * @param content
   * @returns
   */
  async sendMessages(user: any, assistantId: string, threadId: string, content: any) {
    try {
      const start = Date.now() // 总开始时间
      let runStart = null // 记录 run 开始的时间

      const [responseMsg, stream] = await Promise.all([
        // 创建消息
        await this.openai.beta.threads.messages.create(threadId, {
          role: 'user',
          content: content,
        }),

        // 启动 stream 订阅
        this.openai.beta.threads.runs
          .stream(threadId, { assistant_id: assistantId })
          .on('connect', () => {
            // console.log('开始连接')
            runStart = Date.now() // 记录 stream 连接的时间
            const connectTime = runStart - start // 计算从开始到 connect 的耗时
            console.log(`${threadId} connect------------> took ${connectTime} ms`) // 打印 connect 的耗时
            if (connectTime >= 13000) {
              // 超时过长则创建新的thread
              this.newUserThreadId(user)
            }
          })
          .on('event', (event) => {
            // console.log('event===>',event)
            if (event.event === 'thread.run.created') {
              this.eventEmitter.emit('openai.thread.message', {
                type: 'event',
                user,
                data: {
                  runId: event.data.id,
                },
              })
            }
            if (event.event === 'thread.run.failed') {
              let msg = new AssistantErrorException().getResponse()
              // 'server_error' | 'rate_limit_exceeded' | 'invalid_prompt'
              if (event.data.last_error.code === 'rate_limit_exceeded') {
                msg = new AssistantRateLimitException().getResponse()
              }
              this.eventEmitter.emit('openai.thread.message', {
                type: 'fail',
                user,
                data: {
                  last_error: event.data.last_error,
                  failed_at: event.data.failed_at,
                },
                msg,
              })
            }
          })
          .on('textDelta', (delta, snapshot) => {
            this.eventEmitter.emit('openai.thread.message', {
              type: 'textDelta',
              user,
              data: {
                snapshot,
                delta,
              },
            })
          })
          .on('messageDelta', () => {
            // this.websocketGateway.sendAssistantThreadMessage('messageDelta', user, {
            //   snapshot,
            //   delta,
            // })
          }),
        // .on('run', () => {}),
      ])

      // 记录user消息到数据库
      await this.saveConversationData({
        role: 'user',
        msgId: responseMsg.id,
        runId: null,
        user,
        assistantId,
        threadId,
        content: responseMsg.content,
      })
      const result = await stream.finalRun()
      const totalTime = Date.now() - start // 计算整个流程的耗时
      console.log('总耗时:', totalTime, 'ms')

      return result
    } catch (error) {
      // 报错 则创建新的thread
      await this.newUserThreadId(user)
      await this.clientErrorRepository.save({ assistantId, threadId, errorMsg: error })
      return error
    }
  }

  /**
   * 保存对话和消息到 MySQL 和 MongoDB
   * @param threadRuns
   * @param user
   * @param assistantId
   * @param threadId
   * @param content
   */
  async saveConversationData(data: {
    msgId: string
    runId: string
    user: any
    assistantId: string
    threadId: string
    content: any
    role: string
  }) {
    const { user, runId, msgId, assistantId, threadId, content, role } = data
    const { userId, classId, gradeId, schoolId, isTeacher } = user

    // 记录对话次数到 MySQL
    await this.assistantThreadMessageRunsRepository.save({
      msgId,
      runId,
      userClassId: classId,
      userType: isTeacher ? EUserType.TEACHER : EUserType.STUDENT,
      assistantId,
      threadId,
      userId,
      schoolId,
      gradeId,
      msgType: role,
    })

    // 保存消息到 MongoDB
    await this.saveMessage({
      userId,
      assistantId,
      threadId,
      msgId,
      runId,
      content,
      role,
      object: 'thread.message',
    })
  }

  /**
   * 保存对话消息到mongodb
   * @param conversationData
   * @returns
   */
  async saveMessage(data: Partial<AssistantMessages>): Promise<AssistantMessages> {
    const createdConversation = new this.assistantMessagesModel(data)
    return await createdConversation.save()
  }

  /**
   * 更新对话消息到mongodb
   * @param updateData
   * @returns
   */
  async updateMessage(
    runId: string,
    updateData: Partial<AssistantMessages>
  ): Promise<AssistantMessages | null> {
    return this.assistantMessagesModel.findOneAndUpdate({ runId: runId }, updateData, {
      new: true,
    })
  }

  /**
   * 处理assistant回复的消息文本 显示对应的书名引用
   * @param contents
   * @returns
   */
  private async handleContent(
    user: any,
    assistant: Assistant,
    contents: OpenAI.Beta.Threads.Messages.MessageContent[]
  ) {
    for (const item of contents) {
      if (item.type === 'text' && item.text.annotations) {
        for (const annotation of item.text.annotations) {
          if (annotation.type === 'file_citation') {
            const fileId = annotation.file_citation.file_id
            let fileInfo = await this.getFileCache(fileId)
            if (!fileInfo) {
              const file = await this.getFile(fileId)
              fileInfo = {
                fileName: file?.filename.split('.')[0] || '引用書籍名稱缺失',
                bookId: '',
                version: '',
                assistantPreferredVersion: '',
              }
              await this.redisService.hset(
                ASSISTANTS_BOOKS_KEY,
                fileId,
                JSON.stringify(fileInfo)
              )
            }

            (annotation as any).book_name =
              fileInfo?.fileName?.[user.locale] ||
              fileInfo?.fileName?.['zh_HK'] ||
              fileInfo?.fileName ||
              '引用書籍名稱缺失'
            ;(annotation as any).book_id = fileInfo.bookId || ''
            ;(annotation as any).version = fileInfo.version || ''
            ;(annotation as any).assistantPreferredVersion =
              assistant.preferredVersion || ''
            ;(annotation as any).schoolVersion = user.version || ''
            let isJump = false
            const bookVersion = fileInfo.version
            let jumpVersion: 'SUBSCRIPTION' | 'REFERENCE' = null
            const aiPackageVersion = assistant.preferredVersion
            const schoolVersion = user.version
            let schoolReferenceBook = []
            if (fileInfo.bookId) {
              schoolReferenceBook = await this.referenceBookRepository.query(
                ` select
                    book_id as id
                  from
                    reference_books
                  where
                    book_id = ${fileInfo.bookId}
                  and 
                    school_id = ${user.schoolId}
                  `
              )
            }

            // a.AI套餐指定版本 为 REFERENCE
            if (aiPackageVersion === 'REFERENCE') {
              // 1. 如果书籍是 SUBSCRIPTION_REFERENCE 再看学校版本
              if (bookVersion === 'SUBSCRIPTION,REFERENCE') {
                // 1.1 如果学校版本是 SUBSCRIPTION_REFERENCE
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 1.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 1.1.2 如果当前学校参考馆没有这本书，则跳转 SUBSCRIPTION
                    isJump = true
                    jumpVersion = 'SUBSCRIPTION'
                  }
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 1.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'REFERENCE') {
                  // 1.3 如果学校版本是 REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 1.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 1.3.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                }
              } else if (bookVersion === 'SUBSCRIPTION') {
                // 2. 如果书籍是 SUBSCRIPTION, 再看学校版本
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  // 2.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 2.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'REFERENCE') {
                  // 2.3 如果学校版本是 REFERENCE，不跳转
                  isJump = false
                }
              } else if (bookVersion === 'REFERENCE') {
                // 3. 如果书籍是 REFERENCE, 再看学校版本
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  // 3.1 如果学校版本是 SUBSCRIPTION_REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 3.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 3.1.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 3.2 如果学校版本是 SUBSCRIPTION，不跳转
                  isJump = false
                } else if (schoolVersion === 'REFERENCE') {
                  // 3.3 如果学校版本是 REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 3.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 3.3.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                }
              }
            } else if (aiPackageVersion === 'SUBSCRIPTION') {
              // b.AI套餐指定版本 为 SUBSCRIPTION
              // 1. 如果书籍是 SUBSCRIPTION_REFERENCE 版本 再看学校版本
              if (bookVersion === 'SUBSCRIPTION,REFERENCE') {
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  // 1.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 1.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'REFERENCE') {
                  // 1.3 如果学校版本是 REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 1.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 1.3.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                }
              } else if (bookVersion === 'SUBSCRIPTION') {
                // 2. 如果书籍是 SUBSCRIPTION, 再看学校版本
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  // 2.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 2.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
                  isJump = true
                  jumpVersion = 'SUBSCRIPTION'
                } else if (schoolVersion === 'REFERENCE') {
                  // 2.3 如果学校版本是 REFERENCE，不跳转
                  isJump = false
                }
              } else if (bookVersion === 'REFERENCE') {
                // 3. 如果书籍是 REFERENCE, 再看学校版本
                if (schoolVersion === 'SUBSCRIPTION_REFERENCE') {
                  // 3.1 如果学校版本是 SUBSCRIPTION_REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 3.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 3.1.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                } else if (schoolVersion === 'SUBSCRIPTION') {
                  // 3.2 如果学校版本是 SUBSCRIPTION，不跳转
                  isJump = false
                } else if (schoolVersion === 'REFERENCE') {
                  // 3.3 如果学校版本是 REFERENCE
                  if (schoolReferenceBook && schoolReferenceBook.length > 0) {
                    // 3.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
                    isJump = true
                    jumpVersion = 'REFERENCE'
                  } else {
                    // 3.3.2 如果当前学校参考馆没有这本书，不跳转
                    isJump = false
                  }
                }
              }
            }
            (annotation as any).isJump = isJump
            ;(annotation as any).jumpVersion = jumpVersion

            // 有关引用书籍跳转逻辑，校本书籍不可点击跳转（即系统中不存在的书籍），非校本书籍可点击跳转（即系统中存在的书籍）
            // 先判断AI套餐指定版本，然后看书籍是否有该版本，最后在看学校是什么版本
            // a.AI套餐指定版本 为 REFERENCE
            //   1. 如果书籍是 SUBSCRIPTION_REFERENCE 再看学校版本
            //      1.1 如果学校版本是 SUBSCRIPTION_REFERENCE
            // 1.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            // 1.1.2 如果当前学校参考馆没有这本书，则跳转 SUBSCRIPTION
            //      1.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
            //      1.3 如果学校版本是 REFERENCE
            //          1.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //          1.3.2 如果当前学校参考馆没有这本书，不跳转

            //   2. 如果书籍是 SUBSCRIPTION, 再看学校版本
            //      2.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
            //      2.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
            //      2.3 如果学校版本是 REFERENCE，不跳转

            //  3. 如果书籍是 REFERENCE, 再看学校版本
            //      3.1 如果学校版本是 SUBSCRIPTION_REFERENCE
            //      3.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //      3.1.2 如果当前学校参考馆没有这本书，不跳转
            //      3.2 如果学校版本是 SUBSCRIPTION，不跳转
            //      3.3 如果学校版本是 REFERENCE
            //      3.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //      3.3.2 如果当前学校参考馆没有这本书，不跳转

            // b.AI套餐指定版本 为 SUBSCRIPTION
            //    1. 如果书籍是 SUBSCRIPTION_REFERENCE 版本 再看学校版本
            //      1.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
            //      1.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
            //      1.3 如果学校版本是 REFERENCE
            //          1.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //          1.3.2 如果当前学校参考馆没有这本书，不跳转

            //    2. 如果书籍是 SUBSCRIPTION, 再看学校版本
            //      2.1 如果学校版本是 SUBSCRIPTION_REFERENCE，则跳转到 SUBSCRIPTION 版本
            //      2.2 如果学校版本是 SUBSCRIPTION，则跳转到 SUBSCRIPTION 版本
            //      2.3 如果学校版本是 REFERENCE，不跳转

            //    3. 如果书籍是 REFERENCE, 再看学校版本
            //      3.1 如果学校版本是 SUBSCRIPTION_REFERENCE
            //          3.1.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //          3.1.2 如果当前学校参考馆没有这本书，不跳转
            //      3.2 如果学校版本是 SUBSCRIPTION，不跳转
            //      3.3 如果学校版本是 REFERENCE
            //          3.3.1 如果当前学校参考馆有这本书，则跳转 REFERENCE
            //          3.3.2 如果当前学校参考馆没有这本书，不跳转
          }
        }
      }
      return contents
    }
  }
}
