import { forwardRef, Inject, Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WsException,
} from '@nestjs/websockets'
import { Server, Socket } from 'socket.io'
import { AuthSchema, RedisService } from '@/common'
import { WsAuthGuard } from '@/common/guards/wsAuth.guard'
import { CancelThreadMessageDto } from '../../assistant/dto/assistant'
import { getSchoolRoom, getUserAssistantThreadRoom, getUserRoom } from '../../constants'
import { WsAssistantService } from './ws.assistant.service'

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
@Injectable()
export class WebsocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  // private logger: Logger = new Logger('AppGateway')
  private server: Server
  constructor(
    private readonly redis: RedisService,
    private readonly wsAuthGuard: WsAuthGuard,
    @Inject(forwardRef(() => WsAssistantService))
    private readonly assistantService: WsAssistantService,
  ) {}

  /**
   * 监听 'assistant-thread-runs' 事件并处理消息
   * @param client
   * @param payload
   */
  @SubscribeMessage('assistant-thread-runs')
  async handleAssistantThreadMessage(client: Socket, payload: any) {
    console.log(
      `Received message from user: ${client.id}, payload: ${JSON.stringify(payload)}`,
    )
    const user = client.data.user
    if (!user) {
      throw new WsException('Unauthorized')
    }
    if (!user.hasAssistant) {
      throw new WsException('Assistant not found')
    }
    user.locale = client.data.locale
    const res = await this.assistantService.threadRun(user, payload)
    if (res.status === 'fail') {
      await this.sendAssistantThreadMessage('fail', user, res, res.msg)
    } else {
      await this.sendAssistantThreadMessage('result', user, res)
    }
  }

  /**
   * 监听 'assistant-user-thread-cancel' 事件并处理取消请求
   * @param client
   * @param payload
   */
  @SubscribeMessage('assistant-thread-runs-cancel')
  async cancelAssistantThreadRuns(client: Socket, payload: CancelThreadMessageDto) {
    console.log(
      `Received message from user: ${client.id}, payload: ${JSON.stringify(payload)}`,
    )
    const user = client.data.user
    if (!user) {
      throw new WsException('Unauthorized')
    }
    const res = await this.assistantService.cancelThreadRun(user, payload)
    await this.sendAssistantThreadMessage('cancel', user, res)
  }

  @OnEvent('openai.thread.message')
  handleOpenAIMessage(payload: { type: string; user: any; data: any }) {
    this.sendAssistantThreadMessage(payload.type, payload.user, payload.data)
  }

  afterInit(server: Server) {
    this.server = server
  }

  /**
   * 处理 WebSocket 客户端连接
   * @param client
   */
  async handleConnection(client: Socket) {
    try {
      await this.wsAuthGuard.verify(client)
      const { userId, schoolId, authSchema, gradeId, classId } = client.data.user
      await client.join(getSchoolRoom(schoolId, gradeId, classId, authSchema))
      await client.join(getUserRoom(userId, AuthSchema.CLIENT))
      await client.join(getUserAssistantThreadRoom(userId, AuthSchema.CLIENT))
    } catch (error) {
      console.error('Unhandled error in WebSocket connection:', error)
      client.emit('error', { message: error.message }) // 向客户端发送错误消息
      client.disconnect(true) // 断开连接
    }
  }

  /**
   * 处理 WebSocket 客户端断开连接
   * @param client
   */
  async handleDisconnect(client: Socket) {
    try {
      await this.wsAuthGuard.verify(client)
      const { userId, schoolId, authSchema, gradeId, classId } = client.data.user
      await client.leave(getSchoolRoom(schoolId, gradeId, classId, authSchema))
      await client.leave(getUserAssistantThreadRoom(userId, authSchema))
    } catch (error) {
      console.log(error)
    }
  }

  /**
   * 发送通知到学校
   * @param schoolId
   * @param gradeId
   * @param classId
   * @param schema
   * @param payload
   */
  async sendNotification(
    schoolId: number,
    gradeId: number,
    classId: number,
    schema: AuthSchema,
    payload: any,
  ) {
    console.log({ schoolId, gradeId, classId, schema, payload })
    return this.server
      .to(getSchoolRoom(schoolId, gradeId, classId, schema))
      .emit('notifications', payload)
  }

  /**
   * 发送用户个人通知
   * @param userId
   * @param schema
   * @param payload
   */
  async sendNotifications(userId: number, schema: AuthSchema, payload: any) {
    return this.server.to(getUserRoom(userId, schema)).emit('notifications', payload)
  }

  /**
   * 发送消息到客户端 assistant
   * @param type
   * @param user
   * @param data
   * @param msg
   */
  async sendAssistantThreadMessage(type: string, user: any, data: any, msg?: any) {
    return this.server
      .to(getUserAssistantThreadRoom(user.userId, user.authSchema))
      .emit('assistant-user-thread', {
        type: type,
        data: data,
        msg: msg,
      })
  }

  /**
   * 获取活跃用户列表
   */
  async listActiveUsers() {
    return this.redis.instance.smembers('websocket.active.users')
  }
}
