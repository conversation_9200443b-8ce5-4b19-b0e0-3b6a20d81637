import { forwardRef, Inject, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { RedisService } from '@/common'
import { Assistant, AssistantThread } from '@/entities'
import { CancelThreadMessageDto } from '@/modules/assistant/dto/assistant'
import {
  AssistantErrorException,
  UserThreadNotExistException,
} from '@/modules/assistant/exception'
import { getUserAssistantThreadKey } from '@/modules/constants'
import { OpenAIService } from '@/modules/websocket/services/openai.service'

@Injectable()
export class WsAssistantService {
  constructor(
    @InjectRepository(AssistantThread)
    private readonly assistantThreadRepository: Repository<AssistantThread>,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => OpenAIService))
    private readonly openAIService: OpenAIService,
  ) {}

  /**
   * 根据用户id获取线程对话
   * @param userId
   * @returns
   */
  async getUserThreadId(userId: number, schoolId: number) {
    // get redis
    const userThreadId = await this.redisService.hget(
      getUserAssistantThreadKey(schoolId),
      userId.toString(),
    )
    if (userThreadId) {
      return userThreadId
    }
    // 不存在查询db
    const userThread = await this.assistantThreadRepository.findOne({
      where: { userId },
      order: { createdAt: 'DESC' },
    })
    if (!userThread) {
      return ''
    }
    // 设置 Redis 数据
    await this.redisService.hset(
      getUserAssistantThreadKey(schoolId),
      userId.toString(),
      userThread.threadId.toString(),
    )
    return userThread.threadId
  }

  /**
   * 运行openai thread
   * @param user
   * @param data
   * @returns
   */
  async threadRun(user: any, content: string) {
    if (content.length >= 500) {
      return { status: 'fail', msg: new AssistantErrorException().getResponse() }
    }
    const assistants = await this.openAIService.getAssistant(user.assistantId)
    let userThreadId = await this.getUserThreadId(user.userId, user.schoolId)
    if (!userThreadId) {
      userThreadId = await this.openAIService.createUserThread(
        user,
        assistants.assistantId,
        assistants.vectorStoreId,
      )
    }
    return await this.sendMsg(user, assistants, userThreadId, content)
  }

  /**
   *
   * @param threadId
   * @param runId
   * @returns
   */
  async cancelThreadRun(user: any, data: CancelThreadMessageDto) {
    const userThreadId = await this.getUserThreadId(user.userId, user.schoolId)
    if (!userThreadId) {
      return { status: 'fail', msg: new UserThreadNotExistException().getResponse() }
    }
    try {
      const res = await this.openAIService.cancelRun(userThreadId, data.runId)
      console.log('res', res)
      return {
        runId: res.id,
        status: res.status,
        createdAt: res.created_at,
      }
    } catch (error) {
      return {
        status: 'fail',
        msg: new AssistantErrorException().getResponse(),
        type: 'cancelThreadRun',
      }
    }
  }

  /**
   * 发送消息并记录
   * @param assistantId
   * @param user
   * @param threadId
   * @param content
   * @returns
   */
  async sendMsg(user: any, assistant: Assistant, threadId: string, userContent: string) {
    // 发送消息阶段
    const threadRuns = await this.openAIService.sendMessages(
      user,
      assistant.assistantId,
      threadId,
      userContent,
    )

    // 查询一下最后的结果
    return await this.openAIService.getRuns(user, assistant, threadId, threadRuns.id)
  }
}
