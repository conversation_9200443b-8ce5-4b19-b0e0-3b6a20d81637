import { Global, Module } from '@nestjs/common'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { MongooseModule } from '@nestjs/mongoose'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AuthGuard, WsAuthGuard } from '@/common/guards'
import {
  Assistant,
  AssistantAdminErrorResponse,
  AssistantClientErrorResponse,
  AssistantThread,
  AssistantThreadMessageRuns,
  ReferenceBook,
} from '@/entities'
import {
  AssistantMessages,
  AssistantMessagesSchema,
} from '@/entities/assistantMessage.entity'
import { OpenAIService } from '@/modules/websocket/services/openai.service'
import { WebsocketGateway } from '@/modules/websocket/services/websocket.gateway'
import { WsAssistantService } from './services/ws.assistant.service'

@Global()
@Module({
  exports: [WebsocketGateway, OpenAIService],
  imports: [
    EventEmitterModule.forRoot(),
    TypeOrmModule.forFeature([
      Assistant,
      AssistantThread,
      AssistantThreadMessageRuns,
      AssistantClientErrorResponse,
      AssistantAdminErrorResponse,
      ReferenceBook,
    ]),
    MongooseModule.forFeature([
      { name: AssistantMessages.name, schema: AssistantMessagesSchema },
    ]),
  ],
  providers: [
    WebsocketGateway,
    OpenAIService,
    WsAssistantService,
    WsAuthGuard,
    AuthGuard,
  ],
})
export class WebsocketModule {}
