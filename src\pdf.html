<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>PDF.js Spreads Example</title>
    <script src="https://images-dev.enrichculturegroup.com/public/images/pdf.js"></script>
  </head>
  <body>
    <div id="title"></div>
    <div id="viewer" class="spreads"></div>
    <a id="prev" href="#prev" class="arrow">‹</a>
    <a id="next" href="#next" class="arrow">›</a>
    <div id="controls"><input id="current-percent" size="3" value="0" />%</div>
    <div id="result"></div>
    <script type="module">
      var pdfjsLib = window['pdfjs-dist/build/pdf']
      pdfjsLib.GlobalWorkerOptions.workerSrc =
        'https://images-dev.enrichculturegroup.com/public/images/pdf.worker.js'
      // Your loadingTask
      // var data =
      // var loadingTask = pdfjsLib.getDocument(new Uint8Array(data))
      var loadingTask = pdfjsLib.getDocument(
        'https://images-develop.trusive.hk/public/books/orignal/1BOOK_15a80c535c0941ca95a413e3bafb712e.pdf',
      )

      loadingTask.promise.then(
        function (pdf) {
          localStorage.setItem('load:finish', JSON.stringify({ loadFinished: true }))
          function getPdfOutline(outline) {
            return outline.map((item) => ({
              name: item.title,
              children: item.items.length ? getPdfOutline(item.items) : undefined,
            }))
          }
          // Get the tree outline
          pdf.getOutline().then(
            async function (outline) {
              localStorage.setItem(
                'outline:finish',
                JSON.stringify({ outlineFinished: true }),
              )
              const chapters = getPdfOutline(outline)
              console.log({ chapters })
              localStorage.setItem(
                'book:chapter',
                JSON.stringify({ already: true, chapters }),
              )
            },
            function (err) {
              localStorage.setItem('outline:error', JSON.stringify({ outline: reason }))
            },
          )
        },
        function (reason) {
          // PDF loading error
          localStorage.setItem('load:error', JSON.stringify({ loading: reason }))
          console.error(reason)
        },
      )
    </script>
  </body>
</html>
