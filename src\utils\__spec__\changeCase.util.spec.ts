import { camelCase, snakeCase } from 'change-case'
import { changeCaseObject } from '../changeCase.util'

describe('snakeCase', () => {
  it('it should return snake keys with value', () => {
    expect(changeCaseObject({ expMonth: 9, expYear: 2021 }, snakeCase)).toMatchObject({
      exp_month: 9,
      exp_year: 2021,
    })
  })

  it('should work with array, nested object', () => {
    const camelKey = {
      expMonth: 9,
      iBIs: 'god',
      goods: [
        {
          color: ['red', 'green', 'yellow'],
          price: undefined,
          adminUser: {
            firstName: 'Sam',
            age: 9,
          },
        },
      ],
    }

    const snakeKey = {
      exp_month: 9,
      i_b_is: 'god',
      goods: [
        {
          color: ['red', 'green', 'yellow'],
          price: undefined,
          admin_user: {
            first_name: 'Sam',
            age: 9,
          },
        },
      ],
    }

    expect(changeCaseObject(camel<PERSON>ey, snakeCase)).toMatchObject(snake<PERSON><PERSON>)

    expect(changeCaseObject(snakeKey, camelCase)).toMatchObject(camel<PERSON>ey)
  })
})
