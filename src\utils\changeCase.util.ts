import R from 'ramda'

function isObject(obj: any): boolean {
  return !R.isNil(obj) && typeof obj === 'object' && !Array.isArray(obj)
}

export const changeCaseObject = (obj, fn) => {
  if (!isObject(obj)) {
    return obj
  }

  return R.mergeAll(
    Object.keys(obj).map((key) => {
      if (Array.isArray(obj[key])) {
        return { [`${fn(key)}`]: obj[key].map((item) => changeCaseObject(item, fn)) }
      }
      if (isObject(obj[key])) {
        return { [`${fn(key)}`]: changeCaseObject(obj[key], fn) }
      }
      return { [`${fn(key)}`]: obj[key] }
    }),
  )
}
