import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

/**
 * GET 请求
 *
 * @param url 请求地址
 * @param config 请求配置
 * @returns 响应数据
 */
export const getRequest = async <T = any>(
  url: string,
  config?: AxiosRequestConfig,
): Promise<T> => {
  const axiosInstance: AxiosInstance = axios.create({
    timeout: 10000, // 设置超时时间为 10 秒
  })

  try {
    const response: AxiosResponse<T> = await axiosInstance.get(url, config)
    return response.data
  } catch (error) {
    throw new Error(`GET request failed: ${url}`)
  }
}

/**
 * POST 请求
 *
 * @param url 请求地址
 * @param data 请求体
 * @param config 请求配置
 * @returns 响应数据
 */
export const postRequest = async <T = any>(
  url: string,
  data: any,
  config?: AxiosRequestConfig,
): Promise<T> => {
  const axiosInstance: AxiosInstance = axios.create({
    timeout: 10000, // 设置超时时间为 10 秒
  })

  try {
    const response: AxiosResponse<T> = await axiosInstance.post(url, data, config)
    return response.data
  } catch (error) {
    throw new Error(`POST request failed: ${url}`)
  }
}
