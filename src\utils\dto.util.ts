import { ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import { IsNumber, IsOptional } from 'class-validator'

export class SelectDto {
  @ApiPropertyOptional({ description: '选择的id' })
  @IsOptional()
  @Type(() => Number)
  @Transform((params) => [].concat(params.value))
  @IsNumber({ allowNaN: false }, { each: true })
  select?: number[]

  @IsOptional()
  @Type(() => Number)
  @Transform((params) => [].concat(params.value))
  @IsNumber({ allowNaN: false }, { each: true })
  @ApiPropertyOptional({ description: '排除的id' })
  exclude?: number[]
}
