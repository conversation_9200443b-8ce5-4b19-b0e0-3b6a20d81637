import { Cell, CellHyperlinkValue, ValueType } from 'exceljs'
import moment from 'moment'
import R from 'ramda'
import { ELocaleType } from '@/common'
import { countries, ESchoolStatus, ESchoolVersion } from '@/enums'
import { LANG_NO, LANG_YES } from '../modules/account/constant'

export const schoolExcelData = (school, local: ELocaleType) => ({
  name: school.name?.[local],
  address: school.address?.[local] ?? '',
  description: school.description?.[local] ?? '',
  principalName: school.principalName ?? '',
  principalNo: school.principalNo ?? '',
  principalEmail: school.principalEmail ?? '',
  joinedAt: moment(new Date(school.joinedAt * 1000)).format('YYYY/MM/DD') ?? '',
  studentEmailSuffix: school.studentEmailSuffix ?? '',
  teacherEmailSuffix: school.teacherEmailSuffix ?? '',
  region: school.region ? countries[school.region]?.[local] : '',
})

export const schoolExcelDataV2 = (school, local: ELocaleType) => ({
  ...schoolExcelData(school, local),
  hasSubVer: school.version.includes(ESchoolVersion.SUBSCRIPTION) ? '訂閱版' : '',
  hasRefVer: school.version.includes(ESchoolVersion.REFERENCE) ? '參考館' : '',
  hasSrVer: school.hasScienceRoom ? '科學活動室' : '',
  hasAIVer: school.hasAssistant ? '文心智友' : '',
  disable: school.status == ESchoolStatus.ACTIVE ? LANG_NO[local] : LANG_YES[local],
})

export const publisherExcelData = (publisher, local: ELocaleType) => ({
  name: publisher.name[local],
  publisherGroupName: publisher.publisherGroupName?.[local] ?? '',
  description: publisher.description?.[local] ?? '',
  contactUserName: publisher.contactUserName ?? '',
  email: publisher.email ?? '',
  address: publisher.address ?? '',
})

export const regionExcelData = (data = []) => {
  const excelData = {}
  for (const item of data) {
    excelData[item.region] = convertTime(item.totalReadingTime)
    excelData[`${item.region}count`] = item.totalUser
  }
  return excelData
}

export const regionSpecification = (
  regions: string[],
  local: ELocaleType = ELocaleType.ZH_HK,
) => {
  const specification = []
  for (const keyName of regions) {
    specification.push({
      keyName,
      displayName: `${countries[keyName][local]}閱讀時數`,
    })
  }
  specification.push({
    keyName: 'readingTime',
    displayName:
      local === ELocaleType.ZH_HK
        ? '總閱讀時長 (所有地區總和)'
        : 'Total reading time (sum of all regions)',
  })
  return specification
}

export const regionSchoolSpecification = (
  regions: string[],
  isAdmin: boolean,
  local: ELocaleType = ELocaleType.ZH_HK,
) => {
  const specification = []
  for (const keyName of regions) {
    specification.push({
      keyName,
      displayName: `${countries[keyName][local]}閱讀時數`,
    })
  }
  specification.push({
    keyName: 'readingTime',
    displayName:
      local === ELocaleType.ZH_HK
        ? '校內總閱讀時長'
        : 'Total reading time (sum of all regions)',
  })
  if (isAdmin)
    specification.push({
      keyName: 'readingTimes',
      displayName:
        local === ELocaleType.ZH_HK
          ? '校內總閱讀時長 (秒)'
          : 'Total reading time (seconds)',
    })
  return specification
}

export const regionLeftTimeSpecification = (
  regions: string[],
  local: ELocaleType = ELocaleType.ZH_HK,
) => {
  const specification = []
  for (const keyName of regions) {
    specification.push({
      keyName,
      displayName: `${countries[keyName][local]}剩餘閱讀時數`,
    })
  }
  specification.push({
    keyName: 'leftReadingTime',
    displayName:
      local === ELocaleType.ZH_HK
        ? '校內總剩餘閱讀時數 (所有地區總和)'
        : 'Total Remaining Reading Hours in School(sum of all regions)',
  })
  return specification
}

export const regionPublisherSpecification = (
  regions: string[],
  isAdmin: boolean,
  local: ELocaleType = ELocaleType.ZH_HK,
) => {
  const specification = []
  for (const keyName of regions) {
    specification.push({
      keyName,
      displayName: `${countries[keyName][local]}閱讀時數`,
    })
    if (isAdmin) {
      specification.push({
        keyName: `${keyName}count`,
        displayName: `${countries[keyName][local]}閱讀人數`,
      })
      specification.push({
        keyName: `isDelete`,
        displayName: `是否刪除`,
      })
    }
  }
  specification.push({
    keyName: 'readingTime',
    displayName:
      local === ELocaleType.ZH_HK
        ? '總閱讀時長 (所有地區總和)'
        : 'Total reading time (sum of all regions)',
  })
  if (isAdmin) {
    specification.push({
      keyName: 'userCount',
      displayName: local === ELocaleType.ZH_HK ? '總閱讀人數' : 'Total number of readers',
    })
  }
  return specification
}

export const authorExcelData = (author, local: ELocaleType) => ({
  name: author?.name?.[local] ?? '',
  description: author?.description?.[local] ?? '',
})

export const convertTime = (time: number, local?: ELocaleType) =>
  !R.isNil(time) ? `${Number(time) / (60 * 60)}` : ''

export const getCellValue = (cell: Cell) => {
  if (!cell) return null
  const { value, type } = cell
  if (type === ValueType.RichText) {
    const keys = Object.keys(value)
    for (const key of keys) {
      if (key === 'richText') {
        return value[key].reduce((x, y) => {
          return x + y.text
        }, '')
      }
    }
  }

  if (type === ValueType.Hyperlink) {
    return (value as CellHyperlinkValue).text
  }

  return value
}
