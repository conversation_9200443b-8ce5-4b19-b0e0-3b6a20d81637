import { createHash } from 'crypto'
import ffmpeg from 'fluent-ffmpeg'
import { writeFileSync } from 'fs'
import { tmpdir } from 'os'
import { join } from 'path'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'

ffmpeg.setFfmpegPath(ffmpegInstaller.path)

/**
 * 将音频 Buffer（如 webm）转换为 mp3 格式 Buffer
 * @param buffer 原始音频 Buffer（如 .webm）
 * @returns 转换后的 mp3 Buffer
 */
export async function convertBufferToMp3(
  buffer: Buffer,
  type = 'webm',
): Promise<{ mp3Path: string; fileName: string }> {
  const hash = getFileHash(buffer)
  const inputPath = join(tmpdir(), `${hash}.${type}`)
  const wavPath = join(tmpdir(), `${hash}.wav`)
  const outputPath = join(tmpdir(), `${hash}.m4a`)
  const fileName = `${hash}.m4a`

  writeFileSync(inputPath, buffer)

  return new Promise((resolve, reject) => {
    // Step 1: webm → wav
    ffmpeg(inputPath)
      .noVideo()
      .audioCodec('pcm_s16le') // WAV 格式
      .audioChannels(2)
      .audioFrequency(44100)
      .on('error', reject)
      .on('end', () => {
        // Step 2: wav → m4a
        ffmpeg(wavPath)
          .noVideo()
          .audioCodec('aac')
          .audioBitrate('128k')
          .audioChannels(2)
          .audioFrequency(44100)
          .outputOptions(['-movflags faststart'])
          .on('error', reject)
          .on('end', () => resolve({ mp3Path: outputPath, fileName }))
          .save(outputPath)
      })
      .save(wavPath)
    // ffmpeg(inputPath)
    //   .audioCodec('libmp3lame')
    //   .audioBitrate('128k')
    //   .outputOptions([
    //     '-write_xing 1', // 添加 seek 索引
    //     '-fflags +bitexact', // 保证兼容性
    //     '-movflags faststart', // 更快加载元数据（主要用于 MP4，但无害）
    //   ])
    //   .on('error', (err) => reject(err))
    //   .on('end', () => resolve({ mp3Path: outputPath, fileName }))
    //   .save(outputPath)
  })
}

export function getFileHash(buffer: Buffer): string {
  return createHash('sha256').update(buffer).digest('hex')
}
