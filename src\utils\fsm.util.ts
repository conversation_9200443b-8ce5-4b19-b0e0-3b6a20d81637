// Generic FSM state checker

import { Map, Set } from 'immutable'

export class FSM<S, E> {
  private _state: Set<S>
  private _event: Set<E>
  private _mapping: Map<S, Set<E>>

  constructor() {
    this._state = Set()
    this._event = Set()
    this._mapping = Map()
  }

  register(state: S, event: E) {
    if (event && state) {
      const events = this._mapping.get(state)

      if (events) {
        this._mapping = this._mapping.update(state, (events) => events.add(event))
      } else {
        this._mapping = this._mapping.set(state, Set().add(event) as Set<E>)
      }

      this._event = this._event.add(event)
      this._state = this._state.add(state)

      return this
    } else {
      throw new Error('empty state or event!')
    }
  }

  getAllState() {
    return this._state
  }

  getAllEvent() {
    return this._event
  }

  validate(event: E, state: S) {
    const events = this._mapping.get(state)
    return events ? events.has(event) : false
  }
}
