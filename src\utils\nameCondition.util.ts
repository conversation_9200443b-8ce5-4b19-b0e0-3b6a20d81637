import R from 'ramda'
import { localTypes } from '@/common'
import { LabelType } from '@/enums/label.enum'
import { MultiLanguage } from '@/interfaces'

export const nameCondition = (name: string, tableName?: string) => {
  const field = R.isNil(tableName) ? 'name' : `${tableName}.name`

  return {
    where: `(${localTypes
      .map((key) => `${field}->'$.${key}' =:${tableName}${key}name`)
      .join(' OR ')} COLLATE 'utf8mb4_unicode_ci' )`,
    parameter: localTypes.reduce(
      (obj, key) =>
        Object.assign(obj, { [`${tableName}${key}name`]: convertKeyword(name) }),
      {},
    ) as any,
  }
}

export const allNameCondition = (name: MultiLanguage, tableName?: string) => {
  const field = R.isNil(tableName) ? 'all_name' : `${tableName}.all_name`

  return `UPPER(${field}) = UPPER(CONCAT('\"${convertKeyword(
    String(name.zh_HK),
  )}\"', " ", '\"${convertKeyword(String(name.en_uk))}\"', " ", '\"${convertKeyword(
    String(name.zh_cn),
  )}\"'))`
}

export const multiNameCondition = (names: string[], tableName?: string) => {
  const field = R.isNil(tableName) ? 'all_name' : `${tableName}.all_name`

  const parameter = {}
  for (let i = 0; i < names.length; i++) {
    parameter[`${i}`] = `"${names[i]}" "${names[i]}"`
  }

  return {
    where: `UPPER(${field}) IN (${names
      .map((item, index) => `UPPER(:${index})`)
      .join(',')})`,
    parameter,
  }
}

export const multiLabelNameCondition = (
  names: string[],
  type: LabelType,
  tableName?: string,
) => {
  const field = R.isNil(tableName) ? 'name' : `${tableName}.name`

  const parameter = {}
  for (let i = 0; i < names.length; i++) {
    parameter[`${i}`] = `"${convertKeyword(String(names[i]))}" "${convertKeyword(
      String(names[i]),
    )}" ${type}`
  }

  return {
    where: `UPPER(all_name) IN (${names
      .map((item, index) => `UPPER(:${index})`)
      .join(',')})`,
    parameter,
  }
}

export const nameLikeCondition = (name: string, tableName?: string) => {
  const field = R.isNil(tableName) ? 'name' : `${tableName}.name`

  return {
    where: `(${localTypes
      .map((key) => `${field}->'$.${key}' LIKE :${tableName}${key}name`)
      .join(' OR ')} COLLATE 'utf8mb4_unicode_ci' )`,
    parameter: localTypes.reduce(
      (obj, key) =>
        Object.assign(obj, { [`${tableName}${key}name`]: `%${convertKeyword(name)}%` }),
      {},
    ) as any,
  }
}

export const nameLikeWhere = (name: string, tableName?: string) => {
  const field = R.isNil(tableName) ? 'name' : `${tableName}.name`
  return `(${localTypes
    .map((key) => `${field}->'$.${key}' LIKE '%${convertKeyword(name)}%'`)
    .join(' OR ')} COLLATE 'utf8mb4_unicode_ci' )`
}

export const detailLikeWhere = (name: string, tableName?: string) => {
  const field = R.isNil(tableName) ? 'description' : `${tableName}.description`
  return `(${localTypes
    .map((key) => `${field}->'$.${key}' LIKE '%${convertKeyword(name)}%'`)
    .join(' OR ')} COLLATE 'utf8mb4_unicode_ci' )`
}

export const nameWhere = (name: string) =>
  `(${localTypes
    .map((key) => `name->'$.${key}' = '${convertKeyword(name)}'`)
    .join(' OR ')} COLLATE 'utf8mb4_unicode_ci' )`

export const nameFilter = (item: any, name: string) =>
  localTypes.some((local) => item.name[`${local}`].toUpperCase() === name.toUpperCase())

export const convertKeyword = (keyword: string) => keyword.replace(/'/g, `\\'`)

export const getName = (name: string, nameEn: string, nameCn: string) => {
  const zh = name?.trim()
  const en = nameEn?.trim()
  const cn = nameCn?.trim()
  return {
    zh_HK: zh?.length ? zh : en.length ? en : cn,
    en_uk: en?.length ? en : zh.length ? zh : cn,
    zh_cn: cn?.length ? cn : zh.length ? zh : en,
  }
}
