import axios from 'axios'
import { load } from 'cheerio'
import fs from 'fs'
import * as pdf2html from 'pdf2html'

interface IBookmark {
  name: string
  children?: IBookmark[]
}

const downloadFile = async (remotePath: string, localPath: string) => {
  const result: any = await axios.get(remotePath, {
    responseType: 'arraybuffer',
    timeout: 10000,
  })
  fs.writeFileSync(localPath, Buffer.from(result.data, 'binary'))
}

const getBookmarks = async (options: {
  remotePath?: string
  fileBuffer?: Buffer
}): Promise<IBookmark[]> => {
  const { remotePath, fileBuffer } = options
  const localPath = `${Math.random().toString().substring(2, 12)}.pdf`
  try {
    if (remotePath) await downloadFile(remotePath, localPath)
    else if (fileBuffer)
      fs.writeFileSync(localPath, Buffer.from(fileBuffer as any, 'binary'))
    else return null

    const html = await pdf2html.html(localPath)
    const $ = load(html)

    let index = 0
    let indexBackup = index
    const result: any[] = []
    const ul = $('ul').toArray().shift()

    $(ul)
      .children()
      .toArray()
      .forEach((x) => {
        const children = $(x).children()
        if (children.length <= 0) {
          result.push({
            id: ++index,
            name: $(x).text(),
          })
        } else {
          indexBackup = index
          children.toArray().forEach((y) => {
            result.push({
              id: ++index,
              name: $(y).text(),
              parentId: indexBackup,
            })
          })
        }
      })
    return result
      .filter((x) => !x.parentId)
      .map((x) => ({
        name: x.name,
        children: result
          .filter((y) => y.parentId === x.id)
          .map((y) => ({ name: y.name })),
      }))
  } catch (error) {
    return []
  } finally {
    fs.unlinkSync(localPath)
  }
}

export { getBookmarks, IBookmark }
