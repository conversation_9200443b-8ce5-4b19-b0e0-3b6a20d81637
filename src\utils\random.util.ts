import crypto from 'crypto'
export class RandomUtil {
  static generateRandomNumber(min = 0, max = 10) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  static guid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0
      const v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }

  static generateId(prefix = '', len = 8): string {
    const ALPHA_NUM = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const rnd = crypto.randomBytes(len)
    const value = new Array(len)
    const charsLength = ALPHA_NUM.length

    for (let i = 0; i < len; i++) {
      value[i] = ALPHA_NUM[rnd[i] % charsLength]
    }

    return prefix + value.join('')
  }
}
