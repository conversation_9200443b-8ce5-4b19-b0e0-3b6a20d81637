import * as AWS from 'aws-sdk'
import { ManagedUpload } from 'aws-sdk/clients/s3'

export const getImageSuffix = (response: any) =>
  String(response.headers['content-type']).split('/').pop()

export const upload = async (
  connection: AWS.S3,
  options: {
    fileName: string
    fileBuffer: any
    path: string
    bucket: string
    contentType?: string
    acl?: string
  },
): Promise<string> => {
  const { fileName, fileBuffer, path, bucket, contentType, acl = 'private' } = options
  const params = {
    Bucket: bucket,
    Key: `${path}/${fileName}`,
    Body: fileBuffer instanceof Buffer ? fileBuffer : fileBuffer.buffer,
    ACL: acl,
  }
  if (contentType) Object.assign(params, { ContentType: contentType })
  const { Key } = await connection

    .upload(params as any, {}, (err: Error, data: ManagedUpload.SendData) => undefined)
    /* elist-disable */
    .promise()
  return Key
}

export const fetch = async (
  connection: AWS.S3,
  options: {
    fileFullName: string
    bucket: string
  },
) => {
  return new Promise((resolve, reject) => {
    connection.getObject(
      {
        Bucket: options.bucket,
        Key: options.fileFullName,
      },
      (error, data) => {
        if (error) {
          console.log(error, error.stack)
          return reject(error)
        } else {
          return resolve(data)
        }
      },
    )
  })
}

export const remove = async (
  connection: AWS.S3,
  options: {
    fileFullName: string
    bucket: string
  },
) => {
  return new Promise((resolve, reject) => {
    connection.deleteObject(
      {
        Bucket: options.bucket,
        Key: options.fileFullName,
      },
      (error, data) => {
        if (error) {
          console.log(error, error.stack)
          return reject(error)
        } else {
          return resolve(data)
        }
      },
    )
  })
}
