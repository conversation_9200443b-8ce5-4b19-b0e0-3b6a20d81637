import { registerAs } from '@nestjs/config'
import R from 'ramda'

export default registerAs('common', () => ({
  withCore: {
    pino: {
      pinoHttp: {
        level: 'fatal',
        transport: process.env.NODE_ENV === 'development' ? {
          target: 'pino-pretty',
          options: { 
            translateTime: true,
          },
        } : undefined,
        serializers: {
          req: (req) => R.evolve({ headers: R.omit(['authorization', 'cookie']) }, req),
        },
      },
    },
  },
  withRedis: {
    port: process.env.REDIS_PORT,
    host: process.env.REDIS_HOST,
    password: process.env.REDIS_PASSWORD,
  },
  withRabbitMQ: {
    asyncTaskMQ: {
      exchangeName: 'SjrcAsyncTaskExchange',
      queueName: 'SjrcAyncTaskQueue',
      deadLetterExchangeName: 'DeadSjrcAsyncTaskExchange',
      deadLetterQueueName: 'DeadSjrcAsyncTaskQueue',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
    topicMQ: {
      exchangeName: 'SjrcTopicExchange',
      queueName: 'SjrcTopicQueue',
      deadLetterExchangeName: 'DeadSjrcTopicExchange',
      deadLetterQueueName: 'DeadSjrcTopicQueue',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
    delayTaskMQ: {
      exchangeName: 'SjrcDelayTaskExchange',
      queueName: 'SjrcDelayTaskQueue',
      deadLetterExchangeName: 'DeadSjrcDelayTaskExchange',
      deadLetterQueueName: 'DeadSjrcDelayTaskQueue',
      url: process.env.RABBIT_MQ_URL,
      prefetch: 1,
    },
  },
  jwt: {
    secret: {
      // for web client user, x-auth-schema === CLIENT will use this secret
      client: process.env.CLIENT_SECRET || 'json_client_token_secret_key',
      // for admin user, x-auth-schema === ADMIN will use this secret
      admin: process.env.ADMIN_SECRET || 'json_admin_token_secret_key',
    },
  },
}))
