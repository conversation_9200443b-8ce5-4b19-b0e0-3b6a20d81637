import Joi from 'joi'

export default Joi.object({
  DB_HOST: Joi.string().hostname(),
  DB_PORT: Joi.number().integer(),
  DB_USERNAME: Joi.string(),
  DB_PASSWORD: Joi.string(),
  DB_NAME: Joi.string(),
  REDIS_PORT: Joi.number().integer(),
  REDIS_HOST: Joi.string().hostname(),
  RABBIT_MQ_URL: Joi.string().uri(),
  CLIENT_SECRET: Joi.string(),
  MERCHANT_SECRET: Joi.string(),
  ADMIN_SECRET: Joi.string(),
  API_SECRET: Joi.string(),
  INTERNAL_SECRET: Joi.string(),
})
