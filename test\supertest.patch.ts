import { AuthSchema } from '../src/common/enums'
import { strict as assert } from 'assert'

import jwt from 'jsonwebtoken'
import { SuperAgentRequest } from 'superagent'
import request from 'supertest'

const Test = request['Test']

assert.ifError(Test.prototype.authenticate)

declare module 'supertest' {
  interface Test extends SuperAgentRequest {
    authenticate(
      user: string | Buffer | Record<string, unknown>,
      schema?: AuthSchema,
    ): this
  }
}

const getJwtSecret = (authSchema: AuthSchema) => {
  let secret = process.env.CLIENT_SECRET || 'json_client_token_secret_key'

  switch (authSchema) {
    case AuthSchema.CLIENT:
      secret = process.env.CLIENT_SECRET || 'json_client_token_secret_key'
      break
    case AuthSchema.MERCHANT:
      secret = process.env.MERCHANT_SECRET || 'json_merchant_token_secret_key'
      break
    case AuthSchema.ADMIN:
      secret = process.env.ADMIN_SECRET || 'json_admin_token_secret_key'
      break
    case AuthSchema.API:
      secret = process.env.API_SECRET || 'json_api_token_secret_key'
      break
    case AuthSchema.INTERNAL:
      secret = process.env.INTERNAL_SECRET || 'json_internal_token_secret_key'
      break
  }
  return secret
}

Test.prototype.authenticate = function (
  user: string | Buffer | Record<string, unknown>,
  schema = AuthSchema.DEBUG,
) {
  const secret = getJwtSecret(schema)
  const token = jwt.sign(user, secret)
  return this.set('x-auth-schema', schema).set('authorization', `Bearer ${token}`)
}
