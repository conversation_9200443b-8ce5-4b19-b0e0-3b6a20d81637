# Client Assistants API 接口文档

本文档描述了 NestJS 应用中 `v1/client/assistants/*` 路由下的所有接口。

## 基础信息

- **Controller**: `AssistantClientController`
- **Base Path**: `v1/client/assistants`
- **API Tags**: `AI client`
- **认证方式**: `@ClientAuth()` - 客户端认证

## 接口列表

### 1. 获取用户对话线程ID

**接口路径**: `GET /v1/client/assistants/thread`

**功能描述**: 获取当前用户的OpenAI对话线程ID，用于维持对话会话的连续性。

**认证**: 需要客户端认证

**请求参数**: 无（从当前用户上下文获取）

**响应**:
- **成功**: 返回用户的线程ID字符串
- **无线程**: 返回空字符串 `""`

**业务逻辑**:
- 根据用户ID和学校ID获取对应的线程ID
- 如果用户没有对应的线程ID，返回空字符串

---

### 2. 获取话题列表

**接口路径**: `GET /v1/client/assistants/topics`

**功能描述**: 获取当前用户可用的AI助手话题列表，支持分页查询。

**认证**: 需要客户端认证

**请求参数**: `QuerySchoolTopicListDto`
- `page?`: number - 页码（可选）
- `pageSize?`: number - 每页大小（可选）
- `name?`: string - 话题名称搜索（可选）

**响应**: 分页的话题列表
- **类型**: `ApiPageResult<AssistantTopic>`
- **状态码**: 200

**业务逻辑**:
1. 自动设置用户相关参数：
   - `gradeId`: 用户年级ID
   - `schoolId`: 用户学校ID
   - `classId`: 用户班级ID
   - `status`: 固定为 `ONLINE`（在线状态）
   - `userType`: 根据用户是否为教师设置为 `TEACHER` 或 `STUDENT`

2. 查询优先级：
   - 首先查询学校话题
   - 如果学校话题存在，直接返回
   - 如果是学生且无学校话题，查询管理员话题（按年级过滤）

---

### 3. 获取对话消息列表

**接口路径**: `GET /v1/client/assistants/thread/messages`

**功能描述**: 获取用户对话线程中的消息列表，用于显示历史对话记录。

**认证**: 需要客户端认证

**请求参数**: `QueryThreadMessageDto`
- `startTime?`: number - 开始时间戳（可选）
- `endTime?`: number - 结束时间戳（可选）
- `schoolId?`: number - 学校ID（可选）
- `after?`: string - 分页游标（可选）
- `limit?`: number - 限制数量（可选，默认10）
- `order?`: any - 排序参数（可选）

**响应**: 消息列表数据

**业务逻辑**:
- 自动设置 `userId` 为当前用户ID
- 默认限制为10条消息
- 支持OpenAI的分页机制（最多100,000条消息）
- 当消息超过模型上下文窗口时，会智能截断不重要的消息

---

### 4. 创建会话统计

**接口路径**: `POST /v1/client/assistants/session/count`

**功能描述**: 记录用户打开AI助手对话的统计信息，用于数据分析。

**认证**: 需要客户端认证

**请求参数**: 无（从当前用户上下文获取）

**响应**: 空对象 `{}`

**业务逻辑**:
1. 获取用户的线程ID
2. 创建会话统计记录，包含：
   - `assistantId`: 助手ID
   - `threadId`: 线程ID（可能为null）
   - `userId`: 用户ID
   - `schoolId`: 学校ID
   - `gradeId`: 年级ID
   - `userClassId`: 班级ID
   - `userType`: 用户类型（教师/学生）

---

### 5. 创建话题使用统计

**接口路径**: `POST /v1/client/assistants/topic/count/:topicId`

**功能描述**: 记录用户使用特定话题的统计信息。

**认证**: 需要客户端认证

**路径参数**:
- `topicId`: number - 话题ID

**请求参数**: 无（从当前用户上下文获取）

**响应**: 空对象 `{}`

**业务逻辑**:
1. 获取用户的线程ID
2. 检查是否存在学校话题：
   - 如果存在学校话题，调用学校话题统计服务
   - 如果不存在，调用管理员话题统计服务
3. 统计记录包含：
   - `assistantId`: 助手ID
   - `userType`: 用户类型
   - `threadId`: 线程ID
   - `userId`: 用户ID
   - `schoolId`: 学校ID
   - `gradeId`: 年级ID
   - `topicId`: 话题ID
   - `userClassId`: 班级ID

## 数据模型

### AssistantTopic
话题实体，包含话题的基本信息如名称、内容、状态等。

### QuerySchoolTopicListDto
学校话题查询DTO，继承自分页查询基类，包含学校、年级、班级等过滤条件。

### QueryThreadMessageDto
线程消息查询DTO，包含时间范围、分页参数等。

## 枚举类型

### EAssistantTopicStatus
- `ONLINE`: 在线状态

### EUserType
- `TEACHER`: 教师
- `STUDENT`: 学生

## 注意事项

1. 所有接口都需要客户端认证
2. 用户相关参数会自动从认证上下文中获取
3. 话题查询有优先级：学校话题 > 管理员话题
4. 消息列表支持OpenAI的智能截断机制
5. 统计接口用于数据分析，不返回具体数据
