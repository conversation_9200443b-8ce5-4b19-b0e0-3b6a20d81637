const fs = require('fs');
const path = require('path');

// 递归获取所有 TypeScript 文件
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('dist')) {
      getAllTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复 findOne 调用
function fixFindOneCalls(content) {
  let fixed = content;
  
  // 1. 修复 findOne({ field: value }) 格式 - 不包含 where 的
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      // 如果是空对象，跳过
      if (conditions.trim() === '') {
        return match;
      }
      return `.findOne({ where: { ${conditions} } })`;
    }
  );
  
  // 2. 修复 findOne({ field: value }, { relations: [...] }) 格式
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*,\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions, options) => {
      // 如果 conditions 已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      return `.findOne({ where: { ${conditions} }, ${options} })`;
    }
  );
  
  // 3. 修复 findOne(id) 格式 - 单个参数且不是对象
  fixed = fixed.replace(
    /\.findOne\(\s*([^{][^,)]*)\s*\)/g,
    (match, param) => {
      // 跳过已经是对象的情况
      if (param.trim().startsWith('{')) {
        return match;
      }
      // 跳过函数调用
      if (param.includes('(')) {
        return match;
      }
      return `.findOne({ where: { id: ${param.trim()} } })`;
    }
  );
  
  // 4. 修复多行 findOne 调用
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*\n\s*([^}]+)\s*\n\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      return `.findOne({\n      where: {\n        ${conditions}\n      }\n    })`;
    }
  );
  
  return fixed;
}

// 修复 find 调用
function fixFindCalls(content) {
  let fixed = content;
  
  // 修复 find({ field: value }) 格式 - 不包含 where、take、skip、order 的
  fixed = fixed.replace(
    /\.find\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含这些关键字，跳过
      if (conditions.includes('where:') || 
          conditions.includes('take:') || 
          conditions.includes('skip:') || 
          conditions.includes('order:') ||
          conditions.includes('relations:') ||
          conditions.includes('select:')) {
        return match;
      }
      return `.find({ where: { ${conditions} } })`;
    }
  );
  
  return fixed;
}

// 修复 count 调用
function fixCountCalls(content) {
  let fixed = content;
  
  // 修复 count({ where: { field: value } }) 调用
  fixed = fixed.replace(
    /\.count\(\s*{\s*where:\s*{\s*([^}]+)\s*}\s*}\s*\)/g,
    '.countBy({ $1 })'
  );
  
  // 修复 count({ field: value }) 调用
  fixed = fixed.replace(
    /\.count\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      return `.countBy({ ${conditions} })`;
    }
  );
  
  return fixed;
}

// 主修复函数
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 应用修复
    content = fixFindOneCalls(content);
    content = fixFindCalls(content);
    content = fixCountCalls(content);
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    return;
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`📁 Found ${tsFiles.length} TypeScript files`);
  
  console.log('🔧 Starting final findOne fixes...');
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${tsFiles.length} total files.`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixFindOneCalls, fixFindCalls, fixCountCalls };
