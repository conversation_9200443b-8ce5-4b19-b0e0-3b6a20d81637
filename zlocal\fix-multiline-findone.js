const fs = require('fs');
const path = require('path');

// 递归获取所有 TypeScript 文件
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('dist')) {
      getAllTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复跨行的 findOne 调用
function fixMultilineFindOne(content) {
  let fixed = content;
  
  // 匹配跨行的 findOne 调用模式：
  // .findOne(
  //   { field: value },
  //   { relations: [...] }
  // )
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*([^,\n]+),\s*\n\s*{\s*([^}]+)\s*}\s*\n\s*\)/g,
    (match, conditions, options) => {
      // 清理条件参数
      const cleanConditions = conditions.trim();
      
      // 如果条件是对象形式 { field: value }，需要包装在 where 中
      if (cleanConditions.startsWith('{') && !cleanConditions.includes('where:')) {
        const conditionsContent = cleanConditions.slice(1, -1).trim();
        return `.findOne({\n      where: { ${conditionsContent} },\n      ${options}\n    })`;
      }
      
      // 如果条件不是对象（如 R.pick(...) 调用），也需要包装
      if (!cleanConditions.startsWith('{')) {
        return `.findOne({\n      where: ${cleanConditions},\n      ${options}\n    })`;
      }
      
      return match;
    }
  );
  
  // 匹配另一种模式：
  // .findOne(
  //   R.pick(['field'], options)
  // )
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*([^,\n)]+)\s*\n\s*\)/g,
    (match, conditions) => {
      const cleanConditions = conditions.trim();
      
      // 如果不是对象且不包含 where，包装它
      if (!cleanConditions.startsWith('{') && !cleanConditions.includes('where:')) {
        return `.findOne({\n      where: ${cleanConditions}\n    })`;
      }
      
      return match;
    }
  );
  
  // 处理单行但没有 where 的情况
  fixed = fixed.replace(
    /\.findOne\(\s*([^{][^,)]*)\s*\)/g,
    (match, param) => {
      const cleanParam = param.trim();
      
      // 跳过已经是对象的情况
      if (cleanParam.startsWith('{')) {
        return match;
      }
      
      // 跳过包含 where 的情况
      if (cleanParam.includes('where:')) {
        return match;
      }
      
      // 如果是简单的 id 参数
      if (/^\w+$/.test(cleanParam)) {
        return `.findOne({ where: { id: ${cleanParam} } })`;
      }
      
      // 其他情况（如函数调用）
      return `.findOne({ where: ${cleanParam} })`;
    }
  );
  
  return fixed;
}

// 修复跨行的 find 调用
function fixMultilineFind(content) {
  let fixed = content;
  
  // 类似的逻辑处理 find 调用
  fixed = fixed.replace(
    /\.find\(\s*\n\s*([^,\n]+),\s*\n\s*{\s*([^}]+)\s*}\s*\n\s*\)/g,
    (match, conditions, options) => {
      const cleanConditions = conditions.trim();
      
      if (cleanConditions.startsWith('{') && !cleanConditions.includes('where:')) {
        const conditionsContent = cleanConditions.slice(1, -1).trim();
        return `.find({\n      where: { ${conditionsContent} },\n      ${options}\n    })`;
      }
      
      if (!cleanConditions.startsWith('{')) {
        return `.find({\n      where: ${cleanConditions},\n      ${options}\n    })`;
      }
      
      return match;
    }
  );
  
  return fixed;
}

// 主修复函数
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 应用修复
    content = fixMultilineFindOne(content);
    content = fixMultilineFind(content);
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    return;
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`📁 Found ${tsFiles.length} TypeScript files`);
  
  console.log('🔧 Starting multiline findOne fixes...');
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${tsFiles.length} total files.`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixMultilineFindOne, fixMultilineFind };
