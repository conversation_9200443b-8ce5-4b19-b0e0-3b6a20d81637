const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取所有包含未修复 findOne 的文件
function getFilesWithUnfixedFindOne() {
  try {
    const result = execSync('grep -r "\\.findOne(" src/ --include="*.ts" | grep -v "where:" | cut -d: -f1 | sort | uniq', 
      { encoding: 'utf8', cwd: __dirname });
    return result.trim().split('\n').filter(f => f);
  } catch (error) {
    console.log('No files found or error occurred');
    return [];
  }
}

// 修复单个文件中的 findOne 调用
function fixFileContent(content) {
  let fixed = content;
  
  // 模式1: .findOne(\n  { field: value },\n  { options }\n)
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*{\s*([^}]+)\s*},\s*\n\s*{\s*([^}]+)\s*}\s*\n\s*\)/g,
    (match, conditions, options) => {
      return `.findOne({\n      where: { ${conditions.trim()} },\n      ${options.trim()}\n    })`;
    }
  );
  
  // 模式2: .findOne(\n  R.pick(...),\n  { options }\n)
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*([^,\n]+),\s*\n\s*{\s*([^}]+)\s*}\s*\n\s*\)/g,
    (match, conditions, options) => {
      const cleanConditions = conditions.trim();
      return `.findOne({\n      where: ${cleanConditions},\n      ${options.trim()}\n    })`;
    }
  );
  
  // 模式3: .findOne(\n  { field: value }\n)
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*{\s*([^}]+)\s*}\s*\n\s*\)/g,
    (match, conditions) => {
      return `.findOne({\n      where: { ${conditions.trim()} }\n    })`;
    }
  );
  
  // 模式4: .findOne(\n  R.pick(...)\n)
  fixed = fixed.replace(
    /\.findOne\(\s*\n\s*([^,\n)]+)\s*\n\s*\)/g,
    (match, conditions) => {
      const cleanConditions = conditions.trim();
      if (!cleanConditions.startsWith('{')) {
        return `.findOne({\n      where: ${cleanConditions}\n    })`;
      }
      return match;
    }
  );
  
  // 模式5: .findOne({ field: value }) - 单行，不包含 where
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      if (conditions.includes('where:')) {
        return match;
      }
      return `.findOne({ where: { ${conditions.trim()} } })`;
    }
  );
  
  // 模式6: .findOne(R.pick(...)) - 单行函数调用
  fixed = fixed.replace(
    /\.findOne\(\s*([^{][^,)]*)\s*\)/g,
    (match, param) => {
      const cleanParam = param.trim();
      
      // 跳过已经是对象的情况
      if (cleanParam.startsWith('{')) {
        return match;
      }
      
      // 跳过包含 where 的情况
      if (cleanParam.includes('where:')) {
        return match;
      }
      
      // 如果是简单的变量名（可能是 id）
      if (/^\w+$/.test(cleanParam)) {
        return `.findOne({ where: { id: ${cleanParam} } })`;
      }
      
      // 其他情况（如函数调用）
      return `.findOne({ where: ${cleanParam} })`;
    }
  );
  
  return fixed;
}

// 修复单个文件
function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixFileContent(content);
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🔍 Finding files with unfixed findOne calls...');
  const files = getFilesWithUnfixedFindOne();
  
  if (files.length === 0) {
    console.log('✨ No files found with unfixed findOne calls!');
    return;
  }
  
  console.log(`📁 Found ${files.length} files with unfixed findOne calls`);
  console.log('🔧 Starting fixes...');
  
  let fixedCount = 0;
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${files.length} total files.`);
  
  // 检查剩余的未修复调用
  console.log('\n🔍 Checking remaining unfixed calls...');
  const remainingFiles = getFilesWithUnfixedFindOne();
  console.log(`📊 Remaining unfixed files: ${remainingFiles.length}`);
  
  if (remainingFiles.length > 0) {
    console.log('📋 Files still needing manual review:');
    remainingFiles.slice(0, 10).forEach(file => console.log(`   - ${file}`));
    if (remainingFiles.length > 10) {
      console.log(`   ... and ${remainingFiles.length - 10} more`);
    }
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixFileContent };
