const fs = require('fs');
const path = require('path');

// 递归获取所有 TypeScript 文件
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('dist')) {
      getAllTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复特定的错误模式
function fixSpecificErrors(content) {
  let fixed = content;
  
  // 1. 修复 findOne({ id: number }) 错误
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*id:\s*([^}]+)\s*}\s*\)/g,
    '.findOne({ where: { id: $1 } })'
  );
  
  // 2. 修复 findOne({ field: value }) 错误
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^:]+):\s*([^}]+)\s*}\s*\)/g,
    (match, field, value) => {
      // 如果已经包含 where，跳过
      if (field.includes('where')) {
        return match;
      }
      return `.findOne({ where: { ${field}: ${value} } })`;
    }
  );
  
  // 3. 修复 find({ field: value }) 错误
  fixed = fixed.replace(
    /\.find\(\s*{\s*([^:]+):\s*([^}]+)\s*}\s*\)/g,
    (match, field, value) => {
      // 如果已经包含 where、take、skip 等，跳过
      if (field.includes('where') || field.includes('take') || field.includes('skip') || field.includes('order')) {
        return match;
      }
      return `.find({ where: { ${field}: ${value} } })`;
    }
  );
  
  // 4. 修复 count({ where: { field: value } }) 错误
  fixed = fixed.replace(
    /\.count\(\s*{\s*where:\s*{\s*([^}]+)\s*}\s*}\s*\)/g,
    '.countBy({ $1 })'
  );
  
  // 5. 修复 findByIds 调用（已被移除）
  fixed = fixed.replace(
    /\.findByIds\(\s*([^,]+)\s*,?\s*([^)]*)\s*\)/g,
    (match, ids, options) => {
      if (options && options.trim()) {
        return `.find({ where: { id: In(${ids}) }, ${options} })`;
      } else {
        return `.find({ where: { id: In(${ids}) } })`;
      }
    }
  );
  
  // 6. 修复 Between 类型错误
  fixed = fixed.replace(
    /createdAt:\s*Between\(\s*\[\s*([^,]+),\s*([^\]]+)\s*\]\s*\)/g,
    'createdAt: Between(new Date($1), new Date($2))'
  );
  
  // 7. 修复字符串 where 条件
  fixed = fixed.replace(
    /where:\s*`([^`]+)`/g,
    (match, condition) => {
      // 简单的字符串条件转换为 createQueryBuilder
      return `// TODO: Convert to createQueryBuilder\n      where: \`${condition}\``;
    }
  );
  
  // 8. 修复 allNameCondition 字符串条件
  fixed = fixed.replace(
    /where:\s*allNameCondition\(([^)]+)\)/g,
    '// TODO: Convert allNameCondition to proper where clause\n      where: allNameCondition($1)'
  );
  
  return fixed;
}

// 修复导入问题
function fixImports(content) {
  let fixed = content;
  
  // 修复 CACHE_MANAGER 导入
  if (fixed.includes('CACHE_MANAGER') && !fixed.includes("from '@nestjs/cache-manager'")) {
    // 从 @nestjs/common 中移除 CACHE_MANAGER
    fixed = fixed.replace(
      /import\s*{\s*([^}]*),?\s*CACHE_MANAGER\s*,?\s*([^}]*)\s*}\s*from\s*'@nestjs\/common'/g,
      (match, before, after) => {
        const imports = [before, after].filter(Boolean).join(', ').replace(/,\s*,/g, ',').replace(/^,|,$/g, '').trim();
        let result = '';
        if (imports) {
          result += `import { ${imports} } from '@nestjs/common'\n`;
        }
        result += `import { CACHE_MANAGER } from '@nestjs/cache-manager'`;
        return result;
      }
    );
  }
  
  // 修复 CacheModule 导入
  if (fixed.includes('CacheModule') && !fixed.includes("from '@nestjs/cache-manager'")) {
    fixed = fixed.replace(
      /import\s*{\s*([^}]*),?\s*CacheModule\s*,?\s*([^}]*)\s*}\s*from\s*'@nestjs\/common'/g,
      (match, before, after) => {
        const imports = [before, after].filter(Boolean).join(', ').replace(/,\s*,/g, ',').replace(/^,|,$/g, '').trim();
        let result = '';
        if (imports) {
          result += `import { ${imports} } from '@nestjs/common'\n`;
        }
        result += `import { CacheModule } from '@nestjs/cache-manager'`;
        return result;
      }
    );
  }
  
  return fixed;
}

// 修复构造函数中的 DataSource 注入
function fixDataSourceInjection(content) {
  let fixed = content;
  
  // 如果使用了 this.dataSource 但没有注入
  if (fixed.includes('this.dataSource') && !fixed.includes('dataSource: DataSource')) {
    // 查找构造函数
    const constructorRegex = /constructor\s*\(\s*([^)]*)\s*\)\s*{\s*}/;
    const match = fixed.match(constructorRegex);
    
    if (match) {
      const params = match[1].trim();
      let newParams;
      
      if (params) {
        newParams = `${params},\n    private readonly dataSource: DataSource`;
      } else {
        newParams = 'private readonly dataSource: DataSource';
      }
      
      fixed = fixed.replace(match[0], `constructor(\n    ${newParams},\n  ) {}`);
      
      // 确保导入 DataSource
      if (!fixed.includes('DataSource')) {
        const typeormImport = fixed.match(/import\s*{([^}]+)}\s*from\s*['"]typeorm['"]/);
        if (typeormImport) {
          const imports = typeormImport[1];
          if (!imports.includes('DataSource')) {
            fixed = fixed.replace(typeormImport[0], `import { ${imports}, DataSource } from 'typeorm'`);
          }
        } else {
          // 添加新的导入
          const firstImport = fixed.match(/^import.*$/m);
          if (firstImport) {
            const insertPos = fixed.indexOf(firstImport[0]) + firstImport[0].length;
            fixed = fixed.slice(0, insertPos) + '\nimport { DataSource } from \'typeorm\'' + fixed.slice(insertPos);
          }
        }
      }
    }
  }
  
  return fixed;
}

// 主修复函数
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 应用修复
    content = fixSpecificErrors(content);
    content = fixImports(content);
    content = fixDataSourceInjection(content);
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    return;
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`📁 Found ${tsFiles.length} TypeScript files`);
  
  console.log('🔧 Starting specific error fixes...');
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${tsFiles.length} total files.`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixSpecificErrors };
