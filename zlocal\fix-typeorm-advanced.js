const fs = require('fs');
const path = require('path');

// 递归获取所有 TypeScript 文件
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('dist')) {
      getAllTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复复杂的 findOne 调用
function fixComplexFindOneCalls(content) {
  let fixed = content;
  
  // 修复 findOne(id) 调用
  fixed = fixed.replace(
    /\.findOne\(\s*(\w+)\s*\)/g,
    (match, id) => {
      // 如果是数字或变量，转换为 where 格式
      if (!/^{/.test(id)) {
        return `.findOne({ where: { id: ${id} } })`;
      }
      return match;
    }
  );
  
  // 修复 findOne({ id }, { relations: [...] }) 格式
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*,\s*{\s*relations:\s*\[([^\]]+)\]([^}]*)\s*}\s*\)/g,
    (match, conditions, relations, otherOptions) => {
      return `.findOne({ where: { ${conditions} }, relations: [${relations}]${otherOptions ? ', ' + otherOptions : ''} })`;
    }
  );
  
  // 修复 findOne({ id }, { select: [...] }) 格式
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*,\s*{\s*select:\s*\[([^\]]+)\]([^}]*)\s*}\s*\)/g,
    (match, conditions, select, otherOptions) => {
      return `.findOne({ where: { ${conditions} }, select: [${select}]${otherOptions ? ', ' + otherOptions : ''} })`;
    }
  );
  
  return fixed;
}

// 修复 find 调用
function fixFindCalls(content) {
  let fixed = content;
  
  // 修复 find({ field: In([...]) }) 调用
  fixed = fixed.replace(
    /\.find\(\s*{\s*([^:]+):\s*In\(([^)]+)\)\s*}\s*\)/g,
    (match, field, values) => {
      return `.find({ where: { ${field}: In(${values}) } })`;
    }
  );
  
  // 修复 find({ field: value }, { relations: [...] }) 格式
  fixed = fixed.replace(
    /\.find\(\s*{\s*([^}]+)\s*}\s*,\s*{\s*relations:\s*\[([^\]]+)\]([^}]*)\s*}\s*\)/g,
    (match, conditions, relations, otherOptions) => {
      return `.find({ where: { ${conditions} }, relations: [${relations}]${otherOptions ? ', ' + otherOptions : ''} })`;
    }
  );
  
  return fixed;
}

// 修复 count 调用
function fixCountCalls(content) {
  let fixed = content;
  
  // 修复 count({ where: { field: value } }) 调用
  fixed = fixed.replace(
    /\.count\(\s*{\s*where:\s*{\s*([^}]+)\s*}\s*}\s*\)/g,
    (match, conditions) => {
      return `.countBy({ ${conditions} })`;
    }
  );
  
  return fixed;
}

// 修复 mime.getType 调用
function fixMimeGetType(content) {
  let fixed = content;
  
  // 修复 mime.getType 调用
  fixed = fixed.replace(/mime\.getType/g, 'mime.getType');
  
  // 如果文件包含 mime 导入，确保正确导入
  if (fixed.includes('mime.getType')) {
    fixed = fixed.replace(
      /import\s+\*\s+as\s+mime\s+from\s+['"]mime['"]/g,
      "import mime from 'mime'"
    );
    
    // 如果没有 mime 导入，添加它
    if (!fixed.includes("import mime from 'mime'") && !fixed.includes('import * as mime')) {
      const importMatch = fixed.match(/^import.*from.*['"][^'"]+['"];?\s*$/m);
      if (importMatch) {
        const insertIndex = fixed.indexOf(importMatch[0]) + importMatch[0].length;
        fixed = fixed.slice(0, insertIndex) + "\nimport mime from 'mime';" + fixed.slice(insertIndex);
      }
    }
  }
  
  return fixed;
}

// 修复 DataSource 注入
function fixDataSourceInjection(content) {
  let fixed = content;
  
  // 如果文件使用了 this.dataSource 但没有在构造函数中注入，添加注入
  if (fixed.includes('this.dataSource') && !fixed.includes('private readonly dataSource: DataSource')) {
    // 查找构造函数
    const constructorMatch = fixed.match(/constructor\s*\(\s*([^)]*)\s*\)\s*{/);
    if (constructorMatch) {
      const params = constructorMatch[1].trim();
      const newParams = params ? `${params},\n    private readonly dataSource: DataSource` : 'private readonly dataSource: DataSource';
      fixed = fixed.replace(constructorMatch[0], `constructor(\n    ${newParams},\n  ) {`);
      
      // 确保 DataSource 被导入
      if (!fixed.includes('import') || !fixed.includes('DataSource')) {
        const typeormImportMatch = fixed.match(/import\s*{([^}]+)}\s*from\s*['"]typeorm['"]/);
        if (typeormImportMatch) {
          const imports = typeormImportMatch[1];
          if (!imports.includes('DataSource')) {
            fixed = fixed.replace(typeormImportMatch[0], `import { ${imports}, DataSource } from 'typeorm'`);
          }
        } else {
          // 添加新的 typeorm 导入
          const firstImport = fixed.match(/^import.*from.*['"][^'"]+['"];?\s*$/m);
          if (firstImport) {
            const insertIndex = fixed.indexOf(firstImport[0]) + firstImport[0].length;
            fixed = fixed.slice(0, insertIndex) + "\nimport { DataSource } from 'typeorm';" + fixed.slice(insertIndex);
          }
        }
      }
    }
  }
  
  return fixed;
}

// 主修复函数
function fixAdvancedIssues(content) {
  let fixed = content;
  
  fixed = fixComplexFindOneCalls(fixed);
  fixed = fixFindCalls(fixed);
  fixed = fixCountCalls(fixed);
  fixed = fixMimeGetType(fixed);
  fixed = fixDataSourceInjection(fixed);
  
  return fixed;
}

// 修复文件
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 应用高级修复
    content = fixAdvancedIssues(content);
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    return;
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`📁 Found ${tsFiles.length} TypeScript files`);
  
  console.log('🔧 Starting advanced TypeORM fixes...');
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${tsFiles.length} total files.`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixAdvancedIssues };
