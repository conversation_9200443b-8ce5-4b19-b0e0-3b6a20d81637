const fs = require('fs');
const path = require('path');

// 递归获取所有 TypeScript 文件
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('dist')) {
      getAllTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 修复函数
function fixTypeOrmIssues(content) {
  let fixed = content;
  
  // 1. 修复 CACHE_MANAGER 导入
  fixed = fixed.replace(
    /import\s*{\s*([^}]*),?\s*CACHE_MANAGER\s*,?\s*([^}]*)\s*}\s*from\s*'@nestjs\/common'/g,
    (match, before, after) => {
      const imports = [before, after].filter(Boolean).join(', ').replace(/,\s*,/g, ',').trim();
      return `import { ${imports} } from '@nestjs/common'\nimport { CACHE_MANAGER } from '@nestjs/cache-manager'`;
    }
  );
  
  // 2. 修复 CacheModule 导入
  fixed = fixed.replace(
    /import\s*{\s*CacheModule\s*,\s*([^}]+)\s*}\s*from\s*'@nestjs\/common'/g,
    'import { $1 } from \'@nestjs/common\'\nimport { CacheModule } from \'@nestjs/cache-manager\''
  );
  
  // 3. 修复 Connection 导入和使用
  fixed = fixed.replace(/import.*Connection.*from\s*['"]typeorm['"]/g, (match) => {
    return match.replace(/Connection/g, 'DataSource');
  });
  
  // 4. 修复 FindConditions 导入
  fixed = fixed.replace(/FindConditions/g, 'FindOptionsWhere');
  
  // 5. 修复 getManager() 使用
  fixed = fixed.replace(/getManager\(\)/g, 'this.dataSource.manager');
  fixed = fixed.replace(/await\s+getManager\(\)\.transaction/g, 'await this.dataSource.transaction');
  
  // 6. 修复 TransactionManager 和 Transaction 装饰器
  fixed = fixed.replace(/import.*TransactionManager.*from\s*['"]typeorm['"].*\n/g, '');
  fixed = fixed.replace(/import.*Transaction.*from\s*['"]typeorm['"].*\n/g, '');
  fixed = fixed.replace(/@Transaction\(\)\s*\n\s*/g, '');
  fixed = fixed.replace(/@TransactionManager\(\)\s*/g, '');
  
  return fixed;
}

// 修复 findOne 调用的复杂模式
function fixFindOneCalls(content) {
  let fixed = content;
  
  // 修复简单的 findOne({ field: value }) 调用
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      return `.findOne({ where: { ${conditions} } })`;
    }
  );
  
  // 修复 findOne(conditions, options) 调用
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*([^}]+)\s*}\s*,\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions, options) => {
      // 如果 conditions 已经包含 where，跳过
      if (conditions.includes('where:')) {
        return match;
      }
      return `.findOne({ where: { ${conditions} }, ${options} })`;
    }
  );
  
  // 修复 find({ field: value }) 调用
  fixed = fixed.replace(
    /\.find\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，跳过
      if (conditions.includes('where:') || conditions.includes('take:') || conditions.includes('skip:')) {
        return match;
      }
      return `.find({ where: { ${conditions} } })`;
    }
  );
  
  // 修复 count({ field: value }) 调用
  fixed = fixed.replace(
    /\.count\(\s*{\s*([^}]+)\s*}\s*\)/g,
    (match, conditions) => {
      // 如果已经包含 where，使用 count，否则使用 countBy
      if (conditions.includes('where:')) {
        return match;
      }
      return `.countBy({ ${conditions} })`;
    }
  );
  
  return fixed;
}

// 修复字符串 where 条件
function fixStringWhereConditions(content) {
  let fixed = content;
  
  // 修复字符串 where 条件，转换为 createQueryBuilder
  fixed = fixed.replace(
    /\.find\(\s*{\s*where:\s*`([^`]+)`([^}]*)\s*}\s*\)/g,
    (match, whereCondition, otherOptions) => {
      const tableName = 'entity'; // 默认表名
      return `.createQueryBuilder('${tableName}').where('${whereCondition}').getMany()`;
    }
  );
  
  fixed = fixed.replace(
    /\.findOne\(\s*{\s*where:\s*`([^`]+)`([^}]*)\s*}\s*\)/g,
    (match, whereCondition, otherOptions) => {
      const tableName = 'entity'; // 默认表名
      return `.createQueryBuilder('${tableName}').where('${whereCondition}').getOne()`;
    }
  );
  
  return fixed;
}

// 主修复函数
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // 应用所有修复
    content = fixTypeOrmIssues(content);
    content = fixFindOneCalls(content);
    content = fixStringWhereConditions(content);
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found');
    return;
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = getAllTsFiles(srcDir);
  console.log(`📁 Found ${tsFiles.length} TypeScript files`);
  
  console.log('🔧 Starting TypeORM fixes...');
  let fixedCount = 0;
  
  for (const file of tsFiles) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Completed! Fixed ${fixedCount} files out of ${tsFiles.length} total files.`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, fixTypeOrmIssues, fixFindOneCalls, fixStringWhereConditions };
